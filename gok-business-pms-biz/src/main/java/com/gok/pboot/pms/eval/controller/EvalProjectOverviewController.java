package com.gok.pboot.pms.eval.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.eval.entity.dto.EvalProjectOverviewDTO;
import com.gok.pboot.pms.eval.entity.vo.EvalProjectOverviewVO;
import com.gok.pboot.pms.eval.entity.vo.EvalTaskListVO;
import com.gok.pboot.pms.eval.service.IEvalProjectOverviewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目整体评价前端控制器
 *
 * <AUTHOR>
 * @menu 项目评价
 * @date 2025/05/08
 */
@Slf4j
@Api(tags = "项目整体评价")
@RestController
@RequestMapping("/evalProjectOverview")
@RequiredArgsConstructor
public class EvalProjectOverviewController {

    private final IEvalProjectOverviewService evalProjectOverviewService;

    /**
     * 自动创建项目整体评价
     *
     * @return
     */
    @Inner(false)
    @GetMapping("/autoSave")
    public ApiResult<String> autoSaveProjectOverviewEval() {
        evalProjectOverviewService.autoSaveProjectOverviewEval();
        return ApiResult.success("操作成功");
    }

    /**
     * 查询项目评价详情
     *
     * @param projectId 项目ID
     * @return 项目评价详情
     */
    @ApiOperation("查询项目评价详情")
    @GetMapping("/{projectId}")
    public ApiResult<EvalProjectOverviewVO> findEvalProjectOverviewByProjectId(@PathVariable("projectId") Long projectId) {
        return ApiResult.success(evalProjectOverviewService.findEvalProjectOverviewByProjectId(projectId));
    }

    /**
     * 分页查询项目评价详情列表
     *
     * @return 项目评价详情列表
     */
    @ApiOperation("分页查询项目评价详情列表")
    @PostMapping("/page")
    public ApiResult<Page<EvalProjectOverviewVO>> findEvalProjectOverviewList(PageRequest pageRequest, @RequestBody EvalProjectOverviewDTO request) {
        return ApiResult.success(evalProjectOverviewService.findEvalProjectOverviewList(pageRequest, request));
    }

    /**
     * 导出项目评价列表
     *
     * @param dto 查询条件
     * @return {@link ApiResult}<{@link Page}<{@link EvalTaskListVO}>>
     */
    @ResponseExcel(name = "项目评价")
    @GetMapping("/export")
    public List<EvalProjectOverviewVO> export(EvalProjectOverviewDTO dto) {
        return evalProjectOverviewService.export(dto);
    }

    /**
     * 查询当前用户未评价的项目ID列表
     *
     * @return 未评价的项目ID列表
     */
    @ApiOperation("查询当前用户未评价的项目ID列表")
    @GetMapping("/unevaluated")
    public ApiResult<List<Long>> findUnevaluatedProjectIds(EvalProjectOverviewDTO request) {
        return ApiResult.success(evalProjectOverviewService.findUnevaluatedProjectIds(request));
    }

}