package com.gok.pboot.pms.common.base;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;

/**
 * <AUTHOR> @ 163.com)
 * @version 1.0
 * @date 2016年11月25日
 */
@Data
@EqualsAndHashCode(callSuper = false)
//@ToString(callSuper = true)
public class BeanEntity<T> extends BaseEntity<T> {

    private static final long serialVersionUID = -6413790591579149234L;
    /**
     * 创建人
     */
    @TableField(exist = true)
    private String creator;

    /**
     * 创建人
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long creatorId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp ctime;

    /**
     * 修改人
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long modifierId;
    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp mtime;

    /**
     * 删除标识 0正常 1.删除
     */
    @TableLogic
    private Integer delFlag;

}
