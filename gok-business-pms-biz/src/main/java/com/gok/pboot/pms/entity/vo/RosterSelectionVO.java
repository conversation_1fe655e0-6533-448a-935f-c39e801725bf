package com.gok.pboot.pms.entity.vo;

import com.dtflys.forest.utils.StringUtils;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.entity.bo.RosterSelectionBO;
import com.gok.pboot.pms.enumeration.EmployeeStatusEnum;
import lombok.*;

/**
 * 花名册选项对象
 *
 * <AUTHOR>
 * @version 1.3.4
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class RosterSelectionVO {

    /**
     * 人员ID
     */
    private String id;

    /**
     * 人员姓名
     */
    private String realName;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 是否离职
     */
    private <PERSON><PERSON><PERSON> resigned;

    /**
     * 展示文本
     */
    private String displayText;

    public static RosterSelectionVO of(RosterSelectionBO bo, String deptName) {
        RosterSelectionVO result = new RosterSelectionVO();
        Integer employeeStatus = bo.getEmployeeStatus();
        String aliasName = bo.getAliasName();

        result.setId(String.valueOf(bo.getId()));
        result.setRealName(aliasName);
        result.setDeptName(deptName);
        result.setResigned(
                EnumUtils.valueEquals(employeeStatus, EmployeeStatusEnum.RESIGN)
        );
        result.setDisplayText(StringUtils.isBlank(deptName) ? aliasName : aliasName + "（"  + deptName + "）");

        return result;
    }
}
