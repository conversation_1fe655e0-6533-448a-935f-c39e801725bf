package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.entity.EntityOption;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * - 实体标识映射 -
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface EntityOptionMapper extends BaseMapper<EntityOption> {

    /**
     * 按实体 ID 和符号判断是否存在
     *
     * @param entityId 实体 ID
     * @param sign     标志
     * @return boolean
     */
    boolean existsByEntityIdAndSign(@Param("entityId") Long entityId, @Param("sign") String sign);

    /**
     * 按标志查找
     *
     * @param sign 标志
     * @return {@link List}<{@link EntityOption}>
     */
    List<EntityOption> findBySign(String sign);

    /**
     * 按符号查找 ID
     *
     * @param sign 标志
     * @return {@link List}<{@link Long}>
     */
    List<Long> findIdsBySign(String sign);

    /**
     * 查找按符号设置 ID
     *
     * @param sign 标志
     * @return {@link Set}<{@link Long}>
     */
    Set<Long> findIdSetBySign(String sign);
}
