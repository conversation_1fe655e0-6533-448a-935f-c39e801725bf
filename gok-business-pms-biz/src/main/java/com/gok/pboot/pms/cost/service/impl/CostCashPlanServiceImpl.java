package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.components.common.str.StrUtils;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.DbApiUtil;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.domain.CostCashPlan;
import com.gok.pboot.pms.cost.entity.domain.CostCashPlanVersion;
import com.gok.pboot.pms.cost.entity.dto.CostCashPlanDetailDTO;
import com.gok.pboot.pms.cost.entity.dto.CostCashPlanDto;
import com.gok.pboot.pms.cost.entity.dto.CostCashPlanSaveDTO;
import com.gok.pboot.pms.cost.entity.vo.CostCashPlanVO;
import com.gok.pboot.pms.cost.entity.vo.CostCashPlanVersionVO;
import com.gok.pboot.pms.cost.enums.CostRequestStatusEnum;
import com.gok.pboot.pms.cost.enums.VersionStatusEnum;
import com.gok.pboot.pms.cost.mapper.CostCashPlanMapper;
import com.gok.pboot.pms.cost.mapper.CostCashPlanVersionMapper;
import com.gok.pboot.pms.cost.service.ICostBaselineVersionRecordService;
import com.gok.pboot.pms.cost.service.ICostCashPlanService;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.enumeration.CostManageStatusEnum;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 成本现金计划服务实施
 *
 * <AUTHOR>
 * @date 2025/02/26
 */
@Service
@RequiredArgsConstructor
public class CostCashPlanServiceImpl extends ServiceImpl<CostCashPlanMapper, CostCashPlan> implements ICostCashPlanService {

    private final ProjectInfoMapper projectInfoMapper;
    private final CostCashPlanVersionMapper versionMapper;
    private final ICostBaselineVersionRecordService baselineVersionRecordService;
    private final DbApiUtil dbApiUtil;

    private final static String EXIST_MONTH_LENGTH_LONGER = "EXIST_MONTH_LENGTH_LONGER";
    private final static String EXIST_MONTH_LENGTH_SHORTER = "EXIST_MONTH_LENGTH_SHORTER";
    private final static String MONTH_CONTENT_MISMATCH = "MONTH_CONTENT_MISMATCH";

    @Override
    public List<CostCashPlanVO> getCostCashPlanList(CostCashPlanDto request) {
        Long versionId = null == request.getVersionId()
                ? Optional.ofNullable(versionMapper.getCurrentVersion(request.getProjectId())).orElse(new CostCashPlanVersion()).getId()
                : request.getVersionId();
        if (null == versionId) {
            return ListUtil.empty();
        }

        // 获取计划列表
        List<CostCashPlanVO> planList = baseMapper.getCostCashPlanList(Arrays.asList(versionId));
        return getCostCashPlanList(planList);
    }

    @Override
    public List<CostCashPlanVO> getCostCashPlanList(List<CostCashPlanVO> planList) {
        if (CollUtil.isEmpty(planList)) {
            return ListUtil.empty();
        }

        // 初始化累计值
        AtomicReference<BigDecimal> totalIncome = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> totalLaborCost = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> totalExpenseCost = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> totalOutsourcingCost = new AtomicReference<>(BigDecimal.ZERO);

        planList.forEach(plan -> {
            // 更新累计值并设置
            totalIncome.updateAndGet(v -> v.add(plan.getMonthIncome() == null ? BigDecimal.ZERO : plan.getMonthIncome()));
            plan.setTotalIncome(totalIncome.get());

            totalLaborCost.updateAndGet(v -> v.add(plan.getLaborCost() == null ? BigDecimal.ZERO : plan.getLaborCost()));
            plan.setTotalLaborCost(totalLaborCost.get());

            totalExpenseCost.updateAndGet(v -> v.add(plan.getExpenseCost() == null ? BigDecimal.ZERO : plan.getExpenseCost()));
            plan.setTotalExpenseCost(totalExpenseCost.get());

            totalOutsourcingCost.updateAndGet(v -> v.add(plan.getOutsourcingCost() == null ? BigDecimal.ZERO : plan.getOutsourcingCost()));
            plan.setTotalOutsourcingCost(totalOutsourcingCost.get());

            // 计算总流出累计
            BigDecimal totalOutcome = totalLaborCost.get().add(totalExpenseCost.get()).add(totalOutsourcingCost.get());
            plan.setTotalOutcome(totalOutcome);

            // 计算现金流金额和利息
            BigDecimal cashFlow = totalIncome.get().subtract(totalOutcome);
            plan.setCashFlowAmount(cashFlow);
            plan.setCashFlowInterest(computeCashFlowInterest(cashFlow));
        });

        return planList;
    }

    /**
     * 计算垫资成本
     *
     * @param cashFlow 现金流量
     * @return {@link BigDecimal }
     */
    private static BigDecimal computeCashFlowInterest(BigDecimal cashFlow) {
        return cashFlow.multiply(new BigDecimal("0.10")).divide(new BigDecimal("12"), 0, RoundingMode.HALF_UP);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveCostCashPlan(CostCashPlanSaveDTO dto) {
        Long projectId = dto.getProjectId();
        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
        if (null == projectInfo) {
            return null;
        }
        // 获取当前版本
        CostCashPlanVersion currentVersion = versionMapper.getCurrentVersion(projectId);
        List<CostCashPlanDetailDTO> requestPlanList = dto.getPlanList();
        if (null != currentVersion && !currentVersion.getId().equals(dto.getVersionId())) {
            List<CostCashPlanVO> currentPlanList = baseMapper.getCostCashPlanList(Arrays.asList(currentVersion.getId()));
            if (currentPlanList.size() > requestPlanList.size()) {
                throw new BusinessException(EXIST_MONTH_LENGTH_LONGER);
            } else if (currentPlanList.size() < requestPlanList.size()) {
                throw new BusinessException(EXIST_MONTH_LENGTH_SHORTER);
            } else {
                String currentMonthStr = currentPlanList.stream()
                        .sorted(Comparator.comparing(CostCashPlanVO::getTimeMonth))
                        .map(CostCashPlanVO::getPlanMonth)
                        .collect(Collectors.joining(","));
                String requestMonthStr = requestPlanList.stream()
                        .sorted(Comparator.comparing(CostCashPlanDetailDTO::getTimeMonth))
                        .map(CostCashPlanDetailDTO::getPlanMonth)
                        .collect(Collectors.joining(","));
                if (!requestMonthStr.equals(currentMonthStr)) {
                    throw new BusinessException(MONTH_CONTENT_MISMATCH);
                }
            }
        }
        String versionNo = Objects.nonNull(currentVersion) ? currentVersion.getVersionNo() : StrUtils.EMPTY;
        // 更新历史版本状态
        versionMapper.updateHistoryVersion(projectId);

        // 创建新版本
        Long newVersionId = IdWorker.getId();
        Long currentUserId = SecurityUtils.getUser().getId();
        String creatorRole = currentUserId.equals(projectInfo.getManagerUserId()) ? "项目经理"
                : currentUserId.equals(projectInfo.getSalesmanUserId()) ? "客户经理"
                : StrUtils.EMPTY;
        CostCashPlanVersion newVersion = CostCashPlanVersion.builder()
                .projectId(projectId)
                .versionNo(CostConfigVersionServiceImpl.getVersionName(versionNo))
                .versionStatus(VersionStatusEnum.CURRENT.getValue())
                .creatorRole(creatorRole)
                .build();
        BaseBuildEntityUtil.buildInsertNoId(newVersion);
        newVersion.setId(newVersionId);
        versionMapper.insert(newVersion);

        // 保存计划明细
        List<CostCashPlan> planList = new ArrayList<>();
        for (CostCashPlanDetailDTO detail : requestPlanList) {
            CostCashPlan plan = new CostCashPlan();
            BeanUtils.copyProperties(detail, plan);
            plan.setProjectId(projectId);
            plan.setVersionId(newVersionId);
            BaseBuildEntityUtil.buildInsert(plan);
            planList.add(plan);
        }

        saveBatch(planList);

        // 同步项目基线
        baselineVersionRecordService.syncCostBaselineVersionRecord(projectId, StrUtil.EMPTY, NumberUtils.INTEGER_ZERO);
        return newVersionId;
    }

    @Override
    public Page<CostCashPlanVersionVO> findVersionPage(Long projectId, PageRequest pageRequest) {
        Page<CostCashPlanVersionVO> page = Page.of(pageRequest.getPageNumber(), pageRequest.getPageSize());
        versionMapper.findPage(page, projectId);

        List<CostCashPlanVersionVO> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return page;
        }
        // 实时查询流程状态
        Map<Long, Integer> oaRequestStatus = dbApiUtil.getOaRequestStatus(CollStreamUtil.toList(records, CostCashPlanVersionVO::getRequestId));
        records.forEach(item -> {
            Long requestId = item.getRequestId();
            if (requestId != null) {
                Integer requestStatus = oaRequestStatus.get(requestId);
                CostManageStatusEnum statusEnum = CostManageStatusEnum.getEnumByFlowStatus(requestStatus);
                item.setStatus(statusEnum.getValue());
                item.setStatusStr(statusEnum.getName());
                item.setRequestStatus(statusEnum.getFlowStatus());
                item.setRequestStatusName(CostRequestStatusEnum.getNameByNodeType(requestStatus));
            } else {
                item.setStatus(CostManageStatusEnum.DRAFT.getValue());
                item.setStatusStr(CostManageStatusEnum.DRAFT.getName());
            }
        });
        return page;
    }

    @Override
    public CostCashPlanVersionVO getCurrentVersion(Long projectId) {
        CostCashPlanVersion currentVersion = versionMapper.getCurrentVersion(projectId);
        if (currentVersion == null) {
            return new CostCashPlanVersionVO();
        }
        CostCashPlanVersionVO versionVO = BeanUtil.copyProperties(currentVersion, CostCashPlanVersionVO.class);
        Long requestId = currentVersion.getRequestId();
        if (requestId != null) {
            Map<Long, Integer> oaRequestStatus = dbApiUtil.getOaRequestStatus(Collections.singletonList(currentVersion.getRequestId()));
            Integer requestStatus = oaRequestStatus.get(requestId);
            CostManageStatusEnum statusEnum = CostManageStatusEnum.getEnumByFlowStatus(requestStatus);
            versionVO.setStatus(statusEnum.getValue());
            versionVO.setStatusStr(statusEnum.getName());
            versionVO.setRequestStatus(statusEnum.getFlowStatus());
            versionVO.setRequestStatusName(CostRequestStatusEnum.getNameByNodeType(requestStatus));
        } else {
            versionVO.setStatus(CostManageStatusEnum.DRAFT.getValue());
            versionVO.setStatusStr(CostManageStatusEnum.DRAFT.getName());
        }
        return versionVO;
    }

}