package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 结算状态枚举
 *
 * <AUTHOR>
 * @create 2025/02/18
 **/
@Getter
@AllArgsConstructor
public enum SettlementStatusEnum implements ValueEnum<Integer> {

    /**
     * 待结算
     */
    AWAIT_SETTLEMENT(0, "待结算"),

    /**
     * 已结算
     */
    SETTLEMENT(1, "已结算"),

    /**
     * 部分结算
     */
    PART_SETTLEMENT(2, "部分结算");

    private final Integer value;

    private final String name;

}
