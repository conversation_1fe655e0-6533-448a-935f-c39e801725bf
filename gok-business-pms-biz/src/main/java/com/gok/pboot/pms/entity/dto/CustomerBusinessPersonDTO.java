package com.gok.pboot.pms.entity.dto;

import com.gok.pboot.pms.entity.CustomerBusinessPerson;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
* <p>
* 客户经营单元-经营组织架构（相关负责人）RO
* </p>
*
* <AUTHOR>
* @since 2024-10-12
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CustomerBusinessPersonDTO {

    /**
     * ids数组
     */
    private List<Long> ids;

    /**
     * 相关负责人列表
     */
    private List<CustomerBusinessPerson> persons;
}
