package com.gok.pboot.pms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.entity.domain.ProjectResearchRisk;
import com.gok.pboot.pms.mapper.ProjectResearchRiskMapper;
import com.gok.pboot.pms.service.IProjectResearchRiskService;
import org.springframework.stereotype.Service;

import java.util.List;


@Service("projectResearchRiskService")
public class ProjectResearchRiskServiceImpl extends ServiceImpl<ProjectResearchRiskMapper, ProjectResearchRisk> implements IProjectResearchRiskService {

    @Override
    public List<ProjectResearchRisk> getByProjectId(Long id) {
        QueryWrapper<ProjectResearchRisk> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProjectResearchRisk::getProjectId, id);
        return baseMapper.selectList(queryWrapper);
    }

}