package com.gok.pboot.pms.entity.bo;

import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 工时审核 复用+交付 条目查询
 *
 * <AUTHOR>
 * @version 1.3.2
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class DailyReviewReuseAndDeliveryViewBO {

    /**
     * ID
     */
    private String id;

    /**
     * 人员名称
     */
    private String userRealName;

    /**
     * 所属月份日期（当月1号）
     */
    private LocalDate monthDate;

    /**
     * 审核状态
     * @see com.gok.pboot.pms.enumeration.ApprovalStatusEnum
     */
    private Integer approvalStatus;

    /**
     * 汇总工时（人天）
     */
    private Double aggregatedDays;

    /**
     * 工作日正常工时（天）
     */
    private BigDecimal normalWorkDays;

    /**
     * 休息日加班工时（天）
     */
    private BigDecimal restWorkDays;

    /**
     * 节假日加班工时（天）
     */
    private BigDecimal holidaysWorkDays;

    /**
     * 工作日调休工时（天）
     */
    private BigDecimal ompensatoryDays;

    /**
     * 类型（"人才复用" | "交付人员"）
     */
    private String type;

    /**
     * 审核人（最后修改人）
     */
    private String approvalName;
}
