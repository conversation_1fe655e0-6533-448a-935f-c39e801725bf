package com.gok.pboot.pms.cost.entity.vo;

import com.gok.pboot.pms.common.join.ProjectInDailyPaperEntry;
import com.gok.pboot.pms.cost.entity.domain.CostTaskDailyPaper;
import com.gok.pboot.pms.cost.entity.domain.CostTaskDailyPaperEntry;
import com.gok.pboot.pms.cost.entity.domain.CostTomorrowPlanPaperEntry;
import com.gok.pboot.pms.entity.Holiday;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工单工时详情VO
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CostTaskDailyPaperDetailsVO {
    /**
     * 日报信息
     */
    private CostTaskDailyPaperVO dailyPaper;

    /**
     * 异常情况
     */
    private String abnormalDesc;

    /**
     * 日报条目信息
     */
    private List<CostTaskDailyPaperEntryVO> entries;

    private List<CostTaskTomorrowPlanEntryVO> tomorrowPlanPaperEntries;

    public CostTaskDailyPaperDetailsVO(
            CostTaskDailyPaper dailyPaper,
            List<CostTaskDailyPaperEntry> entries,
            List<CostTomorrowPlanPaperEntry> tomorrowPlanPaperEntries,
            Map<Long, ProjectInDailyPaperEntry> projects
    ) {
        this.tomorrowPlanPaperEntries = tomorrowPlanPaperEntries.stream()
                .map(tomorrowPlanPaperEntry -> new CostTaskTomorrowPlanEntryVO(tomorrowPlanPaperEntry,
                        projects.getOrDefault(tomorrowPlanPaperEntry.getProjectId(), new ProjectInDailyPaperEntry())))
                .collect(Collectors.toList());
        this.dailyPaper = new CostTaskDailyPaperVO(dailyPaper);
        this.entries = entries.stream()
                .map(entry -> new CostTaskDailyPaperEntryVO(entry, projects.get(entry.getProjectId())))
                .collect(Collectors.toList());
    }

    public CostTaskDailyPaperDetailsVO(
            CostTaskDailyPaper dailyPaper,
            List<CostTaskDailyPaperEntry> entries,
            List<CostTomorrowPlanPaperEntry> tomorrowPlanPaperEntries,
            Map<Long, ProjectInDailyPaperEntry> projects,
            String userRealName,
            String abnormalDesc,
            BigDecimal leaveHour,
            BigDecimal compensatoryLeave,
            Holiday holiday
    ) {
        this.tomorrowPlanPaperEntries = tomorrowPlanPaperEntries.stream()
                .map(tomorrowPlanPaperEntry ->
                        new CostTaskTomorrowPlanEntryVO(tomorrowPlanPaperEntry,
                                projects.getOrDefault(tomorrowPlanPaperEntry.getProjectId(),new ProjectInDailyPaperEntry())))
                .collect(Collectors.toList());
        this.dailyPaper = new CostTaskDailyPaperVO(dailyPaper, userRealName, leaveHour,compensatoryLeave,holiday);
        this.entries = entries.stream()
                .map(entry -> new CostTaskDailyPaperEntryVO(entry, projects.get(entry.getProjectId())))
                .collect(Collectors.toList());
        this.abnormalDesc = abnormalDesc;
    }
} 