package com.gok.pboot.pms.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.domain.ProjectCustomerState;
import com.gok.pboot.pms.entity.vo.ProjectCustomerStateVO;
import com.gok.pboot.pms.enumeration.ExpectedOrderAmountWeightedEnum;
import com.gok.pboot.pms.enumeration.ProjectCuntomerRoleEnum;
import com.gok.pboot.pms.mapper.ProjectCustomerStateMapper;
import com.gok.pboot.pms.service.IProjectCustomerStateService;
import com.google.common.base.Strings;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Service
public class ProjectCustomerStateServiceImpl extends ServiceImpl<ProjectCustomerStateMapper, ProjectCustomerState> implements IProjectCustomerStateService {

    @Override
    public Page<ProjectCustomerStateVO> findPage(PageRequest pageRequest, Map<String, Object> filter) {
        Page<ProjectCustomerStateVO> listPage = baseMapper.findListPage(new Page<>(pageRequest.getPageNumber(),
                pageRequest.getPageSize()), filter);
        List<ProjectCustomerStateVO> records = listPage.getRecords();
        records.forEach(v -> {
            v.setContact(Strings.nullToEmpty(v.getContact()));
            v.setContactPhone(Strings.nullToEmpty(v.getContactPhone()));
            v.setProjectId(v.getProjectId());
            //角色
            String role = Strings.nullToEmpty(v.getRole());
            v.setRole(role);
            v.setRoleName(EnumUtils.getNameByValue(ProjectCuntomerRoleEnum.class, role));
            //客情状态
            String status = Strings.nullToEmpty(v.getStatus());
            v.setStatus(status);
            v.setStatusName(StrUtil.isBlank(status) ? StrUtil.EMPTY : ExpectedOrderAmountWeightedEnum.getNameByVal(Integer.parseInt(status)));
        });
        return listPage;
    }

}
