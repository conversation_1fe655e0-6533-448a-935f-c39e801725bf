package com.gok.pboot.pms.entity.vo;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerListVO {

    private Long id;

    /**
     * 客户名称
     */
    private String khmc;
    /**
     * 客户分级
     */
    private Integer khfj;
    /**
     * 客户分级Txt
     */
    private String khfjTxt;
    /**
     * 客户所在地
     */
    private Integer khszd;
    /**
     * 客户所在地Txt
     */
    private String khszdTxt;
    /**
     * 行业"
     */
    private Integer khxyyj;

    /**
     * 行业Txt
     */
    private String khxyyjTxt;
    /**
     * 归属业务部门
     */
    private Long gsywbmejbm;

    /**
     * 归属业务部门Txt
     */
    private String gsywbmejbmTxt;
    /**
     * 客户经理id
     */
    private String khjl;
    /**
     * 客户经理名字
     */
    private String khjlxm;

    /**
     * 跟进商机
     */
    private Integer gjsj=0;
    /**
     * 在建商机
     */
    private Integer zjsj=0;
    /**
     * 结项商机
     */
    private Integer jxsj=0;

    /**
     * 关联的
     */
    private Long userId;

    /**
     * true=已关注  false未关注
     */
    private Boolean isAttention=false;


    /**
     * 创建时间
     */
    private String ctime;

    /**
     * 业务角色
     */
    private String  managerIds;
}