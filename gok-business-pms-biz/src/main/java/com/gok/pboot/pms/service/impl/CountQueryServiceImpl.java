package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.bcp.message.dto.BcpMessageBatchDTO;
import com.gok.bcp.message.dto.BcpMessageTargetBatchDTO;
import com.gok.bcp.message.dto.BcpMessageTargetDTO;
import com.gok.bcp.message.entity.enums.ChannelEnum;
import com.gok.bcp.message.entity.enums.MsgTypeEnum;
import com.gok.bcp.message.entity.enums.SourceEnum;
import com.gok.bcp.message.entity.enums.TargetTypeEnum;
import com.gok.bcp.message.entity.model.BcpMessageContentModel;
import com.gok.bcp.message.entity.model.WeComBatchModel;
import com.gok.bcp.message.entity.model.WeComModel;
import com.gok.bcp.message.feign.RemoteSendMsgService;
import com.gok.bcp.upms.dto.DeptCacheDto;
import com.gok.bcp.upms.dto.UserPmsDTO;
import com.gok.bcp.upms.feign.RemoteDeptService;
import com.gok.bcp.upms.feign.RemoteOutPmsService;
import com.gok.bcp.upms.vo.SysUserOutVO;
import com.gok.components.common.user.PigxUser;
import com.gok.pboot.common.core.constant.SecurityConstants;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.*;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.BaseEntity;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.constant.EntitySign;
import com.gok.pboot.pms.common.constant.FunctionConstants;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.entity.*;
import com.gok.pboot.pms.entity.bo.TaskReviewerInfoBO;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.domain.ProjectTaskeUser;
import com.gok.pboot.pms.entity.domain.Roster;
import com.gok.pboot.pms.entity.dto.*;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.entity.vo.facade.AllocationInternalExportExcelVO;
import com.gok.pboot.pms.enumeration.*;
import com.gok.pboot.pms.enumeration.facade.BusinessStatusEnum;
import com.gok.pboot.pms.handler.DailyPaperAnalyzer;
import com.gok.pboot.pms.handler.ProjectScopeHandle;
import com.gok.pboot.pms.handler.perm.PmsRetriever;
import com.gok.pboot.pms.mapper.*;
import com.gok.pboot.pms.service.ICompensatoryLeaveDataService;
import com.gok.pboot.pms.service.ICountQueryService;
import com.gok.pboot.pms.service.processor.SaturationCalculator;
import com.google.common.base.Charsets;
import com.google.common.base.Strings;
import com.google.common.collect.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import javax.validation.ValidationException;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.gok.pboot.pms.common.base.BaseConstants.YES;
import static com.gok.pboot.pms.enumeration.ApprovalStatusEnum.*;

/**
 * 工时查询服务类
 *
 * <AUTHOR> Xinyp
 * @date 2022-08-24 09:43
 */
@Service
@Slf4j
@Transactional(readOnly = true, rollbackFor = Exception.class)
@RequiredArgsConstructor
public class CountQueryServiceImpl implements ICountQueryService {

    private final DailyPaperMapper dailyPaperMapper;
    private final DailyPaperEntryMapper dailyPaperEntryMapper;
    private final PersonnelReuseMapper personnelReuseMapper;
    private final ProjectMapper projectMapper;
    private final OvertimeLeaveDataMapper overtimeLeaveDataMapper;
    private final SpecialSettingMapper specialSettingMapper;
    private final EntityOptionMapper entityOptionMapper;
    private final SaturationCalculator saturationCalculator;
    private final ProjectInfoMapper projectInfoMapper;
    private final ProjectTaskeUserMapper projectTaskeUserMapper;
    private final RosterMapper rosterMapper;
    private final FilingMapper filingMapper;
    private final HolidayMapper holidayMapper;
    private final PmsRetriever pmsRetriever;
    private final PrivilegeMapper privilegeMapper;
    private final ProjectTaskeMapper projectTaskeMapper;
    private final ProjectScopeHandle projectScopeHandle;
    private final DailyPaperAnalyzer dailyPaperAnalyzer;
    private final ICompensatoryLeaveDataService overtimeLeaveDataService;
    // 接入中台的接口
    private final RemoteOutPmsService remoteOutPmsService;
    private final RemoteDeptService remoteDeptService;
    private final RemoteSendMsgService remoteSendMsgService;
    private final BcpLoggerUtils bcpLoggerUtils;

    @Value("${project.task.leftUrl}")
    private String hostUrl;

    @Value("${pushMessage.redirectPrefixUrl}")
    private String redirectPrefixUrl;

    private static final Integer DEFAULT_PAGE_NUM = 1;
    private static final Integer DEFAULT_PAGE_SIZE = 50;
    private static final String ADD_FLAG = "+";
    private static final String CONTENT_TYPE_SHEET= "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    private static final String CONTENT_TYPE_JSON = "application/json";
    private static final String PARAM_CONTENT_DISPOSITION = "Content-disposition";
    private static final String CONTENT_DISPOSITION = "attachment;filename*=utf-8''";
    private static final String XLSX_SUFFIX = ".xlsx";
    private static final String PROJECT_IDS = "projectIds";

    @Override
    public ApiResult<Page<DailyFindPageVO>> dailyFindPage(
            PageRequest pageRequest,
            DailyFindPageDTO dailyFindPageDTO
    ) {
        // 通过一次前置查询（应用数据权限），得到分页后的用户ID列表
        Page<Long> userIdsFrontQueryPage;
        List<Long> userIds;
        int userIdsSize;
        int rangeDays;
        Table<Long, LocalDate, DailyPaper> userIdAndSubmissionDateAndPaperTable;
        Map<Long, DailyPaper> userIdAndPaperMap;
        Map<Long, Roster> userIdRosterMap;
        Table<Long, LocalDate, List<OvertimeLeaveData>> userIdAndSubmissionDateAndleaveDataListTable;
        LocalDate startDate;
        LocalDate endDate;
        Multimap<Long, DailyPaperQcVO> userIdAndDailyPaperQcVOMap;
        List<DeptCacheDto> deptList;
        Map<Long, SysDept> deptIdMap;
        Set<LocalDate> holidayDates;
        List<Holiday> holidayList;
        Map<LocalDate, Integer> holidayTypeMap = new HashMap<>();
        Table<Long, LocalDate, List<CompensatoryLeaveDataDetailVO>> userIdAndDateAndCompensatoryLeaveTable;
        validateDailyFindPageDTO(dailyFindPageDTO);
        // 前置查询分页后的用户ID（应用人员数据权限）
        userIdsFrontQueryPage = new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize());
        userIds = dailyPaperMapper.findSubmitUserIdList(userIdsFrontQueryPage, dailyFindPageDTO);
        if (userIds.isEmpty()) {
            return ApiResult.success(PageUtils.mapTo(userIdsFrontQueryPage, x -> null));
        }
        userIdsFrontQueryPage.setRecords(userIds);
        dailyFindPageDTO.setUserIds(userIds);
        // 查询整合日报数据
        userIdsSize = userIds.size();
        startDate = dailyFindPageDTO.getStartTime();
        endDate = dailyFindPageDTO.getEndTime();
        rangeDays = (int) startDate.until(endDate, ChronoUnit.DAYS);
        userIdAndSubmissionDateAndPaperTable = HashBasedTable.create(userIdsSize, Math.max(1, rangeDays));
        dailyPaperMapper.findSubmitList(dailyFindPageDTO).forEach(paper -> {
            if (EnumUtils.valueEquals(paper.getApprovalStatus(), WTJ)) {
                // 未提交算作未填报
                paper.setApprovalStatus(WTB.getValue());
                paper.setDailyHourCount(BigDecimal.ZERO);
                paper.setAddedHourCount(BigDecimal.ZERO);
                paper.setProjectCount(0);
                paper.setTaskCount(0);
            }
            userIdAndSubmissionDateAndPaperTable.put(paper.getUserId(), paper.getSubmissionDate(), paper);
        });
        if (userIdAndSubmissionDateAndPaperTable.isEmpty()) {
            log.warn("找不到日报信息: {}", dailyFindPageDTO);

            return ApiResult.success(PageUtils.mapTo(userIdsFrontQueryPage, x -> null));
        }
        // 查询用户信息
        userIdRosterMap = rosterMapper.findUserIdMap(userIds);
        if (userIdRosterMap.isEmpty()) {
            log.warn("找不到用户信息: {}", userIds);

            return ApiResult.success(PageUtils.mapTo(userIdsFrontQueryPage, x -> null));
        }
        holidayList = holidayMapper.selByDateRange(startDate, endDate);
        List<LocalDate> holidayDateList = holidayList.stream().map(Holiday::getDayDate).collect(Collectors.toList());
        // 查询整合请假信息
        userIdAndSubmissionDateAndleaveDataListTable = HashBasedTable.create(userIdsSize, Math.max(1, rangeDays));
        overtimeLeaveDataMapper.getLeaveDataListByUserIds(startDate, endDate, userIds)
                .forEach(leaveData -> {
                    LocalDate date = leaveData.getBelongdate();
                    Long userId = leaveData.getOaId();
                    if (!holidayDateList.contains(date)) {
                        List<OvertimeLeaveData> exists = userIdAndSubmissionDateAndleaveDataListTable.get(userId, date);

                        if (exists == null) {
                            userIdAndSubmissionDateAndleaveDataListTable.put(userId, date, Lists.newArrayList(leaveData));
                        } else {
                            exists.add(leaveData);
                        }
                    }
                });
        // 组装日报数据、处理请休假数据
        userIdAndDailyPaperQcVOMap =
                HashMultimap.create(userIdAndSubmissionDateAndPaperTable.size(), Math.max(1, rangeDays));

        // 查询整合项目调休信息
        userIdAndDateAndCompensatoryLeaveTable = HashBasedTable.create(userIdsSize, Math.max(1, rangeDays));
        overtimeLeaveDataService.findByDateTimeRangeAndUserIds(startDate,endDate.plusDays(1),userIds)
                .forEach(compensatoryLeaveData -> {
                    Long userId = compensatoryLeaveData.getOaId();
                    LocalDate date = compensatoryLeaveData.getBelongDate();
                    if (!holidayDateList.contains(date)) {
                        List<CompensatoryLeaveDataDetailVO> exists = userIdAndDateAndCompensatoryLeaveTable.get(userId, date);

                        if (exists == null) {
                            userIdAndDateAndCompensatoryLeaveTable.put(userId, date, Lists.newArrayList(compensatoryLeaveData));
                        } else {
                            exists.add(compensatoryLeaveData);
                        }
                    }
                });

        if (CollectionUtil.isNotEmpty(holidayList)) {
            holidayTypeMap = holidayList.stream().collect(Collectors.toMap(Holiday::getDayDate, Holiday::getHolidayType, (a, b) -> a));
        }
        holidayDates = holidayList.stream().map(Holiday::getDayDate).collect(Collectors.toSet());
        do {
            userIdAndPaperMap = userIdAndSubmissionDateAndPaperTable.column(startDate);
            startDate = startDate.plusDays(1);
            if (userIdAndPaperMap == null) {
                continue;
            }
            Map<LocalDate, Integer> finalHolidayTypeMap = holidayTypeMap;
            userIdAndPaperMap.forEach((userId, paper) -> {
                LocalDate submissionDate = paper.getSubmissionDate();
                List<OvertimeLeaveData> leaveDataList =
                        userIdAndSubmissionDateAndleaveDataListTable.get(userId, submissionDate);
                List<CompensatoryLeaveDataDetailVO> compensatoryLeaveDataList =
                        userIdAndDateAndCompensatoryLeaveTable.get(userId, submissionDate);
                DailyPaperQcVO qc = new DailyPaperQcVO();
                BigDecimal dailyHourCount = paper.getDailyHourCount();
                BigDecimal addedHourCount = paper.getAddedHourCount();
                BigDecimal yearlyLeaveHourNum = BigDecimal.ZERO;
                BigDecimal cancelLeaveHourNum = BigDecimal.ZERO;
                BigDecimal otherLeaveHourNum = BigDecimal.ZERO;
                BigDecimal avaliableLeaveHourNum;
                BigDecimal compensatoryHourCountNum = BigDecimal.ZERO;
                String type;
                StringJoiner leaveInfoPromptJoiner;
                boolean hasLeave = false;

                qc.setId(paper.getId());
                qc.setUserDeptId(paper.getUserDeptId());
                qc.setUserStatus(paper.getUserStatus());
                qc.setDailyHourCount(dailyHourCount);
                qc.setAddedHourCount(addedHourCount);
                qc.setFillingState(paper.getFillingState());
                qc.setSubmissionDate(submissionDate);
                qc.setWorkday(
                        holidayDates.contains(submissionDate) ? YesOrNoEnum.NO.getValue() : YesOrNoEnum.YES.getValue()
                );
                qc.setHolidayType(
                        Optional.ofNullable(finalHolidayTypeMap.getOrDefault(submissionDate,null)).isPresent() ? finalHolidayTypeMap.getOrDefault(submissionDate,null) : null
                );
                qc.setApprovalStatus(paper.getApprovalStatus());
                if (leaveDataList == null && compensatoryLeaveDataList == null) {
                    avaliableLeaveHourNum = BigDecimal.ZERO;
                    qc.setHasLeaveHour(false);
                    qc.setAvaliableLeavehour(avaliableLeaveHourNum);
                    qc.setLeaveHourPrompt(StringUtils.EMPTY);
                    qc.setLeaveInfoPrompt(StringUtils.EMPTY);
                } else {
                    // 先排序，让同类假期信息排列在一起，便于请假信息文字排列
                    leaveInfoPromptJoiner = new StringJoiner("，");

                    if (CollectionUtil.isNotEmpty(leaveDataList)) {
                        leaveDataList = leaveDataList.stream()
                                .sorted(Comparator.comparingInt(l -> Integer.parseInt(l.getType())))
                                .collect(Collectors.toList());
                        for (OvertimeLeaveData leaveData : leaveDataList) {
                            type = leaveData.getType();
                            if (EnumUtils.valueEquals(type, LeaveStatusEnum.XJ)) {
                                cancelLeaveHourNum = cancelLeaveHourNum.add(leaveData.getHourData());
                            } else if (EnumUtils.valueEquals(type, LeaveStatusEnum.NJ)) {
                                yearlyLeaveHourNum = yearlyLeaveHourNum.add(leaveData.getHourData());
                            } else {
                                otherLeaveHourNum = otherLeaveHourNum.add(leaveData.getHourData());
                            }
                        }
                    }
                    if (CollectionUtil.isNotEmpty(compensatoryLeaveDataList)){
                        compensatoryHourCountNum = compensatoryLeaveDataList.stream()
                                .map(CompensatoryLeaveDataDetailVO::getHourData)
                                .reduce(BigDecimal.ZERO,BigDecimal::add)
                                .setScale(2, RoundingMode.HALF_UP);
                    }

                    if (!yearlyLeaveHourNum.equals(BigDecimal.ZERO)) {
                        hasLeave = true;
                        leaveInfoPromptJoiner.add(
                                "年假" + yearlyLeaveHourNum.stripTrailingZeros().toPlainString() + "小时"
                        );
                    }
                    if (!otherLeaveHourNum.equals(BigDecimal.ZERO)) {
                        hasLeave = true;
                        leaveInfoPromptJoiner.add(
                                "请休假" + otherLeaveHourNum.stripTrailingZeros().toPlainString() + "小时"
                        );
                    }
                    if (!cancelLeaveHourNum.equals(BigDecimal.ZERO)) {
                        hasLeave = true;
                        leaveInfoPromptJoiner.add(
                                "销假" + cancelLeaveHourNum.stripTrailingZeros().toPlainString() + "小时"
                        );
                    }
                    if (!compensatoryHourCountNum.equals(BigDecimal.ZERO)) {
                        hasLeave = true;
                        leaveInfoPromptJoiner.add(
                                "项目调休" + compensatoryHourCountNum.stripTrailingZeros().toPlainString() + "小时"
                        );
                    }
                    qc.setHasLeaveHour(hasLeave);
                    if (hasLeave) {
                        avaliableLeaveHourNum = yearlyLeaveHourNum.add(otherLeaveHourNum).add(compensatoryHourCountNum)
                                .subtract(cancelLeaveHourNum)
                                .setScale(2, RoundingMode.HALF_UP);
                        if (BigDecimalUtils.SEVEN_DECIMAL.compareTo(avaliableLeaveHourNum) < 0) {
                            avaliableLeaveHourNum = BigDecimalUtils.SEVEN_DECIMAL;
                        }
                        qc.setAvaliableLeavehour(avaliableLeaveHourNum);
                        qc.setLeaveHourPrompt("有效请休假时长: " + avaliableLeaveHourNum + "小时");
                        qc.setLeaveInfoPrompt(leaveInfoPromptJoiner.toString());
                    } else {
                        avaliableLeaveHourNum = BigDecimal.ZERO;
                        qc.setLeaveHourPrompt(StringUtils.EMPTY);
                        qc.setLeaveInfoPrompt(StringUtils.EMPTY);
                        qc.setAvaliableLeavehour(BigDecimal.ZERO);
                    }
                }
                qc.setAbnormalFlag(
                        !dailyPaperAnalyzer.getDailyPaperAbnormalInfo(paper, avaliableLeaveHourNum).isEmpty()
                );
                userIdAndDailyPaperQcVOMap.put(userId, qc);
            });
        } while (endDate.plusDays(1).isAfter(startDate));
        deptList = remoteDeptService.getAllDeptList(false);
        if (CollectionUtils.isEmpty(deptList)) {
            log.warn("未找到中台部门数据");
            deptIdMap = ImmutableMap.of();
        } else {
            deptIdMap = SysDeptUtils.mapBCPDeptCacheDtoToSysDept(deptList)
                    .stream()
                    .collect(Collectors.toMap(SysDept::getDeptId, dept -> dept));
        }

        // 拼装最终结果
        return ApiResult.success(PageUtils.mapTo(userIdsFrontQueryPage, userId -> {
            DailyFindPageVO result = new DailyFindPageVO();
            Collection<DailyPaperQcVO> qcPapers = userIdAndDailyPaperQcVOMap.get(userId);
            Roster roster = userIdRosterMap.get(userId);
            DailyPaperQcVO paper;

            result.setUserId(userId);
            if (roster != null) {
                result.setName(roster.getAliasName());
            }
            if (CollectionUtils.isEmpty(qcPapers)) {
                result.setDailyPapers(ImmutableList.of());
                result.setDeptName(StringUtils.EMPTY);
                result.setPersonnelStatusName(StringUtils.EMPTY);
                result.setDailyPapers(ImmutableList.of());
                result.setAbnormalDays(0);
            } else {
                paper = qcPapers.iterator().next();
                result.setDeptName(SysDeptUtils.collectFullName(deptIdMap, paper.getUserDeptId()));
                result.setPersonnelStatusName(EnumUtils.getNameByValue(
                        PersonnelStatusEnum.class, paper.getUserStatus()
                ));
                result.setDailyPapers(qcPapers);
                result.setAbnormalDays((int) qcPapers.stream().filter(DailyPaperQcVO::getAbnormalFlag).count());
            }

            return result;
        }));
    }
    @Override
    public List<DailyExcelExportVO> dailyExport(DailyFindPageDTO dailyFindPageDTO){
        List<DailyExcelExportVO> dailyExcelExportVOList = new ArrayList<>();
        // 前置查询分页后的用户ID（应用人员数据权限）
        List<Long> userIds = dailyPaperMapper.findSubmitUserIdList(null, dailyFindPageDTO);
        if (userIds.isEmpty()) {
            return dailyExcelExportVOList;
        }
        dailyFindPageDTO.setUserIds(userIds);

        dailyExcelExportVOList = dailyPaperMapper.findDailyExcelList(dailyFindPageDTO);
        if(CollUtil.isNotEmpty(dailyExcelExportVOList)){
            //人员
            Map<Long, Roster> userIdRosterMap = rosterMapper.findUserIdMap(userIds);
            if (userIdRosterMap.isEmpty()) {
                log.warn("找不到用户信息: {}", userIds);
                return dailyExcelExportVOList;
            }
            //部门
            List<DeptCacheDto> deptList = remoteDeptService.getAllDeptList(false);
            Map<Long, SysDept> deptIdMap;
            if (CollectionUtils.isEmpty(deptList)) {
                log.warn("未找到中台部门数据");
                deptIdMap = ImmutableMap.of();
            } else {
                deptIdMap = SysDeptUtils.mapBCPDeptCacheDtoToSysDept(deptList)
                        .stream()
                        .collect(Collectors.toMap(SysDept::getDeptId, dept -> dept));
            }

            List<Holiday> holidayList = holidayMapper.selByDateRange(dailyFindPageDTO.getStartTime(), dailyFindPageDTO.getEndTime());
            List<LocalDate> holidayDateList = holidayList.stream().map(Holiday::getDayDate).collect(Collectors.toList());

            //请休假
            // 查询整合请假信息
            int rangeDays = (int) dailyFindPageDTO.getStartTime().until(dailyFindPageDTO.getEndTime(), ChronoUnit.DAYS);
            Table<Long, LocalDate, List<OvertimeLeaveData>> userIdAndSubmissionDateAndleaveDataListTable
                    = HashBasedTable.create(userIds.size(), Math.max(1, rangeDays));
            overtimeLeaveDataMapper.getLeaveDataListByUserIds(dailyFindPageDTO.getStartTime(), dailyFindPageDTO.getEndTime(), userIds)
                    .forEach(leaveData -> {
                        Long userId = leaveData.getOaId();
                        LocalDate date = leaveData.getBelongdate();
                        if (!holidayDateList.contains(date)){
                            List<OvertimeLeaveData> exists = userIdAndSubmissionDateAndleaveDataListTable.get(userId, date);

                            if (exists == null) {
                                userIdAndSubmissionDateAndleaveDataListTable.put(userId, date, Lists.newArrayList(leaveData));
                            } else {
                                exists.add(leaveData);
                            }
                        }

                    });

            // 查询整合项目调休信息
            Table<Long, LocalDate, List<CompensatoryLeaveDataDetailVO>>userIdAndDateAndCompensatoryLeaveTable = HashBasedTable.create(userIds.size(), Math.max(1, rangeDays));
            overtimeLeaveDataService.findByDateTimeRangeAndUserIds(dailyFindPageDTO.getStartTime(),dailyFindPageDTO.getEndTime().plusDays(1),userIds)
                    .forEach(compensatoryLeaveData -> {
                        Long userId = compensatoryLeaveData.getOaId();
                        LocalDate date = compensatoryLeaveData.getBelongDate();
                        if (!holidayDateList.contains(date)) {
                            List<CompensatoryLeaveDataDetailVO> exists = userIdAndDateAndCompensatoryLeaveTable.get(userId, date);

                            if (exists == null) {
                                userIdAndDateAndCompensatoryLeaveTable.put(userId, date, Lists.newArrayList(compensatoryLeaveData));
                            } else {
                                exists.add(compensatoryLeaveData);
                            }
                        }
                    });

            for (DailyExcelExportVO result: dailyExcelExportVOList) {
                Roster roster = userIdRosterMap.get(result.getUserId());
                if (roster != null) {
                    result.setName(roster.getAliasName());
                }
                result.setDeptName(SysDeptUtils.collectFullName(deptIdMap, result.getUserDeptId()));
                result.setUserStatusStr(EnumUtils.getNameByValue(
                        PersonnelStatusEnum.class, result.getUserStatus()));

                DayOfWeek weekday = result.getSubmissionDate().getDayOfWeek();
                result.setMonth(weekday.getDisplayName(TextStyle.SHORT, Locale.CHINA));

                result.setApprovalStatusStr(EnumUtils.getNameByValue(
                        ApprovalStatusEnum.class, result.getApprovalStatus()));

                result.setFillingStateStr(EnumUtils.getNameByValue(
                        DailyPaperFillingStateEnum.class, result.getFillingState()));
                //请休假
                List<OvertimeLeaveData> leaveDataList =
                        userIdAndSubmissionDateAndleaveDataListTable.get(result.getUserId(), result.getSubmissionDate());
                result.setLeaveHours(BigDecimal.ZERO);
                if (CollUtil.isNotEmpty(leaveDataList)) {
                    BigDecimal avaliableLeaveHourNum ;
                    BigDecimal yearlyLeaveHourNum = BigDecimal.ZERO;
                    BigDecimal cancelLeaveHourNum = BigDecimal.ZERO;
                    BigDecimal otherLeaveHourNum = BigDecimal.ZERO;
                    Boolean hasLeave=false;
                    // 先排序，让同类假期信息排列在一起，便于请假信息文字排列
                    leaveDataList = leaveDataList.stream()
                            .sorted(Comparator.comparingInt(l -> Integer.parseInt(l.getType())))
                            .collect(Collectors.toList());
                    for (OvertimeLeaveData leaveData : leaveDataList) {
                        String type = leaveData.getType();
                        if (EnumUtils.valueEquals(type, LeaveStatusEnum.XJ)) {
                            cancelLeaveHourNum = cancelLeaveHourNum.add(leaveData.getHourData());
                        } else if (EnumUtils.valueEquals(type, LeaveStatusEnum.NJ)) {
                            yearlyLeaveHourNum = yearlyLeaveHourNum.add(leaveData.getHourData());
                        } else {
                            otherLeaveHourNum = otherLeaveHourNum.add(leaveData.getHourData());
                        }
                    }
                    if (!yearlyLeaveHourNum.equals(BigDecimal.ZERO)) {
                        hasLeave = true;

                    }
                    if (!otherLeaveHourNum.equals(BigDecimal.ZERO)) {
                        hasLeave = true;
                    }
                    if (!cancelLeaveHourNum.equals(BigDecimal.ZERO)) {
                        hasLeave = true;
                    }
                    if (hasLeave) {
                        avaliableLeaveHourNum = yearlyLeaveHourNum.add(otherLeaveHourNum)
                                .subtract(cancelLeaveHourNum)
                                .setScale(2, RoundingMode.HALF_UP);
                        if (BigDecimalUtils.SEVEN_DECIMAL.compareTo(avaliableLeaveHourNum) < 0) {
                            avaliableLeaveHourNum = BigDecimalUtils.SEVEN_DECIMAL;
                        }
                        result.setLeaveHours(avaliableLeaveHourNum);
                    }
                }

                //项目调休
                List<CompensatoryLeaveDataDetailVO> compensatoryLeaveList =
                        userIdAndDateAndCompensatoryLeaveTable.get(result.getUserId(), result.getSubmissionDate());
                result.setCompensatoryHourCount(BigDecimal.ZERO);
                if (CollUtil.isNotEmpty(compensatoryLeaveList)) {
                    BigDecimal compensatoryHourCount = compensatoryLeaveList.stream()
                            .map(CompensatoryLeaveDataDetailVO::getHourData)
                            .reduce(BigDecimal.ZERO,BigDecimal::add)
                            .setScale(2, RoundingMode.HALF_UP);

                    result.setCompensatoryHourCount(compensatoryHourCount);
                }

                //判断异常
                result.setIsAbnormal(
                        dailyPaperAnalyzer.getAbnormalInfo(result.getSubmissionDate(), result.getApprovalStatus(),
                                result.getWorkday(), result.getDailyHourCount(), result.getLeaveHours().add(result.getCompensatoryHourCount())).isEmpty()
                                ? "否" : "是");
            }
        }

        return dailyExcelExportVOList;
    }


    @Override
    public AbnormalStatisticsVO findAbnormalStatistics(DailyFindPageDTO dto) {
        List<Long> userIds;
        List<DailyPaper> dailyPapers;
        Table<Long, LocalDate, BigDecimal> userIdAndSubmissionDateAndleaveHoursTable;
        Set<Long> abnormalPeopleUserIds;
        int totalAbnormalDays;

        validateDailyFindPageDTO(dto);
        // 数据准备
        userIds = dailyPaperMapper.findSubmitUserIdList(dto);
        if (userIds.isEmpty()) {
            return AbnormalStatisticsVO.EMPTY;
        }
        dto.setUserIds(userIds);
        dailyPapers = dailyPaperMapper.findSubmitList(dto);
        if (dailyPapers.isEmpty()) {
            return AbnormalStatisticsVO.EMPTY;
        }
        userIdAndSubmissionDateAndleaveHoursTable = HashBasedTable.create();
        // 请假数据计算（考虑销假）
        overtimeLeaveDataMapper.getLeaveDataListByUserIds(dto.getStartTime(), dto.getEndTime(), userIds)
                .forEach(leaveData -> {
                    Long userId = leaveData.getOaId();
                    LocalDate date = leaveData.getBelongdate();
                    BigDecimal exists = ObjectUtils.defaultIfNull(
                            userIdAndSubmissionDateAndleaveHoursTable.get(userId, date),
                            BigDecimal.ZERO
                    );
                    BigDecimal hourData = leaveData.getHourData();

                    exists = EnumUtils.valueEquals(leaveData.getType(), LeaveStatusEnum.XJ) ?
                            exists.subtract(hourData) : exists.add(hourData);
                    userIdAndSubmissionDateAndleaveHoursTable.put(userId, date, exists);
                });
        // 统计最终结果
        abnormalPeopleUserIds = Sets.newHashSetWithExpectedSize(userIds.size() / 2);
        totalAbnormalDays = 0;
        for (DailyPaper paper : dailyPapers) {
            Long userId = paper.getUserId();
            List<Pair<DailyPaperAbnormalEnum, String>> aInfo = dailyPaperAnalyzer.getDailyPaperAbnormalInfo(
                    paper,
                    ObjectUtils.defaultIfNull(
                            userIdAndSubmissionDateAndleaveHoursTable.get(userId, paper.getSubmissionDate()),
                            BigDecimal.ZERO
                    )
            );

            if (!aInfo.isEmpty()) {
                abnormalPeopleUserIds.add(userId);
                totalAbnormalDays++;
            }
        }

        return AbnormalStatisticsVO.of(userIds.size(), abnormalPeopleUserIds.size(), totalAbnormalDays);
    }

    @Override
    @SneakyThrows
    public void exportDaily(DailyFindPageDTO dto, HttpServletResponse response) {
        long startTime = System.currentTimeMillis();
        List<TaskUserInfoVO> taskUserInfoVOS;
        // 获取查询前置条件 用户id列表
        // 根据name deptIds 查询用户id列表
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("aliasName", dto.getName());
        queryMap.put("deptIds", dto.getDeptIds());
        List<Long> userIds = rosterMapper.findIds(queryMap);
//        UserPmsDTO userPmsDTO = new UserPmsDTO();
//        List<SysUserPmsCqVO> sysUserPmsCqVOS = new ArrayList<>();
//        BeanUtils.copyProperties(dto, userPmsDTO);
//        ObjectUtils.<List<SysUserOutVO>>defaultIfNull(
//                remoteOutPmsService.getUserListByMultiParameterPms(userPmsDTO).getData(), ImmutableList.of()
//        ).forEach(u -> {
//            SysUserPmsCqVO sysUserPmsCqVO = SysUserPmsCqVO.of(u, null);
//            sysUserPmsCqVOS.add(sysUserPmsCqVO);
//        });

        // 是否能导出数据标记位
        boolean isAllowExport = true;
        if (CollUtil.isEmpty(userIds)) {// if (CollUtil.isEmpty(sysUserPmsCqVOS))
            isAllowExport = false;
        } else {
//            List<Long> userIds = sysUserPmsCqVOS.stream().map(SysUserPmsCqVO::getUserId).collect(Collectors.toList());
            dto.setUserIds(userIds);
            taskUserInfoVOS = dailyPaperMapper.dailyFindPageFront(dto);
            if (CollUtil.isNotEmpty(taskUserInfoVOS)) {
                List<Long> ids = taskUserInfoVOS.stream().map(TaskUserInfoVO::getUserId).collect(Collectors.toList());
                dto.setUserIds(ids);
            } else {
                isAllowExport = false;
            }
        }

        // 查询数据总数
        int total = dailyPaperMapper.dailyFindPageCount(dto).size();
        // 获取分页总数
        int pageNum = PageUtil.totalPage(total, DEFAULT_PAGE_SIZE);
        // 获取导出数据集合
        List<List<Object>> dataList = new ArrayList<>();
        // 根据开始和结束时间获取天数集合
        List<LocalDate> dateList = getDateList(dto);

        // 允许导出时，分页多线程获取导出数据
        if (isAllowExport) {
            // 设置计数器，当每个线程都执行就绪，则继续执行后续逻辑
            CountDownLatch countDownLatch = new CountDownLatch(pageNum);
            for (int current = DEFAULT_PAGE_NUM; current <= pageNum; current++) {
                // 使用分页查询方法进行日报查询
                PageRequest pageRequest = new PageRequest(current, DEFAULT_PAGE_SIZE);
                // 分片列表
                ApiResult<Page<DailyFindPageVO>> dailyFindPage = dailyFindPage(pageRequest, dto);
                Page<DailyFindPageVO> pageData = dailyFindPage.getData();
                if (Optional.ofNullable(pageData).isPresent() && CollUtil.isNotEmpty(pageData.getRecords())) {
                    exchangeData(dataList, dateList, pageData);
                }
                // 计数
                countDownLatch.countDown();
            }
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                log.error("分批获取日报数据失败", e);
            }
        }

        log.info("查询所有数据耗时:{}", (System.currentTimeMillis() - startTime));

        // 获取动态表头
        List<List<String>> headList = getDynamicHeadList(new DailyExportVO(), dateList);
        if (CollUtil.isEmpty(dataList)) {
            dataList.add(new ArrayList<>());
        }

        log.info("获取导出数据集合耗时:{}", (System.currentTimeMillis() - startTime));

        // 导出数据
        try {
            response.setContentType(CONTENT_TYPE_SHEET);
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            String fileName = URLEncoder.encode("日报提交一览表", StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
            response.setHeader(PARAM_CONTENT_DISPOSITION, CONTENT_DISPOSITION + fileName + XLSX_SUFFIX);
            EasyExcel.write(response.getOutputStream())
                    //.registerConverter(new LocalDateTimeConverter())
                    .head(headList)
                    .autoCloseStream(Boolean.FALSE)
                    .sheet("日报提交一览表")
                    .doWrite(dataList);
            log.info("导出数据耗时:{}", (System.currentTimeMillis() - startTime));
        } catch (Exception e) {
            e.printStackTrace();
            response.reset();
            response.setContentType(CONTENT_TYPE_JSON);
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            ApiResult<String> result = ApiResult.failureMsg("导出失败", ApiResultEnum.FAILURE);
            response.getWriter().println(JSONUtil.toJsonStr(result));
        }
    }

    private static void exchangeData(List<List<Object>> dataList, List<LocalDate> dateList, Page<DailyFindPageVO> pageData) {
        // 查询结果转成导出VO类
        List<List<Object>> objectList = new ArrayList<>();
        pageData.getRecords().forEach(r -> {
            List<Object> dataItem = new ArrayList<>();
            dataItem.add(r.getName());
            dataItem.add(r.getDeptName());
            dataItem.add(r.getPersonnelStatusName());
            Map<LocalDate, DailyPaperQcVO> dailyWorkHourMap = CollUtil.isEmpty(r.getDailyPapers())
                    ? new HashMap<>()
                    : r.getDailyPapers().stream()
                    .collect(Collectors.toMap(DailyPaperQcVO::getSubmissionDate, Function.identity(), (a, b) -> a));
            if (CollUtil.isNotEmpty(dateList)) {
                for (LocalDate date : dateList) {
                    DailyPaperQcVO vo = dailyWorkHourMap.get(date);
                    if (Optional.ofNullable(vo).isPresent()) {
                        if (YES.equals(vo.getWorkday())) {
                            BigDecimal dailyHour = vo.getDailyHourCount().setScale(1, RoundingMode.HALF_DOWN).stripTrailingZeros();
                            BigDecimal addedHour = vo.getAddedHourCount().setScale(1, RoundingMode.HALF_DOWN).stripTrailingZeros();
                            String workHourStr = StrUtil.format("{}{}{}", dailyHour.toString(), ADD_FLAG, addedHour.toString());
                            dataItem.add(workHourStr);
                        } else {
                            dataItem.add("假期");
                        }
                    } else {
                        dataItem.add(StrUtil.EMPTY);
                    }
                }
            }
            objectList.add(dataItem);
        });
        // 获取导出数据集合
        dataList.addAll(objectList);
    }

    @Override
    public List<ProjectHourSumFindPageVO> projectHourSumExport2(
            ProjectHourSumFindPageDTO projectHourSumFindPageDTO,
            Boolean showUnreviewed
    ) {
        return projectHourSumFindPageV2(
                new PageRequest(1, Integer.MAX_VALUE),
                projectHourSumFindPageDTO,
                showUnreviewed
        ).getData().getRecords();
    }

    private List<LocalDate> getDateList(DailyFindPageDTO dto) {
        List<LocalDate> dateList = new ArrayList<>();
        LocalDate startTime = Optional.ofNullable(dto.getStartTime()).orElse(LocalDate.now().minusDays(365));
        LocalDate endTime = Optional.ofNullable(dto.getEndTime()).orElse(LocalDate.now());
        while (!startTime.isAfter(endTime)) {
            dateList.add(startTime);
            startTime = startTime.plusDays(1);
        }
        return dateList;
    }

    /**
     * 获取动态表头集合
     *
     */
    private List<List<String>> getDynamicHeadList(DailyExportVO headDemo, List<LocalDate> dateList) {
        List<List<String>> headList = new ArrayList<>();

        // 根据ExcelProperty注解获取部分表头字段
        Field[] declaredFields = headDemo.getClass().getDeclaredFields();
        List<ExcelProperty> annotationList = new ArrayList<>();
        for (Field field : declaredFields) {
            ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
            if (!Optional.ofNullable(annotation).isPresent()) {
                continue;
            }
            annotationList.add(annotation);
        }
        annotationList.stream()
                .sorted(Comparator.comparingInt(ExcelProperty::index))
                .forEach(a -> {
                    String headItem = String.join(StrUtil.COMMA, a.value());
                    headList.add(Collections.singletonList(headItem));
                });

        // 根据日报集合获取动态表头
        if (CollUtil.isEmpty(dateList)) {
            return headList;
        }
        dateList.forEach(d -> {
            String dateHead = d.format(DateTimeFormatter.ISO_DATE);
            headList.add(Collections.singletonList(dateHead));
        });

        return headList;
    }

    /**
     * @param pageRequest 分页参数
     * @param dailyFindPageDTO 查询参数
     * @param isDataScope      是否需要权限过滤  true：是(是否页面引用)   false：否(是否推送引用)
     * @return 异常日报数据
     */
    @Override
    public ApiResult<AbnormalDailyPaperFindPageVO> findAbnormalPage(
            PageRequest pageRequest, DailyFindPageDTO dailyFindPageDTO, boolean isDataScope
    ) {

        validateDailyFindPageDTO(dailyFindPageDTO);
        Page<AbnormalDailyPaperVO> dailyPaperPage;
        Map<Long, SysDept> sysDeptMap;
        Map<Long, String> userIdAndNameMap;
        Set<Long> ignoredUserIds;
        SysUserDataScopeVO bcpDataScope;
        Collection<Long> userIdsInDataScope;
        List<DailyPaper> papers;
        // <dailyPaperId, userId, taskId>
        Multimap<Long, Triple<Long, Long, Long>> entries;
        Set<Pair<Long, Long>> userIdAndTaskIdPairSet;
        List<Long> userIds = new ArrayList<>();
        List<Long> paperIds;
        Table<Long, Long, TaskReviewerInfoBO> reviewerInfoTable;
        List<AbnormalDailyPaperVO> resultList;
        LocalDate startTime = dailyFindPageDTO.getStartTime();
        LocalDate endTime = dailyFindPageDTO.getEndTime();
        // <userId, submissionDate, 请假时长>
        Table<Long, LocalDate, BigDecimal> overtimeLeaveDataTable;
        Table<Long, LocalDate, List<CompensatoryLeaveDataDetailVO>> compensatoryLeaveDataTable;
        Page<AbnormalDailyPaperVO> resultPage;
        AbnormalDailyPaperFindPageVO result = AbnormalDailyPaperFindPageVO.of(0, 0, 0, null);
        Integer abnormalType = dailyFindPageDTO.getAbnormalType();
        DailyPaperAbnormalEnum abnormalTypeEnum = EnumUtils.getEnumByValue(DailyPaperAbnormalEnum.class, abnormalType);
        List<Holiday> holidayList = holidayMapper.selByDateRange(startTime, endTime);
        Map<LocalDate, Integer> holidayTypeMap = holidayList.stream().collect(Collectors.toMap(Holiday::getDayDate, Holiday::getHolidayType, (a, b) -> a));

        // 查询时间范围内的日报（过滤特殊配置人员）
        ignoredUserIds = specialSettingMapper.findUserIds();
        dailyPaperPage = new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize());
        if (isDataScope) {
            bcpDataScope = projectScopeHandle.findDataScope();
            if (BooleanUtils.toBoolean(bcpDataScope.getIsAll())) {
                papers = dailyPaperMapper.findByDailyFindParams(
                        startTime,
                        endTime,
                        dailyFindPageDTO.getName(),
                        dailyFindPageDTO.getPersonnelStatus(),
                        null,
                        dailyFindPageDTO.getDeptIds(),
                        ignoredUserIds
                );
            } else {
                userIdsInDataScope = org.apache.commons.collections4.CollectionUtils.subtract(
                        bcpDataScope.getUserIdList(),
                        ignoredUserIds
                );
                if (userIdsInDataScope.isEmpty()) {
                    result.setPageData(PageUtils.mapTo(dailyPaperPage, x -> null));

                    return ApiResult.success(result);
                }
                papers = dailyPaperMapper.findByDailyFindParams(
                        startTime,
                        endTime,
                        dailyFindPageDTO.getName(),
                        dailyFindPageDTO.getPersonnelStatus(),
                        userIdsInDataScope,
                        dailyFindPageDTO.getDeptIds(),
                        null
                );
            }
        } else {
            papers = dailyPaperMapper.findByDailyFindParams(
                    startTime,
                    endTime,
                    dailyFindPageDTO.getName(),
                    dailyFindPageDTO.getPersonnelStatus(),
                    null,
                    dailyFindPageDTO.getDeptIds(),
                    ignoredUserIds
            );
        }
        if (papers.isEmpty()) {
            result.setPageData(PageUtils.mapTo(dailyPaperPage, x -> null));

            return ApiResult.success(result);
        }

        paperIds = BaseEntityUtils.mapToIdList(papers);
        for (DailyPaper paper : papers) {
            userIds.add(paper.getUserId());
            paperIds.add(paper.getId());
        }
        userIdAndNameMap = Maps.transformValues(rosterMapper.findUserIdMap(userIds), Roster::getAliasName);
        // 收集请假信息
        overtimeLeaveDataTable = HashBasedTable.create();
        overtimeLeaveDataMapper.getLeaveDataListByUserIds(startTime, endTime, userIds).forEach(ol -> {
            Long oaId = ol.getOaId();
            LocalDate belongDate = ol.getBelongdate();
            BigDecimal hour = ObjectUtils.defaultIfNull(overtimeLeaveDataTable.get(oaId, belongDate), BigDecimal.ZERO);

            if (EnumUtils.valueEquals(ol.getType(), LeaveStatusEnum.XJ)) {
                overtimeLeaveDataTable.put(oaId, belongDate, hour.subtract(ol.getHourData()));
            } else {
                overtimeLeaveDataTable.put(oaId, belongDate, hour.add(ol.getHourData()));
            }
        });
        //项目调休
        compensatoryLeaveDataTable = HashBasedTable.create();
        overtimeLeaveDataService.findByDateTimeRangeAndUserIds(dailyFindPageDTO.getStartTime(),dailyFindPageDTO.getEndTime().plusDays(1),userIds)
                .forEach(compensatoryLeaveData -> {
                    Long userId = compensatoryLeaveData.getOaId();
                    LocalDate date = compensatoryLeaveData.getBelongDate();
                    List<CompensatoryLeaveDataDetailVO> exists = compensatoryLeaveDataTable.get(userId, date);
                    if (exists == null) {
                        compensatoryLeaveDataTable.put(userId, date, Lists.newArrayList(compensatoryLeaveData));
                    } else {
                        exists.add(compensatoryLeaveData);
                    }
                });

        // 统计异常情况：获取主责审核人信息
        sysDeptMap = fetchIdDeptMap();
        resultList = Lists.newArrayListWithCapacity(papers.size() / 2);
        // 统计异常情况
        papers.forEach(p -> {
            BigDecimal leaveHours = ObjectUtils.defaultIfNull(
                    overtimeLeaveDataTable.get(p.getUserId(), p.getSubmissionDate()), BigDecimal.ZERO
            );

            leaveHours = BigDecimalUtils.SEVEN_DECIMAL.compareTo(leaveHours) < 0 ?
                    BigDecimalUtils.SEVEN_DECIMAL : leaveHours;

            BigDecimal compensatoryLeaveHour = BigDecimal.ZERO;
            List<CompensatoryLeaveDataDetailVO> compensatoryLeaveList = compensatoryLeaveDataTable.get(p.getUserId(), p.getSubmissionDate());
            if (CollUtil.isNotEmpty(compensatoryLeaveList)) {
                compensatoryLeaveHour = compensatoryLeaveList.stream()
                        .map(CompensatoryLeaveDataDetailVO::getHourData)
                        .reduce(BigDecimal.ZERO,BigDecimal::add)
                        .setScale(2, RoundingMode.HALF_UP);
            }

            compensatoryLeaveHour = BigDecimalUtils.SEVEN_DECIMAL.compareTo(compensatoryLeaveHour) < 0 ?
                    BigDecimalUtils.SEVEN_DECIMAL : compensatoryLeaveHour;
            BigDecimal leaveHourSum = leaveHours.add(compensatoryLeaveHour);
            for (
                    Pair<DailyPaperAbnormalEnum, String> abnormalTypeAndRemark :
                    dailyPaperAnalyzer.getDailyPaperAbnormalInfo(p, leaveHourSum)
            ) {
                String name = userIdAndNameMap.get(p.getUserId());
                DailyPaperAbnormalEnum first = abnormalTypeAndRemark.getFirst();
                AbnormalDailyPaperVO vo;

                if (name == null) {
                    continue;
                }
                vo = AbnormalDailyPaperVO.of(
                        p, name, sysDeptMap, leaveHours,compensatoryLeaveHour,
                        abnormalTypeAndRemark, StringUtils.EMPTY,holidayTypeMap
                );
                if (first == DailyPaperAbnormalEnum.SUBMIT) {
                    result.setWrongSubmitNum(result.getWrongSubmitNum() + 1);
                } else {
                    result.setWaitingReviewNum(result.getWaitingReviewNum() + 1);
                }
                if (abnormalTypeEnum != null && first != abnormalTypeEnum) {
                    continue;
                }
                resultList.add(vo);
            }
        });
        resultPage = PageUtils.page(resultList, pageRequest);
        // 性能原因，分页后再遍历组装审核员数据
        paperIds = BaseEntityUtils.filterAndMapCollectionToList(
                resultPage.getRecords(),
                r -> EnumUtils.valueEquals(r.getApprovalStatus(), ApprovalStatusEnum.DSH),
                AbnormalDailyPaperVO::getId
        );
        userIdAndTaskIdPairSet = Sets.newHashSetWithExpectedSize(papers.size());
        if (!paperIds.isEmpty()) {
            // 有待审核日报，组装审核员数据
            entries = HashMultimap.create(paperIds.size(), 4);
            dailyPaperEntryMapper.findDailyPaperIdAndUserIdAndTaskIdTripleByDailyPaperIdsAndApprovalStatus(
                    paperIds, ApprovalStatusEnum.DSH.getValue()
            ).forEach(t -> entries.put(t.getLeft(), t));
            resultPage.getRecords().forEach(p -> {
                Collection<Triple<Long, Long, Long>> dailyPaperIdAndUserIdAndTaskIdTriples = entries.get(p.getId());

                dailyPaperIdAndUserIdAndTaskIdTriples.forEach(
                        t -> userIdAndTaskIdPairSet.add(Pair.of(t.getMiddle(), t.getRight()))
                );
            });
            reviewerInfoTable = pmsRetriever.getReviewerInfo(userIdAndTaskIdPairSet);
            resultPage.getRecords().forEach(p -> {
                Collection<Triple<Long, Long, Long>> dailyPaperIdAndUserIdAndTaskIdTriples = entries.get(p.getId());

                setAuditorNamesInDailyPaper(
                        p, dailyPaperIdAndUserIdAndTaskIdTriples, reviewerInfoTable
                );
            });
        }
        result.setPageData(resultPage);
        result.setTotalNum(result.getWrongSubmitNum() + result.getWaitingReviewNum());

        return ApiResult.success(result);
    }

    private Map<Long, SysDept> fetchIdDeptMap() {
        List<DeptCacheDto> deptList = remoteDeptService.getAllDeptList(true);
        Collection<SysDept> sysDeptList;

        if (CollectionUtils.isEmpty(deptList)) {
            return ImmutableMap.of();
        }
        sysDeptList = SysDeptUtils.mapBCPDeptCacheDtoToSysDept(deptList);

        return sysDeptList.stream().collect(Collectors.toMap(SysDept::getDeptId, sysDept -> sysDept));
    }

    @Override
    public List<AbnormalDailyPaperExcelVO> findAbnormal(
            DailyFindPageDTO dailyFindPageDTO, Integer abnormalType
    ) {
        Set<Long> ignoredUserIds;
        List<Long> userIds;
        List<DailyPaper> papers;
        LocalDate startDate;
        LocalDate endDate;
        // <userId, submissionDate, 请假时长>
        Table<Long, LocalDate, BigDecimal> overtimeLeaveDataTable;
        //<userId, submissionDate, 项目调休>
        Table<Long, LocalDate, List<CompensatoryLeaveDataDetailVO>> compensatoryLeaveDataTable;
        Map<Long, SysDept> deptIdMap;
        Set<Pair<Long, Long>> userIdAndTaskIdPairSet;
        List<AbnormalDailyPaperVO> resultPaperList;
        List<AbnormalDailyPaperExcelVO> resultList;
        Map<Long, String> userIdAndNameMap;
        List<Long> dailyPaperIds;
        // <dailyPaperId, userId, taskId>
        Multimap<Long, Triple<Long, Long, Long>> entries;
        Table<Long, Long, TaskReviewerInfoBO> reviewerInfoTable;
        DailyPaperAbnormalEnum abnormalEnum = EnumUtils.getEnumByValue(DailyPaperAbnormalEnum.class, abnormalType);

        if (abnormalEnum == null) {
            throw new ValidationException("异常类型不能为空");
        }
        validateDailyFindPageDTO(dailyFindPageDTO);
        // 收集用户、日报信息
        ignoredUserIds = specialSettingMapper.findUserIds();
        userIds = dailyPaperMapper.findSubmitUserIdList(dailyFindPageDTO);
        if (userIds.isEmpty()) {
            return ImmutableList.of();
        }
        userIds = userIds.stream()
                .filter(uid -> !ignoredUserIds.contains(uid))
                .collect(Collectors.toList());
        dailyFindPageDTO.setUserIds(userIds);
        papers = dailyPaperMapper.findSubmitList(dailyFindPageDTO);
        if (papers.isEmpty()) {
            return ImmutableList.of();
        }
        // 收集请假信息
        startDate = dailyFindPageDTO.getStartTime();
        endDate = dailyFindPageDTO.getEndTime();
        //获取假期信息
        List<Holiday> holidayList = holidayMapper.selByDateRange(startDate, endDate);
        Map<LocalDate, Integer> holidayTypeMap = holidayList.stream().collect(Collectors.toMap(Holiday::getDayDate, Holiday::getHolidayType, (a, b) -> a));

        overtimeLeaveDataTable = HashBasedTable.create();
        overtimeLeaveDataMapper.getLeaveDataListByUserIds(startDate, endDate, userIds)
                .forEach(ol -> {
                    Long oaId = ol.getOaId();
                    LocalDate belongDate = ol.getBelongdate();
                    BigDecimal hour = ObjectUtils.defaultIfNull(
                            overtimeLeaveDataTable.get(oaId, belongDate), BigDecimal.ZERO
                    );

                    if (EnumUtils.valueEquals(ol.getType(), LeaveStatusEnum.XJ)) {
                        overtimeLeaveDataTable.put(oaId, belongDate, hour.subtract(ol.getHourData()));
                    } else {
                        overtimeLeaveDataTable.put(oaId, belongDate, hour.add(ol.getHourData()));
                    }
                });
        //项目调休
        compensatoryLeaveDataTable = HashBasedTable.create();
        overtimeLeaveDataService.findByDateTimeRangeAndUserIds(dailyFindPageDTO.getStartTime(),dailyFindPageDTO.getEndTime().plusDays(1),userIds)
                .forEach(compensatoryLeaveData -> {
                    Long userId = compensatoryLeaveData.getOaId();
                    LocalDate date = compensatoryLeaveData.getBelongDate();
                    List<CompensatoryLeaveDataDetailVO> exists = compensatoryLeaveDataTable.get(userId, date);
                    if (exists == null) {
                        compensatoryLeaveDataTable.put(userId, date, Lists.newArrayList(compensatoryLeaveData));
                    } else {
                        exists.add(compensatoryLeaveData);
                    }
                });

        // 获取人员、部门信息
        deptIdMap = fetchIdDeptMap();
        userIdAndNameMap = Maps.transformValues(rosterMapper.findUserIdMap(userIds), Roster::getAliasName);
        userIdAndTaskIdPairSet = abnormalEnum == DailyPaperAbnormalEnum.WAITING_REVIEW ?
                Sets.newHashSetWithExpectedSize(papers.size())
                :
                ImmutableSet.of();
        resultPaperList = Lists.newArrayListWithCapacity(papers.size() / 2);
        dailyPaperIds = abnormalEnum == DailyPaperAbnormalEnum.WAITING_REVIEW ?
                Lists.newArrayListWithCapacity(papers.size() / 2)
                :
                ImmutableList.of();
        papers.forEach(p -> {
            BigDecimal leaveHours = ObjectUtils.defaultIfNull(
                    overtimeLeaveDataTable.get(p.getUserId(), p.getSubmissionDate()), BigDecimal.ZERO
            );

            leaveHours = BigDecimalUtils.SEVEN_DECIMAL.compareTo(leaveHours) < 0 ?
                    BigDecimalUtils.SEVEN_DECIMAL : leaveHours;

            BigDecimal compensatoryLeaveHour = BigDecimal.ZERO;
            List<CompensatoryLeaveDataDetailVO> compensatoryLeaveList = compensatoryLeaveDataTable.get(p.getUserId(), p.getSubmissionDate());
            if (CollUtil.isNotEmpty(compensatoryLeaveList)) {
                compensatoryLeaveHour = compensatoryLeaveList.stream()
                        .map(CompensatoryLeaveDataDetailVO::getHourData)
                        .reduce(BigDecimal.ZERO,BigDecimal::add)
                        .setScale(2, RoundingMode.HALF_UP);
            }

            compensatoryLeaveHour = BigDecimalUtils.SEVEN_DECIMAL.compareTo(compensatoryLeaveHour) < 0 ?
                    BigDecimalUtils.SEVEN_DECIMAL : compensatoryLeaveHour;
            BigDecimal leaveHourSum = leaveHours.add(compensatoryLeaveHour);
            for (
                    Pair<DailyPaperAbnormalEnum, String> abnormalTypeAndRemark :
                    dailyPaperAnalyzer.getDailyPaperAbnormalInfo(p, leaveHourSum)
            ) {
                String name = userIdAndNameMap.get(p.getUserId());
                AbnormalDailyPaperVO vo;

                if (name == null) {
                    continue;
                }
                vo = AbnormalDailyPaperVO.of(
                        p, name, deptIdMap, leaveHours,compensatoryLeaveHour,
                        abnormalTypeAndRemark, StringUtils.EMPTY,holidayTypeMap
                );
                if (abnormalEnum != abnormalTypeAndRemark.getFirst()) {
                    continue;
                }
                resultPaperList.add(vo);
                if (abnormalEnum == DailyPaperAbnormalEnum.WAITING_REVIEW) {
                    dailyPaperIds.add(vo.getId());
                }
            }
        });
        if (dailyPaperIds.isEmpty()) {
            resultList = resultPaperList.stream().map(AbnormalDailyPaperExcelVO::from).collect(Collectors.toList());
        } else {
            // 有待审核日报，查询条目和审核人
            entries = HashMultimap.create();
            dailyPaperEntryMapper.findDailyPaperIdAndUserIdAndTaskIdTripleByDailyPaperIdsAndApprovalStatus(
                    dailyPaperIds, ApprovalStatusEnum.DSH.getValue()
            ).forEach(e -> entries.put(e.getLeft(), Triple.of(e.getLeft(), e.getMiddle(), e.getRight())));
            entries.values().forEach(
                    triple -> userIdAndTaskIdPairSet.add(Pair.of(triple.getMiddle(), triple.getRight()))
            );
            reviewerInfoTable = pmsRetriever.getReviewerInfo(userIdAndTaskIdPairSet);
            resultList = resultPaperList.stream().map(p -> {
                Collection<Triple<Long, Long, Long>> dailyPaperIdAndUserIdAndTaskIdTriples = entries.get(p.getId());

                setAuditorNamesInDailyPaper(
                        p, dailyPaperIdAndUserIdAndTaskIdTriples, reviewerInfoTable
                );

                return AbnormalDailyPaperExcelVO.from(p);
            }).collect(Collectors.toList());
        }

        return resultList;
    }

    @Override
    public List<AbnormalDailyPaperExcelVO> findAbnormal(DailyFindPageDTO dailyFindPageDTO) {
        List<AbnormalDailyPaperExcelVO> abnormalDailyPaperExcelVOList;
        if (dailyFindPageDTO.getAbnormalType() == null){
             abnormalDailyPaperExcelVOList=Stream.of(
                    findAbnormal(dailyFindPageDTO, DailyPaperAbnormalEnum.SUBMIT.getValue()),
                    findAbnormal(dailyFindPageDTO, DailyPaperAbnormalEnum.WAITING_REVIEW.getValue())
            ).flatMap(Collection::stream).collect(Collectors.toList());
        } else {
            abnormalDailyPaperExcelVOList=findAbnormal(dailyFindPageDTO, dailyFindPageDTO.getAbnormalType());
        }
        //导出【数量】
        bcpLoggerUtils.log(FunctionConstants.DAILY_REPORT_SUBMISSION_CHECKLIST, LogContentEnum.EXPORT_ABNORMAL_DAILY_REPORT,
                abnormalDailyPaperExcelVOList.size());
        return abnormalDailyPaperExcelVOList;
    }

    /**
     * 前置条件处理
     *
     * @param dto 查询参数
     */
    private void validateDailyFindPageDTO(DailyFindPageDTO dto) {
        LocalDate startTime = dto.getStartTime();
        LocalDate endTime = dto.getEndTime();

        if (startTime == null || endTime == null) {
            throw new ValidationException("开始时间和结束时间不能为空");
        }
        if (startTime.isAfter(endTime)) {
            throw new ValidationException("开始时间不能晚于结束时间");
        }
    }

    @Async
    @Override
    public void sendWeiXinCpMessageForAbnormal(DailyFindPageDTO dailyFindPageDTO, List<AbnormalDailyPaperExcelVO> list, PigxUser user) {
        Map<Long, List<AbnormalDailyPaperExcelVO>> userIdAndAbnormalPaperMap;
        List<WeComModel> models;
        String redirectUrl;
        if (list.isEmpty()) {
            return;
        }
        //查询mhour_roster
        List<Long> normalUserIds = rosterMapper.normalUserIds();
        userIdAndAbnormalPaperMap = list.stream().filter(e->normalUserIds.contains(e.getUserId()))
                .collect(Collectors.groupingBy(AbnormalDailyPaperExcelVO::getUserId));
        models = Lists.newArrayListWithCapacity(userIdAndAbnormalPaperMap.size());
        String startDate = DateUtil.format(dailyFindPageDTO.getStartTime() == null ? LocalDateTime.now() : dailyFindPageDTO.getStartTime().atStartOfDay(), "yyyy-MM");
        redirectUrl = redirectPrefixUrl + Base64.encode(
                hostUrl + "/view-businesses/daily/write?approvalStatusTab=99&currentIndex=2&startDate=" + startDate, Charsets.UTF_8
        );
        userIdAndAbnormalPaperMap.keySet().forEach(uid -> {
            List<AbnormalDailyPaperExcelVO> abnormalPapers = userIdAndAbnormalPaperMap.get(uid);
            WeComModel model = new WeComModel();
            String baseContent = "您好，您目前存在" + abnormalPapers.size() + "条日报异常，请及时处理~";

            model.setSource(SourceEnum.PROJECT.getValue());
            model.setType(MsgTypeEnum.WARN_MSG.getValue());
            model.setTitle("【异常日报】通知");
            model.setContent(baseContent + "\n<a href=\""+ redirectUrl + "\">查看详情</a>");
            model.setTargetType(TargetTypeEnum.USERS.getValue());
            model.setTargetList(ImmutableList.of(
                    new BcpMessageTargetDTO(String.valueOf(uid), abnormalPapers.get(0).getUserRealName(), null)
            ));
            model.setRedirectUrl(redirectUrl);
            model.setSendTime(LocalDateTimeUtil.formatNormal(LocalDate.now()));
            if (user != null) {
                model.setSenderId(user.getId());
                model.setSender(user.getNickname());
            } else {
                model.setSenderId(10000L);
                model.setSender("admin");
            }
            models.add(model);
        });
        if (models.isEmpty()) {
            return;
        }
        log.info("发送消息: {}条", models.size());
        for (List<WeComModel> mList : CollUtil.split(models, 300)) {
            WeComBatchModel batchModel = new WeComBatchModel();
            batchModel.setData(mList);
            CompletableFuture.runAsync(() -> {
                try {
                    log.info("开始发送{}消息", ChannelEnum.WECOM.getName());
                    // 发送企微消息
                    remoteSendMsgService.sendWeComMsgBatch(SecurityConstants.FROM_IN, batchModel);
                    log.info("{}消息发送成功", ChannelEnum.WECOM.getName());
                } catch (Exception e) {
                    log.error("{}消息发送失败", ChannelEnum.WECOM.getName(), e);
                }
            });

            CompletableFuture.runAsync(() -> {
                try {
                    log.info("开始发送{}消息", ChannelEnum.MAIL.getName());
                    // 发送门户消息
                    remoteSendMsgService.sendMsgBatch(SecurityConstants.FROM_IN, buildBcpMessageBatchDTO(mList, ChannelEnum.MAIL));
                    log.info("{}消息发送成功", ChannelEnum.MAIL.getName());
                } catch (Exception e) {
                    log.error("{}消息发送失败", ChannelEnum.MAIL.getName(), e);
                }
            });
        }

        //提交异常提醒
        bcpLoggerUtils.log(FunctionConstants.DAILY_REPORT_SUBMISSION_CHECKLIST, LogContentEnum.SUBMIT_EXCEPTION_REMINDER, user, models.size());
    }

    /**
     * 根据企微消息对象列表构建消息列表
     * @param models 企微消息列表
     * @return 门户消息列表
     */
    private BcpMessageBatchDTO buildBcpMessageBatchDTO(List<WeComModel> models, ChannelEnum channel) {
        WeComModel model = models.get(0);
        BcpMessageBatchDTO result = new BcpMessageBatchDTO();
        int mSize = models.size();
        List<BcpMessageTargetBatchDTO> targetList = Lists.newArrayListWithExpectedSize(mSize);
        int contentId = 0;
        Map<String, BcpMessageContentModel> contentIdMap = Maps.newHashMapWithExpectedSize(mSize);

        result.setSource(SourceEnum.PROJECT.getValue());
        result.setChannel(channel.getValue());
        result.setSenderId(model.getSenderId());
        result.setSender(model.getSender());
        result.setSendTime(model.getSendTime());
        result.setTargetType(TargetTypeEnum.USERS.getValue());
        for (WeComModel m : models) {
            contentId++;
            for (BcpMessageTargetDTO t : m.getTargetList()) {
                BcpMessageTargetBatchDTO target = new BcpMessageTargetBatchDTO();
                BcpMessageContentModel content = new BcpMessageContentModel();
                String contentIdStr = String.valueOf(contentId);

                target.setTargetId(t.getTargetId());
                target.setTargetName(t.getTargetName());
                target.setContentId(contentIdStr);
                targetList.add(target);
                content.setType(m.getType());
                content.setTitle(m.getTitle());
                if (channel == ChannelEnum.MAIL) {
                    content.setContent(ArrayUtils.get(StringUtils.split(m.getContent(), "\n"), 0));
                } else {
                    content.setContent(m.getContent());
                }
                content.setRedirectUrl(m.getRedirectUrl());
                contentIdMap.put(contentIdStr, content);
            }
        }
        result.setTargetList(targetList);
        result.setContentMap(contentIdMap);
        result.setSendTime(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now()));

        return result;
    }

    @Async
    @Override
    public void sendWeiXinCpMsgForAuditor(DailyFindPageDTO dailyFindPageDTO, boolean manual, PigxUser user) {
        Map<String, Object> entryQueryFilter;
        List<DailyPaperEntry> entryList;
        int entryListSize;
        Set<Long> projectIds;
        Set<Long> taskIds;
        Map<Long, ProjectInfo> projectIdMap;
        Map<Long, List<ProjectTaskeUser>> taskAndLeadersMap;
        Map<Long, Roster> userIdAndRosterMap;
        List<Pair<LocalDate, LocalDate>> filingDateRanges;
        Set<Long> specialProjectIds;
        // 消息发送对象<用户ID, 待审核数量>
        Map<Long, Integer> userIdAndWaitingReviewNumMap;
        Map<Long, String> userIdAndNameMap;
        List<WeComModel> models;
        String redirectUrl;

        // 获取、拼装查询参数
        validateDailyFindPageDTO(dailyFindPageDTO);
        filingDateRanges = filingMapper.findByFiled(FilingTypeEnum.YGD.getValue())
                .stream()
                .map(f -> Pair.of(
                        f.getFilingStartDatetime().toLocalDate(),
                        f.getFilingEndDatetime().toLocalDate().minusDays(1)
                ))
                .collect(Collectors.toList());
        entryQueryFilter = buildFilterByDailyFindPageDTO(dailyFindPageDTO);
        entryQueryFilter.put("approvalStatus", 2);
        entryQueryFilter.put("orderBy", "submissionDate");
        if (!filingDateRanges.isEmpty()) {
            entryQueryFilter.put("filingDateRanges", filingDateRanges);
        }
        if (manual) {
            // 用户手动触发，额外添加权限查询参数
            entryQueryFilter.put("dailyPaperIds", dailyPaperMapper.findIdMapInDataScope(entryQueryFilter).keySet());
        }
        entryList = dailyPaperEntryMapper.findList(entryQueryFilter);
        entryListSize = entryList.size();
        if (entryListSize < 1) {
            return;
        }
        // 收集项目ID、任务ID、人员ID，查询出相关项目、任务负责人和用户直属上级关系
        projectIds = Sets.newHashSetWithExpectedSize(entryListSize);
        taskIds = Sets.newHashSetWithExpectedSize(entryListSize);
        entryList.forEach(entry -> {
            projectIds.add(entry.getProjectId());
            taskIds.add(entry.getTaskId());
        });
        specialProjectIds = entityOptionMapper.findBySign(EntitySign.SPECIAL_PROJECT)
                .stream()
                .map(EntityOption::getEntityId)
                .collect(Collectors.toSet());
        projectIdMap = projectInfoMapper.selectBatchIds(CollUtil.union(projectIds, specialProjectIds))
                .stream()
                .collect(Collectors.toMap(BaseEntity::getId, p -> p));
        taskAndLeadersMap = projectTaskeUserMapper.findByTaskIdsAndTaskRole(
                taskIds, ProjectTaskRoleEnum.LEADER.getValue()
        ).stream().collect(Collectors.groupingBy(ProjectTaskeUser::getTaskId));
        userIdAndRosterMap = rosterMapper.findUserIdMap(null);
        userIdAndWaitingReviewNumMap = Maps.newHashMap();
        models = Lists.newArrayList();
        entryList.forEach(entry -> {
            Long taskId = entry.getTaskId();
            List<ProjectTaskeUser> leaders = taskAndLeadersMap.get(taskId);
            ProjectInfo projectInfo = projectIdMap.get(entry.getProjectId());
            if (projectInfo == null) {
                return;
            }
            Long userId = entry.getUserId();
            Integer projectStatus;
            Roster roster = userIdAndRosterMap.get(userId);
            Long leaderId = roster == null ? null : roster.getLeaderId();
            Long projectManagerId = projectInfo.getManagerUserId();
            Long salesmanId = projectInfo.getSalesmanUserId();
            Long preSalesmanId = projectInfo.getPreSaleUserId();
            // 用户是否有上级
            boolean isEntryUserHasLeader = leaderId != null;
            // 用户是否是任务负责人
            boolean isEntryUserTaskLeader = leaders != null && leaders.stream()
                    .anyMatch(l -> userId.equals(l.getUserId()));
            // 用户是否是铁三角
            boolean isEntryUserIronTriangle;
            // 是否为特殊项目
            boolean isSpecialProject = specialProjectIds.contains(entry.getProjectId());
            // 销售、售前是否可以审核项目
            boolean isSalesmanCanAudit;
            Long userIdTmp;

            // 特殊任务
            if (isSpecialProject) {
                isEntryUserIronTriangle = userId.equals(projectManagerId) ||
                        userId.equals(salesmanId) ||
                        userId.equals(preSalesmanId);
                // 给直属上级发送
                if (isEntryUserHasLeader) {
                    increaseWaitingReviewNumInMap(userIdAndWaitingReviewNumMap, leaderId);
                }
                if (isEntryUserIronTriangle) {
                    if (projectManagerId != null) {
                        increaseWaitingReviewNumInMap(userIdAndWaitingReviewNumMap, projectManagerId);
                    }
                    if (salesmanId != null) {
                        increaseWaitingReviewNumInMap(userIdAndWaitingReviewNumMap, salesmanId);
                    }
                    if (preSalesmanId != null) {
                        increaseWaitingReviewNumInMap(userIdAndWaitingReviewNumMap, preSalesmanId);
                    }
                }

            } else if (isEntryUserTaskLeader) {
                // 任务负责人等主责的推送
                projectStatus = NumberUtils.toInt(projectInfo.getProjectStatus(), Integer.MIN_VALUE);
                isSalesmanCanAudit = (
                        EnumUtils.valueEquals(projectStatus, BusinessStatusEnum.SJ) ||
                        EnumUtils.valueEquals(projectStatus, BusinessStatusEnum.SJZZ)
                );
                isEntryUserIronTriangle = userId.equals(projectManagerId) || (
                        isSalesmanCanAudit && (
                            userId.equals(preSalesmanId) ||
                            userId.equals(salesmanId)
                        )
                );
                if (isEntryUserIronTriangle) {
                    if (isEntryUserHasLeader) {
                        // 给用户的直属上级推送（工时条目的提交人既是任务负责人又是铁三角）
                        increaseWaitingReviewNumInMap(userIdAndWaitingReviewNumMap, leaderId);
                    }
                } else {
                    // 任务负责人的条目，给铁三角推送
                    if (isSalesmanCanAudit) {
                        // 给销售、售前推送
                        userIdTmp = salesmanId;
                        if (userIdTmp != null) {
                            increaseWaitingReviewNumInMap(userIdAndWaitingReviewNumMap, userIdTmp);
                        }
                        userIdTmp = preSalesmanId;
                        if (userIdTmp != null) {
                            increaseWaitingReviewNumInMap(userIdAndWaitingReviewNumMap, userIdTmp);
                        }
                    }
                    // 给项目经理推送
                    userIdTmp = projectManagerId;
                    if (userIdTmp != null) {
                        increaseWaitingReviewNumInMap(userIdAndWaitingReviewNumMap, userIdTmp);
                    }
                }
            } else if (leaders != null){
                // 工时条目的提交人不是任务负责人，给任务负责人推送
                leaders.forEach(l ->
                        increaseWaitingReviewNumInMap(userIdAndWaitingReviewNumMap, l.getUserId())
                );
            }
        });
        // 收集用户信息
        userIdAndNameMap = Maps.transformValues(
                rosterMapper.findUserIdMap(userIdAndWaitingReviewNumMap.keySet()), Roster::getAliasName
        );
        // 进行消息推送
        redirectUrl = redirectPrefixUrl +
                Base64.encode(hostUrl + "/view-businesses/daily-verify/index?tab=1", Charsets.UTF_8);
        userIdAndWaitingReviewNumMap.keySet().forEach(userId -> {
            Integer num = userIdAndWaitingReviewNumMap.get(userId);
            WeComModel model = new WeComModel();
            model.setSource(SourceEnum.PROJECT.getValue());
            model.setType(MsgTypeEnum.WARN_MSG.getValue());
            model.setTitle("【异常日报】审核通知");
            model.setRedirectUrl(redirectUrl);
            model.setContent(
                    "您好，您目前存在" +
                            num +
                            "条未审核的日报请及时审核~\n<a href=\"" +
                            redirectUrl +
                            "\">查看详情</a>"
            );
            model.setTargetType(TargetTypeEnum.USERS.getValue());
            model.setTargetList(ImmutableList.of(
                    new BcpMessageTargetDTO(
                            String.valueOf(userId),
                            userIdAndNameMap.getOrDefault(userId, StringUtils.EMPTY),
                            null
                    )
            ));

            if (manual) {
                model.setSenderId(user.getId());
                model.setSender(user.getNickname());
            } else {
                model.setSenderId(10000L);
                model.setSender("admin");
            }
            models.add(model);
        });
        if (!models.isEmpty()) {
            for (List<WeComModel> mList : CollUtil.split(models, 300)) {
                WeComBatchModel batchModel = new WeComBatchModel();
                batchModel.setData(mList);
                CompletableFuture.runAsync(() -> {
                    try {
                        log.info("开始发送{}消息", ChannelEnum.WECOM.getName());
                        // 发送企微消息
                        remoteSendMsgService.sendWeComMsgBatch(SecurityConstants.FROM_IN, batchModel);
                        log.info("{}消息发送成功", ChannelEnum.WECOM.getName());
                    } catch (Exception e) {
                        log.error("{}消息发送失败", ChannelEnum.WECOM.getName(), e);
                    }
                });

                CompletableFuture.runAsync(() -> {
                    try {
                        log.info("开始发送{}消息", ChannelEnum.MAIL.getName());
                        // 发送门户消息
                        remoteSendMsgService.sendMsgBatch(SecurityConstants.FROM_IN, buildBcpMessageBatchDTO(mList, ChannelEnum.MAIL));
                        log.info("{}消息发送成功", ChannelEnum.MAIL.getName());
                    } catch (Exception e) {
                        log.error("{}消息发送失败", ChannelEnum.MAIL.getName(), e);
                    }
                });
            }
            //日志
            bcpLoggerUtils.log(FunctionConstants.DAILY_REPORT_SUBMISSION_CHECKLIST, LogContentEnum.PENDING_REVIEW_REMINDER, user, models.size());
        }
    }


    /**
     * 增加userId-waitingNum计数
     * @param userIdAndWaitingReviewNumMap 用户ID-待审核日报条目数量Map
     * @param userId 要发送的用户ID
     */
    private void increaseWaitingReviewNumInMap(
            Map<Long, Integer> userIdAndWaitingReviewNumMap,
            Long userId
    ) {
        Integer num = userIdAndWaitingReviewNumMap.getOrDefault(userId, 0);

        userIdAndWaitingReviewNumMap.put(userId, num + 1);
    }

    /**
     * 将DailyFindPageDTO转化为findList的参数Map
     * @param dto dto对象
     * @return 参数Map
     */
    private Map<String, Object> buildFilterByDailyFindPageDTO(DailyFindPageDTO dto) {
        Map<String, Object> filter = Maps.newHashMapWithExpectedSize(7);
        List<Long> deptIds = dto.getDeptIds();
        List<Long> userIds = dto.getUserIds();

        filter.put("userRealName", Strings.emptyToNull(dto.getName()));
        if (CollectionUtils.isNotEmpty(deptIds)) {
            filter.put("deptIds", deptIds);
        }
        filter.put("personnelStatus", dto.getPersonnelStatus());
        filter.put("startDate", dto.getStartTime());
        filter.put("endDate", dto.getEndTime());
        if (CollectionUtils.isNotEmpty(userIds)) {
            filter.put("userIds", userIds);
        }

        return filter;
    }

    @Override
    public List<UnReviewedDailyPaperEntryExcelVO> exportUnreviewed(ProjectHourSumFindPageDTO dto) {
        //判断角色权限

        List<Long> projectIds = null;
        Boolean isAll = projectScopeHandle.findDataScope().getIsAll();
        if (!isAll) {
            projectIds = commonPage(dto);
        }
        if (CollectionUtils.isEmpty(projectIds)) {
            new ArrayList<>();
        }
        List<UnReviewedDailyPaperEntryExcelVO> dailyPaperEntries = dailyPaperEntryMapper.findForExport(projectIds, DSH.getValue(), dto);
        dailyPaperEntries=dailyPaperEntries.stream().filter(s->s!=null).collect(Collectors.toList());
        if (CollUtil.isEmpty(dailyPaperEntries)) {
            new ArrayList<>();
        }
        //部门映射
        Map<Long, String> deptMap = Maps.transformValues(fetchIdDeptMap(), SysDept::getName);
        for (UnReviewedDailyPaperEntryExcelVO dailyPaperEntry : dailyPaperEntries) {
            dailyPaperEntry.setProjectDeptName(deptMap.get(dailyPaperEntry.getProjectDeptId()));
        }
        return dailyPaperEntries;
    }

    /**
     * 已审核的日报
     *
     * @param dto 查询参数
     * @return {@link List<UnReviewedDailyPaperEntryExcelVO>}
     */
    @Override
    public List<UnReviewedDailyPaperEntryExcelVO> exportReviewed(ProjectHourSumFindPageDTO dto) {
        List<Long> projectIds = commonPage(dto);
        if (CollectionUtils.isEmpty(projectIds)) {
            new ArrayList<>();
        }
        List<UnReviewedDailyPaperEntryExcelVO> dailyPaperEntries = dailyPaperEntryMapper.findForExport(projectIds, YTG.getValue(), dto);
        dailyPaperEntries=dailyPaperEntries.stream().filter(s->s!=null).collect(Collectors.toList());
        if (CollUtil.isEmpty(dailyPaperEntries)) {
            new ArrayList<>();
        }
        //部门映射
        Map<Long, String> deptMap = Maps.transformValues(fetchIdDeptMap(), SysDept::getName);
        for (UnReviewedDailyPaperEntryExcelVO dailyPaperEntry : dailyPaperEntries) {
            dailyPaperEntry.setProjectDeptName(deptMap.get(dailyPaperEntry.getProjectDeptId()));
        }
        return dailyPaperEntries;
    }

    /**
     * 全部的日报（包含已审核和未审核）
     *
     * @param dto 查询参数
     * @return {@link List<UnReviewedDailyPaperEntryExcelVO>}
     */
    @Override
    public List<UnReviewedDailyPaperEntryExcelVO> exportAll(ProjectHourSumFindPageDTO dto) {
        //判断角色权限
        List<Long> projectIds = commonPage(dto);
        if (CollectionUtils.isEmpty(projectIds)) {
            new ArrayList<>();
        }
        List<UnReviewedDailyPaperEntryExcelVO> dailyPaperEntries = dailyPaperEntryMapper.findForExport(projectIds, null, dto);
        dailyPaperEntries=dailyPaperEntries.stream().filter(s->s!=null).collect(Collectors.toList());
        if (CollUtil.isEmpty(dailyPaperEntries)) {
            new ArrayList<>();
        }
        //部门映射
        Map<Long, String> deptMap = Maps.transformValues(fetchIdDeptMap(), SysDept::getName);
        for (UnReviewedDailyPaperEntryExcelVO dailyPaperEntry : dailyPaperEntries) {
            dailyPaperEntry.setProjectDeptName(deptMap.get(dailyPaperEntry.getProjectDeptId()));
        }
        return dailyPaperEntries;
    }

    /**
     * 项目 工时汇总的日报条目导出
     * 包含【全部、已审核、未审核】多个sheet页数据
     *
     * @param projectHourSumFindPageDTO 查询参数
     */
    @Override
    public void exportReviewedAndUnreviewed(HttpServletResponse response, ProjectHourSumFindPageDTO
            projectHourSumFindPageDTO) {
        try {
            // 获取【全部、已审核、未审核】日报条目信息
            ProjectHourSumFindPageDTO projectHourSumFindPageDTO1 =
                    BeanUtil.copyProperties(projectHourSumFindPageDTO, ProjectHourSumFindPageDTO.class);
            ProjectHourSumFindPageDTO projectHourSumFindPageDTO2 =
                    BeanUtil.copyProperties(projectHourSumFindPageDTO, ProjectHourSumFindPageDTO.class);
            ProjectHourSumFindPageDTO projectHourSumFindPageDTO3 =
                    BeanUtil.copyProperties(projectHourSumFindPageDTO, ProjectHourSumFindPageDTO.class);

            List<Object> unreviewedList = new ArrayList<>();
            List<Object> unreviewedListHour = new ArrayList<>();
            List<Object> reviewedList = new ArrayList<>();
            List<Object> reviewedListHour = new ArrayList<>();
            List<Object> allList = new ArrayList<>();
            List<Object> allListHour = new ArrayList<>();

            List<UnReviewedDailyPaperEntryExcelVO> unreviewed = exportUnreviewed(projectHourSumFindPageDTO1);
            WriteSheet sheet2 = new WriteSheet();
            WriteSheet sheet22 = new WriteSheet();
            if (CollectionUtils.isNotEmpty(unreviewed)) {
                projectHourSumFindPageDTO1.setWorkloadType(NumberUtils.INTEGER_TWO);
                exportUnreviewedVo(unreviewed,projectHourSumFindPageDTO1,true);
                unreviewedListHour = BaseEntityUtils.mapCollectionToList(unreviewed, ExternalProjectHourSumFindExcelVO::from);
                sheet22 = EasyExcel.writerSheet(5, "未审核(小时)")
                        .head(ExternalProjectHourSumFindExcelVO.class)
                        .build();
                projectHourSumFindPageDTO1.setWorkloadType(NumberUtils.INTEGER_ONE);
                exportUnreviewedVo(unreviewed,projectHourSumFindPageDTO1,true);
                unreviewedList = BaseEntityUtils.mapCollectionToList(unreviewed, ProjectHourSumFindExcelVO::from);
                sheet2 = EasyExcel.writerSheet(2, "未审核(人天)")
                        .head(ProjectHourSumFindExcelVO.class)
                        .build();
            }
            List<UnReviewedDailyPaperEntryExcelVO> reviewed = exportReviewed(projectHourSumFindPageDTO2);
            WriteSheet sheet1 = new WriteSheet();
            WriteSheet sheet11 = new WriteSheet();
            if (CollectionUtils.isNotEmpty(reviewed)) {
                    projectHourSumFindPageDTO2.setWorkloadType(NumberUtils.INTEGER_TWO);
                    exportUnreviewedVo(reviewed,projectHourSumFindPageDTO2,false);
                    reviewedListHour = BaseEntityUtils.mapCollectionToList(reviewed, ExternalProjectHourSumFindExcelVO::from);
                    sheet11 = EasyExcel.writerSheet(4, "已审核(小时)")
                            .head(ExternalProjectHourSumFindExcelVO.class)
                            .build();
                    projectHourSumFindPageDTO2.setWorkloadType(NumberUtils.INTEGER_ONE);
                    exportUnreviewedVo(reviewed,projectHourSumFindPageDTO2,false);
                    reviewedList = BaseEntityUtils.mapCollectionToList(reviewed, ProjectHourSumFindExcelVO::from);
                    sheet1 = EasyExcel.writerSheet(1, "已审核(人天)")
                            .head(ProjectHourSumFindExcelVO.class)
                            .build();

            }
            List<UnReviewedDailyPaperEntryExcelVO> all = exportAll(projectHourSumFindPageDTO3);
            WriteSheet sheet0 = new WriteSheet();
            WriteSheet sheet00 = new WriteSheet();
            if (CollectionUtils.isNotEmpty(all)) {
                projectHourSumFindPageDTO3.setWorkloadType(NumberUtils.INTEGER_TWO);
                exportUnreviewedVo(all,projectHourSumFindPageDTO3,false);
                allListHour = BaseEntityUtils.mapCollectionToList(all, ExternalProjectHourSumFindExcelVO::from);
                sheet00 = EasyExcel.writerSheet(3, "全部(小时)")
                        .head(ExternalProjectHourSumFindExcelVO.class)
                        .build();
                projectHourSumFindPageDTO3.setWorkloadType(NumberUtils.INTEGER_ONE);
                exportUnreviewedVo(all,projectHourSumFindPageDTO3,false);
                allList = BaseEntityUtils.mapCollectionToList(all, ProjectHourSumFindExcelVO::from);
                sheet0 = EasyExcel.writerSheet(0, "全部(人天)")
                        .head(ProjectHourSumFindExcelVO.class)
                        .build();
            }else{
                //空数据，模拟一条假数据，导出表头
                all.add(new UnReviewedDailyPaperEntryExcelVO());
                allList = BaseEntityUtils.mapCollectionToList(all, ProjectHourSumFindExcelVO::from);
                sheet0 = EasyExcel.writerSheet(0, "全部(人天)")
                        .head(ProjectHourSumFindExcelVO.class)
                        .build();
            }

            // 文件名
            String fileName = URLEncoder.encode(
                    projectHourSumFindPageDTO.getStartTime().toString()
                            + "-"
                            + projectHourSumFindPageDTO.getEndTime().toString()
                            + "项目工时汇总",
                    StandardCharsets.UTF_8.name()
            ).replaceAll("\\+", "%20");

            // 写入响应体信息
            response.setContentType(CONTENT_TYPE_SHEET);
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.setHeader(PARAM_CONTENT_DISPOSITION, CONTENT_DISPOSITION + fileName + XLSX_SUFFIX);

            // 设置sheet名
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).autoCloseStream(Boolean.FALSE).build();


            // 写入excelWriter  day
            excelWriter.write(allList, sheet0);
            excelWriter.write(reviewedList, sheet1);
            excelWriter.write(unreviewedList, sheet2);
            // 写入excelWriter  hour
            excelWriter.write(allListHour, sheet00);
            excelWriter.write(reviewedListHour, sheet11);
            excelWriter.write(unreviewedListHour, sheet22);


            // 关闭excelWriter
            excelWriter.finish();
            response.flushBuffer();

            //导出【数量】
            bcpLoggerUtils.log(FunctionConstants.SUMMARY_OF_PROJECT_WORKING_HOURS, LogContentEnum.EXPORT_DATA,
                    allList.size()+reviewedList.size()+unreviewedList.size());
        } catch (Exception e) {
            e.printStackTrace();
        }



    }
    private void setAuditorNamesInDailyPaper(
            AbnormalDailyPaperVO dailyPaper,
            Collection<Triple<Long, Long, Long>> dailyPaperIdAndUserIdAndTaskIdTriples,
            Table<Long, Long, TaskReviewerInfoBO> reviewerInfoBOTable
    ) {
        Set<Long> auditorUserIds = Sets.newHashSet();
        String auditorNames;

        if (CollectionUtils.isEmpty(dailyPaperIdAndUserIdAndTaskIdTriples)) {
            return;
        }
        dailyPaperIdAndUserIdAndTaskIdTriples.forEach(e -> {
            TaskReviewerInfoBO reviewerInfoBO = reviewerInfoBOTable.get(e.getMiddle(), e.getRight());
            Set<TaskReviewerTypeEnum> types;

            if (reviewerInfoBO == null) {
                return;
            }
            types = reviewerInfoBO.getTypes();
            if (types.contains(TaskReviewerTypeEnum.TASK_LEADER)) {
                auditorUserIds.addAll(reviewerInfoBO.getTaskLeaderUserIds());
            }
            if (types.contains(TaskReviewerTypeEnum.IRON_TRIANGLE)) {
                auditorUserIds.add(reviewerInfoBO.getProjectSalesmanUserId());
                auditorUserIds.add(reviewerInfoBO.getProjectManagerUserId());
                auditorUserIds.add(reviewerInfoBO.getProjectPreSalesmanUserId());
            }
            if (types.contains(TaskReviewerTypeEnum.DIRECT_LEADER)) {
                auditorUserIds.add(reviewerInfoBO.getDirectLeaderUserId());
            }
            if (!auditorUserIds.isEmpty()) {
                auditorUserIds.remove(null);
            }
        });
        if (auditorUserIds.isEmpty()) {
            return;
        }

        List<String> userIdAndNameList= rosterMapper.findIdAndAliasNameMapByIds(auditorUserIds);
        auditorNames = StringUtils.join(userIdAndNameList,",");
        dailyPaper.setOperator(auditorNames);
    }

    @Override
    public AllocationOverviewVO findOverview(AllocationFindPageDTO dto) {
        List<AllocationFindPageVO> allocationFindPageVoS = dailyPaperEntryMapper.allocationFindAll(dto);
        if (CollUtil.isEmpty(allocationFindPageVoS)) {
            if(Optional.ofNullable(dto.getWorkloadType()).isPresent() && NumberUtils.INTEGER_TWO.equals(dto.getWorkloadType())){
                //按小时 返回1位小数
                return new AllocationOverviewVO(CommonUtils.roundOnePoint(BigDecimal.ZERO).toPlainString(),
                        CommonUtils.roundOnePoint(BigDecimal.ZERO).toPlainString(),
                        CommonUtils.roundOnePoint(BigDecimal.ZERO).toPlainString(),
                        CommonUtils.roundOnePoint(BigDecimal.ZERO).toPlainString(),
                        CommonUtils.roundOnePoint(BigDecimal.ZERO).toPlainString(),
                        CommonUtils.roundOnePoint(BigDecimal.ZERO).toPlainString(),
                        CommonUtils.roundOnePoint(BigDecimal.ZERO).toPlainString(),
                        CommonUtils.roundOnePoint(BigDecimal.ZERO).toPlainString(),
                        CommonUtils.roundOnePoint(BigDecimal.ZERO).toPlainString());
            }else{
                return new AllocationOverviewVO();
            }
        }

        //总工时
        BigDecimal totalHours = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);

        //正常工时
        BigDecimal normalHours = allocationFindPageVoS.stream()
                .map(AllocationFindPageVO::getProjectNormalHours)
                .reduce(BigDecimal.ZERO,BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);
        //加班工时
        BigDecimal addedHours = allocationFindPageVoS.stream()
                .map(AllocationFindPageVO::getProjectAddedHours)
                .reduce(BigDecimal.ZERO,BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);
        //工作日加班工时
        BigDecimal workOvertimeHours = allocationFindPageVoS.stream()
                .map(AllocationFindPageVO::getWorkOvertimeHours)
                .reduce(BigDecimal.ZERO,BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);
        //休息日加班工时
        BigDecimal restOvertimeHours = allocationFindPageVoS.stream()
                .map(AllocationFindPageVO::getRestOvertimeHours)
                .reduce(BigDecimal.ZERO,BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);
        //节假日加班工时
        BigDecimal holidayOvertimeHours = allocationFindPageVoS.stream()
                .map(AllocationFindPageVO::getHolidayOvertimeHours)
                .reduce(BigDecimal.ZERO,BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP)
                .setScale(2, RoundingMode.HALF_UP);

        //调休工时
        BigDecimal projectLeaveHours = allocationFindPageVoS.stream()
                .map(AllocationFindPageVO::getLeaveHours)
                .reduce(BigDecimal.ZERO,BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP)
                .setScale(2, RoundingMode.HALF_UP);

        //实际出勤天数
        List<Long> userIds = allocationFindPageVoS.stream().map(AllocationFindPageVO::getUserId).collect(Collectors.toList());
        DateTime parse = DateUtil.parse(dto.getSelectMonth(), "yyyy-MM");
        // 获取该月份的第一天和最后一天
        LocalDate start = DateUtil.beginOfMonth(parse).toLocalDateTime().toLocalDate();
        LocalDate end = DateUtil.endOfMonth(parse).toLocalDateTime().toLocalDate();
        Map<Long, BigDecimal> userAttendanceMap = Maps.transformValues(saturationCalculator.calcAttendance(start, end, userIds), Pair::getFirst);

        BigDecimal cwActualAttendance = allocationFindPageVoS.stream()
                .map(vo -> {
                    BigDecimal baseAttendance = ObjectUtils.defaultIfNull(vo.getCwActualAttendance(), BigDecimal.ZERO);
                    return baseAttendance.add(userAttendanceMap.getOrDefault(vo.getUserId(), BigDecimal.ZERO));
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP)
                .setScale(2, RoundingMode.HALF_UP);

        // 查询整合项目调休信息
        // 获取该月份的第一天和最后一天
        // 当月开始时间
        LocalDateTime startOfMonth = LocalDateTime.of(DateUtil.beginOfMonth(parse).toLocalDateTime().toLocalDate(), LocalTime.MIN);
        // 当月结束时间
        LocalDateTime endOfMonth = LocalDateTime.of(DateUtil.endOfMonth(parse).toLocalDateTime().toLocalDate(), LocalTime.MAX);
        List<Holiday> holidayList = holidayMapper.selByDateRange(startOfMonth.toLocalDate(), endOfMonth.toLocalDate());
        List<LocalDate> holidayDateList = holidayList.stream().map(Holiday::getDayDate).collect(Collectors.toList());

        List<CompensatoryLeaveDataDetailVO> compensatoryLeaveList =
                overtimeLeaveDataService.findByDateTimeRangeAndUserIds(startOfMonth.toLocalDate(), endOfMonth.toLocalDate(), userIds)
                .stream().filter(e -> !holidayDateList.contains(e.getBelongDate())).collect(Collectors.toList());

        List<Long> projectIds = allocationFindPageVoS.stream().map(AllocationFindPageVO::getId).collect(Collectors.toList());
        List<CompensatoryLeaveDataDetailVO> filterCompensatoryLeaveList =
                compensatoryLeaveList.stream().filter(c -> projectIds.contains(c.getXmmc())).collect(Collectors.toList());
        //调休工时
        BigDecimal leaveHours = filterCompensatoryLeaveList.stream()
                .map(CompensatoryLeaveDataDetailVO::getHourData)
                .reduce(BigDecimal.ZERO,BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP).add(projectLeaveHours);

        //项目分摊工时
        BigDecimal projectShareHours = normalHours.add(restOvertimeHours).add(holidayOvertimeHours).add(leaveHours)
                .setScale(2, RoundingMode.HALF_UP);
        if(Optional.ofNullable(dto.getWorkloadType()).isPresent() && NumberUtils.INTEGER_TWO.equals(dto.getWorkloadType())){
            //按小时
            return new AllocationOverviewVO(CommonUtils.roundOnePoint(totalHours).toPlainString(),
                    CommonUtils.roundOnePoint(normalHours).toPlainString(),
                    CommonUtils.roundOnePoint(addedHours).toPlainString(),
                    CommonUtils.roundOnePoint(workOvertimeHours).toPlainString(),
                    CommonUtils.roundOnePoint(restOvertimeHours).toPlainString(),
                    CommonUtils.roundOnePoint(holidayOvertimeHours).toPlainString(),
                    CommonUtils.roundOnePoint(leaveHours).toPlainString(),
                    CommonUtils.roundOnePoint(projectShareHours).toPlainString(),
                    CommonUtils.roundOnePoint(cwActualAttendance).toPlainString());
        }
        return new AllocationOverviewVO(CommonUtils.unitConversion(totalHours).toPlainString(),
                    CommonUtils.unitConversion(normalHours).toPlainString(),
                    CommonUtils.unitConversion(addedHours).toPlainString(),
                    CommonUtils.unitConversion(workOvertimeHours).toPlainString(),
                    CommonUtils.unitConversion(restOvertimeHours).toPlainString(),
                    CommonUtils.unitConversion(holidayOvertimeHours).toPlainString(),
                    CommonUtils.unitConversion(leaveHours).toPlainString(),
                    CommonUtils.unitConversion(projectShareHours).toPlainString(),
                    cwActualAttendance.toPlainString());
    }

    @Override
    public ApiResult<Page<AllocationFindPageVO>> allocationFindPage(Boolean ifExport, AllocationFindPageDTO
            allocationFindPageDTO) {

        Page<AllocationFindPageVO> allocationFindPageVOPage = new Page<>();
        List<AllocationFindPageVO> records;
        if (!ifExport) {
            allocationFindPageVOPage = dailyPaperEntryMapper.allocationFindPage(new Page<>(allocationFindPageDTO.getPageNumber(), allocationFindPageDTO.getPageSize()), allocationFindPageDTO);
            records = allocationFindPageVOPage.getRecords();
        } else {
            records = dailyPaperEntryMapper.allocationFindPage(allocationFindPageDTO);
        }
        if (records.isEmpty()) {
            return ApiResult.success(allocationFindPageVOPage);
        }
        // 处理售前售后工时
        List<EntityOption> entityOptions = entityOptionMapper.findBySign(EntitySign.SPECIAL_PROJECT);
        List<Long> entityIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(entityOptions)) {
            entityIds = ObjectUtil.defaultIfNull(entityOptions.stream().map(EntityOption::getEntityId).distinct().collect(Collectors.toList()), ImmutableList.of());
        }
        List<Long> finalEntityIds = entityIds;
        records.forEach(r -> {
            if(!ifExport){
                if(Optional.ofNullable(allocationFindPageDTO.getWorkloadType()).isPresent() && NumberUtils.INTEGER_TWO.equals(allocationFindPageDTO.getWorkloadType())){
                    r.setPreSaleHours(r.getPreSaleHours() == null ? "0.0" : CommonUtils.roundOnePoint(new BigDecimal(r.getPreSaleHours())).stripTrailingZeros().toString());
                    r.setAfterSaleHours(r.getAfterSaleHours() == null ? "0.0" : CommonUtils.roundOnePoint(new BigDecimal(r.getAfterSaleHours())).stripTrailingZeros().toString());
                }else{
                    r.setPreSaleHours(r.getPreSaleHours() == null ? "0.0" : CommonUtils.unitConversion(new BigDecimal(r.getPreSaleHours())).stripTrailingZeros().toString());
                    r.setAfterSaleHours(r.getAfterSaleHours() == null ? "0.0" : CommonUtils.unitConversion(new BigDecimal(r.getAfterSaleHours())).stripTrailingZeros().toString());
                }
            }
        });
        //处理赋值字段【项目部门名称、人员部门名称】
        // 人员状态
        List<Long> userIds = records.stream().map(AllocationFindPageVO::getUserId).collect(Collectors.toList());
        Map<Long, Roster> userIdMap = rosterMapper.findUserIdMap(userIds);
        //部门映射
        Map<Long, SysDept> sysDeptMap = fetchIdDeptMap();
        if (records.size() == 0) {
            return ApiResult.success(allocationFindPageVOPage);
        }
        DateTime parse = DateUtil.parse(allocationFindPageDTO.getSelectMonth(), "yyyy-MM");
        // 获取该月份的第一天和最后一天
        LocalDate start = DateUtil.beginOfMonth(parse).toLocalDateTime().toLocalDate();
        LocalDate end = DateUtil.endOfMonth(parse).toLocalDateTime().toLocalDate();
        Map<Long, Pair<BigDecimal, BigDecimal>> userAttendanceMap = saturationCalculator.calcAttendance(start, end, userIds);
        List<Holiday> holidayList = holidayMapper.selByDateRange(start, end);
        List<LocalDate> holidayDateList = holidayList.stream().map(Holiday::getDayDate).collect(Collectors.toList());

        // 查询整合项目调休信息
        int rangeDays = (int) start.until(end, ChronoUnit.DAYS);
        Table<Long, Long, List<CompensatoryLeaveDataDetailVO>> userIdAndDateAndCompensatoryLeaveTable = HashBasedTable.create(userIds.size(), Math.max(1, rangeDays));
        overtimeLeaveDataService.findByDateTimeRangeAndUserIds(LocalDateTime.of(start, LocalTime.MIN).toLocalDate(), LocalDateTime.of(end, LocalTime.MAX).toLocalDate(), allocationFindPageDTO.getUserIds())
                .stream().filter(e -> !holidayDateList.contains(e.getBelongDate()))
                .forEach(compensatoryLeaveData -> {
                    Long userId = compensatoryLeaveData.getOaId();
                    Long xmmc = compensatoryLeaveData.getXmmc();
                    List<CompensatoryLeaveDataDetailVO> exists = userIdAndDateAndCompensatoryLeaveTable.get(userId, xmmc);

                    if (exists == null) {
                        userIdAndDateAndCompensatoryLeaveTable.put(userId, xmmc, Lists.newArrayList(compensatoryLeaveData));
                    } else {
                        exists.add(compensatoryLeaveData);
                    }
                });
        List<AllocationFindPageVO> allocationFindPageVOS = talentAllocation(
                userIdMap, sysDeptMap, Maps.transformValues(userAttendanceMap, Pair::getFirst),userIdAndDateAndCompensatoryLeaveTable, records,allocationFindPageDTO.getWorkloadType()
        );
        allocationFindPageVOPage.setRecords(allocationFindPageVOS);
        return ApiResult.success(allocationFindPageVOPage);
    }

    /**
     * 人才分摊数据分页、导出封装
     *
     * @param sysDeptMap 部门IdMap
     * @param records 数据列表
     */
    private List<AllocationFindPageVO> talentAllocation(
            Map<Long, Roster> rosterIdMap,
            Map<Long, SysDept> sysDeptMap,
            Map<Long, BigDecimal> userAttendanceMap,
            Table<Long, Long, List<CompensatoryLeaveDataDetailVO>> userIdAndDateAndCompensatoryLeaveTable,
            List<AllocationFindPageVO> records,
            Integer workloadType
    ) {

        List<Long> collect1 = records.stream().map(AllocationFindPageVO::getUserId).collect(Collectors.toList());
        UserPmsPageDTO userPmsPageDTO = new UserPmsPageDTO();
        UserPmsDTO userPmsDTO = userPmsPageDTO.toUserPmsDTO();
        List<SysUserPmsCqVO> sysUserPmsCqVOS = new ArrayList<>();
        ObjectUtils.<List<SysUserOutVO>>defaultIfNull(
                remoteOutPmsService.getUserListByMultiParameterPms(userPmsDTO).getData(),
                ImmutableList.of()
        ).forEach(u -> {
            SysUserPmsCqVO sysUserPmsCqVO = SysUserPmsCqVO.of(u, sysDeptMap);

            sysUserPmsCqVOS.add(sysUserPmsCqVO);
        });

        userPmsPageDTO.setUserIds(collect1);

        List<Long> projectIds = records.stream().map(AllocationFindPageVO::getId).collect(Collectors.toList());
        //项目id集合获取审核人员和项目id的map
        List<Privilege> privileges = privilegeMapper.getPrivilegeByProjectIdAndPrivilegeType(
                projectIds, PrivilegeTypeEnum.PROJECT_AUDITOR.getValue()
        );
        Map<Long, String> prjUsermap = privileges.stream().collect(Collectors.toMap(Privilege::getProjectId, Privilege::getUserName));
        Map<Long, String> sysUserPmsCqVOSMap = sysUserPmsCqVOS.stream().collect(Collectors.toMap(
                SysUserPmsCqVO::getUserId, SysUserPmsCqVO::getDeptName,
                (v1, v2) -> v1
        ));

        records.forEach(x -> {
            Long userId = x.getUserId();
            Roster roster = rosterIdMap.get(userId);


            if (roster != null) {
                x.setIdCardNo(Strings.nullToEmpty(roster.getIdCardNo()));
            }

            // 将工时小时数换算为天数
            if(Optional.ofNullable(workloadType).isPresent() && NumberUtils.INTEGER_TWO.equals(workloadType)){
                x.setProjectNormalHours(CommonUtils.roundOnePoint(x.getProjectNormalHours()));
                x.setProjectAddedHours(CommonUtils.roundOnePoint(x.getProjectAddedHours()));
                x.setWorkOvertimeHours(CommonUtils.roundOnePoint(x.getWorkOvertimeHours()));
                x.setRestOvertimeHours(CommonUtils.roundOnePoint(x.getRestOvertimeHours()));
                x.setHolidayOvertimeHours(CommonUtils.roundOnePoint(x.getHolidayOvertimeHours()));
            }else{
                x.setProjectNormalHours(CommonUtils.unitConversion(x.getProjectNormalHours()));
                x.setProjectAddedHours(CommonUtils.unitConversion(x.getProjectAddedHours()));
                x.setWorkOvertimeHours(CommonUtils.unitConversion(x.getWorkOvertimeHours()));
                x.setRestOvertimeHours(CommonUtils.unitConversion(x.getRestOvertimeHours()));
                x.setHolidayOvertimeHours(CommonUtils.unitConversion(x.getHolidayOvertimeHours()));
            }

            if (!Objects.equals(x.getPreSaleHours(), StrPool.DASHED) && !Objects.equals(x.getAfterSaleHours(), StrPool.DASHED)) {
                x.setPreSaleHours(new BigDecimal(x.getPreSaleHours()).toPlainString());
                x.setAfterSaleHours(new BigDecimal(x.getAfterSaleHours()).toPlainString());
            }


            List<CompensatoryLeaveDataDetailVO> compensatoryLeaveList = userIdAndDateAndCompensatoryLeaveTable.get(x.getUserId(), x.getId());
            //调休工时
            BigDecimal leaveHours = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
            if (CollectionUtil.isNotEmpty(compensatoryLeaveList)){
                leaveHours = compensatoryLeaveList.stream()
                        .map(CompensatoryLeaveDataDetailVO::getHourData)
                        .reduce(BigDecimal.ZERO,BigDecimal::add)
                        .setScale(2, RoundingMode.HALF_UP);
            }
            x.setCwActualAttendance(x.getCwActualAttendance() == null ? BigDecimal.ZERO : x.getCwActualAttendance().add(userAttendanceMap.getOrDefault(userId,BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP))));

            if(Optional.ofNullable(workloadType).isPresent() && NumberUtils.INTEGER_TWO.equals(workloadType)){
                x.setLeaveHours(CommonUtils.roundOnePoint(x.getLeaveHours().add(leaveHours)));
                x.setProjectShareHours(CommonUtils.roundOnePoint(x.getProjectShareHours().add(x.getLeaveHours())
                        .add(x.getRestOvertimeHours()).add(x.getHolidayOvertimeHours())));
            }else{
                x.setLeaveHours(CommonUtils.unitConversion(x.getLeaveHours().add(leaveHours)));
                x.setProjectShareHours(CommonUtils.unitConversion(x.getProjectShareHours()).add(x.getLeaveHours())
                        .add(x.getRestOvertimeHours()).add(x.getHolidayOvertimeHours()));
            }


            x.setId(IdWorker.getId());
            x.setProjectDeptName(getResultUnitName(x.getProjectDeptId(), sysDeptMap, null));
            if (userId != null) {
                x.setPersonnelDeptName(sysUserPmsCqVOSMap.get(userId));
            }
            // 外包人员赋值 人员归属部门
            if (PersonnelStatusEnum.WB.getName().equals(x.getPersonnelStatusName()) && x.getUserDeptId() != null) {
                x.setPersonnelDeptName(sysDeptMap.get(x.getUserDeptId()) == null ? "" : sysDeptMap.get(x.getUserDeptId()).getName());
            }
            if (null == x.getPrivilegeUserName()) {
                String s = prjUsermap.get(x.getId());
                if (s != null) {
                    x.setPrivilegeUserName(s);
                    return;
                }
                if (x.getManagerUserName() != null) {
                    x.setPrivilegeUserName(x.getManagerUserName());
                    return;
                }
                x.setPrivilegeUserName(x.getSalesmanUserName());
            }
            // 将人员身份证号置为空（不再需要）
            x.setIdCardNo("");

        });
        return records;
    }

    @Override
    public void allocationExport(HttpServletResponse response, AllocationFindPageDTO allocationFindPageDTO) {
        allocationFindPageDTO.setPageNumber(1);
        allocationFindPageDTO.setPageSize(10000);
        List<Object> dataDay = new ArrayList<>();
        List<Object> dataHours = new ArrayList<>();
        allocationFindPageDTO.setWorkloadType(NumberUtils.INTEGER_TWO);
        List<AllocationFindPageVO> data = allocationFindPage(true, allocationFindPageDTO).getData().getRecords();

        dataHours = BaseEntityUtils.mapCollectionToList(data, AllocationInternalExportExcelVO::fromHour);
        dataDay = BaseEntityUtils.mapCollectionToList(data, AllocationExportExcelVO::fromDay);


        OutputStream os = null;
        ExcelWriter excelWriter = null;

        try {
            // 设置文件名
            response.setHeader("content-Type", "application/ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(allocationFindPageDTO.getSelectMonth() + "项目工时分摊表导出", StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20"));
            // excel写入对象
            os = response.getOutputStream();
            excelWriter = EasyExcel.write(os).build();
            WriteSheet mainSheet2 = EasyExcel.writerSheet(0, "统计单位人天").head(AllocationExportExcelVO.class).build();
            excelWriter.write(dataDay, mainSheet2);

            WriteSheet mainSheet = EasyExcel.writerSheet(1, "统计单位小时").head(AllocationInternalExportExcelVO.class).build();
            excelWriter.write(dataHours, mainSheet);

            response.setCharacterEncoding("UTF-8");


        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (os != null) {
                try {
                    os.flush();
                    os.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }

            }
        }
        //导出【数量】
        bcpLoggerUtils.log(FunctionConstants.PROJECT_TIME_ALLOCATION_TABLE, LogContentEnum.EXPORT_DATA,
                dataDay.size());
    }

    @Override
    public ApiResult<Page<ProjectHourSumFindPageVO>> projectHourSumFindPage(PageRequest
                                                                                    pageRequest, ProjectHourSumFindPageDTO projectHourSumFindPageDTO, Boolean showUnreviewed) {
        Map<String, Object> filter = createFilterForProjectHourSumFindPage(showUnreviewed, projectHourSumFindPageDTO);
        Page<ProjectHourSumFindPageVO> page = new Page<>();
        if (showUnreviewed != null && !filter.containsKey(PROJECT_IDS)) {
            // 如果要筛选待审核、已审核条件，同时又没有符合条件的项目ID，直接返回空
            return ApiResult.success(page);
        }

        Set<Long> projectIds;
        projectIds = Sets.newHashSet(pmsRetriever.getProjectIdsAvailable());
        if (CollectionUtils.isEmpty(projectIds)) {
            return ApiResult.success(page);
        }

        filter.put(PROJECT_IDS, projectIds);
        page = dailyPaperEntryMapper.projectHourSumFindPage(new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize()), projectHourSumFindPageDTO, filter);
        List<ProjectHourSumFindPageVO> records = page.getRecords();

        // 检测并设置项目工时汇总状态
        processForHourSumVO(records, projectHourSumFindPageDTO, showUnreviewed);
        //部门映射
        Map<Long, SysDept> sysDeptMap = fetchIdDeptMap();
        // 设置部门字段
        records.forEach(r -> {
            r.setProjectDeptName(getResultUnitName(r.getProjectDeptId(), sysDeptMap, null));

            // 转换成人天
            r.setHourData(CommonUtils.unitConversion(r.getHourData()));
            r.setAddedHours(CommonUtils.unitConversion(r.getAddedHours()));
            r.setNormalHours(CommonUtils.unitConversion(r.getNormalHours()));
            r.setProjectHours(CommonUtils.unitConversion(r.getProjectHours()));
        });

        return ApiResult.success(page);
    }

    private Map<String, Object> createFilterForProjectHourSumFindPage(Boolean
                                                                              showUnreviewed, ProjectHourSumFindPageDTO projectHourSumFindPageDTO) {
        Map<String, Object> filter = Maps.newHashMap();
        List<ProjectVO> projects = projectMapper.findListFront(filter);
        Set<Long> projectIdsSet = Sets.newHashSet(pmsRetriever.getProjectIdsAvailable());

        if (!projectIdsSet.isEmpty() && !projects.isEmpty()) {
            projects.removeIf(p -> !projectIdsSet.contains(p.getId()));
        } else {
            projects = ImmutableList.of();
        }
        Set<ProjectVO> projectSet;
        List<Long> projectIds;
        Set<Long> projectIdsForExclude;
        Integer projectStatus = projectHourSumFindPageDTO.getProjectStatus();

        if (projectStatus != null) {
            filter.put("projectStatus", projectStatus);
            // 将dto对象中的参数置空，防止后续该条件被重复拼入其他SQL
            projectHourSumFindPageDTO.setProjectStatus(null);
        }
        if (!projects.isEmpty()) {
            // 转为set，去重
            projectSet = ImmutableSet.copyOf(projects);
            projectIds = BaseEntityUtils.mapCollectionToList(projectSet, ProjectVO::getId);
            filter.put("showUnreviewed", showUnreviewed);
            if (showUnreviewed != null) {
                // 含有待审核条目的项目ID集合
                projectIdsForExclude = dailyPaperEntryMapper.findProjectIdSetByApprovalStatusForProjectHourSum(DSH.getValue(), projectHourSumFindPageDTO);
                // 筛选待审核条目（showUnreviewed = true）或已审核条目（false），得出需要查询的id列表
                // （projectIdsForExclude用于过滤掉不符合条件的项目ID）
                if (showUnreviewed) {
                    projectIds = BaseEntityUtils.filterCollectionToList(projectIds, projectIdsForExclude::contains);
                } else {
                    projectIds = BaseEntityUtils.filterCollectionToList(projectIds, projectId -> !projectIdsForExclude.contains(projectId));
                }
            }
            if (!projectIds.isEmpty()) {
                // 在外面判断是否能够查询到结果的重要依据，就是这里的projectIds，如果projectIds为空，代表没有查到任何结果，需要返回空列表
                filter.put(PROJECT_IDS, projectIds);
            }
        }

        return filter;
    }

    /**
     * ~ 处理approvalFinished和projectStatusName的设置，为工时汇总查询拼装最终的VO对象 ~
     *
     * @param vos            待处理的VO对象列表
     * @param dto            dto对象
     * @param showUnreviewed 是否展示未审核Flag
     * <AUTHOR>
     */
    private void processForHourSumVO(List<ProjectHourSumFindPageVO> vos, ProjectHourSumFindPageDTO dto, Boolean
            showUnreviewed) {
        List<Long> projectIds;
        // Map<projectId, approvalStatus>
        Multimap<Long, Integer> entriesWithApprovalStatus;
        // Map<projectId, finished>
        Map<Long, Boolean> finishedInfo;

        if (vos.isEmpty()) {
            return;
        }
        if (showUnreviewed != null) {
            // 如果启用了筛选，直接就可以拼装最终的结果
            vos.forEach(vo -> {
                vo.setProjectStatusName(EnumUtils.getNameByValue(BusinessStatusEnum.class, vo.getProjectStatus()));
                vo.setApprovalFinished(!showUnreviewed);
            });

            return;
        }
        // 到这里时，说明用户查询所有审核状态的数据，所以要到数据库中查询项目的审核状况
        projectIds = vos.stream().map(ProjectHourSumFindPageVO::getId).collect(Collectors.toList());
        entriesWithApprovalStatus = HashMultimap.create();
        finishedInfo = Maps.newHashMap();
        dailyPaperEntryMapper.findProjectIdAndApprovalStatusByProjectIdsAndSubmissionDateBetween(projectIds, dto.getStartTime(), dto.getEndTime())
                .forEach(entry -> entriesWithApprovalStatus.put(entry.getProjectId(), entry.getApprovalStatus()));
        entriesWithApprovalStatus.keySet().forEach(projectId -> {
            boolean isFinished = entriesWithApprovalStatus.get(projectId).stream().noneMatch(status -> DSH.getValue().equals(status));

            finishedInfo.put(projectId, isFinished);
        });
        vos.forEach(vo -> {
            vo.setProjectStatusName(EnumUtils.getNameByValue(BusinessStatusEnum.class, vo.getProjectStatus()));
            if (!NumberUtils.INTEGER_ZERO.equals(finishedInfo.size()) && Optional.ofNullable(vo.getId()).isPresent()) {
                vo.setApprovalFinished(finishedInfo.getOrDefault(vo.getId(),Boolean.FALSE));
            }
        });
    }

    @Override
    public void projectHourSumExport(HttpServletResponse response, ProjectHourSumFindPageDTO
            projectHourSumFindPageDTO) {
        List<ProjectHourSumFindPageVO> data = projectHourSumFindPage(new PageRequest(1, Integer.MAX_VALUE), projectHourSumFindPageDTO, null).getData().getRecords();

        sendExcelResponse(response, data);
    }

    private void sendExcelResponse(HttpServletResponse response, List<ProjectHourSumFindPageVO> data) {
        try {
            response.setContentType(CONTENT_TYPE_SHEET);
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            String fileName = URLEncoder.encode("项目工时汇总导出", StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
            response.setHeader(PARAM_CONTENT_DISPOSITION, CONTENT_DISPOSITION + fileName + XLSX_SUFFIX);
            EasyExcel.write(response.getOutputStream(), ProjectHourSumFindPageVO.class).autoCloseStream(Boolean.FALSE).sheet("数据").doWrite(data);
        } catch (UnsupportedEncodingException e) {
            throw new ServiceException("服务器内部错误 (Unsupported Encoding)");
        } catch (IOException e) {
            response.reset();
            response.setContentType(CONTENT_TYPE_JSON);
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            ApiResult<String> result = ApiResult.failureMsg("导出失败", ApiResultEnum.FAILURE);
            try {
                response.getWriter().println(JSONUtil.toJsonStr(result));
            } catch (IOException ioException) {
                log.error("导出Excel失败，IO异常.", ioException);
            }
        }
    }

    @Override
    public ApiResult<Page<PersonnelReuseFindPageVO>> personnelReuseFindPage(PageRequest
                                                                                    pageRequest, PersonnelReuseFindPageDTO personnelReuseFindPageDTO) {
        PigxUser user = SecurityUtils.getUser();
        Long userId = user.getId();
        personnelReuseFindPageDTO.setUserId(userId);
        //根据权限带出可以查看的数据
        if (Objects.isNull(projectScopeHandle.findDataScope())) {
            return ApiResult.success(new Page<>());
        }
        //if (Objects.isNull(projectScopeHandle.findDataScope().getDeptIdList())) {
        //    return ApiResult.success(new Page<>());
        //}
        if (CollectionUtils.isNotEmpty(projectScopeHandle.findDataScope().getDeptIdList())) {
            List<Long> ids = personnelReuseMapper.personnelReuseIdPage(personnelReuseFindPageDTO, YTG.getValue());
            personnelReuseFindPageDTO.setIds(ids);
        }
        //分两次查询
        // 查询当前用户可以审核的项目id
        Set<Long> projectIdsAvailableSet = Sets.newHashSet(pmsRetriever.getProjectIdsAvailable());
        log.info("ID为：{}的用户可查看的项目ID列表：{}", userId, projectIdsAvailableSet);
        personnelReuseFindPageDTO.setProjectIds(
                CollectionUtils.isEmpty(projectIdsAvailableSet) ?
                        ImmutableList.of()
                        :
                        Lists.newArrayList(projectIdsAvailableSet
                        ));
        Page<PersonnelReuseFindPageVO> page = personnelReuseMapper.personnelReuseFindPage(new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize()), personnelReuseFindPageDTO, YTG.getValue());


        return ApiResult.success(page);
    }

    @Override
    public void personnelReuseExport(HttpServletResponse response, PersonnelReuseFindPageDTO
            personnelReuseFindPageDTO) {
        personnelReuseFindPageDTO.setUserId(SecurityUtils.getUser().getId());
        // 旧功能传参有误，先临时处理赋值

        List<PersonnelReuseFindPageVO> data = personnelReuseFindPage(new PageRequest(1, Integer.MAX_VALUE), personnelReuseFindPageDTO).getData().getRecords();
        try {
            response.setContentType(CONTENT_TYPE_SHEET);
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            String fileName = URLEncoder.encode(personnelReuseFindPageDTO.getSelectMonth() + "人才复用工时报表", StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
            response.setHeader(PARAM_CONTENT_DISPOSITION, CONTENT_DISPOSITION + fileName + XLSX_SUFFIX);
            EasyExcel.write(response.getOutputStream(), PersonnelReuseFindPageVO.class).autoCloseStream(Boolean.FALSE).sheet("项目实习").doWrite(data);
        } catch (Exception e) {
            response.reset();
            response.setContentType(CONTENT_TYPE_JSON);
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            ApiResult<String> result = ApiResult.failureMsg("导出失败", ApiResultEnum.FAILURE);
            try {
                response.getWriter().println(JSONUtil.toJsonStr(result));
            } catch (IOException ioException) {
                ioException.printStackTrace();
            }
        }
        //导出【数量】
        bcpLoggerUtils.log(FunctionConstants.TALENT_REUSE_TABLE, LogContentEnum.EXPORT_DATA,
                data.size());
    }


    /**
     * 根据 传入部门id，获取组织架构全路径名称
     */
    @Override
    public String getResultUnitName(Long unitId, Map<Long, SysDept> orgMap, String resultName) {
        SysDept sysDept = orgMap.get(unitId);
        if (sysDept != null) {
            if (resultName != null) {
                resultName = sysDept.getName() + resultName;
            } else {
                resultName = sysDept.getName();
            }
            //递归判断
            if (sysDept.getParentId() != -1) {
                resultName = "-" + resultName;
                return getResultUnitName(sysDept.getParentId(), orgMap, resultName);
            } else {
                return resultName;
            }
        }

        return "";
    }


    @Override
    public WorkHourStatisticsFindPageVO workHourStatisticsFindPage(WorkHourStatisticsFindPageDTO dto) {
        WorkHourStatisticsFindPageVO vo = new WorkHourStatisticsFindPageVO();
        Page<DailyPaperEntryVO> page = new Page<>(dto.getPageNumber(), dto.getPageSize());
        BigDecimal totalHours;
        Page<DailyPaperEntryVO> pagePo;
        List<DailyPaperEntryVO> poList;
        Set<Long> userIdsAvailable;
        // <项目ID, 日期, Map<用户ID, 加班时长>>
        Table<Long, LocalDate, Map<Long, BigDecimal>> overtimeLeaveDataTable;
        List<Long> userIds = dto.getUserIds();
        Set<Long> userIdsForOvertimeDataQuery;
        List<Long> projectIds = dto.getProjectIds();
        LocalDate startDate = dto.getStartDate();
        LocalDate endDate = dto.getEndDate();
        boolean hasUserIds;
        boolean hasProjectIds;
        Collection<Long> projectIdsAvailable;

        vo.setPage(page);
        vo.setTotalHours(BigDecimal.ZERO);
        vo.setTaskRate(BigDecimal.ZERO);
        if (CollectionUtils.isEmpty(userIds) && CollectionUtils.isEmpty(projectIds) && startDate == null && endDate == null) {
            return vo;
        }
        projectIdsAvailable = pmsRetriever.getProjectIdsAvailable();
        if (projectIdsAvailable.isEmpty()) {
            return vo;
        }
        // 获取所有可用用户
        List<Long> taskIds = projectTaskeMapper.findTaskIdByProjectIds(projectIdsAvailable);
        userIdsAvailable = projectTaskeUserMapper.findListByTaskIds(taskIds)
                .stream()
                .map(ProjectTaskeUser::getUserId)
                .collect(Collectors.toSet());
        if (userIdsAvailable.isEmpty()) {
            return vo;
        }
        hasUserIds = CollectionUtils.isNotEmpty(userIds);
        hasProjectIds = CollectionUtils.isNotEmpty(projectIds);
        // 处理用户无权查看的数据
        if (hasProjectIds) {
            projectIds.removeIf(pid -> !projectIdsAvailable.contains(pid));
            if (projectIds.isEmpty()) {
                return vo;
            }
        }
        if (hasUserIds) {
            userIds.removeIf(uid -> !userIdsAvailable.contains(uid));
        }
        //  当传入了userIds，但是过滤之后userIds为空时，直接进行返回
        if (hasUserIds && CollectionUtils.isEmpty(userIds)) {
            return vo;
        }

        // 进行查询
        if (hasProjectIds) {
            pagePo = dailyPaperEntryMapper.findByUserIdsAndProjectIdsAndDateRange(new Page<>(dto.getPageNumber(), dto.getPageSize()), userIds, projectIds, startDate, endDate, dto.getTaskId(),dto.getDeptIds(),dto.getPersonnelStatus(),dto);
            totalHours = dailyPaperEntryMapper.sumHoursByUserIdsAndProjectIdsAndDateRange(userIds, projectIds, startDate, endDate, dto.getTaskId(),dto.getDeptIds(),dto.getPersonnelStatus(),dto);
        } else if (hasUserIds) {
            pagePo = dailyPaperEntryMapper.findByUserIdsAndProjectIdsAndDateRange(new Page<>(dto.getPageNumber(), dto.getPageSize()), userIds, projectIdsAvailable, startDate, endDate, dto.getTaskId(),dto.getDeptIds(),dto.getPersonnelStatus(),dto);
            totalHours = dailyPaperEntryMapper.sumHoursByUserIdsAndProjectIdsAndDateRange(userIds, projectIdsAvailable, startDate, endDate, dto.getTaskId(),dto.getDeptIds(),dto.getPersonnelStatus(),dto);
        } else if (startDate != null || endDate != null) {
            // 有可用用户，按全部可用用户查询
            pagePo = dailyPaperEntryMapper.findByUserIdsAndProjectIdsAndDateRange(new Page<>(dto.getPageNumber(), dto.getPageSize()), new ArrayList<>(userIdsAvailable), projectIdsAvailable, startDate, endDate, dto.getTaskId(),dto.getDeptIds(),dto.getPersonnelStatus(),dto);
            totalHours = dailyPaperEntryMapper.sumHoursByUserIdsAndProjectIdsAndDateRange(new ArrayList<>(userIdsAvailable), projectIdsAvailable, startDate, endDate, dto.getTaskId(),dto.getDeptIds(),dto.getPersonnelStatus(),dto);
        } else {
            // 没有任何可用权限
            return vo;
        }
        if (pagePo.getRecords().isEmpty()) {
            return vo;
        }
        userIdsForOvertimeDataQuery = BaseEntityUtils.mapCollectionToSet(pagePo.getRecords(), DailyPaperEntryVO::getUserId);
        overtimeLeaveDataTable = HashBasedTable.create();
        overtimeLeaveDataMapper.getOvertimeDataListByUserIds(startDate == null ? LocalDate.MIN : startDate, endDate == null ? LocalDate.MAX : endDate, userIdsForOvertimeDataQuery).forEach(olData -> {
            Long projectId = olData.getXmmc();
            LocalDate belongdate = olData.getBelongdate();
            Map<Long, BigDecimal> userIdOverDataMap;

            if (projectId != null) {
                if (overtimeLeaveDataTable.contains(projectId, belongdate)) {
                    userIdOverDataMap = overtimeLeaveDataTable.get(projectId, belongdate);
                    userIdOverDataMap.put(olData.getOaId(), olData.getHourData());
                } else {
                    userIdOverDataMap = Maps.newHashMap();
                    userIdOverDataMap.put(olData.getOaId(), olData.getHourData());
                    overtimeLeaveDataTable.put(projectId, belongdate, userIdOverDataMap);
                }
            }
        });

        List<DeptCacheDto>  deptList  = remoteDeptService.getAllDeptList(false);
        Map<Long, SysDept> deptIdMap;
        if (CollectionUtils.isEmpty(deptList)) {
            log.warn("未找到中台部门数据");
            deptIdMap = ImmutableMap.of();
        } else {
            deptIdMap = SysDeptUtils.mapBCPDeptCacheDtoToSysDept(deptList)
                    .stream()
                    .collect(Collectors.toMap(SysDept::getDeptId, dept -> dept));
        }
        List<Holiday> holidayList = holidayMapper.findHolidayList();
        Map<LocalDate, Integer> holidayMap = holidayList.stream().filter(h-> h.getHolidayType()!=null).
                collect(Collectors.toMap(Holiday::getDayDate,Holiday::getHolidayType, (a, b) -> a));
        poList = pagePo.getRecords().stream().map(entry -> {
            Map<Long, BigDecimal> userIdAddedHourMap = overtimeLeaveDataTable.get(entry.getProjectId(), entry.getSubmissionDate());
            BigDecimal oaAddedHour = userIdAddedHourMap == null ? null : userIdAddedHourMap.get(entry.getUserId());
            entry.setSumHours(entry.getNormalHours().add(entry.getAddedHours()));
            entry.setApprovalStatusName(EnumUtils.getNameByValue(ApprovalStatusEnum.class, entry.getApprovalStatus()));
            entry.setOaAddedHours(BigDecimal.ZERO);
            if( oaAddedHour != null){
                entry.setOaAddedHours(oaAddedHour);
            }
            entry.setDeptName(SysDeptUtils.collectFullName(deptIdMap, entry.getUserDeptId()));
            entry.setPersonnelStatusName(EnumUtils.getNameByValue(PersonnelStatusEnum.class, entry.getUserStatus()));

            entry.setOaAddedHours(CommonUtils.unitConversion(entry.getOaAddedHours()));
            entry.setAddedHours(CommonUtils.unitConversion(entry.getAddedHours()));
            entry.setNormalHours(CommonUtils.unitConversion(entry.getNormalHours()));
            entry.setSumHours(CommonUtils.unitConversion(entry.getSumHours()));
            entry.setSubmissionDateFormatted(DailyPaperDateUtils.asDateString(entry.getSubmissionDate()));
            entry.setHolidayType(holidayMap.getOrDefault(entry.getSubmissionDate(),null));
            return entry;
        }).collect(Collectors.toList());
        assembleVo(dto, vo, page, totalHours, pagePo, poList, projectIds, startDate, endDate);

        return vo;
    }

    private void assembleVo(WorkHourStatisticsFindPageDTO dto,
                            WorkHourStatisticsFindPageVO vo,
                            Page<DailyPaperEntryVO> page,
                            BigDecimal totalHours,
                            Page<DailyPaperEntryVO> pagePo,
                            List<DailyPaperEntryVO> poList,
                            List<Long> projectIds,
                            LocalDate startDate,
                            LocalDate endDate) {
        BeanUtils.copyProperties(pagePo, page);
        page.setRecords(poList);
        if (CollectionUtils.isNotEmpty(dto.getTaskId())) {
            //存在任务参数，查询工时占比
            BigDecimal projectTotalHours = dailyPaperEntryMapper.sumHoursByUserIdsAndProjectIdsAndDateRange(null, projectIds, startDate, endDate, null,dto.getDeptIds(),dto.getPersonnelStatus(),dto);
            vo.setTaskRate(totalHours.divide(projectTotalHours, 4, RoundingMode.HALF_UP).multiply(BigDecimalUtils.HUNDRED_DECIMAL).setScale(2, RoundingMode.HALF_UP));
        } else {
            vo.setTaskRate(BigDecimalUtils.HUNDRED_DECIMAL.setScale(2, RoundingMode.HALF_UP));
        }
        vo.setTotalHours(CommonUtils.unitConversion(totalHours));
    }

    @Override
    @Transactional
    public ApiResult<Page<ProjectHourDetailsFindPageVO>> projectHourDetailsFindPage(ProjectHourDetailsFindPageDTO dto) {
        List<ProjectHourDetailsFindPageVO> records = projectMapper.pageProjectHourDetails(dto);
        if (CollectionUtils.isNotEmpty(records)) {
            records.forEach(r -> {
                // 设置项目状态
                r.setProjectStatusName(BusinessStatusEnum.getNameByVal(r.getProjectStatus()));
                // 工时转化为人天单位
                r.setNormalHours(CommonUtils.unitConversion(r.getNormalHours()));
                r.setAddedHours(CommonUtils.unitConversion(r.getAddedHours()));
            });
        }

        Page<ProjectHourDetailsFindPageVO> result = CommonUtils.getPages(dto.getPageNumber(), dto.getPageSize(), records);

        return ApiResult.success(result);
    }


    @Override
    public List<ProjectHourDetailsFindPageVO> projectHourDetailsExport(ProjectHourDetailsFindPageDTO dto) {
        List<ProjectHourDetailsFindPageVO> vos = projectMapper.pageProjectHourDetails(dto);
        if (CollectionUtils.isNotEmpty(vos)) {
            vos.forEach(r -> {
                // 设置项目状态
                r.setProjectStatusName(BusinessStatusEnum.getNameByVal(r.getProjectStatus()));
                // 工时转化为人天单位
                r.setNormalHours(CommonUtils.unitConversion(r.getNormalHours()));
                r.setAddedHours(CommonUtils.unitConversion(r.getAddedHours()));
            });
        } else {
            return Collections.singletonList(new ProjectHourDetailsFindPageVO());
        }

        return vos;
    }

    @Override
    public ApiResult<Page<DelivererFindPageVO>> delivererFindPage(DelivererFindPageDTO dto) {
        Long userId = SecurityUtils.getUser().getId();
        dto.setUserId(userId);

        List<Long> ids = projectMapper.delivererFindIdPage(dto, YTG.getValue());
        dto.setIds(ids);
        //分两次查询
        // 查询当前用户可以审核的项目id
        Set<Long> projectIdsAvailableSet = Sets.newHashSet(pmsRetriever.getProjectIdsAvailable());
        dto.setProjectIds(
                CollectionUtils.isEmpty(projectIdsAvailableSet) ?
                        ImmutableList.of()
                        :
                        Lists.newArrayList(projectIdsAvailableSet
                        ));
        Page<DelivererFindPageVO> page = projectMapper.delivererFindPage(Page.of(dto.getPageNumber(), dto.getPageSize()), dto, YTG.getValue());
        List<DelivererFindPageVO> records = page.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            records.forEach(r ->
                    r.setProjectConsumed(CommonUtils.unitConversion(r.getProjectConsumed())));
        }

        return ApiResult.success(page);
    }

    @Override
    public List<DelivererFindPageVO> delivererExport(DelivererFindPageDTO dto) {
        Long userId = SecurityUtils.getUser().getId();
        dto.setUserId(userId);

        List<Long> ids = projectMapper.delivererFindIdPage(dto, YTG.getValue());
        dto.setIds(ids);
        //分两次查询
        // 查询当前用户可以审核的项目id
        Set<Long> projectIdsAvailableSet = Sets.newHashSet(pmsRetriever.getProjectIdsAvailable());
        dto.setProjectIds(
                CollectionUtils.isEmpty(projectIdsAvailableSet) ?
                        ImmutableList.of()
                        :
                        Lists.newArrayList(projectIdsAvailableSet
                        ));

        List<DelivererFindPageVO> records = projectMapper.delivererFindPage(dto, YTG.getValue());
        if (CollectionUtils.isNotEmpty(records)) {
            records.forEach(r -> r.setProjectConsumed(CommonUtils.unitConversion(r.getProjectConsumed())));
        } else {
            records.add(new DelivererFindPageVO());
        }
        //导出【数量】
        bcpLoggerUtils.log(FunctionConstants.DELIVERY_PERSONNEL_TABLE, LogContentEnum.EXPORT_DATA,
                records.size());
        return records;
    }

    @Override
    public ApiResult<Page<ProjectHourSumFindPageVO>> projectHourSumFindPageV2(PageRequest pageRequest,
                                                                              ProjectHourSumFindPageDTO projectHourSumFindPageDTO, Boolean showUnreviewed) {
        Set<Long> projectIdsSet = Sets.newHashSet(pmsRetriever.getProjectIdsAvailable());
        if (!projectIdsSet.isEmpty()) {
            projectHourSumFindPageDTO.setProjectIds(Lists.newArrayList(projectIdsSet));
        } else {
            return ApiResult.success(new Page<>());
        }

        Page<ProjectHourSumFindPageVO> page = dailyPaperEntryMapper.projectHourSumFindPageV2(new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize()), projectHourSumFindPageDTO, showUnreviewed);
        List<ProjectHourSumFindPageVO> records = page.getRecords();
        // 查询整合项目调休信息
        Map<Long, List<CompensatoryLeaveDataDetailVO>> compensatoryLeaveLMap = new HashMap<>();
        List<CompensatoryLeaveDataDetailVO> compensatoryLeaveList = overtimeLeaveDataService.findByDateTimeRangeAndUserIds(projectHourSumFindPageDTO.getStartTime(),projectHourSumFindPageDTO.getEndTime(), ImmutableList.of());
        if (CollectionUtil.isNotEmpty(compensatoryLeaveList)){
            compensatoryLeaveLMap = compensatoryLeaveList.stream().collect(Collectors.groupingBy(CompensatoryLeaveDataDetailVO::getXmmc));
        }
        Map<Long, List<CompensatoryLeaveDataDetailVO>> finalCompensatoryLeaveLMap = compensatoryLeaveLMap;
        // 检测并设置项目工时汇总状态
        processForHourSumVO(records, projectHourSumFindPageDTO, showUnreviewed);
        //部门映射
        Map<Long, SysDept> sysDeptMap = fetchIdDeptMap();
        // 设置部门字段
        records.forEach(r -> {
            List<CompensatoryLeaveDataDetailVO> compensatoryLeaves = finalCompensatoryLeaveLMap.getOrDefault(r.getId(), new ArrayList<>());
            //调休工时
            BigDecimal leaveHours = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
            if (CollectionUtil.isNotEmpty(compensatoryLeaves)){
                leaveHours = compensatoryLeaves.stream()
                        .map(CompensatoryLeaveDataDetailVO::getHourData)
                        .reduce(BigDecimal.ZERO,BigDecimal::add)
                        .setScale(2, RoundingMode.HALF_UP);
            }
            r.setProjectDeptName(getResultUnitName(r.getProjectDeptId(), sysDeptMap, null));

            // 转换成人天
            if(Optional.ofNullable(projectHourSumFindPageDTO.getWorkloadType()).isPresent() && NumberUtils.INTEGER_TWO.equals(projectHourSumFindPageDTO.getWorkloadType())){
                //按小时
                r.setHourData(CommonUtils.roundOnePoint(r.getHourData()));
                r.setAddedHours(CommonUtils.roundOnePoint(r.getAddedHours()));
                r.setWorkOvertimeHours(CommonUtils.roundOnePoint(r.getWorkOvertimeHours()));
                r.setRestOvertimeHours(CommonUtils.roundOnePoint(r.getRestOvertimeHours()));
                r.setHolidayOvertimeHours(CommonUtils.roundOnePoint(r.getHolidayOvertimeHours()));
                r.setNormalHours(CommonUtils.roundOnePoint(r.getNormalHours()));
                r.setLeaveHours(CommonUtils.roundOnePoint(leaveHours));
                if (Optional.ofNullable(showUnreviewed).isPresent() && showUnreviewed){
                    r.setLeaveHours(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                }
                r.setProjectHours(CommonUtils.roundOnePoint(r.getProjectHours()));
                r.setPreSaleHours(CommonUtils.roundOnePoint(r.getPreSaleHours()));
                r.setAfterSaleHours(CommonUtils.roundOnePoint(r.getAfterSaleHours()));
                r.setProjectShareHours(r.getProjectHours().add(r.getLeaveHours())
                        .add(r.getHolidayOvertimeHours().add(r.getRestOvertimeHours())));
            }else{
                r.setHourData(CommonUtils.unitConversion(r.getHourData()));
                r.setAddedHours(CommonUtils.unitConversion(r.getAddedHours()));
                r.setWorkOvertimeHours(CommonUtils.unitConversion(r.getWorkOvertimeHours()));
                r.setRestOvertimeHours(CommonUtils.unitConversion(r.getRestOvertimeHours()));
                r.setHolidayOvertimeHours(CommonUtils.unitConversion(r.getHolidayOvertimeHours()));
                r.setNormalHours(CommonUtils.unitConversion(r.getNormalHours()));
                r.setLeaveHours(CommonUtils.unitConversion(leaveHours));
                if (Optional.ofNullable(showUnreviewed).isPresent() && showUnreviewed){
                    r.setLeaveHours(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                }

                r.setProjectHours(CommonUtils.unitConversion(r.getProjectHours()));
                r.setPreSaleHours(CommonUtils.unitConversion(r.getPreSaleHours()));
                r.setAfterSaleHours(CommonUtils.unitConversion(r.getAfterSaleHours()));
                r.setProjectShareHours(r.getProjectHours().add(r.getLeaveHours())
                        .add(r.getHolidayOvertimeHours().add(r.getRestOvertimeHours())));
            }
        });
        return ApiResult.success(page);
    }

    /**
     * 导出、分页共用前置查询条件
     *
     * @param projectHourSumFindPageDTO 查询参数
     */
    private List<Long> commonPage(ProjectHourSumFindPageDTO projectHourSumFindPageDTO) {
        Set<Long> projectIdsSet;
        projectIdsSet = Sets.newHashSet(pmsRetriever.getProjectIdsAvailable());
        if (projectIdsSet.isEmpty()) {
            return ImmutableList.of();
        }
        projectHourSumFindPageDTO.setProjectIds(Lists.newArrayList(projectIdsSet));

        return projectHourSumFindPageDTO.getProjectIds();
    }

    private List<UnReviewedDailyPaperEntryExcelVO> exportUnreviewedVo(List<UnReviewedDailyPaperEntryExcelVO> records,ProjectHourSumFindPageDTO dto,Boolean showUnreviewed){
        // 查询整合项目调休信息
        Map<Long, List<CompensatoryLeaveDataDetailVO>> compensatoryLeaveLMap = new HashMap<>();
        List<CompensatoryLeaveDataDetailVO> compensatoryLeaveList = overtimeLeaveDataService.findByDateTimeRangeAndUserIds(dto.getStartTime(),dto.getEndTime(), ImmutableList.of());
        if (CollectionUtil.isNotEmpty(compensatoryLeaveList)){
            compensatoryLeaveLMap = compensatoryLeaveList.stream().collect(Collectors.groupingBy(CompensatoryLeaveDataDetailVO::getXmmc));
        }
        Map<Long, List<CompensatoryLeaveDataDetailVO>> finalCompensatoryLeaveLMap = compensatoryLeaveLMap;
        // 检测并设置项目工时汇总状态
        //部门映射
        Map<Long, SysDept> sysDeptMap = fetchIdDeptMap();
        // 设置部门字段
        records.forEach(r -> {
            List<CompensatoryLeaveDataDetailVO> compensatoryLeaves = finalCompensatoryLeaveLMap.getOrDefault(r.getId(), new ArrayList<>());
            //调休工时
            BigDecimal leaveHours = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
            if (CollectionUtil.isNotEmpty(compensatoryLeaves)){
                leaveHours = compensatoryLeaves.stream()
                        .map(CompensatoryLeaveDataDetailVO::getHourData)
                        .reduce(BigDecimal.ZERO,BigDecimal::add)
                        .setScale(2, RoundingMode.HALF_UP);
            }
            r.setProjectDeptName(getResultUnitName(r.getProjectDeptId(), sysDeptMap, null));

            if(Optional.ofNullable(dto.getWorkloadType()).isPresent() && NumberUtils.INTEGER_TWO.equals(dto.getWorkloadType())){
                // 转换成小时
                r.setHourData(CommonUtils.roundOnePoint(r.getHourData()));
                r.setAddedHours(CommonUtils.roundOnePoint(r.getAddedHours()));
                r.setWorkOvertimeHours(CommonUtils.roundOnePoint(r.getWorkOvertimeHours()));
                r.setRestOvertimeHours(CommonUtils.roundOnePoint(r.getRestOvertimeHours()));
                r.setHolidayOvertimeHours(CommonUtils.roundOnePoint(r.getHolidayOvertimeHours()));
                r.setNormalHours(CommonUtils.roundOnePoint(r.getNormalHours()));
                r.setLeaveHours(CommonUtils.roundOnePoint(leaveHours));
                if (showUnreviewed){
                    r.setLeaveHours(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                }
                r.setProjectHours(CommonUtils.roundOnePoint(r.getProjectHours()));
                r.setPreSaleHours(CommonUtils.roundOnePoint(r.getPreSaleHours()));
                r.setAfterSaleHours(CommonUtils.roundOnePoint(r.getAfterSaleHours()));
                r.setProjectShareHours(r.getProjectHours().add(r.getLeaveHours())
                        .add(r.getHolidayOvertimeHours().add(r.getRestOvertimeHours())));
            }else{
                // 转换成人天
                r.setHourData(CommonUtils.unitConversion(r.getHourData()));
                r.setAddedHours(CommonUtils.unitConversion(r.getAddedHours()));
                r.setWorkOvertimeHours(CommonUtils.unitConversion(r.getWorkOvertimeHours()));
                r.setRestOvertimeHours(CommonUtils.unitConversion(r.getRestOvertimeHours()));
                r.setHolidayOvertimeHours(CommonUtils.unitConversion(r.getHolidayOvertimeHours()));
                r.setNormalHours(CommonUtils.unitConversion(r.getNormalHours()));
                r.setLeaveHours(CommonUtils.unitConversion(leaveHours));
                if (showUnreviewed){
                    r.setLeaveHours(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                }
                r.setProjectHours(CommonUtils.unitConversion(r.getProjectHours()));
                r.setPreSaleHours(CommonUtils.unitConversion(r.getPreSaleHours()));
                r.setAfterSaleHours(CommonUtils.unitConversion(r.getAfterSaleHours()));
                r.setProjectShareHours(r.getProjectHours().add(r.getLeaveHours())
                        .add(r.getHolidayOvertimeHours().add(r.getRestOvertimeHours())));
            }
        });
        return records;
    }
}
