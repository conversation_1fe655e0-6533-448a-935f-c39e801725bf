package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.cost.entity.domain.CostCashPlanVersion;
import com.gok.pboot.pms.cost.entity.vo.CostCashPlanVersionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 现金流计划版本记录表 Mapper 接口
 *
 * <AUTHOR> generated
 * @date 2024-03-19
 */
@Mapper
public interface CostCashPlanVersionMapper extends BaseMapper<CostCashPlanVersion> {

    /**
     * 获取当前版本
     *
     * @param projectId 项目ID
     * @return 当前版本信息
     */
    CostCashPlanVersion getCurrentVersion(@Param("projectId") Long projectId);

    /**
     * 更新历史版本状态
     *
     * @param projectId 项目ID
     */
    void updateHistoryVersion(@Param("projectId") Long projectId);

    /**
     * 获取版本列表
     *
     * @param projectId 项目ID
     * @return 版本列表
     */
    List<CostCashPlanVersion> getVersionList(@Param("projectId") Long projectId);

    /**
     * 分页查询
     *
     * @param page      页
     * @param projectId 项目 ID
     * @return {@link Page }<{@link CostCashPlanVersionVO }>
     */
    Page<CostCashPlanVersionVO> findPage(@Param("page") Page<CostCashPlanVersionVO> page, @Param("projectId") Long projectId);

    /**
     * 修改现金流关联估算成本
     *
     * @param id            现金流ID
     * @param costVersionId 成本估算结果版本ID
     * @param costVersion   成本估算结果版本
     */
    void updateCostVersion(@Param("id") Long id, @Param("costVersionId") Long costVersionId, @Param("costVersion") String costVersion);

}