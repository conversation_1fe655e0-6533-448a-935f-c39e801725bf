package com.gok.pboot.pms.enumeration;

/**
 * 日报审核状态枚举
 **/
public enum ApprovalStatusEnum implements ValueEnum<Integer> {
    /**
     * 无日报
     */
    WTB(-1, "未填报"),
    /**
     * 未提交
     */
    WTJ(0, "未提交"),
    /**
     * 已退回
     */
    YTH(1, "已退回"),
    /**
     * 待审核
     */
    DSH(2, "待审核"),
    /**
     * 不通过
     */
    BTG(3, "不通过"),
    /**
     * 已通过
     */
    YTG(4, "已通过"),
    /**
     * 异常（不入库，仅在业务中使用）
     */
    YC(99, "异常");

    //值
    private final Integer value;
    //名称
    private final String name;

    ApprovalStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 获取值
     *
     * @return Integer
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取名称
     *
     * @return String
     */
    @Override
    public String getName() {
        return name;
    }

    public static String getNameByVal(Integer value) {
        for (ApprovalStatusEnum statusEnum : ApprovalStatusEnum.values()) {
            if (statusEnum.value.equals(value)) {
                return statusEnum.name;
            }
        }
        return "";
    }
    public static Integer getValByName(String name) {
        for (ApprovalStatusEnum statusEnum : ApprovalStatusEnum.values()) {
            if (statusEnum.getName().equals(name)) {
                return statusEnum.getValue();
            }
        }
        return null;
    }

    public static ApprovalStatusEnum getApprovalStatusEnum(Integer value) {
        for (ApprovalStatusEnum statusEnum : ApprovalStatusEnum.values()) {
            if (statusEnum.value.equals(value)) {
                return statusEnum;
            }
        }
        return null;
    }
}
