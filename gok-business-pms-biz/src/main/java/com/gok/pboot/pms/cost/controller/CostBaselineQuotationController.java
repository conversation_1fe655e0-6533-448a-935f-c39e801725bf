//package com.gok.pboot.pms.cost.controller;
//
//
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.gok.pboot.pms.common.base.ApiResult;
//import com.gok.pboot.pms.common.base.PageRequest;
//import com.gok.pboot.pms.cost.entity.dto.ProjectBusinessInfoDTO;
//import com.gok.pboot.pms.cost.entity.dto.VersionAuditDTO;
//import com.gok.pboot.pms.cost.entity.vo.CostBaselineQuotationHistoryVersionVO;
//import com.gok.pboot.pms.cost.entity.vo.CostBaselineQuotationVO;
//import com.gok.pboot.pms.cost.service.ICostBaselineQuotationService;
//import lombok.RequiredArgsConstructor;
//import org.springframework.web.bind.annotation.*;
//
//import javax.validation.Valid;
//
///**
// * <AUTHOR>
// * @menu 报价与毛利测算
// */
//@RestController
//@RequiredArgsConstructor
//@RequestMapping("/costBaselineQuotation")
//public class CostBaselineQuotationController {
//
//    private final ICostBaselineQuotationService costBaselineQuotationService;
//
//    /**
//     * 同步商业论证B表报价与毛利测算归档信息
//     *
//     * @return {@link ApiResult}<{@link String}>
//     */
//    @PostMapping("/insertGrossProfitMeasurementAndVersionInfo")
//    public ApiResult<String> insertGrossProfitMeasurementAndVersionInfo() {
//        return costBaselineQuotationService.insertGrossProfitMeasurementAndVersionInfo() ?
//                ApiResult.successMsg("同步商业论证B表报价与毛利测算归档信息完成") : ApiResult.failure("同步商业论证B表报价与毛利测算归档信息失败");
//    }
//
//    /**
//     * 获取报价与毛利测算版本信息
//     *
//     * @param projectId 项目ID
//     * @param versionId 版本ID
//     * @return {@link CostBaselineQuotationVO}
//     */
//    @GetMapping("/getGrossProfitMeasurementVersionInfo")
//    public ApiResult<CostBaselineQuotationVO> getGrossProfitMeasurementVersionInfo(@RequestParam("projectId") Long projectId, @RequestParam(value = "versionId", defaultValue = "") Long versionId) {
//        return ApiResult.success(costBaselineQuotationService.getGrossProfitMeasurementVersionInfo(projectId, versionId));
//    }
//
//    /**
//     * 获取报价与毛利测算历史版本信息
//     *
//     * @param pageRequest 分页参数
//     * @param projectId   项目ID
//     * @return {@link Page}<{@link  CostBaselineQuotationHistoryVersionVO}>
//     */
//    @GetMapping("/getGrossProfitMeasurementHistoryVersionInfo")
//    public ApiResult<Page<CostBaselineQuotationHistoryVersionVO>> getGrossProfitMeasurementHistoryVersionInfo(PageRequest pageRequest, @RequestParam("projectId") Long projectId) {
//        return ApiResult.success(costBaselineQuotationService.getGrossProfitMeasurementHistoryVersionInfo(pageRequest, projectId));
//    }
//
//    /**
//     * 更新报价与毛利测算信息
//     *
//     * @param projectBusinessInfoDTO dto
//     * @return {@link String}
//     */
//    @PostMapping("insertGrossProfitMeasurementInfo")
//    public ApiResult<String> insertGrossProfitMeasurementInfo(@RequestBody ProjectBusinessInfoDTO projectBusinessInfoDTO) {
//        costBaselineQuotationService.insertGrossProfitMeasurementInfo(projectBusinessInfoDTO);
//        return ApiResult.successMsg("更新成功");
//    }
//
//    /**
//     * 更新当前版本审核状态
//     *
//     * @param versionAuditDTO dto
//     * @return {@link String}
//     */
//    @PutMapping("/updateAuditStatusInfo")
//    public ApiResult<String> updateAuditStatusInfo(@RequestBody @Valid VersionAuditDTO versionAuditDTO) {
//        costBaselineQuotationService.updateAuditStatusInfo(versionAuditDTO);
//        return ApiResult.successMsg("更新完成");
//    }
//}