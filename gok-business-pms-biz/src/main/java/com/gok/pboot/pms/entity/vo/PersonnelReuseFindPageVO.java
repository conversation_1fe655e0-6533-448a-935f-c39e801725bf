package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @Auther chenhc
 * @Date 2022-08-24 14:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PersonnelReuseFindPageVO {
    @ExcelIgnore
    private Long id;

    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    private String projectName;

    /**
     * 年级
     */
    @ExcelProperty("年级")
    private String grade;

    /**
     * 人员名称
     */
    @ExcelProperty("人员名称")
    private String userRealName;

    /**
     * 汇总工时（人天）
     */
    @ExcelProperty("汇总工时（人天）")
    private BigDecimal aggregatedDays;

    /**
     * 人才类型
     */
    @ExcelProperty("人才类型")
    private String personnelType;

    /**
     * 院校名称
     */
    @ExcelProperty("院校名称")
    private String schoolName;

    /**
     * 专业
     */
    @ExcelProperty("专业")
    private String major;

    /**
     * 手机号码
     */
    @ExcelProperty("手机号码")
    private String mobile;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;

    /**
     * 导入人
     */
    @ExcelProperty("导入人")
    private String executorUserRealName;

}
