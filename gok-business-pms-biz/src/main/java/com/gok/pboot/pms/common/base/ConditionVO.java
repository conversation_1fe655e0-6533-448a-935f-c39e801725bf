package com.gok.pboot.pms.common.base;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019-07-01 12:21
 * @desc 高级查询vo类
 **/
public class ConditionVO implements Serializable {

    private static final long serialVersionUID = 8723122526667298775L;

    /**
     * 条件连接类型 AND|OR
     */
    private String connectionType;

    /**
     * 字段名
     */
    private String fieldName;

    /**
     * 比较方式 如：等于、大于。。。
     */
    private String compare;

    /**
     * 字段值
     */
    private String fieldValue;

    public String getConnectionType() {
        return connectionType;
    }

    public void setConnectionType(String connectionType) {
        this.connectionType = connectionType;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getCompare() {
        return compare;
    }

    public void setCompare(String compare) {
        this.compare = compare;
    }

    public String getFieldValue() {
        return fieldValue;
    }

    public void setFieldValue(String fieldValue) {
        this.fieldValue = fieldValue;
    }
}
