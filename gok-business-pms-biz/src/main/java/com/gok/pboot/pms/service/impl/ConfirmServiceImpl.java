package com.gok.pboot.pms.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.BaseEntityUtils;
import com.gok.pboot.pms.Util.CommonUtils;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.entity.Confirm;
import com.gok.pboot.pms.entity.Privilege;
import com.gok.pboot.pms.entity.SysDept;
import com.gok.pboot.pms.entity.domain.Roster;
import com.gok.pboot.pms.entity.dto.ConfirmHourSumFindPageDTO;
import com.gok.pboot.pms.entity.vo.ConfirmHourSumFindPageVO;
import com.gok.pboot.pms.entity.vo.SysDeptOutVO;
import com.gok.pboot.pms.enumeration.ApiResultEnum;
import com.gok.pboot.pms.enumeration.EmployeeStatusEnum;
import com.gok.pboot.pms.enumeration.PersonnelStatusEnum;
import com.gok.pboot.pms.enumeration.PrivilegeTypeEnum;
import com.gok.pboot.pms.mapper.ConfirmMapper;
import com.gok.pboot.pms.mapper.DailyPaperEntryMapper;
import com.gok.pboot.pms.mapper.PrivilegeMapper;
import com.gok.pboot.pms.mapper.RosterMapper;
import com.gok.pboot.pms.service.IConfirmService;
import com.gok.pboot.pms.service.ICountQueryService;
import com.gok.pboot.pms.service.fegin.CenterDeptService;
import com.google.common.collect.ImmutableList;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 工时确认 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-30
 */
@Service
@Slf4j
@AllArgsConstructor
@Transactional(readOnly = true, rollbackFor = Exception.class)
public class ConfirmServiceImpl implements IConfirmService {

    private final ConfirmMapper mapper;

    private final DailyPaperEntryMapper dailyPaperEntryMapper;

    private final PrivilegeMapper privilegeMapper;

    private final ICountQueryService countQueryService;

    private final CenterDeptService centerDeptService;

    private final RosterMapper rosterMapper;

//    /**
//     * yyyy-MM
//     **/
//    private static final String MONTH_REGEX = "^([1-9]\\d{3}-)(([0][1-9])|([1][0-2]))$";
//    /**
//     * 定义每次调用的批量大小
//     **/
//    private static final int BATCH_SIZE = 100;
    /**
     * 定义mybatisPlus分页查询的最大limit
     **/
    private static final long PAGE_MAX_LIMIT = 2000L;

    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public ApiResult save(ConfirmHourSumFindPageDTO confirmHourSumFindPageDTO) {
        String selectMonth = confirmHourSumFindPageDTO.getSelectMonth();
        String[] split = selectMonth.split("-");
        String year = split[0];
        String month = split[1];

        //Calendar.getInstance()是获取一个Calendar对象并可以进行时间的计算，时区的指定
        Calendar now = Calendar.getInstance();
        int year1 = Integer.parseInt(year);
        int month1 = Integer.parseInt(month);
        if (year1 > now.get(Calendar.YEAR) ||
                (year1 == now.get(Calendar.YEAR) &&
                        month1 > (now.get(Calendar.MONTH) + 1))) {
            return ApiResult.failure("选择日期超过系统日期，本次操作不生效！");
        }

        Long userDeptId = SecurityUtils.getUser().getDeptId();
        Long deptId = centerDeptService.getTopDeptByIds(ImmutableList.of(userDeptId))
                .unpack(result -> log.error("获取一级部门失败，{}", result.getMsg()))
                .orElseThrow(() -> new ServiceException("获取一级部门ID为空"))
                .get(userDeptId)
                .getDeptId();
        log.info("工时确认的deptId:{}", deptId);
        List<Confirm> confirms = mapper.selectList(new QueryWrapper<Confirm>().eq("dept_id", deptId).eq("year", year)
                .eq("month", month));
        if (!confirms.isEmpty()) {
            return ApiResult.failure("当月工时状态为已确认，本次操作不生效！");
        }

        LocalDateTime startTime = LocalDateTime.of(year1, month1, 1, 0, 0);
//        int nextMonth = month1 + 1;
//        int nextYear = year1;
//        if (month1 == 12) {
//            nextMonth = 1;
//            nextYear = nextYear + 1;
//        }
//        LocalDateTime endTime = LocalDateTime.of(nextYear, nextMonth, 1, 0, 0);
        LocalDateTime endTime = startTime.plusMonths(1);

        Confirm confirm = new Confirm();
        confirm.setDeptId(deptId);
        confirm.setYear(Integer.valueOf(year));
        confirm.setMonth(Integer.valueOf(month));
        confirm.setConfirmStartDatetime(startTime);
        confirm.setConfirmEndDatetime(endTime);
        BaseBuildEntityUtil.buildInsert(confirm);

        ArrayList<Confirm> confirmadd = new ArrayList<>();
        confirmadd.add(confirm);

        mapper.batchSave(confirmadd);

        return ApiResult.success("操作成功");
    }


    @Override
    public Page<ConfirmHourSumFindPageVO> confirmHourSumFindPageVO(ConfirmHourSumFindPageDTO confirmHourSumFindPageDTO) {

        Page<ConfirmHourSumFindPageVO> page = new Page<>(confirmHourSumFindPageDTO.getPageNumber(), confirmHourSumFindPageDTO.getPageSize());
        page.setMaxLimit(PAGE_MAX_LIMIT);
        Page<ConfirmHourSumFindPageVO> confirmHourSumFindPageVOPage = dailyPaperEntryMapper.confirmHourSumFindPageVO2(
                page,
                confirmHourSumFindPageDTO
        );

        // 处理赋值字段【项目部门名称、人员部门名称】
        List<SysDeptOutVO> deptList = centerDeptService.getOrgStructList().unpack().orElse(ImmutableList.of());
        Map<Long, SysDept> sysDeptMap = deptList.stream()
                .map(SysDeptOutVO::toSysDept)
                .collect(Collectors.toMap(SysDept::getDeptId, x -> x));

        List<ConfirmHourSumFindPageVO> records = confirmHourSumFindPageVOPage.getRecords();

        if (records.isEmpty()) {
            return confirmHourSumFindPageVOPage;
        }

        List<Long> userIds = BaseEntityUtils.mapCollectionToList(records, ConfirmHourSumFindPageVO::getUserId);
        List<Long> projectIds = BaseEntityUtils.mapCollectionToList(records, ConfirmHourSumFindPageVO::getId);

        Map<Long, Roster> userIdMap = rosterMapper.findUserIdMap(userIds);

        //项目id集合获取审核人员和项目id的map
        List<Privilege> privileges = privilegeMapper.getPrivilegeByProjectIdAndPrivilegeType(
                projectIds, PrivilegeTypeEnum.PROJECT_AUDITOR.getValue()
        );
        Map<Long, String> prjUsermap =
                BaseEntityUtils.mapCollectionToMap(privileges, Privilege::getProjectId, Privilege::getUserName);

//        // 进行分批次远程调用，防止【全量导出时】传参过长导致远程调用失败
//        List<List<Long>> partitionedIds = new ArrayList<>();
//        List<SysUserOutVO> sysUserPmsCqVOS = new ArrayList<>();
//        for (int i = 0; i < userIds.size(); i += BATCH_SIZE) {
//            int end = Math.min(userIds.size(), i + BATCH_SIZE);
//            List<Long> partition = userIds.subList(i, end);
//            partitionedIds.add(partition);
//        }
//        for (List<Long> partition : partitionedIds) {
//            UserPmsDTO partialUserPmsPageDTO = new UserPmsDTO();
//            partialUserPmsPageDTO.setUserIds(partition);
//
//            List<SysUserOutVO> partialSysUserPmsCqVOS = centerUserService.getUserListByMultiParameterPms(partialUserPmsPageDTO).getData();
//            sysUserPmsCqVOS.addAll(partialSysUserPmsCqVOS);
//        }
//
//        Map<Long, String> sysUserPmsCqVOSMap = BaseEntityUtils.mapCollectionToMap(
//                sysUserPmsCqVOS, SysUserOutVO::getUserId, u -> {
//                    Long deptId = null;
//                    Map<Long, List<com.gok.bcp.upms.vo.SysDeptOutVO>> deptMap = u.getDeptMap();
//                    if (MapUtils.isNotEmpty(deptMap)) {
//                        deptId = deptMap.keySet().iterator().next();
//                    }
//                    SysDept dept = sysDeptMap.get(deptId);
//
//                    return dept == null ? "" : dept.getName();
//                },
//                (a, b) -> a
//        );


        confirmHourSumFindPageVOPage.setRecords(records.stream().map(x -> {
            Long userId = x.getUserId();
            Roster roster = userIdMap.get(userId);
            Integer employeeStatus;
            String personnelStatusName = x.getPersonnelStatusName();

            x.setProjectDeptName(countQueryService.getResultUnitName(x.getProjectDeptId(), sysDeptMap, null));
            // 人员归属部门
            if (userId != null || (PersonnelStatusEnum.WB.getName().equals(x.getPersonnelStatusName()) && x.getDeptId() != null)) {
                x.setPersonnelDeptName(sysDeptMap.get(x.getDeptId()) == null ? "" : sysDeptMap.get(x.getDeptId()).getName());
            }
            if (roster != null) {
                employeeStatus = roster.getEmployeeStatus();
                if (
                        employeeStatus != null &&
                                !PersonnelStatusEnum.WB.getName().equals(personnelStatusName) &&
                                !PersonnelStatusEnum.XMSX.getName().equals(personnelStatusName)
                ) {
                    x.setPersonnelStatusName(PersonnelStatusEnum.getByEmployeeStatusEnum(
                            EnumUtils.getEnumByValue(EmployeeStatusEnum.class, employeeStatus)
                    ).getName());
                }
            }
            BigDecimal hourData = x.getHourData();
            if (hourData == null) {
                x.setHourData(BigDecimal.ZERO);
            } else {
                x.setHourData(CommonUtils.unitConversion(x.getHourData()));
            }
            x.setAddedHours(CommonUtils.unitConversion(x.getAddedHours()));
            x.setNormalHours(CommonUtils.unitConversion(x.getNormalHours()));
            x.setProjectHours(CommonUtils.unitConversion(x.getProjectHours()));
            if (null == x.getPrivilegeUserName()) {
                String s = prjUsermap.get(x.getId());
                if (s != null) {
                    x.setPrivilegeUserName(s);
                    return x;
                }
                if (x.getManagerUserName() != null) {
                    x.setPrivilegeUserName(x.getManagerUserName());
                    return x;
                }
                x.setPrivilegeUserName(x.getSalesmanUserName());
                return x;
            }

            return x;
        }).collect(Collectors.toList()));

        return confirmHourSumFindPageVOPage;
    }

    @Override
    public Boolean findConfirm(ConfirmHourSumFindPageDTO confirmHourSumFindPageDTO) {
        String selectMonth = confirmHourSumFindPageDTO.getSelectMonth();
        String[] split = selectMonth.split("-");
        String year = split[0];
        String month = split[1];
//        if (split[1].toCharArray()[0] == 0) {
//            month = String.valueOf(split[1].toCharArray()[0]);
//        }

        Long userDeptId = SecurityUtils.getUser().getDeptId();
        if (userDeptId == null) {
            return false;
        }
        Map<Long, SysDeptOutVO> topDeptRes = centerDeptService.getTopDeptByIds(
                        ImmutableList.of(userDeptId))
                .unpack()
                .orElseThrow(() -> new ServiceException("没有找到顶级部门ID: " + userDeptId));
        List<Confirm> confirms = mapper.selectList(new QueryWrapper<Confirm>()
                .eq("dept_id", topDeptRes.get(userDeptId).getDeptId())
                .eq("year", Integer.parseInt(year))
                .eq("month", Integer.parseInt(month))
        );

        return !confirms.isEmpty();
    }

    @Override
    public void confirmHourSumExport(HttpServletResponse response, ConfirmHourSumFindPageDTO confirmHourSumFindPageDTO) {
        confirmHourSumFindPageDTO.setPageNumber(1);
        confirmHourSumFindPageDTO.setPageSize(Integer.MAX_VALUE);
        List<ConfirmHourSumFindPageVO> data = confirmHourSumFindPageVO(confirmHourSumFindPageDTO).getRecords();

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(confirmHourSumFindPageDTO.getSelectMonth() + "项目工时汇总导出", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), ConfirmHourSumFindPageVO.class)
                    .autoCloseStream(Boolean.FALSE)
                    .sheet("数据").doWrite(data);
        } catch (Exception e) {
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            ApiResult<String> result = ApiResult.failureMsg("导出失败", ApiResultEnum.FAILURE);
            try {
                response.getWriter().println(JSONUtil.toJsonStr(result));
            } catch (IOException ioException) {
                ioException.printStackTrace();
            }
        }
    }


}
