package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.base.admin.dto.PaymentDTO;
import com.gok.base.admin.dto.ProjectPaymentClaimDTO;
import com.gok.base.admin.dto.ProjectPaymentDTO;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.entity.domain.ProjectPayment;
import com.gok.pboot.pms.entity.vo.ProjectPaymentClaimVO;
import com.gok.pboot.pms.entity.vo.ProjectPaymentVO;

import java.util.List;

/**
 * 项目回款跟踪 Service
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
public interface IProjectPaymentService extends IService<ProjectPayment> {

    /**
     * 模糊查询带分页
     *
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link Page}<{@link ProjectPaymentVO}>
     */
    Page<ProjectPaymentVO> findPage(ProjectPaymentDTO projectPaymentDTO);

    /**
     * 锁定
     *
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link Boolean}
     */
    Boolean lock(ProjectPaymentDTO projectPaymentDTO);

    /**
     * 导出Excel选中数据
     *
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link List}
     */
    List export(ProjectPaymentDTO projectPaymentDTO);

    /**
     * 插入数据
     *
     * @param paymentDTO {@link PaymentDTO}
     * @return {@code int}
     */
    int save(PaymentDTO paymentDTO);

    /**
     * 根据id获取详情
     *
     * @param id 项目id
     * @return {@link ProjectPaymentClaimVO}
     */
    ProjectPaymentClaimVO getOne(Long id);

    /**
     * 根据id更新数据
     *
     * @param projectPaymentClaimDTO {@link ProjectPaymentClaimDTO}
     * @return {@code Boolean}
     */
    Boolean update(ProjectPaymentClaimDTO projectPaymentClaimDTO);

    /**
     * 删除
     *
     * @param ids {@link List}
     * @return {@link R}
     */
    com.gok.components.common.util.R<String> delete(List<Long> ids);

    /**
     * 认领操作(已认领 -> 待认领)
     *
     * @param id 项目回款跟踪id
     * @return {@link Boolean}
     */
    Boolean claim(Long id);

    /**
     * 认领操作(待认领 -> 已认领)
     *
     * @param projectPaymentClaimDTO {@link ProjectPaymentClaimDTO}
     * @return {@link Boolean}
     */
    Boolean claim(ProjectPaymentClaimDTO projectPaymentClaimDTO);

    /**
     * 待认领且收款日期超过三十天
     *
     * @return {@link R}
     */
    com.gok.components.common.util.R<Void> updateLockStatus();
}
