package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.entity.bo.DailyReviewReuseAndDeliveryProjectBO;
import lombok.*;

/**
 * 日报审核 人才复用+交付 项目维度
 *
 * <AUTHOR>
 * @version 1.3.2
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class DailyReviewReuseAndDeliveryProjectVO {

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 审核人数
     */
    private Integer approvalNum;

    /**
     * 汇总工时（人天）
     */
    private Double aggregatedDays;

    public static DailyReviewReuseAndDeliveryProjectVO from(DailyReviewReuseAndDeliveryProjectBO bo) {
        DailyReviewReuseAndDeliveryProjectVO result = new DailyReviewReuseAndDeliveryProjectVO();

        result.setProjectId(String.valueOf(bo.getProjectId()));
        result.setProjectName(bo.getProjectName());
        result.setApprovalNum(Math.max(bo.getApprovalNum(), 0));
        result.setAggregatedDays(Math.max(bo.getAggregatedDays(), 0));

        return result;
    }
}
