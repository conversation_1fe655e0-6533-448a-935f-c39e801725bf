package com.gok.pboot.pms.eval.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.eval.entity.domain.EvalCustomerSatisfactionSurvey;
import com.gok.pboot.pms.eval.entity.dto.EvalCustomerSatisfactionSurveyDTO;
import com.gok.pboot.pms.eval.entity.vo.EvalCustomerSatisfactionSurveyVO;


/**
 * 客户满意度调查Service接口
 *
 * <AUTHOR>
 * @date 2025/05/08
 */
public interface IEvalCustomerSatisfactionSurveyService extends IService<EvalCustomerSatisfactionSurvey> {

    /**
     * 根据项目ID获取满意度调查数据
     *
     * @param projectId 项目ID
     * @return 满意度调查数据
     */
    EvalCustomerSatisfactionSurveyVO getSatisfactionSurveyByProjectId(Long projectId);

    /**
     * 更新满意度调查
     *
     * @param dto 满意度调查DTO
     */
    void updateSatisfactionSurvey(EvalCustomerSatisfactionSurveyDTO dto);

    /**
     * 处理超期未评价的满意度调查
     * 将超过10个工作日未评价的调查设置为默认满意
     */
    void handleOverdueSurveyList();

    /**
     * 满意度调查消息推送
     */
    void satisfactionSurveyMsgPush();
} 