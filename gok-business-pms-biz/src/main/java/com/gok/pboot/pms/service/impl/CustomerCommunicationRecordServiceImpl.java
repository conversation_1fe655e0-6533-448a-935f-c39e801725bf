package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.Util.BcpLoggerUtils;
import com.gok.pboot.pms.Util.PmsDictUtil;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.constant.FunctionConstants;
import com.gok.pboot.pms.entity.domain.CustomerCommunicationRecord;
import com.gok.pboot.pms.entity.domain.ProjectFile;
import com.gok.pboot.pms.entity.dto.CustomerCommunicationRecordDTO;
import com.gok.pboot.pms.entity.vo.CCRExportExcelVO;
import com.gok.pboot.pms.entity.vo.CustomerCommunicationRecordVO;
import com.gok.pboot.pms.entity.vo.OaFileVo;
import com.gok.pboot.pms.enumeration.LogContentEnum;
import com.gok.pboot.pms.mapper.CustomerCommunicationRecordMapper;
import com.gok.pboot.pms.mapper.ProjectFileMapper;
import com.gok.pboot.pms.oa.OaUtil;
import com.gok.pboot.pms.service.CustomerCommunicationRecordService;
import com.google.common.collect.ImmutableList;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 客户沟通记录
 *
 * <AUTHOR>
 * @date 2023/11/20
 */
@Service
@Slf4j
@AllArgsConstructor
public class CustomerCommunicationRecordServiceImpl implements CustomerCommunicationRecordService {

    private final CustomerCommunicationRecordMapper customerCommunicationRecordMapper;
    private final ProjectFileMapper projectFileMapper;

    private final OaUtil oaUtil;
    private final PmsDictUtil pmsDictUtil;
    private final BcpLoggerUtils bcpLoggerUtils;

    @Override
    public Page<CustomerCommunicationRecordVO> findPage(PageRequest pageRequest, CustomerCommunicationRecordDTO filter) {
        // 获取分页信息
        Page<CustomerCommunicationRecordVO> page = customerCommunicationRecordMapper.findPage(
                Page.of(pageRequest.getPageNumber(), pageRequest.getPageSize()),
                filter
        );
        // 对于枚举字段进行重新赋值
        List<CustomerCommunicationRecordVO> records = page.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            updateCustomerCommunicationRecordVoParam(records);
        }

        return page;
    }

    private void updateCustomerCommunicationRecordVoParam(List<CustomerCommunicationRecordVO> records) {
        // 更新枚举字段
        records.forEach(r -> CustomerCommunicationRecordVO.updateResultParam(pmsDictUtil.getPmsDictItemMap(),r));
    }

    @Override
    public CustomerCommunicationRecordVO findById(Long id) {
        CustomerCommunicationRecord customerCommunicationRecord = customerCommunicationRecordMapper.selectById(id);
        if (Optional.ofNullable(customerCommunicationRecord).isPresent()) {
            CustomerCommunicationRecordVO customerCommunicationRecordVO = BeanUtil.copyProperties(customerCommunicationRecord, CustomerCommunicationRecordVO.class);
            // 对于枚举字段进行重新赋值
            CustomerCommunicationRecordVO.updateResultParam(pmsDictUtil.getPmsDictItemMap(), customerCommunicationRecordVO);

            ProjectFile projectFile = new ProjectFile();
            projectFile.setProjectId(id);
            projectFile.setAssociationType(2);
            List<ProjectFile> projectFiles = projectFileMapper.selectListByParams(projectFile);
            if (CollUtil.isNotEmpty(projectFiles)) {
                customerCommunicationRecordVO.setRelateAttachments(projectFiles);
            }
            return customerCommunicationRecordVO;
        }
        return null;
    }

    @Override
    public List<CCRExportExcelVO> export(PageRequest pageRequest, CustomerCommunicationRecordDTO filter) {
        Page<CustomerCommunicationRecordVO> page = customerCommunicationRecordMapper.findPage(Page.of(pageRequest.getPageNumber(), pageRequest.getPageSize()), filter);
        if (Optional.ofNullable(page).isPresent()) {
            List<CustomerCommunicationRecordVO> records = page.getRecords();
            if (CollUtil.isNotEmpty(records)) {
                // 对于枚举字段进行重新赋值
                updateCustomerCommunicationRecordVoParam(records);
                //导出【{}】条客户沟通记录
                bcpLoggerUtils.log(FunctionConstants.CUSTOMER_COMMUNICATION_RECORDS, LogContentEnum.EXPORT_CUSTOMER_COMMUNICATION_RECORDS,
                        records.size());
                return BeanUtil.copyToList(records, CCRExportExcelVO.class);
            }
        }

        return new ArrayList<>();
    }

    @Override
    public ApiResult<Object> getFile(Long requestId) {
        // 获取流程对应的相关人id
        CustomerCommunicationRecordVO vo =
                customerCommunicationRecordMapper.findCustomerCommunicationRecordByRequestId(requestId);
        if(vo == null || vo.getNodeoperator() == null){
            return ApiResult.success(ImmutableList.of(new OaFileVo()));
        }
        Long userId = vo.getNodeoperator();

        // 请求OA，获取流程相关资源
        List<OaFileVo> resourcesData = oaUtil.getResourcesData(String.valueOf(requestId), String.valueOf(userId));
        return ApiResult.success(resourcesData);
    }
}
