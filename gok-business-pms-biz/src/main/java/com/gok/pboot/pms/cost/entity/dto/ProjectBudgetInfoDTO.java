package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectBudgetInfoDTO {

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * OA_ID
     */
    private Long oaId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 流程ID
     */
    private Long requestId;

    /**
     * 流程名称
     */
    private String requestName;

    /**
     * 成本科目类别ID
     */
    private Long accountCategoryId;

    /**
     * 成本科目类别名称
     */
    private String accountCategoryName;

    /**
     * 成本科目ID
     */
    private Long accountId;

    /**
     * 科目名称
     */
    private String accountName;

    /**
     * 科目代码
     */
    private String accountCode;

    /**
     * 预算金额(含税)
     */
    private BigDecimal budgetAmountIncludedTax;

    /**
     * 税率OA字典ID
     */
    private Integer taxRate;

    /**
     * 预算金额(不含税)
     */
    private BigDecimal budgetAmountExcludingTax;

    /**
     * 备注说明
     */
    private String remark;

}