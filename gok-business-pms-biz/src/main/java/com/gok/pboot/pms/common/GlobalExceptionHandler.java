package com.gok.pboot.pms.common;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.exception.ExcelCommonException;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.fasterxml.jackson.databind.DatabindException;
import com.fasterxml.jackson.databind.exc.MismatchedInputException;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.enumeration.ApiResultEnum;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.TypeMismatchException;
import org.springframework.boot.json.JsonParseException;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageConversionException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MissingRequestValueException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.HandlerMethod;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.ValidationException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.StringJoiner;

/**
 * 全局异常捕获
 *
 * <AUTHOR> Xinyp
 */
@Slf4j
@RestControllerAdvice(basePackages = "com.gok.pboot.pms")
public class GlobalExceptionHandler {
    private static final Logger LOG = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler(value = {ServiceException.class, BusinessException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<Void> handle(ServiceException exception) {
        return ApiResult.failure(exception.getMessage());
    }

    @ExceptionHandler(ValidationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<String> handle(ValidationException exception, HandlerMethod handlerMethod) {
        if (exception instanceof ConstraintViolationException) {
            ConstraintViolationException exs = (ConstraintViolationException) exception;
            Set<ConstraintViolation<?>> violations = exs.getConstraintViolations();
            List<String> errorMsgList = new ArrayList<>();
            int i = 1;
            for (ConstraintViolation<?> item : violations) {
                LOG.error("请求参数错误 controller出错，类名：{} 方法名：{} 错误详情：{}", handlerMethod.getMethod().getDeclaringClass().getName(),
                        handlerMethod.getMethod().getName(), item.getMessage());
                errorMsgList.add(getErrorMsg(i++, item.getMessage()));
            }
            final String join = "以下" + errorMsgList.size() + "条数据有误:[" + CollUtil.join(errorMsgList, "<br/>") + "]";
            return ApiResult.failureMsg(join, ApiResultEnum.VALIDATION_FAILURE);
        }

        return ApiResult.failureMsg(exception.getMessage(), ApiResultEnum.VALIDATION_FAILURE);
    }

    private static String getErrorMsg(int row, String msg) {
        return "第" + row + "行，" + msg;
    }

    @ExceptionHandler(ExcelDataConvertException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<String> handle(ExcelDataConvertException exception) {
        return ApiResult.failureMsg(
                "导入Excel出错，行[" + exception.getRowIndex() + "]，列[" + exception.getColumnIndex() + "]",
                ApiResultEnum.VALIDATION_FAILURE
        );
    }

    @ExceptionHandler(ExcelCommonException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<String> handle(ExcelCommonException e) {
        log.warn("导入Excel文件解析失败", e);

        return ApiResult.failureMsg("上传的Excel文件无效，请检查", ApiResultEnum.VALIDATION_FAILURE);
    }

    @ExceptionHandler({
            JsonParseException.class,
            DatabindException.class,
            TypeMismatchException.class,
            MismatchedInputException.class,
            HttpMessageConversionException.class,
            MissingRequestValueException.class
    })
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<String> handleValidationError(Exception e) {
        log.warn("参数解析异常", e);
        return ApiResult.failureMsg("参数解析失败，请检查你的传参", ApiResultEnum.VALIDATION_FAILURE);
    }

    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<String> handle(BindException e) {
        StringJoiner errorMsg = new StringJoiner(",");

        e.getBindingResult().getAllErrors().forEach(error -> errorMsg.add(error.getDefaultMessage()));

        return ApiResult.failureMsg("参数校验不通过：" + errorMsg, ApiResultEnum.VALIDATION_FAILURE);
    }

    @ExceptionHandler(SQLException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResult<Void> handle(SQLException e) {
        log.info("SQLException: ", e);

        return ApiResult.failure("服务数据访问异常，请反馈相关人员，谢谢");
    }

    @ExceptionHandler(AccessDeniedException.class)
    public ApiResult<String> handle(AccessDeniedException e) {
        log.warn("AccessDenied.", e);

        return ApiResult.failureMsg("权限不足，拒绝访问", ApiResultEnum.UNAUTHORIZED);
    }

    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResult<Void> handle(Exception e) {
        log.info("Unknown exception", e);

        return ApiResult.failure("抱歉，系统未知异常，请反馈给相关人员，谢谢");
    }
}
