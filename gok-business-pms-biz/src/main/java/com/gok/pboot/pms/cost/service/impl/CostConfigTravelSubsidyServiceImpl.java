package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.cost.entity.domain.CostConfigTravelSubsidy;
import com.gok.pboot.pms.cost.entity.domain.CostConfigVersion;
import com.gok.pboot.pms.cost.entity.dto.CostConfigTravelSubsidyDTO;
import com.gok.pboot.pms.cost.entity.vo.CostConfigTravelSubsidyVO;
import com.gok.pboot.pms.cost.enums.AwayDayEnum;
import com.gok.pboot.pms.cost.enums.CostConfigVersionTypeEnum;
import com.gok.pboot.pms.cost.enums.PersonNumEnum;
import com.gok.pboot.pms.cost.mapper.CostConfigTravelSubsidyMapper;
import com.gok.pboot.pms.cost.service.ICostConfigTravelSubsidyService;
import com.gok.pboot.pms.cost.service.ICostConfigVersionService;
import com.gok.pboot.pms.enumeration.YesOrNoEnum;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 差旅补贴标准配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Service
@AllArgsConstructor
public class CostConfigTravelSubsidyServiceImpl extends ServiceImpl<CostConfigTravelSubsidyMapper, CostConfigTravelSubsidy> implements ICostConfigTravelSubsidyService {

    private final ICostConfigVersionService costConfigVersionService;
    /**
     * 获取最新版本差旅住宿标准配置
     *
     * @return {@link List }<{@link CostConfigTravelSubsidyVO }>
     */
    @Override
    public List<CostConfigTravelSubsidyVO> getCostConfigTravelSubsidyList() {
        // 获取当前最大版本号
        CostConfigVersion crrMaxVersion = costConfigVersionService.getCrrCostConfigVersion(CostConfigVersionTypeEnum.CLBZBZ);
        // 获取最大版本号下的差旅住宿标准配置数据
        List<CostConfigTravelSubsidyVO> configTravelSubsidyVoList = Objects.nonNull(crrMaxVersion)
                ? baseMapper.getTravelSubsidiesByVersionId(crrMaxVersion.getId())
                : Collections.emptyList();
        return handleDataToList(configTravelSubsidyVoList);
    }


    /**
     * 编辑 差旅补贴标准配置列表
     *
     * @param dtoList DTO 列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editCostConfigTravelSubsidyList(List<CostConfigTravelSubsidyDTO> dtoList) {
        if (CollUtil.isEmpty(dtoList)){
            return;
        }
        // 获取当前最大版本号
        Long versionId = costConfigVersionService.generateVersionName(CostConfigVersionTypeEnum.CLBZBZ);
        List<CostConfigTravelSubsidy> configTravelSubsidyList = dtoList.stream()
                .map(item -> BaseBuildEntityUtil.buildInsert(new CostConfigTravelSubsidy()
                        .setVersionId(versionId)
                        .setPersonNum(item.getPersonNum())
                        .setAwayDay(item.getAwayDay())
                        .setStayOwn(item.getStayOwn())
                        .setSubsidyPrice(item.getSubsidyPrice())))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(configTravelSubsidyList)){
            this.saveBatch(configTravelSubsidyList);
        }
    }

    /**
     * 根据版本id获取差旅补贴标准配置
     *
     * @param versionId 版本 ID
     * @return {@link List }<{@link CostConfigTravelSubsidyVO }>
     */
    @Override
    public List<CostConfigTravelSubsidyVO> getCostConfigTravelSubsidyListByVersionId(Long versionId) {
        List<CostConfigTravelSubsidyVO> configTravelSubsidyVoList = baseMapper.getTravelSubsidiesByVersionId(versionId);
        if (CollUtil.isEmpty(configTravelSubsidyVoList)){
            return Collections.emptyList();
        }
        return handleDataToList(configTravelSubsidyVoList);
    }

    /**
     * 处理要列出数据
     *
     * @param configTravelSubsidyVoList 配置 差旅补贴 VO 列表
     * @return {@link List }<{@link CostConfigTravelSubsidyVO }>
     */
    private static List<CostConfigTravelSubsidyVO> handleDataToList(List<CostConfigTravelSubsidyVO> configTravelSubsidyVoList) {
        if (CollUtil.isEmpty(configTravelSubsidyVoList)){
            return Collections.emptyList();
        }
        return configTravelSubsidyVoList.stream()
                .map(item -> item.setPersonNumStr(EnumUtils.getNameByValue(PersonNumEnum.class, item.getPersonNum()))
                        .setAwayDayStr(EnumUtils.getNameByValue(AwayDayEnum.class, item.getAwayDay()))
                        .setStayOwnStr(EnumUtils.getNameByValue(YesOrNoEnum.class, item.getStayOwn())))
                .collect(Collectors.toList());

    }
}
