package com.gok.pboot.pms.eval.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.eval.entity.domain.EvalCustomerSatisfactionSurvey;
import com.gok.pboot.pms.eval.enums.EvalSatisfactionResultEnum;
import com.gok.pboot.pms.eval.enums.EvalSatisfactionSurveyStatusEnum;
import com.gok.pboot.pms.eval.enums.EvalSendStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 客户满意度调查VO类
 *
 * <AUTHOR>
 * @date 2025/05/08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class EvalCustomerSatisfactionSurveyVO {

    /**
     * ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 干系人ID
     */
    private Long stakeholderId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目经理
     */
    private String projectManager;

    /**
     * 客户经理
     */
    private String customerManager;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 联系方式
     */
    private String contactPhone;

    /**
     * 发送状态（0=未发送，1=已发送，2=发送失败）
     * @see EvalSendStatusEnum
     */
    private Integer sendStatus;

    /**
     * 发送状态名称
     */
    private String sendStatusName;

    /**
     * 调查评价日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate surveyDate;

    /**
     * 调查下发日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate releaseDate;

    /**
     * 得分（取平均分）
     */
    private BigDecimal totalScore;

    /**
     * 评价状态（0=未评价,1=已评价,2=超期未评价）
     * @see EvalSatisfactionSurveyStatusEnum
     */
    private Integer evalStatus;
    
    /**
     * 评价状态名称
     */
    private String evalStatusName;

    /**
     * 问题响应结果
     */
    private Integer problemResponseResult;
    
    /**
     * 问题响应结果名称
     */
    private String problemResponseResultName;

    /**
     * 方案设计结果
     */
    private Integer designSchemeResult;
    
    /**
     * 方案设计结果名称
     */
    private String designSchemeResultName;

    /**
     * 进度控制结果
     */
    private Integer progressControlResult;
    
    /**
     * 进度控制结果名称
     */
    private String progressControlResultName;

    /**
     * 质量控制结果
     */
    private Integer qualityControlResult;
    
    /**
     * 质量控制结果名称
     */
    private String qualityControlResultName;

    /**
     * 服务态度与沟通结果
     */
    private Integer serviceAttitudeCommunicationResult;
    
    /**
     * 服务态度与沟通结果名称
     */
    private String serviceAttitudeCommunicationResultName;

    /**
     * 其他建议
     */
    private String otherSuggestion;

    public static EvalCustomerSatisfactionSurveyVO convertToVo(EvalCustomerSatisfactionSurvey entity, ProjectInfo projectInfo) {
        return new EvalCustomerSatisfactionSurveyVO()
                .setId(entity.getId())
                .setProjectId(entity.getProjectId())
                .setStakeholderId(entity.getStakeholderId())
                .setProjectName(projectInfo.getItemName())
                .setProjectManager(projectInfo.getManagerUserName())
                .setCustomerManager(projectInfo.getProjectSalesperson())
                .setCustomerName(entity.getCustomerName())
                .setContactPhone(entity.getContactPhone())
                .setSendStatus(entity.getSendStatus())
                .setSendStatusName(EnumUtils.getNameByValue(EvalSendStatusEnum.class, entity.getSendStatus()))
                .setSurveyDate(entity.getSurveyDate())
                .setReleaseDate(entity.getReleaseDate())
                .setTotalScore(entity.getTotalScore())
                .setEvalStatus(entity.getEvalStatus())
                .setEvalStatusName(EnumUtils.getNameByValue(EvalSatisfactionSurveyStatusEnum.class, entity.getEvalStatus()))
                .setProblemResponseResult(entity.getProblemResponseResult())
                .setProblemResponseResultName(EnumUtils.getNameByValue(EvalSatisfactionResultEnum.class, entity.getProblemResponseResult()))
                .setDesignSchemeResult(entity.getDesignSchemeResult())
                .setDesignSchemeResultName(EnumUtils.getNameByValue(EvalSatisfactionResultEnum.class, entity.getDesignSchemeResult()))
                .setProgressControlResult(entity.getProgressControlResult())
                .setProgressControlResultName(EnumUtils.getNameByValue(EvalSatisfactionResultEnum.class, entity.getProgressControlResult()))
                .setQualityControlResult(entity.getQualityControlResult())
                .setQualityControlResultName(EnumUtils.getNameByValue(EvalSatisfactionResultEnum.class, entity.getQualityControlResult()))
                .setServiceAttitudeCommunicationResult(entity.getServiceAttitudeCommunicationResult())
                .setServiceAttitudeCommunicationResultName(EnumUtils.getNameByValue(EvalSatisfactionResultEnum.class, entity.getServiceAttitudeCommunicationResult()))
                .setOtherSuggestion(entity.getOtherSuggestion());
    }

    /**
     * 获取空对象
     *
     * @param projectInfo 项目信息
     * @return
     */
    public static EvalCustomerSatisfactionSurveyVO emptyVo(ProjectInfo projectInfo) {
        return new EvalCustomerSatisfactionSurveyVO()
                .setProjectId(projectInfo.getId())
                .setProjectName(projectInfo.getItemName())
                .setProjectManager(projectInfo.getManagerUserName())
                .setCustomerManager(projectInfo.getProjectSalesperson());
    }
} 