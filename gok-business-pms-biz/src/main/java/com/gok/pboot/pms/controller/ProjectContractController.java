package com.gok.pboot.pms.controller;

import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.BaseController;
import com.gok.pboot.pms.entity.dto.ContractLedgerListDTO;
import com.gok.pboot.pms.entity.dto.ContractLedgerSelectListDTO;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.enumeration.OaFileDownLoadTypeEnum;
import com.gok.pboot.pms.service.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @menu 项目合同
 * @since 2022-08-19
 */
@Slf4j
@RestController
@RequestMapping("/project/contract")
@AllArgsConstructor
public class ProjectContractController extends BaseController {

    IContractLedgerService contractLedgerService;
    IContractLedgerDetailService contractLedgerDetailService;
    IProjectContractAmendmentService projectContractAmendmentService;
    IProjectContractInvoiceService projectContractInvoiceService;
    ISalesReceiptCollectionRecordsService salesReceiptCollectionRecordsService;
    IContractMilestoneService contractMilestoneService;


    /**
     * 根据合同id查询合同基本信息
     *
     * @param id 合同id
     * @return 合同基本信息
     */
    @GetMapping("/baseInfo/{id}")
    public ApiResult<ContractBaseInfoVo> getContractBaseInfoVoList(@PathVariable("id") Long id) {
        return contractLedgerService.getContractBaseInfoVoList(id);
    }

    /**
     * 根据合同id查询合同变更记录
     *
     * @param id 合同id
     * @return 变更记录
     */
    @GetMapping("/change/{id}")
    public ApiResult<List<ContractChangeInfoVo>> getContractChangeInfoVoList(@PathVariable("id") Long id) {
        return success(projectContractAmendmentService.getContractChangeInfoVoList(id));
    }

    /**
     * 根据合同id查询合同款项记录
     *
     * @param id 合同id
     * @return 款项记录
     */
    @GetMapping("/payment/{id}")
    public ApiResult<List<ContractMilestoneVO>> getContractPaymentInfoVoList(@PathVariable("id") Long id) {
        return success(contractMilestoneService.getContractPaymentById(id));
    }

    /**
     * 根据合同id查询合同催收记录
     *
     * @param id 合同id
     * @return 发票记录
     */
    @GetMapping("/collection/{id}")
    public ApiResult<List<SalesReceiptCollectionRecordsVO>> getContractCollectionVoList(@PathVariable("id") Long id) {
        return success(salesReceiptCollectionRecordsService.getContractCollectionVoList(id));
    }

    /**
     * 根据合同id查询合同发票记录
     *
     * @param id 合同id
     * @return 催收记录
     */
    @GetMapping("/invoice/{id}")
    public ApiResult<List<ContractInvoiceVo>> getContractInvoiceVoList(@PathVariable("id") Long id) {
        return success(projectContractInvoiceService.getContractInvoiceVoList(id));
    }

    /**
     * 合同台账列表
     *
     * @return 合同基本信息
     */
    @PostMapping("/getList")
    @PreAuthorize("@pms.hasPermission('CONTRACT_ACCOUNT')")
    public ApiResult<Page<ContractLedgerListVo>> getContractLedgerVoList(@RequestBody ContractLedgerListDTO dto) {
        return success(contractLedgerService.getContractLedgerVoList(dto));
    }


    /**
     * 导出合同台账列表
     *
     * @return 合同基本信息
     */
    @PostMapping("/export")
    @PreAuthorize("@pms.hasPermission('CONTRACT_ACCOUNT')")
    @ResponseExcel(name = "合同台账列表", dynamicHeader = true)
    public List<ContractLedgerListVo> exportContractLedgerVoList(@RequestBody ContractLedgerListDTO dto) {
        return contractLedgerService.getContractLedgerVoList(dto).getRecords();
    }

    /**
     * 合同台账查询字典值
     *
     * @return
     */
    @GetMapping("/projectDict/{ids}")
    public ApiResult<Map<Integer, Map<Integer, String>>> getProjectDictList(@PathVariable("ids") String ids) {
        String[] arr = ids.split(StringPool.COMMA);
        Set<Integer> fieldIds = Arrays.stream(arr).map(Integer::parseInt).collect(Collectors.toSet());
        return success(contractLedgerService.getProjectDictMap(fieldIds));
    }

    /**
     * 合同台账列表汇总数据
     *
     * @return 合同基本信息
     */
    @PostMapping("/getList/summary")
    @PreAuthorize("@pms.hasPermission('CONTRACT_ACCOUNT')")
    public ApiResult<ContractLedgerSummaryStrVo> getContractLedgerSummary(@RequestBody ContractLedgerListDTO dto) {
        return success(contractLedgerService.getContractLedgerSummary(dto));
    }

    /**
     * 根据合同id查询合同头部信息
     *
     * @param id 合同id
     * @return 合同基本信息
     */
    @GetMapping("/baseInfo/head/{id}")
    @PreAuthorize("@pms.hasPermission('CONTRACT_ACCOUNT')")
    public ApiResult<ContractBaseHeadInfoVo> getContractBaseHeadInfoVo(@PathVariable("id") Long id) {
        return contractLedgerService.getContractBaseHeadInfoVo(id);
    }

    /**
     * 根据合同id查询合同概览信息
     *
     * @param id 合同id
     * @return 合同基本信息
     */
    @GetMapping("/baseInfo/overview/{id}")
    public ApiResult<ContractOverviewInfoVo> getContractOverviewInfoVo(@PathVariable("id") Long id) {
        return success(contractLedgerService.getContractOverviewInfoVo(id));
    }

    /**
     * 根据合同id查询合同流程信息
     *
     * @param id 合同id
     * @return 合同基本信息
     */
    @GetMapping("/baseInfo/process/{id}")
    public ApiResult<List<ContractProcessListVO>> getContractProcessInfoVo(@PathVariable("id") Long id) {
        return success(contractLedgerService.getContractProcessInfoVo(id));
    }

    /**
     * 根据合同id查询合同验收记录
     *
     * @param id 合同id
     * @return 催收记录
     */
    @GetMapping("/acceptance/{id}")
    public ApiResult<List<ContractAcceptanceVo>> getContractAcceptanceVoList(@PathVariable("id") Long id) {
        return success(contractLedgerService.getContractAcceptanceVoList(id));
    }

    /**
     * 根据合同id查询合同风险信息
     *
     * @param id 合同id
     * @return 合同基本信息
     */
    @GetMapping("/risk/{id}")
    public ApiResult<List<ContractRiskListVO>> getContractRiskInfoVo(@PathVariable("id") Long id) {
        return success(contractLedgerService.getContractRiskInfoVo(id));
    }


    /**
     * 合同下载
     *
     * @param id 合同id
     * @return
     */
    @GetMapping("/contractDownload/{id}")
    public ApiResult<ContractDownloadVo> contractDownload(@PathVariable("id") Long id) {
        return success(contractLedgerService.contractDownload(id));
    }

    /**
     * Oa附件下载
     *
     * @param contractId 合同id
     * @param fileType   文件类型
     * @param fileId     文件Id
     * @return
     */
    @GetMapping("/oaFileDownload")
    public ApiResult<OaFileDownloadVo> oaFileDownload(@RequestParam("contractId") Long contractId,
                                                      @RequestParam("fileType") OaFileDownLoadTypeEnum fileType,
                                                      @RequestParam("fileId") String fileId) {
        return success(contractLedgerService.oaFileDownload(contractId, fileType, fileId));
    }


    /**
     * 合同台账下拉框列表
     *
     * @return 合同基本信息
     */
    @PostMapping("/getContractLedger")
    public List<ContractLedgerSelectListVo> getContractLedger(@RequestBody ContractLedgerSelectListDTO dto) {
        return contractLedgerService.getContractLedger(dto);
    }
}
