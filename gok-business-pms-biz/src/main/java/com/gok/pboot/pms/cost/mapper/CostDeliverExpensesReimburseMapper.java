package com.gok.pboot.pms.cost.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.cost.entity.domain.CostDeliverExpensesReimburse;
import com.gok.pboot.pms.cost.entity.dto.DeliverExpensesReimburseListDto;
import com.gok.pboot.pms.cost.entity.vo.DeliverExpensesReimburseListVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CostDeliverExpensesReimburseMapper extends BaseMapper<CostDeliverExpensesReimburse> {

    /**
     * 查询费用报销
     * @param projectId
     * @return
     */
    List<CostDeliverExpensesReimburse> selReimburseList(@Param("projectId") Long projectId);

    /**
     * 查询采购付款
     * @param projectId
     * @return
     */
    List<CostDeliverExpensesReimburse> selPurchaseList(@Param("projectId") Long projectId);

    /**
     * 查询费用报销台账
     * @param page
     * @param dto
     * @return
     */
    Page<DeliverExpensesReimburseListVO> findExpensesReimburse(Page page, @Param("dto") DeliverExpensesReimburseListDto dto);

    /**
     * 查询已归档采购付款
     * @param projectId
     * @return
     */
    List<CostDeliverExpensesReimburse> selFilePurchaseList(@Param("projectId") Long projectId);
}