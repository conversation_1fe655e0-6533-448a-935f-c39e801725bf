package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.Util.DecimalFormatUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;

/**
 * 财务数据-追加预算总成本 分页数据
 *
 * <AUTHOR>
 * @date 2023/08/23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class AdditionalCostsPageVo {

    /**
     * 请求名称
     */
    private String requestName;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 科目名称
     */
    private String kmmc;

    /**
     * 科目代码
     */
    private String kmdm;

    /**
     * 产品费用项
     */
    private String cbfyx;

    /**
     * 预算金额（含税）
     */
    private String budgetAmount;

    /**
     * 税率
     */
    private String taxRate;

    /**
     * 预算金额（不含税）
     */
    private String budgetAmountBhs;

    /**
     * 成本费用说明
     */
    private String cbfysm;

    /**
     * 产生日期
     */
    private LocalDate generationDate;

    /**
     * 流程类型
     */
    private Integer type;

    /**
     * 设置小数位和舍进规则
     *
     * @param newScale      小数保留位数
     * @param roundingMode  舍进规则
     * @param decimalFormat 小数
     */
    public void setScale(int newScale, RoundingMode roundingMode, DecimalFormat decimalFormat) {
        this.budgetAmount = DecimalFormatUtil.setAndValidate(budgetAmount, newScale, roundingMode, decimalFormat);
        this.budgetAmountBhs = DecimalFormatUtil.setAndValidate(budgetAmountBhs, newScale, roundingMode, decimalFormat);
    }

}
