package com.gok.pboot.pms.controller;

import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.entity.vo.PmsInfoVo;
import com.gok.pboot.pms.service.PmsService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * PMS相关控制器
 *
 * <AUTHOR>
 * @version 1.3.2
 */
@RestController
@RequestMapping("/pms")
@RequiredArgsConstructor
public class PmsController {

    private final PmsService service;

    /**
     * 查询PMS信息（用于前端权限按钮判断展示）
     * @return PMS信息
     */
    @GetMapping("/info")
    public ApiResult<PmsInfoVo> getInfo() {
        return ApiResult.success(service.getInfo());
    }
}
