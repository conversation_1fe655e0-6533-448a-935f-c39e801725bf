package com.gok.pboot.pms.cost.entity.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 现金流计划VO
 *
 * <AUTHOR> generated
 * @date 2024-03-19
 */
@Data
public class CostCashPlanVO {

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 版本号
     */
    private String versionNo;

    /**
     * 时间
     */
    private Integer timeMonth;

    /**
     * 计划月份
     */
    private String planMonth;

    /**
     * 当月回款
     */
    private BigDecimal monthIncome;

    /**
     * 流入累计
     */
    private BigDecimal totalIncome;

    /**
     * 人工成本
     */
    private BigDecimal laborCost;

    /**
     * 人工成本累计
     */
    private BigDecimal totalLaborCost;

    /**
     * 费用报销
     */
    private BigDecimal expenseCost;

    /**
     * 费用报销累计
     */
    private BigDecimal totalExpenseCost;

    /**
     * 外采支出
     */
    private BigDecimal outsourcingCost;

    /**
     * 外采支出累计
     */
    private BigDecimal totalOutsourcingCost;

    /**
     * 流出累计
     */
    private BigDecimal totalOutcome;

    /**
     * 现金流金额
     */
    private BigDecimal cashFlowAmount;

    /**
     * 现金流利息
     */
    private BigDecimal cashFlowInterest;

}