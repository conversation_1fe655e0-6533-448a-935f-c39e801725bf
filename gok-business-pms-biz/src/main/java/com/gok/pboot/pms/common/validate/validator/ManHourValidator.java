package com.gok.pboot.pms.common.validate.validator;

import com.gok.pboot.pms.common.validate.constraint.ManHour;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.math.BigDecimal;

/**
 * - 工时校验器 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/23 15:42
 */
public class ManHourValidator implements ConstraintValidator<ManHour, BigDecimal> {

    /**
     * 最大小数位数
     */
    private int maxDecimalPlaces;

    /**
     * 最小值
     */
    private double min;

    /**
     * 最大值
     */
    private double max;

    public static final BigDecimal TWENTY_FOUR_DECIMAL = new BigDecimal(24);

    /**
     * 每日正常工作时间
     */
    public final static BigDecimal DAILY_NORMAL_WORKING_HOURS = new BigDecimal(7);


    @Override
    public void initialize(ManHour constraintAnnotation) {
        maxDecimalPlaces = constraintAnnotation.maxDecimalPlaces();
        min = constraintAnnotation.min();
        max = constraintAnnotation.max();
        ConstraintValidator.super.initialize(constraintAnnotation);
    }

    @Override
    public boolean isValid(BigDecimal manHour, ConstraintValidatorContext constraintValidatorContext) {
        return manHour == null ||
                manHour.compareTo(BigDecimal.valueOf(min)) >= 0 &&
                manHour.compareTo(BigDecimal.valueOf(max)) <= 0 &&
                (!(manHour.compareTo(TWENTY_FOUR_DECIMAL) > 0) &&
                manHour.scale() <= maxDecimalPlaces) &&
                (manHour.scale() == 0 || StringUtils.equalsAny(
                        manHour.toPlainString().split("\\.")[1], "5", "0"
                ));
    }
}
