package com.gok.pboot.pms.cost.entity.vo;

import com.gok.pboot.pms.enumeration.CostManageStatusEnum;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 成本估算结果VO类
 *
 * <AUTHOR>
 * @create 2025/01/07
 **/
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class CostManageListVO {

    /**
     * 成本预算类型（0=售前成本，1=交付总成本）
     */
    private Integer costBudgetType;

    /**
     * 成本预算类型中文
     */
    private String costBudgetTypeName;

    /**
     * 版本id
     */
    private Long versionId;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 预算状态
     * {@link CostManageStatusEnum}
     */
    private Integer status;

    /**
     * 预算状态中文
     */
    private  String statusName;

    /**
     * 关联流程ID
     */
    private Long requestId;

    /**
     * 关联流程名称
     */
    private String requestName;

    /**
     * 关联流程状态（0=未提交，1=审批中，1=已归档）
     */
    private Integer requestStatus;

    /**
     * 关联流程状态名
     */
    private String requestStatusName;

    /**
     * 当前配置版本明细列表
     */
    private List<CostManageConfigVersionVO> currVersionList;

    /**
     * 成本列表
     */
    List<CostManageEstimationResultsVO> costList;
}
