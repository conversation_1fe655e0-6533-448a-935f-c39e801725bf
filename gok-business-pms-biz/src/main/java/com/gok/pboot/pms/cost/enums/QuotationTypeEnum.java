package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum QuotationTypeEnum implements ValueEnum<Integer> {

    RT(0, "人天"),
    RY(1, "人月"),
    GDFL(2, "固定费率");

    /**
     * 值
     */
    private final Integer value;

    /**
     * 名称
     */
    private final String name;

}