package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.entity.OperatingRecord;
import com.gok.pboot.pms.entity.vo.OperatingRecordPageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 操作记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-23
 */
@Mapper
public interface OperatingRecordMapper extends BaseMapper<OperatingRecord> {


    /**
     * 逻辑删除
     *
     * @param id 唯一标识
     * @return
     */
    int deleteByLogic(@Param("id") Long id);

    /**
     * 批量插入
     *
     * @param poList  实体集合
     */
    void batchSave(@Param("poList") List<OperatingRecord> poList);



    /**
     * 批量修改
     *
     * @param list 实体集合
     */
    void batchUpdate(@Param("list") List<OperatingRecord> list);

    /**
    * 批量逻辑删除
    *
    * @param list id集合
    */
    void batchDel(@Param("list") List<Long> list);

    /**
    * 批量修改不为空字段
    *
    * @param list id集合
    */
    void updateBatch(@Param("list") List<Long> list);

    /**
     * 根据任务id获取分页对象
     *
     * @param operatingRecordPageVoPage 操作记录分页vo
     * @param id 任务id
     * @return {@link Page}<{@link OperatingRecordPageVO}>
     */
    Page<OperatingRecordPageVO> findOperatingRecordPage(Page<OperatingRecordPageVO> operatingRecordPageVoPage, @Param("id") Long id);
}
