package com.gok.pboot.pms.eval.entity.vo;

import cn.hutool.core.bean.BeanUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.eval.entity.domain.EvalProjectDetail;
import com.gok.pboot.pms.eval.enums.AssessmentProjectEnum;
import com.gok.pboot.pms.eval.enums.EvalIndexTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 项目评价VO类
 *
 * <AUTHOR>
 * @date 2025/05/08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class EvalProjectDetailVO {

    /**
     * ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目整体评价表ID
     */
    private Long overviewId;

    /**
     * 指标类型
     * {@link com.gok.pboot.pms.eval.enums.EvalIndexTypeEnum}
     */
    private Integer indexType;

    /**
     * 指标类型名称
     * {@link com.gok.pboot.pms.eval.enums.AssessmentProjectEnum}
     */
    private String indexTypeName;

    /**
     * 评定项目
     */
    private Integer assessmentProject;

    /**
     * 评定项目名称
     */
    private String assessmentProjectName;

    /**
     * 权重
     */
    private BigDecimal weight;

    /**
     * 得分
     */
    private BigDecimal score;

    public static EvalProjectDetailVO convertToVO(EvalProjectDetail entity) {
        if (null == entity) {
            return null;
        }
        EvalProjectDetailVO vo = BeanUtil.copyProperties(entity, EvalProjectDetailVO.class);
        vo.setIndexTypeName(EnumUtils.getNameByValue(EvalIndexTypeEnum.class, entity.getIndexType()));
        vo.setAssessmentProjectName(EnumUtils.getNameByValue(AssessmentProjectEnum.class, entity.getAssessmentProject()));
        return vo;
    }

} 