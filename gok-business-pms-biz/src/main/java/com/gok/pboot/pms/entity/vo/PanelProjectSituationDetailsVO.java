package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.gok.pboot.pms.common.serializer.TwoDecimalToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @desc 个人面板-项目情况-明细VO
 * @createTime 2023/2/21 14:49
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@ApiModel("个人面板-项目情况-明细VO")
public class PanelProjectSituationDetailsVO {
    /**
     * 项目id
     */
    @ExcelIgnore
    @ApiModelProperty(value = "项目id")
    private Long projectId;
    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    @ApiModelProperty(value = "项目名称")
    private String projectName;
    /**
     * 任务id
     */
    @ExcelIgnore
    @ApiModelProperty(value = "任务id")
    private Long taskId;
    /**
     * 任务名称
     */
    @ExcelProperty("任务名称")
    @ApiModelProperty(value = "任务名称")
    private String taskName;
    /**
     * 姓名
     */
    @ExcelProperty("姓名")
    @ApiModelProperty(value = "姓名")
    private String userRealName;
    /**
     * 填报日期
     */
    @ExcelIgnore
    @ApiModelProperty(value = "填报日期")
    private LocalDate submissionDate;
    /**
     * 填报日期带周
     */
    @ExcelProperty("填报日期")
    @ApiModelProperty(value = "填报日期")
    private String submissionDateFormatted;
    /**
     * 正常工时（人天）
     */
    @ExcelProperty("正常工时（人天）")
    @ApiModelProperty(value = "正常工时（人天）")
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    @ContentStyle(dataFormat = 2)
    private BigDecimal normalHours;

    /**
     * 假期类型：1-法定节假日 0-普通休息日
     */
    @ExcelIgnore
    private Integer holidayType;
    /**
     * 加班工时（人天）
     */
    @ExcelProperty("加班工时（人天）")
    @ApiModelProperty(value = "加班工时（人天）")
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    @ContentStyle(dataFormat = 2)
    private BigDecimal addedHours;
    /**
     * 工作内容
     */
    @ExcelProperty("工作内容")
    @ApiModelProperty(value = "工作内容")
    private String workContent;
    /**
     * 昨日计划
     */
    @ExcelProperty("昨日计划")
    @ApiModelProperty(value = "昨日计划")
    private String yesterdayPlan;
}
