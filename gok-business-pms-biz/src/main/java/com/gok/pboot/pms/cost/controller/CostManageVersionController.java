package com.gok.pboot.pms.cost.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.dto.CostConfirmDTO;
import com.gok.pboot.pms.cost.entity.dto.CostManageVersionDTO;
import com.gok.pboot.pms.cost.entity.vo.CostManageVersionVO;
import com.gok.pboot.pms.cost.entity.vo.PreCostConfirmVO;
import com.gok.pboot.pms.cost.entity.vo.VersionHistoryVO;
import com.gok.pboot.pms.cost.enums.ChangeContentEnum;
import com.gok.pboot.pms.cost.service.ICostManageVersionService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 成本管理版本记录
 *
 * <AUTHOR>
 * @menu 成本管理版本记录
 * @since 2025-01-07
 */
@RestController
@RequestMapping("/costManageVersion")
@AllArgsConstructor
public class CostManageVersionController {

    private final ICostManageVersionService costManageVersionService;

    /**
     * 查询项目当前使用版本
     *
     * @param projectId
     * @return
     */
    @GetMapping("/{projectId}")
    public ApiResult<List<CostManageVersionVO>> queryProjectVersion(@PathVariable Long projectId) {
        return ApiResult.success(costManageVersionService.queryProjectVersion(projectId));
    }

    /**
     * 售前成本确认
     *
     * @param costConfirmDTO 成本确认 DTO
     * @return {@link ApiResult }<{@link String }>
     */
    @PostMapping("/preSaleCostConfirm")
    public ApiResult<String> preSaleCostConfirm(@RequestBody @Valid CostConfirmDTO costConfirmDTO) {
        costManageVersionService.preSaleCostConfirm(costConfirmDTO);
        return ApiResult.successMsg("操作成功");
    }

    /**
     * 售前成本确认查询
     *
     * @param projectId 项目id
     * @return
     */
    @GetMapping("/queryPreCostConfirm/{projectId}")
    public ApiResult<PreCostConfirmVO> queryPreCostConfirm(@PathVariable Long projectId) {
        return ApiResult.success(costManageVersionService.queryPreCostConfirm(projectId), "查询成功");
    }

    /**
     * 分页查询成本管理历史版本
     *
     * @param pageRequest 分页参数
     * @param request     查询请求
     * @return {@link ApiResult }<{@link Page }<{@link VersionHistoryVO }>>
     */
    @GetMapping("/historyPage")
    public ApiResult<Page<CostManageVersionVO>> getHistoryVersions(PageRequest pageRequest, CostManageVersionDTO request) {
        return ApiResult.success(costManageVersionService.getHistoryVersions(pageRequest, request), "查询成功");
    }

    /**
     * 获取项目是否存在指定类型的成本估算版本
     *
     * @param projectId 项目ID
     * @return Map<Integer, Boolean> key为{@link ChangeContentEnum}，value为是否存在，true=存在
     */
    @GetMapping("/budgetTypes/{projectId}")
    public ApiResult<Map<Integer, Boolean>> getCostBudgetTypeExist(@PathVariable("projectId") Long projectId) {
        return ApiResult.success(costManageVersionService.checkCostBudgetTypeExist(projectId, true));
    }

    /**
     * 根据售前成本保存项目总成本
     *
     * @return
     */
    @Inner(false)
    @GetMapping("/saveByPreSalesCost")
    public ApiResult<String> saveByPreSalesCost() {
        costManageVersionService.saveByPreSalesCost();
        return ApiResult.success("操作成功");
    }

}
