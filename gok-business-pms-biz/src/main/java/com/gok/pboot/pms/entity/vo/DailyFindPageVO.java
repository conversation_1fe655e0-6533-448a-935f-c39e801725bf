package com.gok.pboot.pms.entity.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Collection;

/**
 * @Auther chenhc
 * @Date 2022-08-24 14:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DailyFindPageVO {
    /**
     * 用户编号
     */
    private Long userId;
    /**
     * 姓名
     */
    private String name;
    /**
     * 部门全称
     */
    private String deptName;
    /**
     * 人员状态名称
     */
    private String personnelStatusName;
    /**
     * 日报异常天数
     */
    private Integer abnormalDays;
    /**
     * 日报集合
     */
    private Collection<DailyPaperQcVO> dailyPapers;
}
