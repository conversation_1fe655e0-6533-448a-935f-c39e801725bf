package com.gok.pboot.pms.Util;

import cn.hutool.core.thread.ThreadUtil;
import com.gok.bcp.log.common.enums.EventTypeEnum;
import com.gok.bcp.log.dto.LogsSaveDto;
import com.gok.bcp.log.fegin.RemoteLogService;
import com.gok.bcp.message.entity.enums.SourceEnum;
import com.gok.components.common.constant.SecurityConstants;
import com.gok.components.common.user.PigxUser;
import com.gok.pboot.common.core.util.WebUtils;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.common.constant.PmsConstants;
import com.gok.pboot.pms.enumeration.LogContentEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 中台日志记录器
 *
 * <AUTHOR>
 * @version 2.3.1
 */
@Slf4j
@Component
public class BcpLoggerUtils {
    @Resource
    private  RemoteLogService remoteLogService;

    /**
     * 记录日志
     *
     * @param logFunction 功能模块名称 com.gok.pboot.service.commons.constant.FunctionConstants
     * @param logContent 日志内容，模板字符串
     * @param logContentArgs 日志内容模板字符串参数
     */
    public void log(String logFunction, LogContentEnum logContent, Object... logContentArgs) {
        AtomicReference<LogsSaveDto> logsSaveDto = new AtomicReference<>(new LogsSaveDto());
        try {
            PigxUser user = SecurityUtils.getUser();
            logsSaveDto.get().setOperator(user.getNickname());
            logsSaveDto.get().setOperatorId(user.getId());
        }catch (Exception e){
            logsSaveDto.get().setOperator("admin");
            logsSaveDto.get().setOperatorId(10000L);
        }
        logsSaveDto.get().setIp(ObjectUtils.defaultIfNull(WebUtils.getIP(), "127.0.0.1"));
        ThreadUtil.execAsync(()->{
            try {
                assembleDataOperateLog(logFunction, logContent, logsSaveDto, logContentArgs);
                remoteLogService.syncSave(SecurityConstants.FROM_IN, logsSaveDto.get());
            } catch (Exception e) {
                log.info("中台记录日志失败, 日志对象: {}, 原因: {}", logsSaveDto, e);
            }
        });

    }

    /**
     * 异步记录日志,用户信息外部传入
     *
     * @param logFunction 功能模块名称 com.gok.pboot.service.commons.constant.FunctionConstants
     * @param logContent 日志内容，模板字符串
     * @param logContentArgs 日志内容模板字符串参数
     */
    public void log(String logFunction, LogContentEnum logContent, PigxUser user, Object... logContentArgs) {
        AtomicReference<LogsSaveDto> logsSaveDto = new AtomicReference<>(new LogsSaveDto());
        if (user != null) {
            logsSaveDto.get().setOperator(user.getNickname());
            logsSaveDto.get().setOperatorId(user.getId());
        } else {
            logsSaveDto.get().setOperator("admin");
            logsSaveDto.get().setOperatorId(10000L);
        }
        logsSaveDto.get().setIp("127.0.0.1");
        ThreadUtil.execAsync(()->{
            try {
                assembleDataOperateLog(logFunction, logContent, logsSaveDto, logContentArgs);
                remoteLogService.syncSave(SecurityConstants.FROM_IN, logsSaveDto.get());
            } catch (Exception e) {
                log.info("中台记录日志失败, 日志对象: {}, 原因: {}", logsSaveDto, e);
            }
        });

    }


    /**
     * 组装数据操作类型的日志对象
     * @param logFunction 功能模块名称 com.gok.pboot.service.commons.constant.FunctionConstants
     * @param logContent 日志内容，模板字符串
     * @param logContentArgs 日志内容模板字符串参数
     * @return 日志对象
     */
    private LogsSaveDto assembleDataOperateLog(String logFunction,
                                               LogContentEnum logContent,
                                               AtomicReference<LogsSaveDto> logsSaveDto,
                                               Object... logContentArgs) {
        return assembleLog(EventTypeEnum.DATA_OPERATION, logFunction, logContent,logsSaveDto, logContentArgs);
    }

    /**
     * 组装日志对象
     * @param eventType 事件类型
     * @param logFunction 功能模块名称 com.gok.pboot.service.commons.constant.FunctionConstants
     * @param logContent 日志内容，模板字符串
     * @param logContentArgs 日志内容模板字符串参数
     * @return 日志对象
     */
    private LogsSaveDto assembleLog(
            EventTypeEnum eventType,
            String logFunction,
            LogContentEnum logContent,
            AtomicReference<LogsSaveDto> logsSaveDto,
            Object... logContentArgs
    ) {
        String content = logContent.formatValue(logContentArgs);
        LogsSaveDto log = logsSaveDto.get();

        if (content.length() > 500) {
            content = content.substring(0, 500) + "...";
        }
        log.setClientId(PmsConstants.CLIENT_ID);
        log.setClientName(SourceEnum.PROJECT.getName());
        log.setSourceEnum(SourceEnum.PROJECT);
        log.setLevel(3);
        log.setLevelName(PmsConstants.PMS_LOG_LEVEL_NAME);
        log.setLogName(logContent.getName());
        log.setFeaturesPage(logFunction);
        log.setContent(content);
        log.setEventTypeEnum(eventType);
        return log;
    }

}
