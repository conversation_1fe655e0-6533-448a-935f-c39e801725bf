package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.Util.BcpLoggerUtils;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.common.constant.FunctionConstants;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.entity.domain.ProjectStakeholderCustomer;
import com.gok.pboot.pms.entity.dto.ProjectStakeholderCustomerDTO;
import com.gok.pboot.pms.entity.vo.ProjectStakeholderCustomerVO;
import com.gok.pboot.pms.enumeration.LogContentEnum;
import com.gok.pboot.pms.enumeration.ProjectImpactDegreeEnum;
import com.gok.pboot.pms.enumeration.YesOrNoEnum;
import com.gok.pboot.pms.mapper.ProjectStakeholderCustomerMapper;
import com.gok.pboot.pms.service.IProjectStakeholderCustomerService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 项目利益相关者客户服务实施
 *
 * <AUTHOR>
 * @date 2025/05/08
 */
@Service
@RequiredArgsConstructor
public class ProjectStakeholderCustomerServiceImpl extends ServiceImpl<ProjectStakeholderCustomerMapper, ProjectStakeholderCustomer>
        implements IProjectStakeholderCustomerService {

    private final BcpLoggerUtils bcpLoggerUtils;

    @Override
    public List<ProjectStakeholderCustomerVO> getCustomerByProjectId(Long projectId) {
        List<ProjectStakeholderCustomer> entityList = baseMapper.getCustomerByProjectId(projectId);
        if (CollUtil.isEmpty(entityList)) {
            return new ArrayList<>();
        }

        // 封装响应体
        List<ProjectStakeholderCustomerVO> voList = new ArrayList<>();
        entityList.forEach(e -> {
            ProjectStakeholderCustomerVO vo = BeanUtil.copyProperties(e, ProjectStakeholderCustomerVO.class);
            vo.setImpactDegreeTxt(EnumUtils.getNameByValue(ProjectImpactDegreeEnum.class, vo.getImpactDegree()));
            vo.setSatisfactionSurveyTxt(EnumUtils.getNameByValue(YesOrNoEnum.class, vo.getSatisfactionSurvey()));
            voList.add(vo);
        });
        return voList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdateCustomer(ProjectStakeholderCustomerDTO dto) {
        // 验证客户满意度调查
        verifyCustomerSurvey(dto);

        ProjectStakeholderCustomer projectStakeholderCustomer = ProjectStakeholderCustomer.saveOrUpdate(dto);
        if (Optional.ofNullable(dto.getId()).isPresent()) {
            //编辑客户干系人
            bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_PROJECT_STAKEHOLDERS, LogContentEnum.EDIT_CUSTOMER_STAKEHOLDERS,
                    dto.getContact());
        } else {
            //新增客户干系人
            bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_PROJECT_STAKEHOLDERS, LogContentEnum.ADD_NEW_CUSTOMER_STAKEHOLDERS,
                    dto.getContact());
        }

        return this.saveOrUpdate(projectStakeholderCustomer);
    }

    private void verifyCustomerSurvey(ProjectStakeholderCustomerDTO dto) {
        // 检查满意度调查
        if (YesOrNoEnum.YES.getValue().equals(dto.getSatisfactionSurvey())) {
            // 查询当前项目是否已存在满意度调查为"是"的客户
            LambdaQueryWrapper<ProjectStakeholderCustomer> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ProjectStakeholderCustomer::getProjectId, dto.getProjectId())
                    .eq(ProjectStakeholderCustomer::getSatisfactionSurvey, YesOrNoEnum.YES.getValue());

            // 如果是编辑，排除当前记录
            if (dto.getId() != null) {
                wrapper.ne(ProjectStakeholderCustomer::getId, dto.getId());
            }

            long count = this.count(wrapper);
            if (count > 0) {
                throw new ServiceException("该项目下已存在满意度调查为" + "[是]" + "的客户，每个项目仅允许设置一个客户进行满意度调查");
            }

            // 当满意度调查为"是"时，验证联系方式
            if (dto.getContactPhone() == null || dto.getContactPhone().trim().isEmpty()) {
                throw new ServiceException("当满意度调查为" + "[是]" + "时，联系方式不能为空");
            }
        }
    }

    @Override
    public Boolean deleteById(Long id){
        ProjectStakeholderCustomer projectStakeholderCustomer = this.getById(id);
        if(Optional.ofNullable(projectStakeholderCustomer).isPresent()){
            bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_PROJECT_STAKEHOLDERS, LogContentEnum.DELETE_CUSTOMER_STAKEHOLDERS,
                    projectStakeholderCustomer.getContact());
            return this.removeById(id);
        }
        return false;
    }

}
