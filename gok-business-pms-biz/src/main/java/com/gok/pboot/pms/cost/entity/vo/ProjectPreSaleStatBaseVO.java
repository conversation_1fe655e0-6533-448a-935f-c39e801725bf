package com.gok.pboot.pms.cost.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Project DIM 预售统计 Base Vo
 *
 * <AUTHOR>
 * @date 2025/05/15
 */
@Data
public class ProjectPreSaleStatBaseVO {


    /**
     * 项目ID
     */
    @ExcelIgnore
    private Long projectId;

    /**
     * 项目创建时间
     */
    @ExcelIgnore
    private String projectCreateTime;


    /**
     * 待审核工时
     */
    @ExcelProperty("待审核工时")
    private BigDecimal waitReviewHours;

    /**
     * 已审核总工时
     */
    @ExcelProperty("已审核总工时")
    private BigDecimal reviewedHours;

    /**
     * 已审核人工成本
     */
    @ExcelProperty("已审核人工成本")
    private BigDecimal reviewedLaborCost;
}
