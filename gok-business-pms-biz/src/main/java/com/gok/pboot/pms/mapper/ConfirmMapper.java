package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.entity.Confirm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 工时确认 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-30
 */
@Mapper
public interface ConfirmMapper extends BaseMapper<Confirm> {


    /**
     * 逻辑删除
     *
     * @param id 唯一标识
     * @return int
     */
    int deleteByLogic(@Param("id") Long id);

    /**
     * 批量插入
     *
     * @param poList  实体集合
     */
    void batchSave(@Param("poList") List<Confirm> poList);



    /**
     * 批量修改
     *
     * @param list 实体集合
     */
    void batchUpdate(@Param("list") List<Confirm> list);

    /**
    * 批量逻辑删除
    *
    * @param list id集合
    */
    void batchDel(@Param("list") List<Long> list);

    /**
    * 批量修改不为空字段
    *
    * @param list id集合
    */
    void updateBatch(@Param("list") List<Long> list);

    /**
     * ~ 查询指定年月是否存在工时确认数据 ~
     * @param year 年
     * @param month 月
     * @param firstDeptId 一级部门名称
     * @return boolean
     * <AUTHOR>
     * @date 2022/9/20 11:55
     */
    boolean existsByYearAndMonth(@Param("year") Integer year, @Param("month") Integer month, @Param("deptId") Long firstDeptId);

}
