package com.gok.pboot.pms.enumeration;

import org.apache.commons.lang3.StringUtils;

/**
 * 员工状态枚举
 *
 * <AUTHOR>
 * @version 1.0.0
 * @className EmployeeStatusEnum
 * @date 2021/12/24 5:53 下午
 */
public enum EmployeeStatusEnum implements ValueEnum<Integer> {

    /**
     * 员工状态枚举
     */
    SHIXI(0, "实习"),
    PROBATION(1, "试用"),
    FORMAL(2, "正式"),
    RETIREFP(3, "退休返聘"),
    TEMPORARY(4, "兼职"),
    RESIGN(5, "离职"),
    WAIT_RESIGN(6, "待离职");

    Integer value;
    String name;

    EmployeeStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }

    public static String getNameByValue(Integer value){
        if (value == null){
            return "";
        }
        EmployeeStatusEnum[] values = EmployeeStatusEnum.values();
        for (EmployeeStatusEnum tmp : values){
            if (tmp.getValue().equals(value)){
                return tmp.getName();
            }
        }
        return "";
    }

    public static Integer getValue(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        EmployeeStatusEnum[] values = EmployeeStatusEnum.values();
        for (EmployeeStatusEnum statusEnum : values) {
            if (name.equals(statusEnum.getName())) {
                return statusEnum.getValue();
            }
        }
        return null;
    }
}
