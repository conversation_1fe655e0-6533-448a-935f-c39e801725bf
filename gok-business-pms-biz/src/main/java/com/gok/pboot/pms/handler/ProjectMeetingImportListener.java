package com.gok.pboot.pms.handler;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.gok.components.common.user.PigxUser;
import com.gok.module.excel.api.domain.vo.ErrorMessageVo;
import com.gok.module.excel.api.enums.FunctionEnum;
import com.gok.module.excel.api.listener.ListAnalysisEventListener;
import com.gok.module.excel.api.support.Validators;
import com.gok.module.file.entity.SysFile;
import com.gok.pboot.pms.entity.dto.ProjectMeetingImportExcelDTO;
import lombok.extern.slf4j.Slf4j;

import javax.validation.ConstraintViolation;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会议纪要Excel导入 监听器
 *
 * <AUTHOR>
 * @since 2023-07-13
 **/
@Slf4j
public class ProjectMeetingImportListener extends ListAnalysisEventListener<ProjectMeetingImportExcelDTO> {

    private Long lineNum = 1L;

    private final List<ProjectMeetingImportExcelDTO> list = new ArrayList<>();

    private final List<ErrorMessageVo> errorMessageList = new ArrayList<>();

    @Override
    public List<ProjectMeetingImportExcelDTO> getList() {
        return list;
    }

    @Override
    public <T> void validCustomize(Class aClass, List<T> list, PigxUser pigxUser) throws ParseException {

    }

    @Override
    public void intData(FunctionEnum functionEnum, SysFile sysFile, PigxUser pigxUser, int i) {

    }

    @Override
    public List<ErrorMessageVo> getErrors() {
        return errorMessageList;
    }

    @Override
    public void invoke(ProjectMeetingImportExcelDTO excelData, AnalysisContext context) {
        lineNum++;
        log.debug("解析到第{}行数据:{}", lineNum, excelData);

        // 参数格式校验
        excelData.validate();
        Set<ConstraintViolation<ProjectMeetingImportExcelDTO>> violations = Validators.validate(excelData);
        if (!violations.isEmpty()) {
            // 异常捕捉
            Set<String> messageSet = violations.stream()
                    .map(ConstraintViolation::getMessage)
                    .collect(Collectors.toSet());
            errorMessageList.add(new ErrorMessageVo(lineNum, messageSet));
        } else {
            list.add(excelData);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.debug("Excel read analysed");
        if (CollUtil.isNotEmpty(errorMessageList)) {
            log.error("Excel导入参数校验失败详情为:{}", errorMessageList);
        }
    }

    @Override
    public void invokeHeadMap(Map headMap, AnalysisContext context) {
        log.info("解析到的表头数据: {}", headMap);
    }

    @Override
    public void onException(Exception e, AnalysisContext analysisContext) {
        //  捕获其他异常进行处理
        if (!Optional.ofNullable(e.getMessage()).isPresent()) {
            log.error("第{}行数据保存失败", lineNum, e);
        }
        HashSet messageSet = new HashSet();
        messageSet.add(e.getMessage());
        errorMessageList.add(new ErrorMessageVo(lineNum, messageSet));
    }

}
