package com.gok.pboot.pms.cost.entity.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 人员工单看板-人员维度售前工单统计VO
 *
 * <AUTHOR>
 * @date 2025/05/15
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PersonnelPerDimPreSaleStatVO extends PersonnelPreSaleStatBaseVO {

    /**
     * 人员工号
     */
    @ExcelProperty("员工工号")
    private String workCode;

    /**
     * 人员姓名
     */
    @ExcelProperty("员工姓名")
    private String employeeName;

}
