package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 我的任务Vo
 *
 * <AUTHOR>
 * @date 2024/02/23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MyTaskVo {

    /**
     * 任务ID
     */
    private Long id;

    /**
     * 所属项目ID
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 任务名称
     */
    private String title;

    /**
     * 计划周期
     */
    private String expectDate;

    /**
     * 计划开始日期
     */
    private LocalDate expectStartDate;

    /**
     * 计划结束日期
     */
    private LocalDate expectEndDate;

    /**
     * 实际开始日期
     */
    private LocalDate startDate;

    /**
     * 实际结束日期
     */
    private LocalDate endDate;

    /**
     * 任务状态（0=正常，1=结束）
     * @link com.gok.pboot.pms.enumeration.ProjectTaskStateEnum
     */
    private Integer state;

    /**
     * 任务状态txt
     */
    private String stateTxt;

    /**
     * 总工时
     */
    private String totalHour;
    /**
     * 正常工时
     */
    private String normalHour;
    /**
     * 加班工时
     */
    private String addedHour;

    /**
     * 工作日加班工时
     */
    private String workOvertimeHours;

    /**
     * 休息日加班工时
     */
    private String restOvertimeHours;

    /**
     * 节假日加班工时
     */
    private String holidayOvertimeHours;

    /**
     * 是否挂靠工时（0否，1是）
     */
    private Integer isAttachmentHour;

    /**
     * 是否长期任务
     */
    private Integer permanentFlag;

    /**
     * 展示删除按钮标志
     */
    private Integer showDelButtonFlag;

}
