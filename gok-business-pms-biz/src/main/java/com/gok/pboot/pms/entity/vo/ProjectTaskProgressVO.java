package com.gok.pboot.pms.entity.vo;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.entity.domain.ProjectTaskProgress;
import com.gok.pboot.pms.entity.domain.ProjectTaskProgressFeedback;
import com.gok.pboot.pms.enumeration.ProjectTaskProgressEnum;
import com.google.common.base.Strings;
import lombok.*;

import java.util.Collection;
import java.util.List;

/**
 * 项目任务进展
 *
 * <AUTHOR>
 * @version 1.1.0
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
public class ProjectTaskProgressVO {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户头像
     */
    private String userAvatarUrl;

    /**
     * 进度
     * @see com.gok.pboot.pms.enumeration.ProjectTaskProgressEnum
     */
    private String progress;

    /**
     * 进度名称
     */
    private String progressName;

    /**
     * 进度内容
     */
    private String content;

    /**
     * 是否可以删除
     */
    private Boolean canDelete;

    /**
     * 创建时间
     */
    private String ctime;

    /**
     * 回复列表
     */
    private List<ProjectTaskProgressFeedbackVO> feedbacks;

    public static ProjectTaskProgressVO of(
            ProjectTaskProgress po,
            Collection<ProjectTaskProgressFeedback> feedbacks,
            Long userId
    ){
        ProjectTaskProgressVO result = new ProjectTaskProgressVO();

        result.setId(String.valueOf(po.getId()));
        result.setUserName(Strings.nullToEmpty(po.getUserName()));
        result.setProgress(String.valueOf(po.getProgress()));
        result.setProgressName(EnumUtils.getNameByValue(ProjectTaskProgressEnum.class, po.getProgress()));
        result.setContent(Strings.nullToEmpty(po.getContent()));
        result.setCanDelete(userId.equals(po.getUserId()) && feedbacks.isEmpty());
        result.setCtime(Strings.nullToEmpty(LocalDateTimeUtil.formatNormal(po.getCtime().toLocalDateTime())));
        result.setFeedbacks(ProjectTaskProgressFeedbackVO.batchOf(feedbacks, userId));

        return result;
    }
}
