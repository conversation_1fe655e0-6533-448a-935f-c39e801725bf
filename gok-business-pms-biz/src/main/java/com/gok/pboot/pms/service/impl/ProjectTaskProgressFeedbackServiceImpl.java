package com.gok.pboot.pms.service.impl;

import com.gok.components.common.user.PigxUser;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.entity.domain.ProjectTask;
import com.gok.pboot.pms.entity.domain.ProjectTaskProgress;
import com.gok.pboot.pms.entity.domain.ProjectTaskProgressFeedback;
import com.gok.pboot.pms.entity.dto.ProjectTaskProgressFeedbackAddDTO;
import com.gok.pboot.pms.mapper.ProjectTaskMapper;
import com.gok.pboot.pms.mapper.ProjectTaskProgressFeedbackMapper;
import com.gok.pboot.pms.mapper.ProjectTaskProgressMapper;
import com.gok.pboot.pms.service.IProjectTaskProgressFeedbackService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.ValidationException;

/**
 * 项目任务进展回复
 *
 * <AUTHOR>
 * @version 1.1.0
 */
@Service
@AllArgsConstructor
public class ProjectTaskProgressFeedbackServiceImpl implements IProjectTaskProgressFeedbackService {

    private final ProjectTaskProgressFeedbackMapper mapper;
    private final ProjectTaskProgressMapper projectTaskProgressMapper;
    private final ProjectTaskMapper taskMapper;

    @Override
    @Transactional
    public void add(ProjectTaskProgressFeedbackAddDTO dto) {
        PigxUser user = SecurityUtils.getUser();
        ProjectTaskProgressFeedback po;

        validateAddDTO(dto);
        po = ProjectTaskProgressFeedback.of(dto, user.getId(), user.getAvatar(), user.getNickname());
        BaseBuildEntityUtil.buildInsert(po);
        mapper.insert(po);
    }

    private void validateAddDTO(ProjectTaskProgressFeedbackAddDTO dto){
        ProjectTaskProgress progress;
        ProjectTask task;
        Long progressId = dto.getProgressId();

        progress = projectTaskProgressMapper.selectById(progressId);
        if (progress == null){
            throw new ValidationException("没有找到指定任务进展");
        }
        task = taskMapper.selectById(progress.getTaskId());
        if (task == null){
            throw new ValidationException("没有找到指定任务");
        }
        if (mapper.countByProgressId(progressId) >= 50){
            throw new ValidationException("该进展回复已达上限（50条），无法再新增回复");
        }
    }

    @Override
    public void deleteById(Long id) {
        ProjectTaskProgressFeedback feedback = mapper.selectById(id);

        if (feedback != null){
            if (!SecurityUtils.getUser().getId().equals(feedback.getUserId())){
                throw new ValidationException("您不是回复提交者，无法删除该回复");
            }
            mapper.deleteById(id);
        }
    }

}
