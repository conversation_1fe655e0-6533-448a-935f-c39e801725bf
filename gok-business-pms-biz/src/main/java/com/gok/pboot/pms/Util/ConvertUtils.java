/**
 * Copyright (c) 2005-2012 springside.org.cn
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 */
package com.gok.pboot.pms.Util;

import org.apache.commons.beanutils.converters.DateConverter;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Map;

/**
 * 类型转换工具类
 *
 * <AUTHOR>
 *
 */
public class ConvertUtils extends org.apache.commons.beanutils.ConvertUtils{

	private ConvertUtils() {
		throw new IllegalStateException("Utility class could not be instantiated");
	}

	static {
		registerDateConverter(
				"yyyy-MM-dd",
				"yyyy-MM-dd HH:mm:ss",
				"yyyyMMddHHmmss",
				"yyyyMMdd",
				"yyyyMMddHHmmssSSS");
	}

	/**
	 * 注册一个时间类型的转换器,当前默认的格式为：yyyy-MM-dd
	 *
	 * @param patterns 日期格式
	 */
	public static void registerDateConverter(String... patterns) {
		DateConverter dc = new DateConverter();
		dc.setUseLocaleFormat(true);
		dc.setPatterns(patterns);
		register(dc, Date.class);
	}

	/**
	 * 基于Apache BeanUtils转换字符串到相应类型.
	 *
	 * @param value 待转换的字符串.
	 * @param toType 转换目标类型.
	 */
	public static Object convertToObject(String value, Class<?> toType) {
		try {
			if (toType.equals(LocalDateTime.class)) {
				DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
				return toType.cast(LocalDateTime.parse(value, dateTimeFormatter));
			}else if(toType.equals(LocalDate.class)){
				DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
				return toType.cast(LocalDate.parse(value, dateTimeFormatter));
			}else if(toType.equals(YearMonth.class)){
				DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
				return toType.cast(YearMonth.parse(value, dateTimeFormatter));
			}else {
				return convert(value, toType);
			}
		} catch (Exception e) {
			throw ReflectionUtils.convertReflectionExceptionToUnchecked(e);
		}
	}

	/**
	 * 转换字符串数组到相应类型.
	 *
	 * @param values 待转换的字符串.
	 * @param toType 转换目标类型.
	 */
	public static Object convertToObject(String[] values,Class<?> toType) {
		try {
			return convert(values, toType);
		} catch (Exception e) {
			throw ReflectionUtils.convertReflectionExceptionToUnchecked(e);
		}
	}

	public static Object convertMap(Class type, Map map) throws Exception {
		BeanInfo beanInfo = Introspector.getBeanInfo(type);
		Object obj = type.newInstance();
		PropertyDescriptor[] propertyDescriptors =  beanInfo.getPropertyDescriptors();
		for (PropertyDescriptor descriptor : propertyDescriptors) {
			String propertyName = descriptor.getName();
			if (map.containsKey(propertyName)) {
				Object value = map.get(propertyName);
				descriptor.getWriteMethod().invoke(obj, value);
			}
		}
		return obj;
	}
}
