package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 客户分级枚举类
 *
 * <AUTHOR>
 * @Version v1.3.7
 * @create 2024/07/10
 **/
@Getter
@AllArgsConstructor
public enum CustomerGradeEnum implements ValueEnum<Integer> {

    STRATEGIC_DEPLOYMENT(0, "战略部署"),
    ACTIVELY_EXPLORE(1, "积极开拓"),
    POTENTIAL_EXPLORE(2, "潜力探索"),
    PASSIVE_BUSINESS_EXPANSION(3, "被动展业"),
    OTHERS(4, "其他");

    private final Integer value;
    private final String name;

}
