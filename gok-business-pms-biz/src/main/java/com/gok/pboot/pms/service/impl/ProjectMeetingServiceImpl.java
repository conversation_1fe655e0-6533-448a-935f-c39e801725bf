package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.module.excel.api.domain.vo.ErrorMessageVo;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.BcpLoggerUtils;
import com.gok.pboot.pms.Util.CollectionUtils;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.constant.FunctionConstants;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.domain.ProjectMeeting;
import com.gok.pboot.pms.entity.dto.ProjectMeetingDTO;
import com.gok.pboot.pms.entity.dto.ProjectMeetingImportExcelDTO;
import com.gok.pboot.pms.entity.dto.ProjectSystemProcessInfoDTO;
import com.gok.pboot.pms.entity.vo.ProjectMeetingExportExcelVO;
import com.gok.pboot.pms.entity.vo.ProjectMeetingFindPageVO;
import com.gok.pboot.pms.entity.vo.ProjectMeetingVO;
import com.gok.pboot.pms.enumeration.LogContentEnum;
import com.gok.pboot.pms.enumeration.ProcessInfoStatusEnum;
import com.gok.pboot.pms.enumeration.SystemProcessTypeEnum;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import com.gok.pboot.pms.mapper.ProjectMeetingMapper;
import com.gok.pboot.pms.service.IProjectMeetingService;
import com.gok.pboot.pms.service.IProjectSystemProcessInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BindingResult;

import java.util.*;

import static com.gok.pboot.pms.service.IProjectSystemProcessInfoService.MEETING_PROCESS_PREFIX;

/**
 * <p>
 * 项目会议纪要 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-13
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectMeetingServiceImpl extends ServiceImpl<ProjectMeetingMapper, ProjectMeeting> implements IProjectMeetingService {

    @Autowired
    private IProjectSystemProcessInfoService systemProcessService;

    private final BcpLoggerUtils bcpLoggerUtils;

    private final ProjectInfoMapper projectInfoMapper;


    @Override
    public Page<ProjectMeetingFindPageVO> findPageList(PageRequest pageRequest, Map<String, Object> filter) {
        //分页查询项目风险信息
        Page<ProjectMeetingFindPageVO> pageResult =
                baseMapper.findListPage(new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize()), filter);
        return pageResult;
    }

    @Override
    public ProjectMeetingVO getById(Long id) {
        ProjectMeeting entity = findById(id);
        ProjectMeetingVO vo = ProjectMeetingVO.buildByEntity(entity);
        return vo;
    }

    @Override
    public ProjectMeeting findById(Long id) {
        if (!Optional.ofNullable(id).isPresent()) {
            return null;
        }
        return baseMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long save(ProjectMeetingDTO request) {
        request.validate();

        // 赋值实体类
        ProjectMeeting entity = ProjectMeeting.buildSave(request);

        // 持久化数据库
        baseMapper.insert(entity);

        // 同步保存项目过程动态
        ProjectSystemProcessInfoDTO systemProcessInfoDTO = covert2ProjectSystemProcessInfoDTO(entity);
        systemProcessService.innerBatchSave(Arrays.asList(systemProcessInfoDTO));
        //
        ProjectInfo projectInfo = projectInfoMapper.selectById(request.getProjectId());
        if(Optional.ofNullable(projectInfo).isPresent()){
            bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_MEETING_MINUTES, LogContentEnum.NEW_MEETING_MINUTES,
                    projectInfo.getItemName(),request.getName());
        }

        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long update(ProjectMeetingDTO request) {
        request.validate();

        Long id = request.getId();
        Assert.isTrue(Optional.ofNullable(id).isPresent(), "主键id不能为空");
        ProjectMeeting entity = findById(id);
        if (!Optional.ofNullable(entity).isPresent()) {
            log.warn("id:{}对应数据不存在，操作结束", id);
            return request.getId();
        }
        BeanUtil.copyProperties(request, entity);
        BaseBuildEntityUtil.buildUpdate(entity);

        // 持久化数据库
        baseMapper.updateById(entity);
        ProjectInfo projectInfo = projectInfoMapper.selectById(request.getProjectId());
        bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_MEETING_MINUTES, LogContentEnum.EDIT_MEETING_MINUTES,
                projectInfo.getItemName(),request.getName());

        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> batchDel(List<Long> list) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        baseMapper.batchDel(list);
        return list;
    }

    @Override
    public List<ProjectMeetingExportExcelVO> export(PageRequest pageRequest, Map<String, Object> filter) {
        Page<ProjectMeetingExportExcelVO> listPage =
                baseMapper.findExportPage(new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize()), filter);
        if (CollUtil.isEmpty(listPage.getRecords())) {
            // 避免空数据导出造成文件格式损坏
            return Arrays.asList(new ProjectMeetingExportExcelVO());
        }
        // 针对非法数据进行处理
        List<ProjectMeetingExportExcelVO> records = listPage.getRecords();
        records.forEach(o -> o.setTime(StrUtil.containsOnly(o.getTime(), '-') ? StrUtil.EMPTY : o.getTime()));
        //日志记录
        bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_MEETING_MINUTES, LogContentEnum.EXPORT_MEETING_MINUTES,
                records.size());
        return records;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResult importByExcel(Long projectId, List<ProjectMeetingImportExcelDTO> excelDtoList, BindingResult bindingResult) {
        List<ErrorMessageVo> errorMessageList = new ArrayList<>();
        if (Optional.ofNullable(bindingResult.getTarget()).isPresent()) {
            errorMessageList = (List<ErrorMessageVo>) bindingResult.getTarget();
        }

        String responseMsg;
        if (CollUtil.isEmpty(excelDtoList)) {
            responseMsg = StrUtil.format("解析导入文件完成\n导入:0条数据成功\n导入:{}条数据失败\n失败数据行数为:{}",
                    CollUtil.size(errorMessageList),
                    CollUtil.getFieldValues(errorMessageList, "lineNum"));
            return ApiResult.success(responseMsg);
        }

        // 赋值实体类集合
        List<ProjectMeeting> entityList = new ArrayList<>();
        excelDtoList.forEach(o -> {
            ProjectMeeting entityItem = ProjectMeeting.buildSave(o);
            entityItem.setProjectId(projectId);
            entityList.add(entityItem);
        });

        // 持久化数据库
        this.saveBatch(entityList);

        // 批量保存项目过程动态
        List<ProjectSystemProcessInfoDTO> processInfoDTOList = new ArrayList<>();
        entityList.forEach(o -> processInfoDTOList.add(covert2ProjectSystemProcessInfoDTO(o)));
        systemProcessService.innerBatchSave(processInfoDTOList);

        if (CollUtil.isNotEmpty(errorMessageList)) {
            responseMsg = StrUtil.format("解析导入文件完成\n导入:{}条数据成功\n导入:{}条数据失败\n失败数据行数为:{}",
                    CollUtil.size(entityList),
                    CollUtil.size(errorMessageList),
                    CollUtil.getFieldValues(errorMessageList, "lineNum"));
        } else {
            responseMsg = StrUtil.format("解析导入文件完成\n导入:{}条数据成功",
                    CollUtil.size(entityList));
        }
        //日志记录
       bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_MEETING_MINUTES, LogContentEnum.IMPORT_MEETING_MINUTES,
               entityList.size()-errorMessageList.size());

        return ApiResult.success(responseMsg);
    }

    /**
     * 封装项目过程动态请求实体
     *
     * @param entity 项目会议纪要实体类
     * @return 项目过程动态请求实体
     */
    private ProjectSystemProcessInfoDTO covert2ProjectSystemProcessInfoDTO(ProjectMeeting entity) {
        ProjectSystemProcessInfoDTO systemProcessInfoDTO = ProjectSystemProcessInfoDTO.builder()
                .name(StrUtil.format("{}{}", MEETING_PROCESS_PREFIX, entity.getName()))
                .status(String.valueOf(ProcessInfoStatusEnum.ARCHIVE.getValue()))
                .processType(SystemProcessTypeEnum.MEETING.getValue())
                .applicatId(SecurityUtils.getUser().getId())
                .applicat(SecurityUtils.getUser().getName())
                .projectId(entity.getProjectId())
                .requestId(entity.getId())
                .build();
        return systemProcessInfoDTO;
    }

}
