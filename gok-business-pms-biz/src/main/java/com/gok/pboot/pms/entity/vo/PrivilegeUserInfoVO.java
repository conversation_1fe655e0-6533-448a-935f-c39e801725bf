package com.gok.pboot.pms.entity.vo;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 任务-人员多对多关联
 *
 * <AUTHOR>
 * @since 2022-08-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PrivilegeUserInfoVO {

    /**
     * 人员ID
     */
    private Long userId;

    /**
     * 人员名字
     */
    private String name;

    /**
     * 人员部门
     */
    private String deptName;

    /**
     * 人员类型
     * {@link com.gok.pboot.pms.enumeration.PrivilegeTypeEnum}
     */
    private Integer privilegeType;

}
