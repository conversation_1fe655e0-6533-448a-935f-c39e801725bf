package com.gok.pboot.pms.cost.entity.vo;

import com.gok.pboot.pms.cost.enums.CostTypeEnum;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 交付管理费用预算VO类
 *
 * <AUTHOR>
 * @create 2025/01/07
 **/
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class DeliverCostBudgetListVO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 成本预算类型（0=售前成本，1=A表成本，2=B表成本）
     * {@link com.gok.pboot.pms.cost.enums.CostBudgetTypeEnum}
     */
    private Integer costBudgetType;

    /**
     * 成本预算类型Txt
     */
    private String costBudgetTypeTxt;

    /**
     * 说明
     */
    private String remark;

    /**
     * 成本科目ID
     */
    private Long accountId;

    /**
     * 科目OA ID
     */
    private Long accountOaId;

    /**
     * 成本科目名称
     */
    private String accountName;

    /**
     * 自定义补贴配置ID
     */
    private Long subsidyCustomConfigId;

    /**
     * 自定义补贴配置名称
     */
    private String subsidyCustomConfigName;

    /**
     * 成本科目类型
     * {@link com.gok.pboot.pms.cost.enums.AccountTypeEnum}
     */
    private Integer accountType;

    /**
     * 成本科目类型Txt
     */
    private String accountTypeTxt;

    /**
     * 成本科目类别id
     */
    private Long accountCategoryId;

    /**
     * 成本科目类别名称
     */
    private String accountCategoryName;

    /**
     * 科目代码
     */
    private String accountCode;

    /**
     * 预算金额(含税)
     */
    private BigDecimal budgetAmountIncludedTax;

    /**
     * 税率中台OA字典ID
     */
    private Integer taxRate;

    /**
     * 税率文本
     */
    private String taxRateTxt;

    /**
     * 预算金额(不含税)
     */
    private BigDecimal budgetAmountExcludingTax;

    /**
     * 已用预算
     */
    private BigDecimal usedBudget;

    /**
     * 剩余预算
     */
    private BigDecimal remainBudget;

    /**
     * 已确认预算
     */
    private BigDecimal confirmedBudget;

    /**
     * 剩余可分配预算
     */
    private BigDecimal remainAllocatableBudget;

    /**
     * 关联工单列表
     */
    private List<CostDeliverTaskVO> taskList;

    /**
     * 对应成本类型（0=人工成本，1=费用报销，2=外采费用）
     * {@link CostTypeEnum}
     */
    private Integer costType;

}
