package com.gok.pboot.pms.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 客户经营单元-经营组织架构（相关负责人）
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Data
@Accessors(chain = true)
@TableName("customer_business_person")
@ApiModel(value="CustomerBusinessPerson对象", description="客户经营单元-经营组织架构（相关负责人）")
public class CustomerBusinessPerson extends BeanEntity<Long> {

    private static final long serialVersionUID = 1L;

    /**
     * 经营单元id
     */
    @ApiModelProperty(name = "businessId",value = "经营单元id")
    @TableField("business_id")
    private Long businessId;

    /**
     * 负责人id
     */
    @ApiModelProperty(name = "managerId",value = "负责人id")
    @TableField("manager_id")
    private Long managerId;

    /**
     * 负责人姓名
     */
    @ApiModelProperty(name = "managerName",value = "负责人姓名")
    @TableField("manager_name")
    private String managerName;

    /**
     * 负责人部门
     */
    @ApiModelProperty(name = "managerDept",value = "负责人部门")
    @TableField("manager_dept_id")
    private Long managerDeptId;


    /**
     * 负责人部门
     */
    @ApiModelProperty(name = "managerDept",value = "负责人部门")
    @TableField("manager_dept_name")
    private String managerDeptName;

    /**
     * 负责人角色（1主办客户经理 2协办客户经理 3总支撑官 4方案经理 5人力BP 6交付经理）
     */
    @ApiModelProperty(name = "managerRole",value = "负责人角色（1主办客户经理 2协办客户经理 3总支撑官 4方案经理 5人力BP 6交付经理）")
    @TableField("manager_role")
    private Integer managerRole;


}
