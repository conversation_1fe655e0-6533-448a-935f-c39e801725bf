package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @desc 日报类型
 * @createTime 2023/2/20 9:26
 */
@Getter
@AllArgsConstructor
public enum DailyPaperTypeEnum {
    /**
     * 普通日报
     */
    NORMAL_DAILY_PAPER(1, "普通日报"),
    /**
     * 人才复用工时
     */
    TALENT_REUSE_MAN_HOUR(2, "人才复用"),
    /**
     * 交付人员工时
     */
    DELIVERER_MAN_HOUR(3, "交付人员");

    private Integer key;
    private String desc;
}
