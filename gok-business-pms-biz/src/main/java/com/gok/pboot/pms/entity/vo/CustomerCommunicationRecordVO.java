package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.Util.PmsDictUtil;
import com.gok.pboot.pms.entity.domain.PmsDictItem;
import com.gok.pboot.pms.entity.domain.ProjectFile;
import com.gok.pboot.pms.enumeration.PmsDictEnum;
import com.google.common.collect.HashMultimap;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * 客户沟通记录
 *
 * <AUTHOR>
 * @date 2023/11/20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class CustomerCommunicationRecordVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户分级
     */
    private String customerGrade;

    /**
     * 交流方式
     */
    private String communicationMethod;

    /**
     * 交流时间
     */
    private String communicationTime;

    /**
     * 时间段
     */
    private String timeSlot;

    /**
     * 交流主题及目标
     */
    private String themeAndObjectives;

    /**
     * 交流内容情况
     */
    private String contentSituation;

    /**
     * 客户反馈及要求
     */
    private String feedbackAndRequirements;

    /**
     * 取得的关键进展
     */
    private String keyProgressMade;

    /**
     * 下一步计划
     */
    private String nextStepForwardPlan;

    /**
     * 客户经理ID
     */
    private Long accountManagerId;

    /**
     * 客户经理
     */
    private String accountManager;

    /**
     * 对方参与人
     */
    private String otherParticipants;

    /**
     * 我方参与人
     */
    private String ourParticipants;

    /**
     * 提交人ID
     */
    private Long submitterId;

    /**
     * 提交人
     */
    private String submitter;

    /**
     * 提交时间
     */
    private LocalDate submissionTime;

    /**
     * 相关附件
     */
    private List<ProjectFile> relateAttachments;

    /**
     * 请求附件的requestid
     */
    private Long requestId;

    /**
     * 流程相关人id
     */
    private Long nodeoperator;

    /**
     * 更新结果
     *
     * @param dictItemMap 字典项map
     * @param r 客户沟通记录
     */
    public static void updateResultParam(
            HashMultimap<String, PmsDictItem> dictItemMap,
            CustomerCommunicationRecordVO r
    ) {

        r.setCustomerGrade(PmsDictUtil.getLabelFromPmsDictItems(
                r.getCustomerGrade(), dictItemMap.get(PmsDictEnum.CUSTOMER_GRADE.getValue())
        ));
        r.setCommunicationMethod(PmsDictUtil.getLabelFromPmsDictItems(
                r.getCommunicationMethod(), dictItemMap.get(PmsDictEnum.COMMUNICATION_METHOD.getValue())
        ));
        r.setTimeSlot(PmsDictUtil.getLabelFromPmsDictItems(
                r.getTimeSlot(), dictItemMap.get(PmsDictEnum.TIME_SLOT.getValue())
        ));

        // 根据需求将交流时间拼接上时间段
        String communicationTime = r.getCommunicationTime();
        String timeSlot = r.getTimeSlot();
        if (communicationTime != null && timeSlot != null) {
            r.setCommunicationTime(communicationTime +" "+ timeSlot);
        }

    }
}
