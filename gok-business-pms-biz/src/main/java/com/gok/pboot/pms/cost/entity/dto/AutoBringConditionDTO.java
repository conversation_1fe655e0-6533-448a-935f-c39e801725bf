package com.gok.pboot.pms.cost.entity.dto;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class AutoBringConditionDTO {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 报价方式(0: 人天,1: 人月,2: 固定费率)
     */
    private Integer quotationType;

    /**
     * 人员属性(0: 国科人员,1: 第三方)
     */
    private Integer personnelAttribute;

}