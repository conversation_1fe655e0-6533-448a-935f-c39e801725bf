package com.gok.pboot.pms.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.bcp.upms.feign.RemoteUserService;
import com.gok.bcp.upms.vo.SysUserVo;
import com.gok.components.common.util.R;
import com.gok.pboot.common.core.constant.CommonConstants;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.entity.dto.DailyPaperMonthlyDTO;
import com.gok.pboot.pms.entity.dto.DailyReviewDTO;
import com.gok.pboot.pms.entity.vo.DailyPaperVO;
import com.gok.pboot.pms.entity.vo.DailyReviewProjectVO;
import com.gok.pboot.pms.entity.vo.MyBaseInfoVO;
import com.gok.pboot.pms.enumeration.YesOrNoEnum;
import com.gok.pboot.pms.mapper.ProjectRiskMapper;
import com.gok.pboot.pms.mapper.ProjectTaskeMapper;
import com.gok.pboot.pms.service.IDailyPaperService;
import com.gok.pboot.pms.service.IDailyReviewService;
import com.gok.pboot.pms.service.MyPanelService;
import com.google.common.collect.ImmutableList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.gok.pboot.pms.enumeration.ApprovalStatusEnum.WTB;

/**
 * 个人面板 service
 *
 * <AUTHOR>
 * @date 2023/8/27
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MyPanelServiceImpl implements MyPanelService {

    private final ProjectRiskMapper riskMapper;
    private final ProjectTaskeMapper taskMapper;
    private final RemoteUserService remoteUserService;
    private final IDailyPaperService dailyPaperService;
    private final IDailyReviewService dailyReviewService;

    // 获取年月的长度
    private static final int LENGTH_OF_THE_MONTH_AND_YEAR = 7;

    @Override
    public MyBaseInfoVO findMyBaseInfo() {
        // 获取用户信息
        MyBaseInfoVO result;
        Long id = SecurityUtils.getUser().getId();
        Integer myTaskNum;
        Integer myRiskNum;

        // 获取风险数量
        myRiskNum = riskMapper.findChargeRiskNum(id);
        // 获取任务数量
        myTaskNum = taskMapper.findMyTaskCount(id);
        // 获取用户基本信息
        R<SysUserVo> userVoR = remoteUserService.getUserById(id);
        if (userVoR.getCode() != CommonConstants.SUCCESS) {
            throw new ServiceException("用户信息远程服务调用异常: " + userVoR.getMsg());
        }
        result = MyBaseInfoVO.of(userVoR.getData(), myRiskNum, myTaskNum);

        return result;
    }

    @Override
    public Integer getNoFillDailyPaperCount() {
        // 构建本月查询数据
        DailyPaperMonthlyDTO request = new DailyPaperMonthlyDTO();
        String today = DateUtil.today();
        String startDate = StringUtils.left(today, LENGTH_OF_THE_MONTH_AND_YEAR);
        request.setStartDate(startDate);
        request.setApprovalStatusList(ImmutableList.of(WTB.getValue()));

        List<DailyPaperVO> result = dailyPaperService.listDailyPaperMonthly(request);

        return result.size();
    }

    @Override
    public Integer getPendingApprovalCount() {
        int pendingApprovalCount;
        DailyReviewDTO request = new DailyReviewDTO();
        request.setAuditStatus(YesOrNoEnum.NO.getValue());
        request.setPageNumber(1);
        request.setPageSize(Integer.MAX_VALUE);

        ApiResult<Page<DailyReviewProjectVO>> result = dailyReviewService.projectDimensionPage(request);
        List<DailyReviewProjectVO> records = result.getData().getRecords();
        pendingApprovalCount = CollectionUtils.isEmpty(records) ?
                0
                :
                records.stream().mapToInt(DailyReviewProjectVO::getApprovalNum).sum();

        return pendingApprovalCount;
    }
}
