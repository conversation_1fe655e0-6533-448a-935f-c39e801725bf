package com.gok.pboot.pms.enumeration;

/**
 * OA内部项目类型枚举
 **/
public enum InternalProjectTypeEnum implements ValueEnum<Integer> {

    GSXXH(0, "公司信息化"),
    TYKCKF(1, "通用课程开发"),
    ZYCPKF(2, "自研产品研发"),
    BZHJJFADZ(3, "标准化解决方案打造"),
    ZXRCGYLGJ(4, "专项人才供应链构建"),
    BMGZ(99, "部门工作");

    //值
    private Integer value;
    //名称
    private String name;

    InternalProjectTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 获取值
     *
     * @return Integer
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取名称
     *
     * @return String
     */
    @Override
    public String getName() {
        return name;
    }

    public static String getNameByVal(Integer value) {
        for (InternalProjectTypeEnum statusEnum : InternalProjectTypeEnum.values()) {
            if (statusEnum.value.equals(value)) {
                return statusEnum.name;
            }
        }
        return "";
    }

    public static Integer getValByName(String name) {
        for (InternalProjectTypeEnum statusEnum : InternalProjectTypeEnum.values()) {
            if (statusEnum.getName().equals(name)) {
                return statusEnum.getValue();
            }
        }
        return null;
    }

}
