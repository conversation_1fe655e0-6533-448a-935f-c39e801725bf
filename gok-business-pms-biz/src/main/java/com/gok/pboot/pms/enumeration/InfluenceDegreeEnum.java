package com.gok.pboot.pms.enumeration;

import lombok.Getter;

/**
 * 项目风险影响程度枚举类
 *
 * <AUTHOR>
 * @since 2023-07-13
 **/
@Getter
public enum InfluenceDegreeEnum implements ValueEnum<Integer> {

    /**
     * 小
     */
    LITTLE(0, "小"),

    /**
     * 中
     */
    MEDIUM(1, "中"),

    /**
     * 大
     */
    LARGE(2, "大"),

    /**
     * 极大
     */
    HUGE(3, "极大");

    /**
     * 值
     */
    private Integer value;

    /**
     * 名称
     */
    private String name;

    InfluenceDegreeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

}
