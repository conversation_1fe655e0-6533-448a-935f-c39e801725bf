package com.gok.pboot.pms.common.validate.constraint;

import com.gok.pboot.pms.common.validate.validator.SelectMothValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 月份校验自定义注解
 *
 * <AUTHOR>
 * @date 2022/8/25
 */
@Documented
@Target({ElementType.PARAMETER, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = SelectMothValidator.class)
public @interface SelectMonth {
    String message() default "选择月份参数不合法";
    Class<?>[] groups() default { };
    Class<? extends Payload>[] payload() default { };
}
