package com.gok.pboot.pms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.bcp.upms.vo.SysMenuVo;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.entity.dto.*;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.service.ICustomerInfoService;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * 客户台账
 *
 * <AUTHOR>
 * @date 2023/11/20
 * @menu 客户台账
 */
@RestController
@RequestMapping("/customer")
@AllArgsConstructor
public class CustomerInfoController {

    private final ICustomerInfoService service;

    /**
     * 获取客户台账-分页
     *
     * @param dto dto 客户台账查询DTO
     * @return {@link ApiResult}<{@link Page}<{@link CustomerListVO}>>
     */
    @PreAuthorize("@pms.hasPermission('CUSTOMER_TB')")
    @PostMapping("/findPage")
    public ApiResult<Page<CustomerListVO>> findPage(@RequestBody  CustomerPageDTO dto) {
        return ApiResult.success(service.findPage(dto));
    }

    /**
     * 获取客户台账数量
     *
     * @return {@link ApiResult}<{@link CustomerNumVO}>
     */
    @PreAuthorize("@pms.hasPermission('CUSTOMER_TB')")
    @GetMapping("/findCustomerNum")
    public ApiResult<CustomerNumVO> findCustomerNum() {
        return ApiResult.success(service.findCustomerNum());
    }


    /**
     * 获取客户台账-分页（通用组件）
     *
     * @param dto dto 客户台账查询DTO
     * @return {@link ApiResult}<{@link Page}<{@link CustomerCommonVO}>>
     */
    @PostMapping("/common/findPage")
    public ApiResult<Page<CustomerCommonVO>> findCommonPage(@RequestBody  CustomerCommonPageDTO dto) {
        return ApiResult.success(service.findCommonPage(dto));
    }

    /**
     * 根据类型发起流程
     *
     * @param dto 客户台账发起流程Dto
     * @return String
     */
    @ApiOperation(value = "根据类型发起流程", notes = "根据类型发起流程")
    @PostMapping("/doCreateRequest")
    public R<String> doCreateRequest(@RequestBody CreateRequestDTO dto) {
        return service.doCreateRequest(dto);
    }


    /**
     * 通过id获取客户台账详情头部
     *
     * @param id 客户台账id
     * @return {@link ApiResult}<{@link CustomerInfoVO}>
     */
    @GetMapping("/head/{id}")
    public ApiResult<CustomerHeadVO> getHeadById(@PathVariable("id") Long id,HttpServletRequest request) {
        return ApiResult.success(service.getHeadById(id, request));
    }

    /**
     * 通过id获取客户台账详情
     *
     * @param id 客户台账id
     * @return {@link ApiResult}<{@link CustomerInfoVO}>
     */
    @GetMapping("/{id}")
    public ApiResult<CustomerInfoVO> findById(@PathVariable("id") Long id) {
        return ApiResult.success(service.findById(id));
    }


    /**
     * 批量获取客户台账详情（通用组件）
     *
     * @param dto 客户台账查询Dto
     * @return {@link ApiResult}<{@link List}<{@link CustomerCommonVO}>
     */
    @PostMapping("/common/findByIds")
    public ApiResult<List<CustomerCommonVO>> findByIds(@RequestBody CustomerRequestDTO dto) {
        return ApiResult.success(service.findByIds(dto.getIds()));
    }

    /**
     * 关注/取消关注
     *
     * @param dto 客户关注DTO
     * @return {@link ApiResult}<{@link String}>
     */
    @PostMapping("/attention")
    public ApiResult<String> attention(@RequestBody CustomerAttentionDTO dto) {
        return ApiResult.success(service.attention(dto));
    }

    /**
     * 模糊查询所在城市（参数没传值，默认前50个）
     *
     * @return {@link ApiResult}<{@link List}<{@link OaHrmcityVO}>
     */
    @GetMapping("/getCityListByName")
    public ApiResult<List<OaHrmcityVO>> getListByName(@RequestParam(value = "name",required = false) String name) {
        return ApiResult.success(service.getCityListByName(name));
    }

    /**
     * 获取详情菜单权限
     * @param request 请求对象
     * @return {@link ApiResult}<{@link List}<{@link SysMenuVo}>>
     * customParam filter_L_customerId 传入的客户id
     * customParam filter_S_permission 传入的权限标识
     * customParam filter_S_menuType 传入的菜单类型（0菜单 1按钮，9系统）
     */
    @GetMapping("/getMenuAuthority")
    @PreAuthorize("@pms.hasPermission('CUSTOMER_TB:DETAIL')")
    public ApiResult<List<SysMenuVo>> getMenuAuthority(HttpServletRequest request) {
        return ApiResult.success(service.getMenuAuthority(request));
    }

}
