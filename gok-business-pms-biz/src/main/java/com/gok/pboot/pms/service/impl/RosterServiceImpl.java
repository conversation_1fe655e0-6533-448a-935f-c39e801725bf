package com.gok.pboot.pms.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.upms.dto.DeptDetailDto;
import com.gok.bcp.upms.feign.RemoteDeptService;
import com.gok.pboot.pms.Util.BaseEntityUtils;
import com.gok.pboot.pms.entity.bo.RosterSelectionBO;
import com.gok.pboot.pms.entity.domain.Roster;
import com.gok.pboot.pms.entity.vo.RosterSelectionVO;
import com.gok.pboot.pms.mapper.RosterMapper;
import com.gok.pboot.pms.service.RosterService;
import com.google.common.collect.ImmutableList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 花名册服务
 *
 * <AUTHOR>
 * @version 1.3.4
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RosterServiceImpl extends ServiceImpl<RosterMapper, Roster> implements RosterService {

    private final RosterMapper rosterMapper;
    private final RemoteDeptService remoteDeptService;

    @Override
    public List<RosterSelectionVO> findSelectionList(String aliasName) {
        List<RosterSelectionBO> selectionBos;
        List<Long> deptIds;
        List<DeptDetailDto> deptList;
        Map<Long, String> deptIdAndNameMap;

        if (StringUtils.isBlank(aliasName)) {
            return ImmutableList.of();
        }
        selectionBos = rosterMapper.findSelectionByAliasNameLike(aliasName);
        if (selectionBos.isEmpty()) {
            return ImmutableList.of();
        }
        deptIds = selectionBos.stream()
                .map(RosterSelectionBO::getDeptId)
                .distinct()
                .collect(Collectors.toList());
        deptList = remoteDeptService.getDeptLeadersByDeptIds(deptIds).getData();
        if (CollectionUtils.isEmpty(deptList)) {
            log.error("请求中台数据失败");

            return ImmutableList.of();
        }
        deptIdAndNameMap = BaseEntityUtils.mapCollectionToMap(
                deptList,
                DeptDetailDto::getDeptId,
                DeptDetailDto::getName
        );

        return selectionBos.stream()
                .map(bo -> RosterSelectionVO.of(bo, deptIdAndNameMap.getOrDefault(bo.getDeptId(), StringUtils.EMPTY)))
                .collect(Collectors.toList());
    }

    @Override
    public Map<Long, Roster> findUserLeaderMap(Collection<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        List<Roster> rosterList = rosterMapper.selectBatchIds(userIds);

        Map<Long, Long> leaderIdMap = rosterList.stream().collect(Collectors.toMap(Roster::getId, Roster::getLeaderId));
        Collection<Long> leaderIds = leaderIdMap.values();
        Map<Long, Roster> leaderMap;
        if (CollUtil.isNotEmpty(leaderIds)) {
            leaderMap = rosterMapper.selectBatchIds(leaderIds).stream()
                    .collect(Collectors.toMap(Roster::getId, e -> e));
        } else {
            leaderMap = Collections.emptyMap();
        }

        Map<Long, Roster> resMap = new HashMap<>(userIds.size());
        leaderIdMap.forEach((userId, leaderId) -> {
            Roster roster = leaderMap.get(leaderId);
            resMap.put(userId, roster);
        });
        return resMap;
    }

    @Override
    public Map<Long, List<Roster>> findUserUnderlingMap(Collection<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        List<Roster> rosterList = rosterMapper.selectList(Wrappers.<Roster>lambdaQuery()
                .in(Roster::getLeaderId, userIds));

        Map<Long, List<Roster>> resMap = rosterList.stream().collect(Collectors.groupingBy(Roster::getLeaderId));
        return resMap;
    }
}
