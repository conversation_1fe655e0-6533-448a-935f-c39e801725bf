package com.gok.pboot.pms.eval.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 获取项目人员整体评价分布
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class EvalProjectOverallStaffVO {

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 整体评分
     */
    private BigDecimal totalScore;

    /**
     * 整体等级
     */
    private String projectGrade;

    /**
     * 评价状态
     * {@link com.gok.pboot.pms.eval.enums.EvalStatusEnum}
     */
    private Integer evalStatus;

    private String evalStatusName;

    /**
     * 退回原因
     */
    private String returnReason;

    /**
     * 人员分布详情
     */
    private List<EvalProjectOverallStaffDistributionVO> distributionList;
}