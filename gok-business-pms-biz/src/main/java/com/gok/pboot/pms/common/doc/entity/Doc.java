package com.gok.pboot.pms.common.doc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.TenantBeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = false)
@TableName("doc_doc")
@Data
public class Doc extends TenantBeanEntity<Long> {

    private String type;
    private String docName;
    private String path;

    @TableField(exist = false)
    private byte[] body;

    @TableField(exist = false)
    private String workCode;

}
