package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 收入结算发起结算审批DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CostIncomeSettlementRequestDTO {

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * ids
     */
    private List<Long> ids;

    /**
     * 是否汇总
     */
    private Boolean isSummary;
}
