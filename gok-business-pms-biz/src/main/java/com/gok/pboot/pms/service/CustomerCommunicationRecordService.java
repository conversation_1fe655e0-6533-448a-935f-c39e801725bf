package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.dto.CustomerCommunicationRecordDTO;
import com.gok.pboot.pms.entity.vo.CCRExportExcelVO;
import com.gok.pboot.pms.entity.vo.CustomerCommunicationRecordVO;

import java.util.List;

/**
 * 客户沟通记录
 *
 * <AUTHOR>
 * @date 2023/11/20
 */
public interface CustomerCommunicationRecordService {

    /**
     * 获取客户沟通记录-分页
     *
     * @param pageRequest 分页请求对象
     * @param filter 客户沟通记录查询DTO
     * @return {@link Page}<{@link CustomerCommunicationRecordVO}>
     */
    Page<CustomerCommunicationRecordVO> findPage(PageRequest pageRequest, CustomerCommunicationRecordDTO filter);

    /**
     * 通过id获取客户沟通记录详情
     *
     * @param id 客户沟通记录id
     * @return {@link CustomerCommunicationRecordVO}
     */
    CustomerCommunicationRecordVO findById(Long id);

    /**
     * 导出列表数据
     *
     * @param pageRequest 分页请求对象
     * @param filter 客户沟通记录查询DTO
     * @return {@link List}<{@link CCRExportExcelVO}>
     */
    List<CCRExportExcelVO> export(PageRequest pageRequest, CustomerCommunicationRecordDTO filter);

    /**
     * 获取文件
     *
     * @param requestId 请求id
     * @return {@link ApiResult}<{@link Object}>
     */
    ApiResult<Object> getFile(Long requestId);
}
