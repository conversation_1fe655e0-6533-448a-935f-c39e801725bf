package com.gok.pboot.pms.entity.dto;

import com.gok.pboot.pms.cost.enums.ChangeContentEnum;
import com.gok.pboot.pms.enumeration.OAFormTypeEnum;
import lombok.Data;

/**
 * 项目里程碑发起流程Dto
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
@Data
public class CreateRequestDTO {

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * OA表格枚举
     * {@link OAFormTypeEnum}
     */
    private Integer formType;

    /**
     * 变更内容字符串（0=售前成本，1=A表成本，2=B表成本，3=项目目标，4=现金流）
     * {@link ChangeContentEnum}
     */
    private String changeContentStr;

}
