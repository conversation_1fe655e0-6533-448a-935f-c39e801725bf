package com.gok.pboot.pms.cost.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 成本工单产值 vo
 *
 * <AUTHOR>
 * @date 2025/03/28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CostDeliverTaskIncomeVo {

    /**
     * 当前产值
     */
    private BigDecimal currentIncome;

    /**
     * 项目估计总产值
     */
    private BigDecimal estimatedTotalIncome;
}
