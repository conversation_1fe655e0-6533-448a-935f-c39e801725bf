package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.data.datascope.BaseMapper;
import com.gok.pboot.pms.entity.PayAttendanceData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 工资条考勤数据同步 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-14
 */
@Mapper
public interface PayAttendanceDataMapper extends BaseMapper<PayAttendanceData> {
    /**
     * 分页查询
     *
     * @param page   分页
     * @param filter 过滤条件
     * @return void
     */
    Page<PayAttendanceData> findList(Page page, @Param("filter") PayAttendanceData filter);

    /**
     * 实体条件查询
     *
     * @param filter 过滤条件
     * @return void
     */
    List<PayAttendanceData> findList(@Param("filter") PayAttendanceData filter);

    /**
     * 批量插入
     *
     * @param poList 实体集合
     * @return: int
     */
    int batchSave(@Param("poList") List<PayAttendanceData> poList);


    /**
     * 批量修改
     *
     * @param list 实体集合
     * @return: int
     */
    int batchUpdate(@Param("list") List<PayAttendanceData> list);

    /**
     * 批量修改不为空字段
     *
     * @param list id集合
     * @return: int
     */
    int updateBatch(@Param("list") List<PayAttendanceData> list);

    /**
     * 根据时间查询考勤天数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param userIds   用户Id
     * @create by yzs at 2023/6/14
     * @return: java.util.List<com.gok.pboot.pms.entity.PayAttendanceData>
     */
    List<PayAttendanceData> findByDateRangeAndUserIds(@Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime, @Param("userIds") Collection<Long> userIds);

    /**
     * 根据工号集合和考勤月份集合批量查询
     *
     * @param workCodes
     * @param payrollTimes
     * @return
     */
    List<PayAttendanceData> findByWorkCodeAndPayrollTime(@Param("workCodes") List<String> workCodes,
                                                         @Param("payrollTimes") List<String> payrollTimes);

}
