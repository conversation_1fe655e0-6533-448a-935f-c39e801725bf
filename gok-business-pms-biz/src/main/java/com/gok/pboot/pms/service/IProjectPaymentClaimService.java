package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.entity.domain.ProjectPaymentClaim;
import com.gok.pboot.pms.entity.vo.BusinessBlockVO;

import java.util.List;

/**
 * 项目回款认领 Service
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
public interface IProjectPaymentClaimService extends IService<ProjectPaymentClaim> {

    /**
     * 获取列表中的业务板块
     *
     * @return {@link List}<{@link BusinessBlockVO}>
     */
    List<BusinessBlockVO> queryBusinessBlock();
}
