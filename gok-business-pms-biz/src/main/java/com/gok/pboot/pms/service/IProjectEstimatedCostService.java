package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.entity.domain.ProjectEstimatedCost;

import java.util.List;

/**
 * 项目预估成本表（Oa项目预算台账）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-17 11:11:29
 */
public interface IProjectEstimatedCostService extends IService<ProjectEstimatedCost> {
    /**
     * 项目id查询
     * @param id
     */
    List<ProjectEstimatedCost> getByProjectId(Long id);
}

