package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.entity.domain.ProjectFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目附件表
 *
 * <AUTHOR>
 * @date 2023/11/22
 */
@Mapper
public interface ProjectFileMapper extends BaseMapper<ProjectFileMapper> {

    /**
     * 通过参数过滤获取附件列表
     *
     * @param filter
     * @return {@link List}<{@link ProjectFile}>
     */
    List<ProjectFile> selectListByParams(@Param("filter") ProjectFile filter);

}
