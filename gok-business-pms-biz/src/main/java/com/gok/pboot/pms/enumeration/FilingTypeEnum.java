package com.gok.pboot.pms.enumeration;

/**
 * 归档类型枚举
 **/
public enum FilingTypeEnum implements ValueEnum<Integer> {
    /**
     * 未归档
     */
    WGD(0, "未归档"),
    /**
     * 已归档
     */
    YGD(1, "已归档"),
    ;

    //值
    private Integer  value;
    //名称
    private String name;

    FilingTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 获取值
     *
     * @return Integer
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取名称
     *
     * @return String
     */
    @Override
    public String getName() {
        return name;
    }

    public static String getNameByVal(Integer value) {
        for (FilingTypeEnum statusEnum : FilingTypeEnum.values()) {
            if (statusEnum.value.equals(value)) {
                return statusEnum.name;
            }
        }
        return "";
    }
    public static Integer getValByName(String name) {
        for (FilingTypeEnum statusEnum : FilingTypeEnum.values()) {
            if (statusEnum.getName().equals(name)) {
                return statusEnum.getValue();
            }
        }
        return null;
    }
}
