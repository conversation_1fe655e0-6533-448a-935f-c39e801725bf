package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 基线版本记录表
 *
 * <AUTHOR>
 * @date 2025/01/15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "cost_baseline_version_record")
@Accessors(chain = true)
public class CostBaselineVersionRecord extends BeanEntity<Long> implements Serializable {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 基线版本
     */
    private String versionName;

    /**
     * 报价与毛利测算版本ID
     */
    private Long quotationVersionId;

    /**
     * 报价与毛利测算版本名称
     */
    private String quotationVersionName;

    /**
     * 目标版本ID
     */
    private Long targetVersionId;

    /**
     * 目标版本名称
     */
    private String targetVersionName;

    /**
     * 成本版本ID
     */
    private Long costVersionId;

    /**
     * 成本版本名称
     */
    private String costVersionName;

    /**
     * 关联现金流版本ID
     */
    private Long cashPlanVersionId;

    /**
     * 关联现金流版本名称
     */
    private String cashPlanVersionName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 关联流程ID
     */
    @TableField(exist = false)
    private Long requestId;

}