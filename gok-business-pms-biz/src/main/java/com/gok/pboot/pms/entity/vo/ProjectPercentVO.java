package com.gok.pboot.pms.entity.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.gok.pboot.pms.common.serializer.TwoDecimalToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023/2/22
 * 返回前端的排名实体
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectPercentVO {

    /**
     * 名称
     */
    private String name;

    /**
     * 百分比
     */
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    private BigDecimal percent;

}
