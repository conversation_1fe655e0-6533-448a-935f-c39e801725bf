package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.time.LocalDate;

/**
 * 项目会议纪要导出Vo类
 *
 * <AUTHOR>
 * @since 2023-07-13
 **/
@Data
public class ProjectMeetingExportExcelVO {

    /**
     * 主键id
     */
    @ExcelIgnore
    private Long id;

    /**
     * 会议名称
     */
    @ExcelProperty({"会议名称"})
    @ColumnWidth(20)
    private String name;

    /**
     * 召集人
     */
    @ExcelProperty({"召集人"})
    @ColumnWidth(20)
    private String convener;

    /**
     * 会议日期
     * 格式为：yyyy-MM-dd
     */
    @ExcelProperty({"会议日期"})
    @ColumnWidth(20)
    private LocalDate meetingDate;

    /**
     * 开始时间
     */
    @ExcelProperty({"起止时间"})
    @ColumnWidth(20)
    private String time;

    /**
     * 记录人
     */
    @ExcelProperty({"记录人"})
    @ColumnWidth(20)
    private String recorder;

    /**
     * 会议地点
     */
    @ExcelProperty({"会议地点"})
    @ColumnWidth(20)
    private String place;

    /**
     * 参会人员
     */
    @ExcelProperty({"参会人员"})
    @ColumnWidth(50)
    private String member;

    /**
     * 会议目标
     */
    @ExcelProperty({"会议目标"})
    @ColumnWidth(50)
//    @ContentStyle(wrapped = true)
    private String objective;

    /**
     * 会议过程
     */
    @ExcelProperty({"会议过程"})
    @ColumnWidth(50)
//    @ContentStyle(wrapped = true)
    private String process;

    /**
     * 会议决议
     */
    @ExcelProperty({"会议决议"})
    @ColumnWidth(50)
//    @ContentStyle(wrapped = true)
    private String resolution;

    /**
     * 待办事项
     */
    @ExcelProperty({"待办事项"})
    @ColumnWidth(50)
//    @ContentStyle(wrapped = true)
    private String backlog;

}
