package com.gok.pboot.pms.cost.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gok.pboot.pms.Util.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 成本任务返回信息 VO
 *
 * <AUTHOR>
 * @date 2025/01/16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CostTaskReturnInfoVO {

    /**
     * 工单名称
     */
    private String taskName;

    /**
     * 退回人
     */
    private String returnUserName;

    /**
     * 退回原因
     */
    private String returnReason;

    /**
     * 退回时间
     */
    @JsonFormat(pattern = DateUtil.TIME_FORMAT, timezone = DateUtil.GMT_8)
    private LocalDateTime returnTime;
}
