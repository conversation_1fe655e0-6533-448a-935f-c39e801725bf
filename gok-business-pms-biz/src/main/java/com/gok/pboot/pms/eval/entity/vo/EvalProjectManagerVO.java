package com.gok.pboot.pms.eval.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 项目经理评价VO类
 *
 * <AUTHOR>
 * @date 2025/05/08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class EvalProjectManagerVO {

    /**
     * ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目整体评价表ID
     */
    private Long overviewId;

    /**
     * 指标类型
     * {@link com.gok.pboot.pms.eval.enums.EvalIndexTypeEnum}
     */
    private Integer indexType;

    /**
     * 指标类型名称
     * {@link com.gok.pboot.pms.eval.enums.AssessmentProjectEnum}
     */
    private String indexTypeName;

    /**
     * 评定项目
     */
    private Integer assessmentProject;

    /**
     * 评定项目名称
     */
    private String assessmentProjectName;

    /**
     * 权重
     */
    private BigDecimal weight;

    /**
     * 总支撑官/职能领导评分
     */
    private BigDecimal managerScore;

    /**
     * PMO评分
     */
    private BigDecimal pmoScore;

    /**
     * 得分
     */
    private BigDecimal score;

} 