package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.bcp.upms.vo.SysMenuVo;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.entity.domain.ProjectAttention;
import com.gok.pboot.pms.entity.dto.RoleProjectPageDto;
import com.gok.pboot.pms.entity.vo.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 项目 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-19
 */
public interface IProjectService {


    /**
     * 分页查询
     *
     * @param pageRequest  分页请求
     * @param filter  过滤条件
     * @return 分页记录
     */
    Page<ProjectVO> findPage2(PageRequest pageRequest, Map
            <String, Object> filter);

    ///**
    // * 分页查询
    // *
    // * @param pageRequest  分页请求
    // * @param filter  过滤条件
    // * @return 分页记录
    // */
    //Page<ProjectVO> findPage(PageRequest pageRequest, Map
    //        <String, Object> filter);
    /**
     * 查询详情
     *
     * @param id 唯一标识
     * @return ApiResult
     */
    ApiResult getByIdCount(Long id);


    /**
     * ~ 根据当前用户ID查找日报条目中所用的项目对象 ~
     * @return java.util.List<com.gok.pboot.pms.entity.vo.ProjectInDailyPaperEntryVO>
     * <AUTHOR>
     * @date 2022/8/25 11:14
     */
    @Deprecated
    List<ProjectWithTasksInDailyPaperEntryVO> findByCurrUserIdForDailyPaperEntry(LocalDate date);

    /**
     * 根据当前用户ID查找日报条目中所用的项目对象(包括新、旧任务)
     *
     * @param date 日期
     * @return {@link List}<{@link ProjectWithTasksInDailyPaperEntryVO}>
     */
    @Deprecated
    List<ProjectWithTasksInDailyPaperEntryVO> findByCurrUserIdForDailyPaperEntryMix(LocalDate date);

    /**
     * 根据当前用户ID查找日报条目中所用的项目对象(新任务)
     *
     * @param date 日期
     * @return {@link List}<{@link ProjectWithTasksInDailyPaperEntryVO}>
     */
    @Deprecated
    List<ProjectWithTasksInDailyPaperEntryVO> findByCurrUserIdForDailyPaperEntryNew(LocalDate date);

    /**
     * 工时填报查询所有当前用户可用的所有项目（新旧任务统一，去除旧任务标识）
     *
     * @param date 填报日期
     * @return {@link List}<{@link ProjectWithTasksInDailyPaperEntryUnifyVO}>
     */
    List<ProjectWithTasksInDailyPaperEntryUnifyVO> findCurrentForDailyPaperEntryUnify(LocalDate date);

    void projectTaskPersonnel(String para);
    /**
     * 子任务管理，项目工时汇总通过不通权限查询项目
     * @create by yzs at 2023/4/20
     * @param:page 分页参数
     * @return: com.gok.pboot.pms.common.base.ApiResult<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.gok.pboot.pms.entity.vo.ProjectVO>>
     */
    Page<ProjectVO> findByPermissions(Page page, RoleProjectPageDto dto) throws ClassNotFoundException, IllegalAccessException, InstantiationException;
    /**
     *  每隔10分钟更新项目的销售人员赋予销售角色
     * @para 0:更新近期数据，1：全量更新
     * @return {@link ApiResult}
     */
    R syncOaSalesTask(String para);
    Long attentionProject(List<Long> projectIds);

    Page<ProjectAttentionVO> findAttentionProject(PageRequest pageRequest, Map
            <String, Object> filter);

    /**
     * 通过Id查询项目基础信息
     * @param id 项目id
     * @return {@link R}<{@link ProjectBaseInfoVO}>
     */
    ApiResult<ProjectBaseInfoVO> getProjectBaseInfoById(Long id);

    /**
     * 获取菜单权限
     *
     * @param request 请求对象
     * @return {@link List}<{@link SysMenuVo}>
     */
    List<SysMenuVo> getMenuAuthority(HttpServletRequest request);

    /**
     * 移除关注
     * @param projectId
     */
    void removeAttentionProject(Long projectId);

    /**
     * 通过Id查询内部项目概况
     *
     * @param id 项目ID
     * @return {@link R}<{@link ProjectInfoVO}>>
     */
    ProjectOverViewInnerVO getProjectBaseInfoInnerById(Long id);
    /**
     * 查询已关注项目
     * @return {@link ApiResult<List<ProjectAttention>>}
     */
    List<ProjectVO> getAttentionProjectList();

    /**
     * 关注目标项目
     * @param projectId 项目id
     * @return 关注结果
     */
    Boolean attentionProject(Long projectId);
}
