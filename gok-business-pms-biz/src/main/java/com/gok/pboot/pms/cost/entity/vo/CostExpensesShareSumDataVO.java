package com.gok.pboot.pms.cost.entity.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 人力外包-费用分摊合计数据
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CostExpensesShareSumDataVO {

    /**
     * 费用总额
     */
    @ExcelProperty("费用总额")
    @ColumnWidth(value = 30)
    private String expensesTotal;

    /**
    * 客户承担费用
    */
    @ExcelProperty("客户承担费用")
    @ColumnWidth(value = 30)
    private String customerBearsCost;

    /**
    * 国科承担费用
    */
    @ExcelProperty("国科承担费用")
    @ColumnWidth(value = 30)
    private String gokBearsCost;

}