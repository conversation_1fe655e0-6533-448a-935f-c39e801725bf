package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.module.excel.api.domain.vo.ErrorMessage;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.BcpLoggerUtils;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.Util.PageUtils;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.constant.FunctionConstants;
import com.gok.pboot.pms.entity.domain.*;
import com.gok.pboot.pms.entity.dto.ProjectSystemProcessInfoDTO;
import com.gok.pboot.pms.entity.dto.ProjectWeeklyAllExcelDTO;
import com.gok.pboot.pms.entity.dto.ProjectWeeklyDTO;
import com.gok.pboot.pms.entity.dto.ProjectWeeklyExcelDTO;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.enumeration.*;
import com.gok.pboot.pms.enumeration.facade.BusinessStatusEnum;
import com.gok.pboot.pms.handler.perm.PmsRetriever;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import com.gok.pboot.pms.mapper.ProjectMapper;
import com.gok.pboot.pms.mapper.ProjectRiskMapper;
import com.gok.pboot.pms.mapper.ProjectWeeklyMapper;
import com.gok.pboot.pms.service.IProjectSystemProcessInfoService;
import com.gok.pboot.pms.service.IProjectWeeklyService;
import com.gok.pboot.pms.service.IProjectWorkHoursService;
import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BindingResult;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.gok.pboot.pms.service.IProjectSystemProcessInfoService.WEEKLY_PROCESS_PREFIX;
import static com.gok.pboot.pms.service.IProjectSystemProcessInfoService.WEEKLY_PROCESS_TIME_FORMAT;


@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectWeeklyServiceImpl extends ServiceImpl<ProjectWeeklyMapper, ProjectWeekly> implements IProjectWeeklyService {

    private final IProjectSystemProcessInfoService systemProcessService;
    private final IProjectWorkHoursService workHoursService;
    private final PmsRetriever pmsRetriever;
    private final ProjectMapper projectMapper;
    private final ProjectRiskMapper projectRiskMapper;
    private final ProjectInfoMapper projectInfoMapper;
    private final BcpLoggerUtils bcpLoggerUtils;

    private static final int EXPORT_MIN_PAGE_SIZE = 50;
    private static final String PARAM_PROJECT_ID = "projectId";
    private static final String PARAM_START_DATE = "startDate";
    private static final String PATTERN_DATE_TIME = "yyyy-MM-dd HH:mm:ss";
    private static final String KEY_TEMPLATE = "{}-{}-{}";
    private static final String PARAM_USER_ID = "userId";
    private static final String PARAM_END_DATE = "endDate";

    @Override
    public Page<ProjectWeeklyVO> findPage(PageRequest pageRequest, Map<String, Object> filter) {
        Page<ProjectWeeklyVO> listPage = baseMapper.findListPage(new Page<>(pageRequest.getPageNumber(),
                pageRequest.getPageSize()), filter
        );
        List<ProjectWeeklyVO> records = listPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return listPage;
        }

        List<Map<String, Object>> filterList = new ArrayList<>();
        records.forEach(r -> {
            Map<String, Object> filterItem = new HashMap<>();
            filterItem.put(PARAM_PROJECT_ID, r.getProjectId());
            filterItem.put(PARAM_START_DATE, r.getReportStart());
            filterItem.put(PARAM_END_DATE, r.getReportEnd());
            filterList.add(filterItem);
        });

        Map<String, ProjectWeeklyPreSaveDataVo> projectWorkHoursVoMap = batchFindPreSaveData(filterList);
        // 获取工时数据
        for (ProjectWeeklyVO record : records) {
            String key = StrUtil.format(KEY_TEMPLATE, record.getProjectId(), record.getReportStart(), record.getReportEnd());
            ProjectWeeklyPreSaveDataVo preSaveData = projectWorkHoursVoMap.getOrDefault(key,
                    ProjectWeeklyPreSaveDataVo.builder()
                            .projectId(record.getProjectId())
                            .currentHours(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP))
                            .totalHours(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP))
                            .build()
            );
            record.setCurrentHours(preSaveData.getCurrentHours());
            record.setTotalHours(preSaveData.getTotalHours());
        }

        setProjectRiskBaseList(records);

        return listPage;
    }

    @Override
    @Transactional
    public Page<ProjectWeeklyVO> findAllPage(PageRequest pageRequest, Map<String, Object> filter) {
        List<ProjectWeeklyReading> newReadList = new ArrayList<>();
        Long userId = SecurityUtils.getUser().getId();
        List<ProjectWeeklyVO> allList;
        List<ProjectWeeklyReading> readList;
        List<Long> pwIds;
        Page<ProjectWeeklyVO> listPage;
        //获取用户数据权限
        pmsRetriever.calcProjectIdsAvailable(filter);
        filter.put(PARAM_USER_ID, userId);
        // 在分页查询前，将该用户所有的周报【列表】、【已读表】查询出来暂存
        allList = baseMapper.findAllList(filter);
        readList = baseMapper.findReadList(userId);
        pwIds = readList.stream()
                .map(ProjectWeeklyReading::getPwId)
                .collect(Collectors.toList());

        // 进行分页查询，将分页查询的信息对比【已读表】，写入isRead字段
        listPage = baseMapper.findListAllPage(new Page<>(pageRequest.getPageNumber(),
                pageRequest.getPageSize()), filter);
        for (ProjectWeeklyVO record : listPage.getRecords()) {
            Long id = record.getId();
            if (pwIds.contains(id)) {
                record.setIsRead(YesOrNoEnum.YES.getValue());
            } else {
                record.setIsRead(YesOrNoEnum.NO.getValue());
            }
        }

        // 【列表】同步到【已读表】里
        for (ProjectWeeklyVO projectWeeklyVO : allList) {
            Long id = projectWeeklyVO.getId();
            if (!pwIds.contains(id)) {
                ProjectWeeklyReading projectWeeklyReading = ProjectWeeklyReading.of(userId, id);
                BaseBuildEntityUtil.buildInsert(projectWeeklyReading);
                newReadList.add(projectWeeklyReading);
            }
        }
        if (!newReadList.isEmpty()) {
            baseMapper.addReadList(newReadList);
        }

        // 补全响应字段
        List<ProjectWeeklyVO> records = listPage.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            List<Long> projectIds = new ArrayList<>();
            // 组装工时查询条件
            List<Map<String, Object>> filterList = new ArrayList<>();
            records.forEach(r -> {
                projectIds.add(r.getProjectId());
                Map<String, Object> filterItem = new HashMap<>();
                filterItem.put(PARAM_PROJECT_ID, r.getProjectId());
                filterItem.put(PARAM_START_DATE, r.getReportStart());
                filterItem.put(PARAM_END_DATE, r.getReportEnd());
                filterList.add(filterItem);

            });
            Map<String, ProjectWeeklyPreSaveDataVo> projectWorkHoursVoMap = batchFindPreSaveData(filterList);

            // 获取工时数据
            for (ProjectWeeklyVO record : records) {
                String key = StrUtil.format(KEY_TEMPLATE, record.getProjectId(), record.getReportStart(), record.getReportEnd());
                ProjectWeeklyPreSaveDataVo preSaveData = projectWorkHoursVoMap.getOrDefault(key,
                        ProjectWeeklyPreSaveDataVo.builder()
                                .projectId(record.getProjectId())
                                .currentHours(BigDecimal.ZERO.setScale(2))
                                .totalHours(BigDecimal.ZERO.setScale(2))
                                .build()
                );
                // 去除当期新增工时、累计工时计算逻辑，直接获取数据库已存在数据
                record.setCurrentHours(preSaveData.getCurrentHours());
                record.setTotalHours(preSaveData.getTotalHours());
                record.setProjectStatusName(StringUtils.EMPTY.equals(record.getProjectStatus()) ? StringUtils.EMPTY :
                        BusinessStatusEnum.getNameByVal(Integer.parseInt(record.getProjectStatus())));
            }
        }

        // 添加风险基础信息列表
        setProjectRiskBaseList(records);

        return listPage;
    }

    @Override
    public WeeklySubmitNum submitNum(Map<String, Object> filter) {
        WeeklySubmitNum result = new WeeklySubmitNum();
        List<ProjectWeeklyVO> weeklyList;
        int submit = 0;
        int unSubmit = 0;
        int lateSubmit = 0;
        Collection<Long> projectIds;

        // 获取用户部门权限列表
        Long userId = SecurityUtils.getUser().getId();
        pmsRetriever.calcProjectIdsAvailable(filter);
        filter.put(PARAM_USER_ID, userId);
        projectIds = pmsRetriever.getProjectIdsAvailable();
        // 获取这期间的所有周报信息
        weeklyList = baseMapper.findAllList(filter);
        if(CollUtil.isNotEmpty(weeklyList)){
            weeklyList=weeklyList.stream()
                    .filter(w->StrUtil.isNotBlank(w.getProjectStatus())&&!w.getProjectStatus().equals("1")
                            &&!w.getProjectStatus().equals("6")&&!w.getProjectStatus().equals("7"))
                    .collect(Collectors.toList());
        }

        // 进行信息比较
        // 1、统计未提交数
        weeklyList=weeklyList.stream()
                .distinct()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(ProjectWeeklyVO::getProjectId, vo -> vo, (vo1, vo2) -> vo1),
                        map -> new ArrayList<>(map.values())
                ));
        List<Long> projectId = weeklyList.stream().map(ProjectWeeklyVO::getProjectId).distinct().collect(Collectors.toList());
        List<Long> unSubmitProjectId = CollectionUtil.subtractToList(projectIds, projectId);
        if (CollectionUtil.isNotEmpty(unSubmitProjectId)) {
            List<ProjectInfo> projectInfoList = projectInfoMapper.selectBatchIds(unSubmitProjectId);
            //过滤1=商机终止，6=结项，7=异常终止
            projectInfoList=projectInfoList.stream()
                    .filter(w->StrUtil.isNotBlank(w.getProjectStatus())&&!w.getProjectStatus().equals("1")
                            &&!w.getProjectStatus().equals("6")&&!w.getProjectStatus().equals("7"))
                    .collect(Collectors.toList());
            unSubmit = projectInfoList.size();
        }
        if (CollectionUtil.isNotEmpty(weeklyList)) {
            // 2、统计已提交数、滞后提交数
            lateSubmit = (int) weeklyList.stream()
                    .filter(vo -> {
                        LocalDate reportEndDate = vo.getReportEnd();
                        LocalDateTime ctimeDateTime = LocalDateTime.parse(vo.getCtime(), DateTimeFormatter.ofPattern(PATTERN_DATE_TIME));
                        return ctimeDateTime.toLocalDate().isAfter(reportEndDate);
                    }).count();
            submit = weeklyList.size() - lateSubmit;
        }

        // 插入返回值
        result.setSubmit(submit);
        result.setUnSubmit(unSubmit);
        result.setLateSubmit(lateSubmit);

        return result;
    }

    @Override
    public  Boolean saveOrUpdateProjectWeekly(ProjectWeeklyDTO dto){
        ProjectWeekly projectWeekly = ProjectWeekly.saveOrUpdate(dto);
        if (!Optional.ofNullable(dto.getId()).isPresent()) {
            // 同步新增项目周报过程动态
            this.syncProcessInfo(ProcessInfoStatusEnum.ARCHIVE, Arrays.asList(projectWeekly));
        }
        boolean b = this.saveOrUpdate(projectWeekly);
        if(dto.getId()==null){
            //日志
            bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_PROJECT_WEEKLY_REPORT, LogContentEnum.NEW_WEEKLY_REPORT,
                    dto.getProjectName(),dto.getReportStart()+"~"+dto.getReportEnd());
        }else{
            //日志
            bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_PROJECT_WEEKLY_REPORT, LogContentEnum.EDIT_WEEKLY_REPORT,
                    dto.getProjectName(),dto.getReportStart()+"~"+dto.getReportEnd());
        }
      return  b;
    }

    @Override
    public   Object deleteById(Long id){
        ProjectWeekly projectWeekly = this.getById(id);
        if(Optional.ofNullable(projectWeekly).isPresent()){
            //日志
            bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_PROJECT_WEEKLY_REPORT, LogContentEnum.DELETE_WEEKLY_REPORT,
                    projectWeekly.getProjectName(),projectWeekly.getReportStart()+"~"+projectWeekly.getReportEnd());
            this.removeById(id);
        }
       return projectWeekly;

    }

    @Override
    public Page<WeeklySubmitInfo> submitInfo(PageRequest pageRequest, Map<String, Object> filter) {

        // 获取用户部门权限列表
        Long userId = SecurityUtils.getUser().getId();
        pmsRetriever.calcProjectIdsAvailable(filter);
        filter.put(PARAM_USER_ID, userId);

        Page<WeeklySubmitInfo> pageResult;
        List<WeeklySubmitInfo> result = new ArrayList<>();
        Integer submitState = (Integer) filter.get("submitState");
        String reportEnd = String.valueOf(filter.get("reportEnd"));
        String reportStart = String.valueOf(filter.get("reportStart"));
        List<ProjectWeeklyVO> weeklyList = baseMapper.findAllList(filter);

        // 请求参数携带项目状态，根据状态查找对应的项目
        // 1查询未提交项目数据
        if (WeeklySubmitStateEnum.UN_SUBMIT.getValue().equals(submitState)) {
            Collection<Long> allProjectId = pmsRetriever.getProjectIdsAvailable();
            List<Long> projectId = weeklyList.stream().map(ProjectWeeklyVO::getProjectId).distinct().collect(Collectors.toList());
            List<Long> unSubmitProjectId = CollectionUtil.subtractToList(allProjectId, projectId);
            List<ProjectInfo> projectInfoList = projectInfoMapper.selectBatchIds(unSubmitProjectId);
            //过滤1=商机终止，6=结项，7=异常终止
            projectInfoList=projectInfoList.stream()
                    .filter(w->StrUtil.isNotBlank(w.getProjectStatus())&&!w.getProjectStatus().equals("1")
                            &&!w.getProjectStatus().equals("6")&&!w.getProjectStatus().equals("7"))
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(projectInfoList)) {
                projectInfoList.forEach(p -> {
                    WeeklySubmitInfo weeklySubmitInfo = WeeklySubmitInfo.of(p);
                    weeklySubmitInfo.setReportStartEnd(reportStart + "~" + reportEnd);
                    weeklySubmitInfo.setSubmitState(submitState);
                    weeklySubmitInfo.setSubmitStateText(EnumUtils.getNameByValue(WeeklySubmitStateEnum.class, submitState));
                    result.add(weeklySubmitInfo);
                });
            }
        }

        // 2 查询已提交项目数据
        if (WeeklySubmitStateEnum.SUBMIT.getValue().equals(submitState)) {
            List<ProjectWeeklyVO> weeklyVOList = weeklyList.stream()
                    .filter(vo -> {
                        LocalDate reportEndDate = vo.getReportEnd();
                        LocalDateTime ctimeDateTime = LocalDateTime.parse(vo.getCtime(), DateTimeFormatter.ofPattern(PATTERN_DATE_TIME));
                        return !ctimeDateTime.toLocalDate().isAfter(reportEndDate);
                    }).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(weeklyVOList)) {
                setResultByWeeklySubmit(result, submitState, weeklyVOList);
            }
        }

        // 3 查询滞后提交项目数据
        if (WeeklySubmitStateEnum.LATE_SUBMIT.getValue().equals(submitState)) {
            List<ProjectWeeklyVO> weeklyVOList = weeklyList.stream()
                    .filter(vo -> {
                        LocalDate reportEndDate = vo.getReportEnd();
                        LocalDateTime ctimeDateTime = LocalDateTime.parse(vo.getCtime(), DateTimeFormatter.ofPattern(PATTERN_DATE_TIME));
                        return ctimeDateTime.toLocalDate().isAfter(reportEndDate);
                    }).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(weeklyVOList)) {
                setResultByWeeklySubmit(result, submitState, weeklyVOList);
            }
        }

        // 封装返回值
        pageResult = PageUtils.page(result, pageRequest);

        return pageResult;
    }

    /**
     * 设置有提交周报的项目信息返回值
     *
     * @param result       返回值
     * @param submitState  周报状态
     * @param weeklyVOList 周报信息
     */
    private void setResultByWeeklySubmit(List<WeeklySubmitInfo> result, Integer submitState, List<ProjectWeeklyVO> weeklyVOList) {
        List<Long> lateSubmitProjectId = weeklyVOList.stream()
                .map(ProjectWeeklyVO::getProjectId)
                .distinct()
                .collect(Collectors.toList());
        List<ProjectInfo> projectInfoList = projectInfoMapper.selectBatchIds(lateSubmitProjectId);
        Map<Long, String> projectIdToManageUserMap = projectInfoList.stream()
                .collect(Collectors.toMap(ProjectInfo::getId, p -> StringUtils.isEmpty(p.getManagerUserName()) ? "" : p.getManagerUserName()));
        weeklyVOList.forEach(w -> {
            Long projectId = w.getProjectId();
            WeeklySubmitInfo weeklySubmitInfo = WeeklySubmitInfo.from(w);
            weeklySubmitInfo.setManagerUserName(projectIdToManageUserMap.get(projectId));
            weeklySubmitInfo.setSubmitState(submitState);
            weeklySubmitInfo.setSubmitStateText(EnumUtils.getNameByValue(WeeklySubmitStateEnum.class, submitState));
            result.add(weeklySubmitInfo);
        });
    }

    private void setProjectRiskBaseList(List<ProjectWeeklyVO> records) {
        // 若有风险id列表，则根据id列表进行写入风险数据
        List<String> weeklyRiskIds = records.stream()
                .map(ProjectWeeklyVO::getWeeklyRiskIds)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
        Map<Long, String> projectRiskMap = null;
        if (CollectionUtils.isNotEmpty(weeklyRiskIds)) {
            Set<String> weeklyRiskIdsSet = new HashSet<>();
            weeklyRiskIds.forEach(w -> {
                List<String> list = Splitter.on(",").splitToList(w);
                weeklyRiskIdsSet.addAll(list);
            });
            // 拿到id列表去搜索风险表，获取风险基本信息
            List<ProjectRisk> projectRisks = projectRiskMapper.selectByIds(weeklyRiskIdsSet);
            projectRiskMap = projectRisks.stream().collect(Collectors.toMap(ProjectRisk::getId, ProjectRisk::getDescription));
        }
        // 若获取对应map，则将风险表写入对应的周报列表
        if (MapUtils.isNotEmpty(projectRiskMap)) {
            Map<Long, String> finalProjectRiskMap = projectRiskMap;
            records.forEach(r -> {
                String weeklyRiskId = r.getWeeklyRiskIds();
                if (StringUtils.isNotEmpty(weeklyRiskId)) {
                    List<String> weeklyRiskIdList = Splitter.on(",").splitToList(weeklyRiskId);
                    List<ProjectRiskBaseVo> riskBaseVoArrayList = new ArrayList<>();
                    weeklyRiskIdList.forEach(w -> {
                        Long id = Long.valueOf(w);
                        String description = finalProjectRiskMap.get(id);
                        ProjectRiskBaseVo projectRiskBaseVo = new ProjectRiskBaseVo(id, description);
                        riskBaseVoArrayList.add(projectRiskBaseVo);
                    });
                    r.setProjectRiskBaseList(riskBaseVoArrayList);
                }
            });
        }
    }

    @Override
    public ProjectWeeklyPreSaveDataVo findPreSaveData(Map<String, Object> filter) {
        // 获取项目当前周工时统计
        ProjectWorkHoursVo currentHours = workHoursService.calculateAvgWorkHours(filter);

        // 获取项目累计工时统计
        filter.remove(PARAM_START_DATE);
        ProjectWorkHoursVo totalHours = workHoursService.calculateAvgWorkHours(filter);

        // 组装返回对象,
        return ProjectWeeklyPreSaveDataVo.builder()
                .currentHours(currentHours.getAvgSumHours())
                .totalHours(totalHours.getAvgSumHours())
                .build();
    }

    public Map<String, ProjectWeeklyPreSaveDataVo> batchFindPreSaveData(List<Map<String, Object>> filterList) {
        if (CollUtil.isEmpty(filterList)) {
            return new HashMap<>();
        }
        // 获取项目当前周工时统计
        List<ProjectWorkHoursVo> currentHoursList = workHoursService.batchCalculateAvgWorkHours(filterList);
        Map<String, ProjectWorkHoursVo> currentHoursMap = covertHoursMap(currentHoursList);

        // 获取项目累计工时统计
        List<Map<String, Object>> totalFilterList = new ArrayList<>();
        filterList.forEach(f -> {
            Map<String, Object> item = new HashMap<>();
            item.put(PARAM_PROJECT_ID, f.get(PARAM_PROJECT_ID));
            item.put(PARAM_END_DATE, f.get(PARAM_END_DATE));
            totalFilterList.add(item);
        });
        List<ProjectWorkHoursVo> totalHoursList = workHoursService.batchCalculateAvgWorkHours(totalFilterList);
        Map<String, ProjectWorkHoursVo> totalHoursMap = covertHoursMap(totalHoursList);

        // 组装返回对象
        Map<String, ProjectWeeklyPreSaveDataVo> result = new HashMap<>();
        filterList.forEach(f -> {
            Long projectId = (Long) f.get(PARAM_PROJECT_ID);
            String key = StrUtil.format(KEY_TEMPLATE, projectId, f.get(PARAM_START_DATE), f.get(PARAM_END_DATE));
            ProjectWorkHoursVo currentHours = currentHoursMap.get(key);
            ProjectWorkHoursVo totalHours = totalHoursMap.get(StrUtil.format("{}-null-{}", projectId, f.get(PARAM_END_DATE)));
            // NPE问题处理
            if (!Optional.ofNullable(currentHours).isPresent()) {
                currentHours = ProjectWorkHoursVo.builder().projectId(projectId).avgSumHours(BigDecimal.ZERO.setScale(2)).build();
            }
            if (!Optional.ofNullable(totalHours).isPresent()) {
                totalHours = ProjectWorkHoursVo.builder().projectId(projectId).avgSumHours(BigDecimal.ZERO.setScale(2)).build();
            }
            ProjectWeeklyPreSaveDataVo vo = ProjectWeeklyPreSaveDataVo.builder()
                    .currentHours(currentHours.getAvgSumHours())
                    .totalHours(totalHours.getAvgSumHours())
                    .build();

            result.put(key, vo);
        });

        return result;
    }

    private Map<String, ProjectWorkHoursVo> covertHoursMap(List<ProjectWorkHoursVo> baseHoursList) {
        Map<String, ProjectWorkHoursVo> hoursMap = new HashMap<>();
        if (CollUtil.isEmpty(baseHoursList)) {
            return hoursMap;
        }
        for (ProjectWorkHoursVo vo : baseHoursList) {
            String key = StrUtil.format(KEY_TEMPLATE, vo.getProjectId(), vo.getStartDate(), vo.getEndDate());
            hoursMap.put(key, vo);
        }
        return hoursMap;
    }

    @Override
    public List<ProjectWeeklyVO> export(PageRequest pageRequest, Map<String, Object> filter) {
        Page<ProjectWeeklyVO> listPage = findPage(pageRequest, filter);
        List<ProjectWeeklyVO> records = listPage.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            for (ProjectWeeklyVO d :
                    records) {
                d.setReportStartEnd(d.getReportStart() + "~" + d.getReportEnd());
                String currentProgress = d.getCurrentProgress();
                d.setCurrentProgress(StringUtils.isEmpty(currentProgress) ? "" : currentProgress + "%");
            }
        } else {
            records.add(new ProjectWeeklyVO());
        }
        //导出【数量】条周报
        bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_PROJECT_WEEKLY_REPORT, LogContentEnum.EXPORT_WEEKLY_REPORT,
                records.size());
        return records;
    }

    @Override
    @Transactional
    public List<ProjectWeeklyAllVO> exportAll(PageRequest pageRequest, Map<String, Object> filter) {
        Page<ProjectWeeklyVO> listPage = findAllPage(pageRequest, filter);
        List<ProjectWeeklyVO> records = listPage.getRecords();
        ArrayList<ProjectWeeklyAllVO> projectWeeklyAllVOArrayList = new ArrayList<>();
        if (CollUtil.isNotEmpty(records)) {
            for (ProjectWeeklyVO d :
                    records) {
                d.setReportStartEnd(d.getReportStart() + "~" + d.getReportEnd());
                String currentProgress = d.getCurrentProgress();
                d.setCurrentProgress(StringUtils.isEmpty(currentProgress) ? "" : currentProgress + "%");
                ProjectWeeklyAllVO projectWeeklyAllVO = new ProjectWeeklyAllVO();
                BeanUtil.copyProperties(d, projectWeeklyAllVO);
                projectWeeklyAllVOArrayList.add(projectWeeklyAllVO);
            }
        } else {
            projectWeeklyAllVOArrayList.add(new ProjectWeeklyAllVO());
        }
        //导出【数量】条周报
        bcpLoggerUtils.log(FunctionConstants.PROJECT_WEEKLY_REPORT, LogContentEnum.EXPORT_WEEKLY_REPORT,
                projectWeeklyAllVOArrayList.size());
        return projectWeeklyAllVOArrayList;
    }

    @Override
    public ApiResult importProjectWeekly(Long projectId, String projectName,
                                         String projectDepartment,
                                         List<ProjectWeeklyExcelDTO> excelVOList,
                                         BindingResult bindingResult,
                                         Map<Long, ProjectInfo> projectInfoMap) {

        List<ErrorMessage> errorMessageList = new ArrayList<>();
        if (Optional.ofNullable(bindingResult.getTarget()).isPresent()) {
            errorMessageList = (List<ErrorMessage>) bindingResult.getTarget();
        }

        List<ProjectWeekly> projectWeeklyList = new ArrayList<>();
        if (CollUtil.isEmpty(excelVOList)) {
            String responseMsg = StrUtil.format("解析导入文件完成\n导入:0条数据成功\n导入:{}条数据失败\n失败数据行数为:{}",
                    CollUtil.size(errorMessageList),
                    CollUtil.getFieldValues(errorMessageList, "lineNum"));
            return ApiResult.success(responseMsg);
        } else {
            List<ProjectSystemProcessInfoDTO> processInfoDTOList = new ArrayList<>();
            for (ProjectWeeklyExcelDTO d :
                    excelVOList) {
                String reportStartEnd = d.getReportStartEnd();
                String[] split = reportStartEnd.split("~");
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                d.setReportStart(LocalDate.parse(split[0], formatter));
                d.setReportEnd(LocalDate.parse(split[1], formatter));

                d.setProjectId(projectId);
                d.setProjectName(projectName);
                d.setProjectDepartment(projectDepartment);
                ProjectWeekly projectWeekly = ProjectWeekly.saveOrUpdate(d);
                // 存储业务归属部门id
                projectWeekly.setProjectDepartmentId(projectInfoMap.get(projectId).getFirstLevelDepartmentId());
                // 调整写入的风险字段，从数据库直接获取字段拼接
                List<ProjectRisk> projectRisks = projectRiskMapper.selectByProjectId(projectId);
                if (CollectionUtils.isNotEmpty(projectRisks)) {
                    String weeklyRiskIds = projectRisks.stream().map(p -> String.valueOf(p.getId())).collect(Collectors.joining(","));
                    String weeklyRisk = projectRisks.stream().map(ProjectRisk::getDescription).collect(Collectors.joining());
                    projectWeekly.setWeeklyRiskIds(weeklyRiskIds);
                    projectWeekly.setWeeklyRisk(weeklyRisk);
                }

                projectWeeklyList.add(projectWeekly);
                processInfoDTOList.add(covert2ProjectSystemProcessInfoDTO(ProcessInfoStatusEnum.ARCHIVE, projectWeekly));
            }
            baseMapper.batchSave(projectWeeklyList);
            systemProcessService.innerBatchSave(processInfoDTOList);
        }

        String responseMsg;
        if (CollUtil.isNotEmpty(errorMessageList)) {
            responseMsg = StrUtil.format("解析导入文件完成\n导入:{}条数据成功\n导入:{}条数据失败\n失败数据行数为:{}",
                    CollUtil.size(projectWeeklyList),
                    CollUtil.size(errorMessageList),
                    errorMessageList.stream().map(ErrorMessage::getLineNum).distinct().sorted().collect(Collectors.toList()).toString());
        } else {
            responseMsg = StrUtil.format("解析导入文件完成\n导入:{}条数据成功",
                    CollUtil.size(projectWeeklyList));
        }
        //导入【数量】条周报
        bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_PROJECT_WEEKLY_REPORT, LogContentEnum.IMPORT_WEEKLY_REPORT,
                projectWeeklyList.size());
        return ApiResult.success(responseMsg);
    }

    @Override
    public ApiResult importAllProjectWeekly(List<ProjectWeeklyAllExcelDTO> excelVOList, BindingResult bindingResult) {

        // 获取解析错误信息
        List<ErrorMessage> errorMessageList = new ArrayList<>();
        int noProjectIdNum = 0;
        if (Optional.ofNullable(bindingResult.getTarget()).isPresent()) {
            errorMessageList = (List<ErrorMessage>) bindingResult.getTarget();
        }
        // 获取空列表时，返回值
        if (CollUtil.isEmpty(excelVOList)) {
            String responseMsg = StrUtil.format("解析导入文件完成\n导入:0条数据成功\n导入:{}条数据失败\n失败数据行数为:{}",
                    CollUtil.size(errorMessageList),
                    CollUtil.getFieldValues(errorMessageList, "lineNum"));
            return ApiResult.success(responseMsg);
        } else {
            // 1、处理数据
            // 根据项目名称获取map
            List<String> projectNames =
                    excelVOList.stream().map(ProjectWeeklyAllExcelDTO::getProjectName).distinct().collect(Collectors.toList());
            List<ProjectInfo> infos = projectInfoMapper.selectByProjectName(projectNames);
            Map<String, ProjectInfo> projectNameMap =
                    infos.stream().collect(Collectors.toMap(ProjectInfo::getItemName, p -> p, (a, b) -> a));

            // 清除未传项目名的行（做完验证后应该无空值）
            List<ProjectWeeklyAllExcelDTO> excelVOListFilter = excelVOList.stream().filter(e -> e.getProjectName() != null).collect(Collectors.toList());
            // 被清除的行值
            noProjectIdNum = excelVOList.size() - excelVOListFilter.size();

            // 将不同项目分组
            List<ProjectWeeklyExcelDTO> projectWeeklyExcelDTOS = BeanUtil.copyToList(excelVOListFilter, ProjectWeeklyExcelDTO.class);
            Map<String, List<ProjectWeeklyExcelDTO>> projectWeeklyListMap = projectWeeklyExcelDTOS.stream()
                    .collect(Collectors.groupingBy(ProjectWeeklyExcelDTO::getProjectName));

            // 获取部门信息 以便于设置业务归属部门id
            Map<Long, ProjectInfo> projectInfoMap = projectInfoMapper.findAll().stream()
                    .collect(Collectors.toMap(ProjectInfo::getId, p -> p, (a, b) -> a));

            // 调用importProjectWeekly方法进行导入
            for (Map.Entry<String, List<ProjectWeeklyExcelDTO>> entry : projectWeeklyListMap.entrySet()) {
                String key = entry.getKey();
                List<ProjectWeeklyExcelDTO> value = entry.getValue();
                ProjectInfo projectInfo = projectNameMap.get(key);
                if (projectInfo == null) {
                    return ApiResult.failure("当前项目不存在，请重新导入，项目名称：" + key);
                }
                Long projectId = projectInfo.getId();
                String projectName = projectInfo.getItemName();
                String projectDepartment = projectInfo.getFirstLevelDepartment();
                importProjectWeekly(projectId, projectName, projectDepartment, value, bindingResult, projectInfoMap);
            }

        }

        // 2、返回值组装
        String responseMsg;
        if (noProjectIdNum > 0 || CollUtil.isNotEmpty(errorMessageList)) {
            responseMsg = StrUtil.format("解析导入文件完成\n导入:{}条数据成功\n导入:{}条数据失败\n失败数据行数为:{}",
                    excelVOList.size() - noProjectIdNum,
                    errorMessageList.size() + noProjectIdNum,
                    errorMessageList.stream().map(ErrorMessage::getLineNum).distinct().sorted().collect(Collectors.toList()).toString());
        } else {
            responseMsg = StrUtil.format("解析导入文件完成\n导入:{}条数据成功",
                    CollUtil.size(excelVOList));
        }
        //导入【数量】条周报
        bcpLoggerUtils.log(FunctionConstants.PROJECT_WEEKLY_REPORT, LogContentEnum.IMPORT_WEEKLY_REPORT,
                excelVOList.size());
        return ApiResult.success(responseMsg);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncProcessInfo(ProcessInfoStatusEnum statusEnum, List<ProjectWeekly> entityList) {
        if (CollUtil.isEmpty(entityList)) {
            log.warn("项目周报实体类集合不存在，跳过项目过程动态同步操作");
            return;
        }
        List<ProjectSystemProcessInfoDTO> dtoList = new ArrayList<>();
        entityList.forEach(o -> dtoList.add(covert2ProjectSystemProcessInfoDTO(ProcessInfoStatusEnum.ARCHIVE, o)));
        systemProcessService.innerBatchSave(dtoList);
    }

    /**
     * 实体类转项目过程动态批量保存Dto类
     *
     * @param statusEnum 项目过程动态枚举
     * @param entity     项目周报实体类
     * @return 项目过程动态批量保存Dto类
     */
    private ProjectSystemProcessInfoDTO covert2ProjectSystemProcessInfoDTO(ProcessInfoStatusEnum statusEnum, ProjectWeekly entity) {
        String name;
        if (!Optional.ofNullable(entity.getReportStart()).isPresent() && !Optional.ofNullable(entity.getReportEnd()).isPresent()) {
            name = WEEKLY_PROCESS_PREFIX;
        } else {
            String start = DateUtil.format(Date.from(entity.getReportStart().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()), WEEKLY_PROCESS_TIME_FORMAT);
            String end = DateUtil.format(Date.from(entity.getReportEnd().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()), WEEKLY_PROCESS_TIME_FORMAT);
            name = StrUtil.format("{}{}-{}", WEEKLY_PROCESS_PREFIX, start, end);
        }

        ProjectSystemProcessInfoDTO dto = ProjectSystemProcessInfoDTO.builder()
                .processType(SystemProcessTypeEnum.WEEKLY.getValue())
                .status(String.valueOf(statusEnum.getValue()))
                .applicatId(entity.getReportUserId())
                .projectId(entity.getProjectId())
                .applicatId(SecurityUtils.getUser().getId())
                .applicat(SecurityUtils.getUser().getName())
                .requestId(entity.getId())
                .name(name)
                .build();
        return dto;
    }

    @Override
    @Transactional
    public Page<ProjectWeeklyVO> findAllAttentionPage(PageRequest pageRequest, Map<String, Object> filter) {
        Long userId = SecurityUtils.getUser().getId();

        // 查找用户关注项目
        List<ProjectAttention>  projectAttentions = projectMapper.findAttentionProjectByUserId(userId);
        Page<ProjectWeeklyVO> allPage=new Page<>();
        if(CollUtil.isNotEmpty(projectAttentions)){
            List<Long> projectIds = projectAttentions.stream()
                    .map(ProjectAttention::getProjectId)
                    .collect(Collectors.toList());
            filter.put("projectIds",projectIds);
            allPage = findAllPage(pageRequest, filter);
        }

        return PageUtils.page(allPage.getRecords(), pageRequest);
    }

    @Override
    @Transactional
    public List<ProjectWeeklyAllVO> exportAllAttention(PageRequest pageRequest, Map<String, Object> filter) {
        int pageSize = pageRequest.getPageSize();
        if (pageSize < EXPORT_MIN_PAGE_SIZE) {
            pageRequest.setPageSize(EXPORT_MIN_PAGE_SIZE);
        }
        Page<ProjectWeeklyVO> listPage = findAllAttentionPage(pageRequest, filter);
        List<ProjectWeeklyVO> records = listPage.getRecords();
        ArrayList<ProjectWeeklyAllVO> projectWeeklyAllVOArrayList = new ArrayList<>();
        if (CollUtil.isNotEmpty(records)) {
            for (ProjectWeeklyVO d :
                    records) {
                d.setReportStartEnd(d.getReportStart() + "~" + d.getReportEnd());
                String currentProgress = d.getCurrentProgress();
                d.setCurrentProgress(StringUtils.isEmpty(currentProgress) ? "" : currentProgress + "%");
                ProjectWeeklyAllVO projectWeeklyAllVO = new ProjectWeeklyAllVO();
                BeanUtil.copyProperties(d, projectWeeklyAllVO);
                projectWeeklyAllVOArrayList.add(projectWeeklyAllVO);
            }
        } else {
            projectWeeklyAllVOArrayList.add(new ProjectWeeklyAllVO());
        }
        return projectWeeklyAllVOArrayList;
    }

    @Override
    public ProjectWeekUnreadNumVO getUnreadNum(Map<String, Object> filter) {
        ProjectWeekUnreadNumVO result;
        Long userId = SecurityUtils.getUser().getId();
        List<ProjectWeeklyVO> allList;
        List<ProjectWeeklyReading> readList;
        List<ProjectAttention> projectAttentions;
        List<Long> projectIds;
        List<Long> pwIds;
        Integer allUnreadNum = 0;
        Integer attentionUnreadNum = 0;

        //获取用户部门权限列表
        pmsRetriever.calcProjectIdsAvailable(filter);
        filter.put(PARAM_USER_ID, userId);

        // 注：未读数统计的是所有时间的未读数，传入的filter并没有开始、结束时间两个字段
        allList = baseMapper.findAllList(filter);
        readList = baseMapper.findReadList(userId);
        pwIds = readList.stream()
                .map(ProjectWeeklyReading::getPwId)
                .collect(Collectors.toList());
        // 计算所有的未读数
        for (ProjectWeeklyVO projectWeeklyVO : allList) {
            Long id = projectWeeklyVO.getId();
            if (!pwIds.contains(id)) {
                allUnreadNum++;
            }
        }

        // 查找用户关注项目
        projectAttentions = projectMapper.findAttentionProjectByUserId(userId);
        projectIds = projectAttentions.stream()
                .map(ProjectAttention::getProjectId)
                .collect(Collectors.toList());
        // 取并集获取关注的项目列表
        List<ProjectWeeklyVO> allReadList = allList.stream()
                .filter(r -> projectIds.contains(r.getProjectId()))
                .collect(Collectors.toList());
        // 计算已关注的未读数
        for (ProjectWeeklyVO projectWeeklyVO : allReadList) {
            Long id = projectWeeklyVO.getId();
            if (!pwIds.contains(id)) {
                attentionUnreadNum++;
            }
        }

        result = ProjectWeekUnreadNumVO.of(allUnreadNum, attentionUnreadNum);
        return result;
    }

    @Override
    public ProjectWeeklyVO findById(Long id) {
        if (!Optional.ofNullable(id).isPresent()) {
            return new ProjectWeeklyVO();
        }
        // 获取实体类
        ProjectWeekly entity = baseMapper.findById(id);
        if (!Optional.ofNullable(entity).isPresent()) {
            return new ProjectWeeklyVO();
        }

        // 获取工时信息
        Map<String, Object> filter = new HashMap<>();
        filter.put(PARAM_PROJECT_ID, entity.getProjectId());
        filter.put(PARAM_START_DATE, entity.getReportStart());
        filter.put(PARAM_END_DATE, entity.getReportEnd());

        ProjectWeeklyVO result = BeanUtil.copyProperties(entity, ProjectWeeklyVO.class);

        // 添加风险集合字段（若有）
        String weeklyRiskId = result.getWeeklyRiskIds();
        if (StringUtils.isNotEmpty(weeklyRiskId)) {
            List<ProjectRiskBaseVo> riskBaseVoArrayList = new ArrayList<>();
            List<String> weeklyRiskIdList = Splitter.on(",").splitToList(weeklyRiskId);
            Set<String> weeklyRiskIdsSet = Sets.newHashSet(weeklyRiskIdList);
            List<ProjectRisk> projectRisks = projectRiskMapper.selectByIds(weeklyRiskIdsSet);
            Map<Long, String> projectRiskMap = projectRisks.stream().collect(Collectors.toMap(ProjectRisk::getId, ProjectRisk::getDescription));
            weeklyRiskIdList.forEach(w -> {
                Long riskId = Long.valueOf(w);
                String description = projectRiskMap.get(riskId);
                ProjectRiskBaseVo projectRiskBaseVo = new ProjectRiskBaseVo(riskId, description);
                riskBaseVoArrayList.add(projectRiskBaseVo);
            });
            result.setProjectRiskBaseList(riskBaseVoArrayList);
        }

        // 去除当期新增工时、累计工时计算逻辑，直接获取数据库已存在数据
        ProjectWeeklyPreSaveDataVo preSaveDataVo = findPreSaveData(filter);
        result.setCurrentHours(preSaveDataVo.getCurrentHours());
        result.setTotalHours(preSaveDataVo.getTotalHours());

        return result;
    }

}
