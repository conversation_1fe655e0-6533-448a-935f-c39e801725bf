package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 内外部项目概览
 * @since 2024/8/2
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectInfoOverViewVO {

    /**
     * ID
     */
    private String id;

    /**
     * 项目编号
     */
    private String itemNo;

    /**
     * 项目名称
     */
    private String itemName;

    /**
     * 项目状态 （0=商机，1=商机终止，2=在建，3=挂起，4=初验，5终验，6=结项，7=异常终止，8=立项审批中）
     *
     * @see com.gok.pboot.pms.enumeration.facade.BusinessStatusEnum
     */
    private String projectStatus;

    /**
     * 经营单元ID
     */
    private Long businessId;

    /**
     * 经营单元名称
     */
    private String businessUnitName;

    /**
     * 所属客户ID
     */
    private Long unitId;

    /**
     * 所属客户名称
     */
    private String unitName;

    /**
     * 进度状态（项目状态）
     */
    private String projectStatusName;

    /**
     * 业务归属部门id
     */
    private Long businessDepartmentId;

    /**
     * 业务归属部门
     */
    private String businessDepartment;

    /**
     * 项目经理人员ID
     */
    private String managerUserId;

    /**
     * 项目经理人员姓名
     */
    private String managerUserName;

    /**
     * 是否关注 0-未关注 1-已关注
     */
    private Integer isAttention;

}
