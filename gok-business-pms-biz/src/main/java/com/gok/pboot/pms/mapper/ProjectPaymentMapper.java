package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.base.admin.dto.ProjectPaymentDTO;
import com.gok.pboot.pms.entity.domain.ProjectPayment;
import com.gok.pboot.pms.entity.dto.PaymentDataScopeDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目回款跟踪 Mapper
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Mapper
public interface ProjectPaymentMapper extends BaseMapper<ProjectPayment> {

    /**
     * 模糊查询带分页
     *
     * @param page {@link Page}
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link Page}<{@link ProjectPayment}>
     */
    Page<ProjectPayment> findPage(Page<ProjectPayment> page,
                                  @Param("query") ProjectPaymentDTO projectPaymentDTO,
                                  @Param("auth") PaymentDataScopeDTO paymentDataScopeDTO);

    /**
     * 模糊查询
     *
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link List}<{@link ProjectPayment}>
     */
    List<ProjectPayment> findPage(@Param("query") ProjectPaymentDTO projectPaymentDTO,
                                  @Param("auth") PaymentDataScopeDTO paymentDataScopeDTO);

    /**
     * 【客户名称】无值且项目干系人未认领，该数据所有人可见
     *
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link List}<{@link ProjectPayment}>
     */
    List<ProjectPayment> listByAuth(@Param("query") ProjectPaymentDTO projectPaymentDTO);

    /**
     * 查询已推送或已锁定的数据
     *
     * @return {@link List}<{@link ProjectPayment}>
     */
    List<ProjectPayment> listByPushOrLock();

}
