package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 合同收(付)款状态枚举
 *
 * <AUTHOR>
 * @date 13/12/2023
 */
@Getter
@AllArgsConstructor
public enum ContractPaymentStatusEnum implements ValueEnum<Integer> {


    /**
     * 已收款
     */
    RECEIVED(0, "已付款"),

    /**
     * 未收款
     */
    NOT_RECEIVED(1, "未付款"),

    /**
     * 已部分收款
     */
    PARTIALLY_RECEIVED(2, "已部分收款"),

    /**
     * 已回款
     */
    RETURN(3, "已回款"),

    /**
     * 未回款
     */
    NOT_RETURN(4, "未回款");

    private final Integer value;

    private final String name;
}
