package com.gok.pboot.pms.entity.vo;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.gok.pboot.pms.common.base.BaseEntity;
import com.gok.pboot.pms.entity.domain.*;
import com.google.common.base.Strings;
import com.google.common.collect.Multimap;
import lombok.*;
import org.apache.commons.lang3.BooleanUtils;

import javax.annotation.Nullable;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目任务详情
 *
 * <AUTHOR>
 * @version 1.1.0
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ProjectTaskDetailsVO {

    /**
     * ID
     */
    private String id;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 是否有上级任务
     */
    private Boolean hasParent;

    /**
     * 上级任务名称
     */
    private String parentTaskTitle;

    /**
     * 任务标题
     */
    private String title;

    /**
     * 负责人姓名
     */
    private String managerUserName;

    /**
     * 参与人姓名
     */
    private String memberUserNames;

    /**
     * 计划开始时间
     */
    private String expectedStartTime;

    /**
     * 计划结束时间
     */
    private String expectedEndTime;

    /**
     * 实际开始时间
     */
    private String actualStartTime;

    /**
     * 实际结束时间
     */
    private String actualEndTime;

    /**
     * 是否里程碑
     */
    private Boolean milestone;

    /**
     * 详细描述
     */
    private String content;

    /**
     * 是否可以回复
     */
    private Boolean canFeedback;

    /**
     * 附件列表
     */
    private List<ProjectTaskAttachmentVO> attachments;

    /**
     * 进度列表
     */
    private List<ProjectTaskProgressVO> progresses;

    public static ProjectTaskDetailsVO of(
            ProjectTask task,
            @Nullable String parentTaskName,
            ProjectInfo project,
            Collection<ProjectTaskUser> members,
            Collection<ProjectTaskAttachment> attachments,
            Collection<ProjectTaskProgress> progresses,
            Multimap<Long, ProjectTaskProgressFeedback> progressFeedbacks,
            Long userId
    ){
        ProjectTaskDetailsVO result = new ProjectTaskDetailsVO();

        result.setId(String.valueOf(task.getId()));
        result.setHasParent(parentTaskName != null);
        if (result.getHasParent()){
            result.setParentTaskTitle(parentTaskName);
        }
        result.setProjectId(String.valueOf(project.getId()));
        result.setProjectName(Strings.nullToEmpty(project.getItemName()));
        result.setTitle(Strings.nullToEmpty(task.getTitle()));
        result.setManagerUserName(Strings.nullToEmpty(task.getManagerUserName()));
        result.setMemberUserNames(
                members.stream().map(ProjectTaskUser::getUserName).collect(Collectors.joining(","))
        );
        result.setExpectedStartTime(formatDateTime(task.getExpectedStartTime()));
        result.setExpectedEndTime(formatDateTime(task.getExpectedEndTime()));
        result.setActualStartTime(formatDateTime(task.getActualStartTime()));
        result.setActualEndTime(formatDateTime(task.getActualEndTime()));
        result.setMilestone(BooleanUtils.toBoolean(task.getMilestone()));
        result.setContent(Strings.nullToEmpty(task.getContent()));
        result.setCanFeedback(true);
        result.setAttachments(ProjectTaskAttachmentVO.batchFrom(attachments));
        result.setProgresses(progresses.stream()
                .sorted(Comparator.<ProjectTaskProgress>comparingLong(BaseEntity::getId).reversed())
                .map(p -> ProjectTaskProgressVO.of(p, progressFeedbacks.get(p.getId()), userId))
                .collect(Collectors.toList())
        );

        return result;
    }

    private static String formatDateTime(LocalDateTime dateTime){
        return Strings.nullToEmpty(LocalDateTimeUtil.formatNormal(dateTime));
    }
}
