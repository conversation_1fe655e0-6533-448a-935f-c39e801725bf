/*
 *
 *      Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: lengleng (<EMAIL>)
 *
 */

package com.gok.pboot.pms.entity.dto;

import com.gok.bcp.upms.dto.UserPmsDTO;
import com.google.common.collect.ImmutableList;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2017/11/5
 */
@Data
@ApiModel(value = "系统用户传输对象")
public class UserPmsPageDTO {

    /**
     * 用户姓名
     */
    private String name;
    /**
     * 部门id
     */
    private List<Long> deptIds;

    /**
     * 过滤人员列表
     */
    private List<Long> filterUserIds;

    /**
     * 人员状态
     */
    private Integer personnelStatus;

    /**
     * 人员状态名称
     */
    private String personnelStatusName;

    /**
     * 人员列表
     */
    private List<Long> userIds;

    public UserPmsDTO toUserPmsDTO(){
        UserPmsDTO result = new UserPmsDTO();

        result.setName(this.name);
        result.setDeptIds(ObjectUtils.defaultIfNull(this.deptIds, ImmutableList.of()));
        result.setFilterUserIds(ObjectUtils.defaultIfNull(this.filterUserIds, ImmutableList.of()));
        result.setUserIds(ObjectUtils.defaultIfNull(this.userIds, ImmutableList.of()));

        return result;
    }

}
