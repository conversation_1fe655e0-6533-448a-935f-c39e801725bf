package com.gok.pboot.pms.common.dateTools;


import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * excel表格导出——日期数据格式转换
 * @param <T>
 */
public abstract class BaseConverter <T> implements Converter<T> {
    private Class<T> clazz;

 // 子类传入class，接收LocalDate.class,LocalDateTime.class
    public BaseConverter(Class<T> clazz) {
        this.clazz = clazz;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Class supportJavaTypeKey() {
        return clazz;
    }

    public T convertToJavaData(CellData cellData, ExcelContentProperty contentProperty,
                               GlobalConfiguration globalConfiguration) {

        // LocalDateTime 时间转换
        if (cellData.getData() instanceof LocalDate) {
            if (cellData.getType().equals(CellDataTypeEnum.NUMBER)) {
                LocalDate localDate = LocalDate.of(1900, 1, 1);
                localDate = localDate.plusDays(cellData.getNumberValue().longValue());
                return (T) localDate;
            } else if (cellData.getType().equals(CellDataTypeEnum.STRING)) {
                return (T) LocalDate.parse(cellData.getStringValue(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } else {
                return null;
            }
        }

        // LocalDateTime 时间转换
        if (cellData.getData() instanceof LocalDateTime) {
            return (T) LocalDateTime.parse(cellData.getStringValue(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return null;

    }




    /**
     * Str转localDate
     */
    public static LocalDate getLocalDateByStr(String localDate){
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate date = LocalDate.parse(localDate, fmt);
        return date;
    }


}

