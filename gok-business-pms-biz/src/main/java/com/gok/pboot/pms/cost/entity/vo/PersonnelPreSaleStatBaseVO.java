package com.gok.pboot.pms.cost.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 人员工单看板-售前支撑工单基础VO
 *
 * <AUTHOR>
 * @date 2025/05/15
 */
@Data
public class PersonnelPreSaleStatBaseVO {
    /**
     * 人员id
     */
    @ExcelIgnore
    private Long userId;

    /**
     * 待审核工时
     */
    @ExcelProperty("待审核工时")
    private BigDecimal waitReviewHours;

    /**
     * 已审核总工时
     */
    @ExcelProperty("已审核总工时")
    private BigDecimal reviewedHours;

    /**
     * 已审核人工成本
     */
    @ExcelProperty("已审核人工成本")
    private BigDecimal reviewedLaborCost;
}
