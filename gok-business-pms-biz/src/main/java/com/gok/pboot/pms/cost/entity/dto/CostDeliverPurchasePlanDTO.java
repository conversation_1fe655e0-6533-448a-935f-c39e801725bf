package com.gok.pboot.pms.cost.entity.dto;

import com.gok.pboot.pms.cost.entity.vo.CostDeliverPurchasePlanAcceptanceVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 交付管理采购计划DTO类
 *
 * <AUTHOR>
 * @create 2025/01/15
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CostDeliverPurchasePlanDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 采购物
     */
    private String purchaseItem;

    /**
     * 销售合同ID
     */
    private Long saleContractId;

    /**
     * 销售合同名称
     */
    private String saleContractName;

    /**
     * 采购合同ID
     */
    private Long purchaseContractId;

    /**
     * 采购合同名称
     */
    private String purchaseContractName;

    /**
     * 关联成本科目ID
     */
    private Long accountId;

    /**
     * 科目OA ID
     */
    private Long accountOaId;

    /**
     * 关联成本科目名称
     */
    private String accountName;

    /**
     * 税率
     */
    private Integer taxRate;

    /**
     * 预算成本
     */
    private BigDecimal budgetAmountIncludedTax;

    /**
     * 计划到货(场)日期
     */
    private LocalDate plannedDeliveryDate;

    /**
     * 当前状态
     */
    private String planStatus;

    /**
     * 实际到货(场)日期
     */
    private LocalDate actualDeliveryDate;

    /**
     * 验收条件及状态
     */
    private List<CostDeliverPurchasePlanAcceptanceVO> voList;
}
