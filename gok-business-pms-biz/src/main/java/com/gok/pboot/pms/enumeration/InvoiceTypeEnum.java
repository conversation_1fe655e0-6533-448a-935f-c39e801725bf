package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 发票类型枚举
 *
 * <AUTHOR>
 * @date 13/12/2023
 */
@Getter
@AllArgsConstructor
public enum InvoiceTypeEnum implements ValueEnum<Integer> {

    /**
     * 纸质发票（增值税普通发票）
     */
    PAPER_INVOICE_ORDINARY(0, "纸质发票（增值税普通发票）"),

    /**
     * 纸质发票（增值税专用发票）
     */
    PAPER_INVOICE_SPECIAL(1, "纸质发票（增值税专用发票）"),

    /**
     * 电子发票（增值税专用发票）
     */
    ELECTRONIC_INVOICE_SPECIAL(2, "电子发票（增值税专用发票）"),

    /**
     * 电子发票（普通发票）
     */
    ELECTRONIC_INVOICE_ORDINARY(3, "电子发票（普通发票）");

    private final Integer value;

    private final String name;
}
