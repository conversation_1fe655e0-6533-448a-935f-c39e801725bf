package com.gok.pboot.pms.entity.dto;


import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.enumeration.DailyPaperFillingStateEnum;
import lombok.Data;

import java.util.List;

/**
 * 工时饱和度审核、滞后工时统计参数实体
 *
 * <AUTHOR>
 * @Date 2022-08-24 11:24
 */
@Data
public class DailyPaperAnalysisDTO extends PageRequest {
    /**
     * 所选日期左边界  年月日
     */
    private String startTime;

    /**
     * 所选日期右边界  年月日
     */
    private String endTime;

    /**
     * 所选部门Id
     */
    private List<Long> deptIds;
    /**
     * 审核状态
     */
    private Integer approvalStatus;
    /**
     * 是否是内部项目
     */
    private Integer insideProject;

    /**
     * 项目收入类型
     * @see com.gok.pboot.pms.enumeration.ProjectIncomeTypeEnum
     */
    private List<Integer> projectIncomeTypes;

    /**
     * （0=正常，1=滞后）
     * {@link DailyPaperFillingStateEnum}
     */
    private Integer fillingState;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 姓名
     */
    private String userName;

    /**
     * 只看筛选条件对应的部门数据
     */
    private Boolean onlySelectedDeptFlag;


}
