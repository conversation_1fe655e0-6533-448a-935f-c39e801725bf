package com.gok.pboot.pms.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.gok.pboot.pms.Util.HolidayUtil;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.BaseConstants;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.entity.Holiday;
import com.gok.pboot.pms.mapper.HolidayMapper;
import com.gok.pboot.pms.service.IHolidayService;
import lombok.RequiredArgsConstructor;
import org.apache.http.util.Asserts;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * <AUTHOR>
 * @desc
 * @createTime 2023/2/22 10:09
 */
@Service
@RequiredArgsConstructor
public class HolidayServiceImpl implements IHolidayService {
    private final HolidayMapper holidayMapper;

    @Override
    public ApiResult<String> updateHoliday(Integer year) {
        DateTimeFormatter struct = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 年度所有休息日
        JSONArray allHoliday = HolidayUtil.getDate2(year, false);
        // 年度法定节假日
        JSONArray legalHoliday = HolidayUtil.getDate2(year, true);
        ArrayList<Holiday> holidays = new ArrayList<>(allHoliday.size());

        Set<LocalDate> legalHolidaySet = new HashSet<>(legalHoliday.size());
        for (int i = 0; i < legalHoliday.size(); i++) {
            LocalDate formatDate = getFormatDate(year, struct, legalHoliday, i);
            legalHolidaySet.add(formatDate);
        }

        // 遍历链表
        for (int i = 0; i < allHoliday.size(); i++) {
            Holiday holiday = new Holiday();
            holiday.setDayDate(getFormatDate(year, struct, allHoliday, i));
            holiday.setHolidayType(BaseConstants.NO);
            holidays.add(holiday);
        }
        for (Holiday holiday : holidays) {
            if (legalHolidaySet.contains(holiday.getDayDate())) {
                holiday.setHolidayType(BaseConstants.YES);
            }
        }
        if (holidays.size() != 0) {
            holidayMapper.deleteByYear(String.valueOf(year));
            holidayMapper.batchSave(holidays);
        }

        return ApiResult.success("更新成功！");
    }

    private static LocalDate getFormatDate(Integer year, DateTimeFormatter struct, JSONArray allHoliday, int index) {
        // 获取时间（20220101）
        String originalDate = allHoliday.getJSONObject(index).getString("date");
        // 截取月（01）
        String mouth = originalDate.substring(4, 6);
        // 截取日（01）
        String day = originalDate.substring(6, 8);
        // 将数据格式化
        String date = year + "-" + mouth + "-" + day;
        return LocalDate.parse(date, struct);
    }

    @Override
    public ApiResult<Integer> getHolidayTypeByDate(LocalDate date){
        Holiday holiday = holidayMapper.selByDate(date);
        if(Optional.ofNullable(holiday).isPresent()){
            return ApiResult.success(holiday.getHolidayType());
        }
        return ApiResult.success(null);
    }

    /**
     * 获取所需出勤天数
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return {@link Integer }
     */
    @Override
    public Integer getRequiredAttendanceDays(LocalDate startDate, LocalDate endDate) {
        if (startDate == null) {
            throw new ServiceException("开始日期不能为空");

        }
        if (endDate == null) {
            throw new ServiceException("结束日期不能为空");
        }

        // 如果开始日期在结束日期之后，视为无效范围
        if (startDate.isAfter(endDate)) {
            throw new ServiceException("开始日期不能在结束日期之后");
        }

        long totalDays = startDate.until(endDate, ChronoUnit.DAYS) + 1;

        // 计算时间范围内有多少非工作日
        Integer holidayCount = holidayMapper.selectCountByDate(startDate, endDate);

        int safeHolidayCount = (holidayCount == null) ? 0 : holidayCount;

        long workDays = totalDays - safeHolidayCount;

        return Math.toIntExact(Math.max(workDays, 0));
    }
}
