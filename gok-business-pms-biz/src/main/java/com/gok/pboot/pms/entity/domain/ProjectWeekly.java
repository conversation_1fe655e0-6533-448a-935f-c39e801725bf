package com.gok.pboot.pms.entity.domain;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.entity.dto.ProjectWeeklyDTO;
import com.gok.pboot.pms.entity.dto.ProjectWeeklyExcelDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 项目周报表
 *
 * <AUTHOR>
 * @date 2023-07-11 17:05:26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("project_weekly")
public class ProjectWeekly extends BeanEntity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * '项目名称'
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String projectName;

    /**
     * '业务归属部门'
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String projectDepartment;

    /**
     * '业务归属部门id'
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private Long projectDepartmentId;

    /**
     * 汇报周期-开始
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private LocalDate reportStart;

    /**
     * 汇报周期-结束
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private LocalDate reportEnd;

    /**
     * 汇报人id
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private Long reportUserId;

    /**
     * 汇报人
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String reportUser;

    /**
     * 本周进展情况
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String currentWorkProgress;

    /**
     * 当前项目进度
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String currentProgress;

    /**
     * 当期新增工时（人天）
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private BigDecimal currentHours;

    /**
     * 累计工时（人天）
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private BigDecimal totalHours;

    /**
     * 下周工作计划
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String nextWorkPlan;

    /**
     * 需配合支撑事项
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String needSupportItem;

    /**
     * 周报风险
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String weeklyRisk;

    /**
     * 周报风险id集合
     */
    private String weeklyRiskIds;

    /**
     * 备注
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String remark;

    /**
     * 文件id集合
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String docIds;

    /**
     * 文件名集合
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String docNames;


    public static ProjectWeekly saveOrUpdate(ProjectWeeklyDTO request) {
        ProjectWeekly result = new ProjectWeekly();
        if (request.getId() != null) {
            result.setId(request.getId());
            result = BaseBuildEntityUtil.buildUpdate(result);
        } else {
            result = BaseBuildEntityUtil.buildSave(result);
        }
        result.setProjectId(request.getProjectId());
        result.setProjectName(request.getProjectName());
        result.setProjectDepartment(request.getProjectDepartment());
        result.setProjectDepartmentId(request.getProjectDepartmentId());
        result.setReportStart(request.getReportStart());
        result.setReportEnd(request.getReportEnd());
        result.setReportUser(request.getReportUser());
        result.setReportUserId(request.getReportUserId());
        result.setCurrentProgress(request.getCurrentProgress());
        result.setCurrentWorkProgress(request.getCurrentWorkProgress());
        result.setCurrentHours(request.getCurrentHours());
        result.setTotalHours(request.getTotalHours());
        result.setNextWorkPlan(request.getNextWorkPlan());
        result.setNeedSupportItem(request.getNeedSupportItem());
        result.setWeeklyRisk(request.getWeeklyRisk());
        if (StringUtils.isNotBlank(request.getWeeklyRiskIds())) {
            result.setWeeklyRiskIds(request.getWeeklyRiskIds());
        }
        result.setRemark(request.getRemark());
        //附件
        result.setDocIds(request.getDocIds());
        result.setDocNames(request.getDocNames());


        return result;
    }

    public static ProjectWeekly saveOrUpdate(ProjectWeeklyExcelDTO request) {
        ProjectWeekly result = new ProjectWeekly();
        if (request.getId() != null) {
            result.setId(request.getId());
            result = BaseBuildEntityUtil.buildUpdate(result);
        } else {
            result = BaseBuildEntityUtil.buildSave(result);
        }
        result.setProjectId(request.getProjectId());
        result.setProjectName(request.getProjectName());
        result.setProjectDepartment(request.getProjectDepartment());
        result.setReportStart(request.getReportStart());
        result.setReportEnd(request.getReportEnd());
        result.setReportUser(request.getReportUser());
        result.setReportUserId(request.getReportUserId());
        result.setCurrentProgress(request.getCurrentProgress());
        result.setCurrentWorkProgress(request.getCurrentWorkProgress());
        result.setCurrentHours(request.getCurrentHours());
        result.setTotalHours(request.getTotalHours());
        result.setNextWorkPlan(request.getNextWorkPlan());
        result.setNeedSupportItem(request.getNeedSupportItem());
        //result.setWeeklyRisk(request.getWeeklyRisk());
        //附件
        result.setDocIds(request.getDocIds());
        result.setDocNames(request.getDocNames());


        return result;
    }


}
