package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/02/18
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class CostIncomeCalculationDTO {

    /**
     * 主键ID集合
     */
    @NotEmpty(groups = UpdateValidation.class, message = "主键ID集合不能为空")
    private List<Long> ids;

    /**
     * 项目ID集合
     */
    private List<Long> projectIds;

    /**
     * 归属月份开始日期
     */
    private String startBelongMonth;

    /**
     * 归属月份结束日期
     */
    private String endBelongMonth;

    /**
     * 查询字段
     */
    private String query;

    /**
     * 确认状态
     */
    private Integer confirmStatus;

    /**
     * 来源
     * 0-明细 1-汇总
     */
    @NotNull(groups = UpdateValidation.class, message = "来源不能为空")
    private Integer source;

    /**
     * 测算ID集合
     */
    private List<Long> calculationIds;

    /**
     * 可查看权限范围人员工号集合
     */
    private List<String> purviewWorkCodes;

    public interface UpdateValidation {
    }

}
