package com.gok.pboot.pms.service;

import com.gok.pboot.pms.entity.dto.ProjectTaskGroupAddDTO;
import com.gok.pboot.pms.entity.dto.ProjectTaskGroupUpdateDTO;
import com.gok.pboot.pms.entity.dto.ProjectTaskToGroupDTO;
import com.gok.pboot.pms.entity.vo.ProjectTaskGroupVO;

import java.util.List;
import java.util.Map;

/**
 * 项目任务看板服务
 *
 * <AUTHOR>
 * @date 2023/8/23
 */
public interface IProjectTaskGroupService {

    /**
     * 新增分组
     * @param dto 实体对象
     * @return {@link Long} 分组id
     */
    Long add(ProjectTaskGroupAddDTO dto);

    /**
     * 更新分组名称
     * @param dto 实体对象
     * @return {@link Long} 分组id
     */
    Long updateTitle(ProjectTaskGroupUpdateDTO dto);

    /**
     * 更新分组在制品数量
     * @param dto 实体对象
     * @return {@link Long}
     */
    Long updateCapacity(ProjectTaskGroupUpdateDTO dto);

    /**
     * 删除分组
     * @param groupId 分组id
     * @return {@link Boolean}
     */
    Boolean delete(Long groupId);

    /**
     * 添加项目到分组下
     * @param dto 实体对象
     * @return {@link Long} 被操作的任务id
     */
    Long putToGroup(ProjectTaskToGroupDTO dto);

    /**
     * 查询分组的项目列表
     * @param filter 请求参数
     * @customParam filter_L_projectId 项目id
     * @customParam filter_S_title 模糊查询的任务标题
     * @customParam filter_S_managerUserName 模糊查询的负责人姓名
     * @customParam filter_I_treeLevel 任务层级
     * @return {@link List<ProjectTaskGroupVO>}
     */
    List<ProjectTaskGroupVO> findList(Map<String, Object> filter);
}
