package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * - 异常日报Excel导出 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/11/16 15:39
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class AbnormalDailyPaperExcelVO {
    /**
     * 部门名称
     */
    @ExcelProperty("部门")
    private String deptName;

    /**
     * 姓名
     */
    @ExcelProperty("姓名")
    private String userRealName;

    /**
     * 用户id
     */
    @ExcelIgnore
    private Long userId;

    /**
     * 人员状态
     */
    @ExcelProperty("状态")
    private String personnelStatusName;

    /**
     * 日报日期
     */
    @ExcelProperty("日期")
    private LocalDate submissionDate;

    /**
     * 异常类型
     */
    @ExcelProperty("异常类型")
    private String abnormalTypeName;

    /**
     * 备注（异常情况）
     */
    @ExcelProperty("异常情况")
    private String remark;

    /**
     * 总工时
     */
    @ExcelProperty("总工时")
    private BigDecimal totalHours;

    /**
     * 正常工时
     */
    @ExcelProperty("正常工时")
    private BigDecimal normalHours;

    /**
     * 请休假
     */
    @ExcelProperty("请休假")
    private BigDecimal leaveHours;

    /**
     * 调休工时
     */
    @ExcelProperty("调休")
    private BigDecimal compensatoryLeaveHours;

    /**
     * 审核员
     */
    @ExcelProperty("审核员")
    private String operator;

    @ExcelIgnore
    private Long id;

    @ExcelIgnore
    private Integer approvalStatus;

    public static AbnormalDailyPaperExcelVO from(AbnormalDailyPaperVO abnormalDailyPaperVO) {
        return new AbnormalDailyPaperExcelVO(
                abnormalDailyPaperVO.getDeptName(),
                abnormalDailyPaperVO.getUserRealName(),
                abnormalDailyPaperVO.getUserId(),
                abnormalDailyPaperVO.getPersonnelStatusName(),
                abnormalDailyPaperVO.getSubmissionDate(),
                abnormalDailyPaperVO.getAbnormalTypeName(),
                abnormalDailyPaperVO.getRemark(),
                abnormalDailyPaperVO.getTotalHours(),
                abnormalDailyPaperVO.getNormalHours(),
                abnormalDailyPaperVO.getLeaveHourData(),
                abnormalDailyPaperVO.getLeaveHours(),
                abnormalDailyPaperVO.getOperator(),
                abnormalDailyPaperVO.getId(),
                abnormalDailyPaperVO.getApprovalStatus()
        );
    }
}
