package com.gok.pboot.pms.enumeration;

/**
 * 加班、请假、销假表状态枚举
 **/
public enum LeaveStatusEnum implements ValueEnum<String> {

    /**
     * 加班
     */
    JB("99", "加班"),
    /**
     * 销假
     */
    XJ("100", "销假"),

    //以下为请假细类
    /**
     * 项目调休(OA【RS-06外出/外派/项目调休申请】中选择「项目调休」类型对应的时长，取归档数据)
     */
    XMTX("200", "项目调休"),

    /**
     * 调休
     */
    TX("-13", "调休"),
    /**
     * 带薪病假
     */
    DBTX("-12", "带薪病假"),
    /**
     * 年假
     */
    NJ("-6", "年假"),
    /**
     * 事假
     */
    SJ("26", "事假"),
    /**
     * 病假
     */
    BJ("27", "病假"),
    /**
     * 陪产假
     */
    PCJ("30", "陪产假"),
    /**
     * 婚假
     */
    HJ("32", "婚假"),
    /**
     * 产假
     */
    CJ("33", "产假"),
    /**
     * 哺乳假
     */
    BRJ("34", "哺乳假"),
    /**
     * 丧假
     */
    SSJ("35", "丧假"),
    /**
     * 儿童陪护假
     */
    ETPHJ("36", "儿童陪护假"),
    /**
     * 产检假
     */
    CJJ("37", "产检假"),
    /**
     * 工伤假
     */
    GSJ("38", "工伤假");

    //值
    private String  value;
    //名称
    private String name;

    LeaveStatusEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 获取值
     *
     * @return Integer
     */
    @Override
    public String getValue() {
        return value;
    }

    /**
     * 获取名称
     *
     * @return String
     */
    @Override
    public String getName() {
        return name;
    }

    public static String getNameByVal(String value) {
        for (LeaveStatusEnum statusEnum : LeaveStatusEnum.values()) {
            if (statusEnum.value.equals(value)) {
                return statusEnum.name;
            }
        }
        return "";
    }
    public static String getValByName(String name) {
        for (LeaveStatusEnum statusEnum : LeaveStatusEnum.values()) {
            if (statusEnum.getName().equals(name)) {
                return statusEnum.getValue();
            }
        }
        return null;
    }
}
