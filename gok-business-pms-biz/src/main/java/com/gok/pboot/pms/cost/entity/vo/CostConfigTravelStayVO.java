package com.gok.pboot.pms.cost.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 差旅住宿标准配置 VO
 *
 * <AUTHOR>
 * @date 2025/01/07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CostConfigTravelStayVO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 城市标准
     */
    private String cityStandards;

    /**
     * 对应城市
     */
    private String cityName;

    /**
     * 城市ids
     */
    private String cityIds;

    /**
     * 总经办金额
     */
    private BigDecimal generalOfficePrice;

    /**
     * 总监级以上金额
     */
    private BigDecimal directorAbovePrice;

    /**
     * 总监级以下金额
     */
    private BigDecimal directorBelowPrice;

    /**
     * 对应版本
     */
    private String versionName;
}
