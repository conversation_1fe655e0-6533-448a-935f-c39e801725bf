package com.gok.pboot.pms.cost.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.cost.entity.domain.CostTaskFiling;
import com.gok.pboot.pms.cost.mapper.CostTaskFilingMapper;
import com.gok.pboot.pms.cost.service.ICostTaskFilingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 工单工时归档服务实现类
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CostTaskFilingServiceImpl implements ICostTaskFilingService {

    private final CostTaskFilingMapper filingMapper;

    @Override
    public boolean isFiled(LocalDate date) {
        Integer filed = filingMapper.isFiledByDate(date);
        return filed != null && filed == 1;
    }

    @Override
    public boolean exists(LocalDate date) {
        return filingMapper.exists(date);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void filing(Integer year, Integer month, String operator) {
        // 检查是否已存在
        LocalDateTime startTime = LocalDateTime.of(year, month, 1, 0, 0);
        LocalDateTime endTime = startTime.plusMonths(1);
        
        LambdaQueryWrapper<CostTaskFiling> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CostTaskFiling::getYear, year)
                .eq(CostTaskFiling::getMonth, month);
        
        CostTaskFiling filing = filingMapper.selectOne(wrapper);
        if (filing != null) {
            if (filing.getFiled() == 1) {
                throw new ServiceException("该月份已归档");
            }
            filing.setFiled(1);
            filing.setOperator(operator);
            filingMapper.updateById(filing);
        } else {
            filing = new CostTaskFiling();
            filing.setYear(year);
            filing.setMonth(month);
            filing.setFilingStartDatetime(startTime);
            filing.setFilingEndDatetime(endTime);
            filing.setFiled(1);
            filing.setOperator(operator);
            filingMapper.insert(filing);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unfiling(Integer year, Integer month, String operator) {
        LambdaQueryWrapper<CostTaskFiling> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CostTaskFiling::getYear, year)
                .eq(CostTaskFiling::getMonth, month);
        
        CostTaskFiling filing = filingMapper.selectOne(wrapper);
        if (filing == null || filing.getFiled() == 0) {
            throw new ServiceException("该月份未归档");
        }
        
        filing.setFiled(0);
        filing.setOperator(operator);
        filingMapper.updateById(filing);
    }
} 