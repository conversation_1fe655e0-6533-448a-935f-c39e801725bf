package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.cost.entity.domain.CostIncomeSettlementDetail;
import com.gok.pboot.pms.cost.entity.dto.CostIncomeSettlementListDTO;
import com.gok.pboot.pms.cost.entity.vo.CostIncomeSettlementDetailVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface CostIncomeSettlementDetailMapper extends BaseMapper<CostIncomeSettlementDetail> {

    /**
     * 查询收入结算明细列表
     *
     * @param dto
     * @return
     */
    List<CostIncomeSettlementDetailVO> selList(@Param("dto") CostIncomeSettlementListDTO dto);

    /**
     * 批量删除
     *
     * @param idList
     */
    void delByIds(@Param("idList") List<Long> idList);

    /**
     * 批量更新
     * @param list
     */
    void batchUpdate(@Param("updateEntries") List<CostIncomeSettlementDetail> list);
    /**
     * 项目测算查询收入结算明细列表
     *
     * @param dto
     * @return
     */
    List<CostIncomeSettlementDetailVO> selListForCalculation(@Param("dto") CostIncomeSettlementListDTO dto);

}