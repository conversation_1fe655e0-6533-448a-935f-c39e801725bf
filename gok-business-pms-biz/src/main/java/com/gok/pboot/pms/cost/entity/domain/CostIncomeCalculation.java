package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 收入测算汇总实体类
 *
 * <AUTHOR>
 * @create 2025/02/18
 **/
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("cost_income_calculation")
public class CostIncomeCalculation extends BeanEntity<Long> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 归属月份
     */
    private LocalDate belongMonth;

    /**
     * 结算人天
     */
    private BigDecimal settlementHours;

    /**
     * 结算单价
     */
    private BigDecimal settlementUnitPrice;

    /**
     * 客户承担费用
     */
    private BigDecimal customerBearingAmount;

    /**
     * 测算含税金额
     */
    private BigDecimal estimatedInclusiveAmountTax;

    /**
     * 确认状态（0=待确认，1=已确认，2=部分确认）
     * {@link com.gok.pboot.pms.cost.enums.ConfirmStatusEnum}
     */
    private Integer confirmStatus;

    /**
     * 确认日期
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDate confirmDate;

    /**
     * 结算状态（0=待结算，1=已结算，2=部分结算）
     * {@link com.gok.pboot.pms.cost.enums.SettlementStatusEnum}
     */
    private Integer settlementStatus;

}
