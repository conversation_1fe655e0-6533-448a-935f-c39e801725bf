package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.admin.vo.DictKvVo;
import com.gok.components.common.user.PigxUser;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.CommonUtils;
import com.gok.pboot.pms.cost.entity.domain.CostIncomeCalculation;
import com.gok.pboot.pms.cost.entity.domain.CostIncomeCalculationDetail;
import com.gok.pboot.pms.cost.entity.domain.CostPersonnelInformation;
import com.gok.pboot.pms.cost.entity.dto.*;
import com.gok.pboot.pms.cost.entity.vo.CostExpensesShareDetailsVO;
import com.gok.pboot.pms.cost.entity.vo.CostIncomeCalculationDetailVO;
import com.gok.pboot.pms.cost.entity.vo.CostIncomeSettlementDetailVO;
import com.gok.pboot.pms.cost.entity.vo.CostPersonnelInformationVO;
import com.gok.pboot.pms.cost.enums.ConfirmStatusEnum;
import com.gok.pboot.pms.cost.enums.SettlementStatusEnum;
import com.gok.pboot.pms.cost.mapper.CostExpensesShareMapper;
import com.gok.pboot.pms.cost.mapper.CostIncomeCalculationDetailMapper;
import com.gok.pboot.pms.cost.mapper.CostIncomeCalculationMapper;
import com.gok.pboot.pms.cost.mapper.CostPersonnelInformationMapper;
import com.gok.pboot.pms.cost.service.ICostIncomeCalculationDetailService;
import com.gok.pboot.pms.entity.PayAttendanceData;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.vo.AllocationFindPageVO;
import com.gok.pboot.pms.entity.vo.PersonnelDeliveryHourPageVO;
import com.gok.pboot.pms.entity.vo.SysUserDataScopeVO;
import com.gok.pboot.pms.enumeration.IsNoInternalProjectEnum;
import com.gok.pboot.pms.handler.ProjectScopeHandle;
import com.gok.pboot.pms.mapper.DailyPaperEntryMapper;
import com.gok.pboot.pms.mapper.PayAttendanceDataMapper;
import com.gok.pboot.pms.mapper.PersonnelDeliveryHourMapper;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import com.gok.pboot.pms.service.IDictService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025/02/18
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class CostIncomeCalculationDetailServiceImpl
        extends ServiceImpl<CostIncomeCalculationDetailMapper, CostIncomeCalculationDetail> implements ICostIncomeCalculationDetailService {

    private final ProjectInfoMapper projectInfoMapper;
    private final DailyPaperEntryMapper dailyPaperEntryMapper;
    private final CostExpensesShareMapper costExpensesShareMapper;
    private final PayAttendanceDataMapper payAttendanceDataMapper;
    private final CostIncomeCalculationMapper costIncomeCalculationMapper;
    private final PersonnelDeliveryHourMapper personnelDeliveryHourMapper;
    private final CostPersonnelInformationMapper costPersonnelInformationMapper;

    private final IDictService idictService;
    private final TransactionTemplate transactionTemplate;
    private final CostIncomeCalculationServiceImpl costCalculationService;
    private final CostIncomeSettlementDetailServiceImpl settlementDetailService;
    private final ProjectScopeHandle projectScopeHandle;
    private final Integer BATCH_SIZE = 100;
    private final Integer CONFIRM_TYPE = 0;
    private final Integer SETTLEMENT_TYPE = 1;
    private static final String MENU_CODE = "DELIVERY_HUMAN_RESOURCE_SRCS";

    @Override
    public List<CostIncomeCalculationDetailVO> findDetailList(Long projectId, CostIncomeCalculationDTO request) {
        request.setProjectIds(Arrays.asList(projectId));

        SysUserDataScopeVO dataPermission = projectScopeHandle.getDeliverManagementDataPermission(MENU_CODE, projectId, null);
        if (!Boolean.TRUE.equals(dataPermission.getIsAll())) {
            List<Long> userIdList = dataPermission.getUserIdList();
            List<CostPersonnelInformationVO> currentPerson =
                    costPersonnelInformationMapper.getPersonInfoList(PersonInfoConditionDTO.builder().projectIds(Arrays.asList(projectId)).userIds(userIdList).build());
            List<String> purviewWorkCodes = CollUtil.emptyIfNull(currentPerson).stream()
                    .map(CostPersonnelInformationVO::getWorkCode)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(purviewWorkCodes)) {
                return ListUtil.empty();
            }
            request.setPurviewWorkCodes(purviewWorkCodes);
        }

        if (StrUtil.isNotBlank(request.getQuery())) {
            List<CostIncomeSettlementDetailVO> querySettlementDetailList =
                    settlementDetailService.findDetailList(CostIncomeSettlementListDTO.builder().projectId(projectId).settlementNumber(request.getQuery()).build());
            List<Long> queryIds = CollUtil.isNotEmpty(querySettlementDetailList)
                    ? querySettlementDetailList.stream().map(CostIncomeSettlementDetailVO::getCostIncomeCalculationDetailId).collect(Collectors.toList())
                    : null;
            request.setIds(queryIds);
        }

        List<CostIncomeCalculationDetail> detailList = baseMapper.findList(request);
        if (CollUtil.isEmpty(detailList)) {
            return ListUtil.empty();
        }

        Map<Integer, String> taxRateMap = idictService.getDictKvList("税率").getData().stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName)
                );

        List<Long> ids = detailList.stream().map(CostIncomeCalculationDetail::getId).collect(Collectors.toList());
        List<CostIncomeSettlementDetailVO> settlementDetailList =
                settlementDetailService.findDetailList(CostIncomeSettlementListDTO.builder().projectId(projectId).costIncomeCalculationDetailIds(ids).build());
        Map<Long, List<CostIncomeSettlementDetailVO>> settlementDetailMap = settlementDetailList.stream()
                .collect(Collectors.groupingBy(CostIncomeSettlementDetailVO::getCostIncomeCalculationDetailId));

        return detailList.stream().map(e -> CostIncomeCalculationDetailVO.of(e, taxRateMap, settlementDetailMap)).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void saveOrUpdateCostIncomeCalculation(List<Long> projectIds) {
        // 获取所有的交付项目
        Map<String, Object> projectFilter = new HashMap<>();
        projectFilter.put("isNotInternalProject", IsNoInternalProjectEnum.no.getValue());
        List<Long> deliverProjectIds = projectInfoMapper.selectList(Wrappers.<ProjectInfo>query().lambda().eq(ProjectInfo::getIsNotInternalProject, IsNoInternalProjectEnum.no.getValue()))
                .stream()
                .map(ProjectInfo::getId)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(deliverProjectIds)) {
            return;
        }

        if (CollUtil.isEmpty(projectIds)) {
            List<Long> finalDeliverProjectIds = deliverProjectIds;
            CompletableFuture.runAsync(() -> {
                try {
                    log.info("生成或更新明细列表异步任务启动");
                    List<List<Long>> batches = ListUtils.partition(finalDeliverProjectIds, BATCH_SIZE);
                    for (List<Long> batch : batches) {
                        transactionTemplate.execute(status -> {
                            batchSaveOrUpdate(batch);
                            return true;
                        });
                    }
                    log.info("生成或更新明细列表异步任务执行成功");
                } catch (Exception e) {
                    log.error("生成或更新明细列表出错", e);
                }
            });
        } else {
            deliverProjectIds = deliverProjectIds.stream().filter(projectIds::contains).collect(Collectors.toList());
            batchSaveOrUpdate(deliverProjectIds);
        }
    }

    /**
     * 异步执行批量保存或更新
     *
     * @param projectIds 项目ID集合
     */
    public void batchSaveOrUpdate(List<Long> projectIds) {
        if (CollUtil.isEmpty(projectIds)) {
            return;
        }

        // 获取项目对应的人员信息
        List<CostPersonnelInformationVO> costPersonnelInformationVOS =
                costPersonnelInformationMapper.getPersonInfoList(new PersonInfoConditionDTO().setProjectIds(projectIds));
        Map<String, CostPersonnelInformationVO> personnelInformationMap = costPersonnelInformationVOS.stream()
                .collect(Collectors.toMap(vo -> StrUtil.format("{}-{}-{}-{}", vo.getProjectId(), vo.getWorkCode(), vo.getName(), vo.getBelongMonth()),
                        Function.identity(),
                        (v1, v2) -> v1)
                );
        if (CollUtil.isEmpty(costPersonnelInformationVOS)) {
            return;
        }
        List<String> workCodes = costPersonnelInformationVOS.stream().map(CostPersonnelInformationVO::getWorkCode).distinct().collect(Collectors.toList());
        List<String> memberNames = costPersonnelInformationVOS.stream().map(CostPersonnelInformationVO::getName).distinct().collect(Collectors.toList());

        // 根据项目ID获取对应项目工时分摊
        List<AllocationFindPageVO> allocationFindPageList =
                CollUtil.emptyIfNull(dailyPaperEntryMapper.allocationForCalculation(projectIds, workCodes, memberNames)).stream()
                        .filter(e -> !BigDecimal.ZERO.equals(e.getProjectShareHours()))
                        .collect(Collectors.toList());
        List<String> dateList = allocationFindPageList.stream().map(AllocationFindPageVO::getDate).collect(Collectors.toList());
        Map<String, AllocationFindPageVO> shareHoursMap = allocationFindPageList.stream()
                .peek(e -> e.setProjectShareHours(CommonUtils.unitConversion(e.getProjectShareHours())))
                .collect(Collectors.toMap(vo -> StrUtil.format("{}-{}-{}-{}", vo.getId(), vo.getWorkCode(), vo.getName(), vo.getDate()),
                        Function.identity())
                );

        // 获取对应费用分摊数据
        CostExpensesShareListDto costExpensesShareQuery = CostExpensesShareListDto.builder()
                .projectIds(projectIds)
                .workCodes(workCodes)
                .customerUndertakes(NumberUtils.INTEGER_ZERO)
                .build();
        List<CostExpensesShareDetailsVO> costExpensesShareDetailsVOList = costExpensesShareMapper.selDetailsList(costExpensesShareQuery);
        Map<String, List<CostExpensesShareDetailsVO>> costExpensesShareDetailsMap =
                CollUtil.emptyIfNull(costExpensesShareDetailsVOList)
                        .stream()
                        .collect(Collectors.groupingBy(vo -> StrUtil.format("{}-{}-{}-{}-01", vo.getProjectId(), vo.getWorkCode(), vo.getRecipientUserName(), vo.getBelongingMonth()))
                        );

        // 获取所有可以生成测算明细的数据
        List<String> allDetailKeyList = CollUtil.intersection(
                CollUtil.unionDistinct(shareHoursMap.keySet(), costExpensesShareDetailsMap.keySet()),
                personnelInformationMap.keySet()
        ).stream().collect(Collectors.toList());

        // 获取当前项目所有测算明细数据
        List<CostIncomeCalculationDetail> existDetailList = Optional.ofNullable(baseMapper.findList(
                CostIncomeCalculationDTO.builder()
                        .projectIds(projectIds)
                        .build()
        )).orElse(ListUtil.empty());
        Map<String, CostIncomeCalculationDetail> existDetailMap = existDetailList.stream()
                .collect(Collectors.toMap(vo -> StrUtil.format("{}-{}-{}-{}", vo.getProjectId(), vo.getWorkCode(), vo.getMemberName(), vo.getBelongMonth()),
                        Function.identity())
                );
        List<String> existDetailKeyList = existDetailMap.keySet().stream().collect(Collectors.toList());

        // 获取对应的薪资数据
        List<String> workCodeList = costPersonnelInformationVOS.stream()
                .map(CostPersonnelInformationVO::getWorkCode).distinct().collect(Collectors.toList());
        Map<String, PayAttendanceData> payAttendanceDataMap = getPayAttendanceDataMap(workCodeList, dateList);
        // 获取对应的应出勤天数
        Map<String, PersonnelDeliveryHourPageVO> personnelDeliveryHourDataMap = getPersonnelDeliveryHourDataMap(workCodes, dateList, projectIds);

        // 删除不存在的数据
        List<String> delDetailsKeyList = CollUtil.subtract(existDetailKeyList, allDetailKeyList).stream().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(delDetailsKeyList)) {
            List<Long> delIds = delDetailsKeyList.stream()
                    .map(e -> existDetailMap.get(e).getId())
                    .collect(Collectors.toList());
            baseMapper.deleteBatchIds(delIds);
        }

        List<CostIncomeCalculationDetail> resultDetails = new ArrayList<>();
        // 批量新增明细数据
        List<String> addDetailsKeyList = CollUtil.subtract(allDetailKeyList, existDetailKeyList).stream().collect(Collectors.toList());
        List<CostIncomeCalculationDetail> addDetails = new ArrayList<>(addDetailsKeyList.size());
        if (CollUtil.isNotEmpty(addDetailsKeyList)) {
            addDetailsKeyList.forEach(key -> {
                AllocationFindPageVO allocationVO = shareHoursMap.get(key);
                CostPersonnelInformationVO personnelInformation = personnelInformationMap.get(key);
                List<CostExpensesShareDetailsVO> costExpensesShareDetailsVOS = costExpensesShareDetailsMap.get(key);
                String workCodeAndMonthMapKey = StrUtil.format("{}-{}", personnelInformation.getWorkCode(), personnelInformation.getBelongMonth());
                PayAttendanceData payAttendanceData = payAttendanceDataMap.get(workCodeAndMonthMapKey);
                String projectIdAndWorkCodeAndMonthMapKey = StrUtil.format("{}-{}-{}", personnelInformation.getProjectId(), personnelInformation.getWorkCode(), personnelInformation.getBelongMonth());
                PersonnelDeliveryHourPageVO personnelDeliveryHourData = personnelDeliveryHourDataMap.get(projectIdAndWorkCodeAndMonthMapKey);
                addDetails.add(CostIncomeCalculationDetail.assemble(null, personnelInformation, allocationVO, costExpensesShareDetailsVOS, payAttendanceData, personnelDeliveryHourData));
            });
            resultDetails.addAll(addDetails);
        }

        // 批量修改明细数据
        List<String> existUpdateDetailKeyList = existDetailList.stream()
                .filter(e -> ConfirmStatusEnum.AWAIT_CONFIRM.getValue().equals(e.getConfirmStatus()))
                .map(e -> StrUtil.format("{}-{}-{}-{}", e.getProjectId(), e.getWorkCode(), e.getMemberName(), e.getBelongMonth()))
                .distinct().collect(Collectors.toList());
        List<String> updateKeyList = CollUtil.intersection(allDetailKeyList, existUpdateDetailKeyList).stream().collect(Collectors.toList());
        List<CostIncomeCalculationDetail> updateDetails = new ArrayList<>(updateKeyList.size());
        if (CollUtil.isNotEmpty(updateKeyList)) {
            updateKeyList.forEach(key -> {
                AllocationFindPageVO allocationVO = shareHoursMap.get(key);
                CostPersonnelInformationVO personnelInformation = personnelInformationMap.get(key);
                List<CostExpensesShareDetailsVO> costExpensesShareDetailsVOS = costExpensesShareDetailsMap.get(key);
                CostIncomeCalculationDetail costIncomeCalculationDetail = existDetailMap.get(key);
                String workCodeAndMonthMapKey = StrUtil.format("{}-{}", personnelInformation.getWorkCode(), personnelInformation.getBelongMonth());
                PayAttendanceData payAttendanceData = payAttendanceDataMap.get(workCodeAndMonthMapKey);
                String projectIdAndWorkCodeAndMonthMapKey = StrUtil.format("{}-{}-{}", personnelInformation.getProjectId(), personnelInformation.getWorkCode(), personnelInformation.getBelongMonth());
                PersonnelDeliveryHourPageVO personnelDeliveryHourData = personnelDeliveryHourDataMap.get(projectIdAndWorkCodeAndMonthMapKey);
                updateDetails.add(CostIncomeCalculationDetail.assemble(costIncomeCalculationDetail, personnelInformation, allocationVO, costExpensesShareDetailsVOS, payAttendanceData, personnelDeliveryHourData));
            });
            resultDetails.addAll(updateDetails);
        }

        // 汇总数据处理
        Map<String, CostIncomeCalculation> costIncomeCalculationMap =
                costCalculationService.batchSaveOrUpdate(resultDetails, allocationFindPageList, costExpensesShareDetailsVOList).stream()
                        .collect(Collectors.toMap(vo -> StrUtil.format("{}-{}", vo.getProjectId(), vo.getBelongMonth()),
                                Function.identity())
                        );
        if (CollUtil.isNotEmpty(addDetails)) {
            addDetails.forEach(e -> {
                CostIncomeCalculation calculation =
                        costIncomeCalculationMap.getOrDefault(StrUtil.format("{}-{}", e.getProjectId(), e.getBelongMonth()), new CostIncomeCalculation());
                e.setCalculationId(calculation.getId());
            });
            baseMapper.batchSave(addDetails);
        }
        if (CollUtil.isNotEmpty(updateDetails)) {
            updateDetails.forEach(e -> {
                CostIncomeCalculation calculation =
                        costIncomeCalculationMap.getOrDefault(StrUtil.format("{}-{}", e.getProjectId(), e.getBelongMonth()), new CostIncomeCalculation());
                e.setCalculationId(calculation.getId());
            });
            baseMapper.batchUpdate(updateDetails);
        }
    }

    @Override
    @Transactional
    public List<Long> batchConfirm(CostIncomeCalculationDTO request) {
        List<CostIncomeCalculationDetail> updateList = CollUtil.emptyIfNull(baseMapper.selectBatchIds(request.getIds())).stream()
                .filter(e -> ConfirmStatusEnum.AWAIT_CONFIRM.getValue().equals(e.getConfirmStatus())
                        && SettlementStatusEnum.AWAIT_SETTLEMENT.getValue().equals(e.getSettlementStatus()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(updateList)) {
            return ListUtil.empty();
        }
        // 批量确认明细数据
        updateList.forEach(e -> {
            e.setConfirmStatus(ConfirmStatusEnum.CONFIRM.getValue());
            e.setConfirmDate(LocalDate.now());
            BaseBuildEntityUtil.buildUpdate(e);
        });
        baseMapper.batchUpdate(updateList);

        // 关联修改汇总数据
        PigxUser user = SecurityUtils.getUser();
        List<Long> calculationIds = updateList.stream()
                .map(CostIncomeCalculationDetail::getCalculationId).distinct().collect(Collectors.toList());
        Map<Long, Integer> confirmStatusMap = getCalculationStatusMap(CONFIRM_TYPE, calculationIds);
        costIncomeCalculationMapper.batchUpdateStatusByDetails(confirmStatusMap, CONFIRM_TYPE, user.getId(), user.getName());

        return updateList.stream().map(CostIncomeCalculationDetail::getId).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public List<Long> batchCancelConfirm(CostIncomeCalculationDTO request) {
        List<CostIncomeCalculationDetail> updateList = CollUtil.emptyIfNull(baseMapper.selectBatchIds(request.getIds())).stream()
                .filter(e -> ConfirmStatusEnum.CONFIRM.getValue().equals(e.getConfirmStatus())
                        && SettlementStatusEnum.AWAIT_SETTLEMENT.getValue().equals(e.getSettlementStatus()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(updateList)) {
            return ListUtil.empty();
        }
        updateList.forEach(e -> {
            e.setConfirmStatus(ConfirmStatusEnum.AWAIT_CONFIRM.getValue());
            e.setConfirmDate(null);
            BaseBuildEntityUtil.buildUpdate(e);
        });
        baseMapper.batchUpdate(updateList);

        // 关联修改汇总数据
        PigxUser user = SecurityUtils.getUser();
        List<Long> calculationIds = updateList.stream()
                .map(CostIncomeCalculationDetail::getCalculationId).distinct().collect(Collectors.toList());
        Map<Long, Integer> confirmStatusMap = getCalculationStatusMap(CONFIRM_TYPE, calculationIds);
        costIncomeCalculationMapper.batchUpdateStatusByDetails(confirmStatusMap, CONFIRM_TYPE, user.getId(), user.getName());

        return updateList.stream().map(CostIncomeCalculationDetail::getId).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public List<Long> batchRegenerate(CostIncomeCalculationDTO request) {
        List<CostIncomeCalculationDetail> detailList = CollUtil.emptyIfNull(baseMapper.selectBatchIds(request.getIds())).stream()
                .filter(e -> ConfirmStatusEnum.AWAIT_CONFIRM.getValue().equals(e.getConfirmStatus())
                        && SettlementStatusEnum.AWAIT_SETTLEMENT.getValue().equals(e.getSettlementStatus()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(detailList)) {
            return ListUtil.empty();
        }
        List<Long> projectIds = detailList.stream()
                .map(CostIncomeCalculationDetail::getProjectId)
                .distinct().collect(Collectors.toList());
        List<Long> memberIds = detailList.stream()
                .map(CostIncomeCalculationDetail::getMemberId)
                .distinct().collect(Collectors.toList());
        List<String> dateList = detailList.stream()
                .map(e -> e.getBelongMonth().toString())
                .distinct().collect(Collectors.toList());

        // 获取项目对应的人员信息
        List<CostPersonnelInformation> costPersonnelInformationVOS = costPersonnelInformationMapper.selectBatchIds(memberIds);
        if (CollUtil.isEmpty(costPersonnelInformationVOS)) {
            return ListUtil.empty();
        }
        Map<String, CostPersonnelInformationVO> personnelInformationMap = costPersonnelInformationVOS.stream()
                .collect(Collectors.toMap(vo -> StrUtil.format("{}-{}-{}-{}", vo.getProjectId(), vo.getWorkCode(), vo.getName(), vo.getBelongMonth()),
                        e -> BeanUtil.copyProperties(e, CostPersonnelInformationVO.class))
                );
        List<String> workCodes = costPersonnelInformationVOS.stream().map(CostPersonnelInformation::getWorkCode).distinct().collect(Collectors.toList());
        List<String> memberNames = costPersonnelInformationVOS.stream().map(CostPersonnelInformation::getName).distinct().collect(Collectors.toList());

        // 根据项目ID获取对应项目工时分摊
        List<AllocationFindPageVO> allocationFindList =
                CollUtil.emptyIfNull(dailyPaperEntryMapper.allocationForCalculation(projectIds, workCodes, memberNames));
        Map<String, AllocationFindPageVO> shareGoursMap = allocationFindList.stream()
                .peek(e -> e.setProjectShareHours(CommonUtils.unitConversion(e.getProjectShareHours())))
                .collect(Collectors.toMap(vo -> StrUtil.format("{}-{}-{}-{}", vo.getId(), vo.getWorkCode(), vo.getName(), vo.getDate()),
                        Function.identity())
                );

        // 获取对应费用分摊数据
        CostExpensesShareListDto costExpensesShareQuery = CostExpensesShareListDto.builder()
                .projectIds(projectIds)
                .customerUndertakes(NumberUtils.INTEGER_ZERO)
                .workCodes(workCodes)
                .build();
        List<CostExpensesShareDetailsVO> costExpensesShareDetailsVOList =
                CollUtil.emptyIfNull(costExpensesShareMapper.selDetailsList(costExpensesShareQuery));
        Map<String, List<CostExpensesShareDetailsVO>> costExpensesShareDetailsMap = costExpensesShareDetailsVOList.stream()
                .collect(Collectors.groupingBy(vo -> StrUtil.format("{}-{}-{}-{}-01", vo.getProjectId(), vo.getWorkCode(), vo.getRecipientUserName(), vo.getBelongingMonth())));

        // 获取对应薪资数据
        List<String> workCodeList = detailList.stream()
                .map(CostIncomeCalculationDetail::getWorkCode).distinct().collect(Collectors.toList());
        Map<String, PayAttendanceData> payAttendanceDataMap = getPayAttendanceDataMap(workCodeList, dateList);
        // 获取对应的应出勤天数
        Map<String, PersonnelDeliveryHourPageVO> personnelDeliveryHourDataMap = getPersonnelDeliveryHourDataMap(workCodes, dateList, projectIds);

        detailList.forEach(item -> {
            String key = StrUtil.format("{}-{}-{}-{}", item.getProjectId(), item.getWorkCode(), item.getMemberName(), item.getBelongMonth());
            AllocationFindPageVO allocationVO = shareGoursMap.getOrDefault(key, new AllocationFindPageVO());
            CostPersonnelInformationVO personnelInformation = personnelInformationMap.get(key);
            List<CostExpensesShareDetailsVO> costExpensesShareDetailsVOS = costExpensesShareDetailsMap.get(key);
            String workCodeAndMonthMapKey = StrUtil.format("{}-{}", personnelInformation.getWorkCode(), personnelInformation.getBelongMonth());
            PayAttendanceData payAttendanceData = payAttendanceDataMap.get(workCodeAndMonthMapKey);
            String projectIdAndWorkCodeAndMonthMapKey = StrUtil.format("{}-{}-{}", personnelInformation.getProjectId(), personnelInformation.getWorkCode(), personnelInformation.getBelongMonth());
            PersonnelDeliveryHourPageVO personnelDeliveryHourData = personnelDeliveryHourDataMap.get(projectIdAndWorkCodeAndMonthMapKey);
            CostIncomeCalculationDetail.assemble(item, personnelInformation, allocationVO, costExpensesShareDetailsVOS, payAttendanceData, personnelDeliveryHourData);
        });

        baseMapper.batchUpdate(detailList);

        // 关联对应汇总数据
        costCalculationService.batchSaveOrUpdate(detailList, allocationFindList, costExpensesShareDetailsVOList);

        return detailList.stream().map(CostIncomeCalculationDetail::getId).collect(Collectors.toList());
    }

    /**
     * 获取薪资数据集合
     *
     * @param workCodeList
     * @param dateList
     * @return
     */
    private Map<String, PayAttendanceData> getPayAttendanceDataMap(List<String> workCodeList, List<String> dateList) {
        // 获取对应的薪资数据
        Map<String, PayAttendanceData> payAttendanceDataMap =
                CollUtil.emptyIfNull(payAttendanceDataMapper.findByWorkCodeAndPayrollTime(workCodeList, dateList))
                        .stream()
                        .collect(Collectors.toMap(vo -> StrUtil.format("{}-{}", vo.getWorkCode(), vo.getPayrollTime()), Function.identity(), (v1, v2) -> v1));
        return payAttendanceDataMap;
    }

    /**
     * 获取应出勤天数集合
     *
     * @param workCodeList
     * @param dateList
     * @return
     */
    private Map<String, PersonnelDeliveryHourPageVO> getPersonnelDeliveryHourDataMap(List<String> workCodeList, List<String> dateList, List<Long> projectIds) {
        Map<String, PersonnelDeliveryHourPageVO> personnelDeliveryHourPageMap =
                CollUtil.emptyIfNull(personnelDeliveryHourMapper.findByWorkCodeAndReuseDate(workCodeList, dateList, projectIds)).stream()
                        .collect(Collectors.toMap(vo -> StrUtil.format("{}-{}-{}", vo.getProjectId(), vo.getWorkCode(), vo.getReuseDate()), Function.identity(), (v1, v2) -> v1));
        return personnelDeliveryHourPageMap;
    }

    @Override
    @Transactional
    public List<Long> batchSettlement(CostIncomeCalculationDTO request) {
        List<CostIncomeCalculationDetail> detailList = CollUtil.emptyIfNull(baseMapper.selectBatchIds(request.getIds())).stream()
                .filter(e -> ConfirmStatusEnum.CONFIRM.getValue().equals(e.getConfirmStatus()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(detailList)) {
            return ListUtil.empty();
        }

        // 获取税率字典
        Map<Integer, String> taxRateMap = idictService.getDictKvList("税率").getData().stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName)
                );

        // 批量插入结算明细
        List<CostIncomeSettlementDetailsEditDTO> dtoList = new ArrayList<>(detailList.size());
        detailList.forEach(item -> {
            YearMonth yearMonth = YearMonth.from(item.getBelongMonth());
            BigDecimal taxRate = null != taxRateMap.get(item.getQuotedRateId())
                    ? new BigDecimal(taxRateMap.get(item.getQuotedRateId()).replaceAll("%", "")).divide(new BigDecimal(100))
                    : BigDecimal.ZERO;
            BigDecimal budgetAmountIncludedTax = item.getEstimatedInclusiveAmountTax();
            BigDecimal budgetAmountExcludingTax = null != budgetAmountIncludedTax
                    ? budgetAmountIncludedTax.divide(BigDecimal.ONE.add(taxRate), 2, BigDecimal.ROUND_HALF_UP)
                    : null;
            CostIncomeSettlementDetailsEditDTO settlementDetailsEditDTO = CostIncomeSettlementDetailsEditDTO.builder()
                    .projectId(item.getProjectId())
                    .startDate(yearMonth.atDay(1))
                    .endDate(yearMonth.atEndOfMonth())
                    .userName(item.getMemberName())
                    .workCode(item.getWorkCode())
                    .budgetAmountIncludedTax(budgetAmountIncludedTax)
                    .estimatedInclusiveAmountTax(budgetAmountIncludedTax)
                    .taxRate(item.getQuotedRateId().toString())
                    .budgetAmountExcludingTax(budgetAmountExcludingTax)
                    .dataSources(0)
                    .costIncomeCalculationDetailId(item.getId())
                    .costIncomeCalculationId(item.getCalculationId())
                    .operationSettlementDate(LocalDateTime.now())
                    .build();
            dtoList.add(settlementDetailsEditDTO);
        });
        // 插入结算数据
        settlementDetailService.addOrUpdate(dtoList);

        detailList.forEach(item -> {
            item.setSettlementStatus(SettlementStatusEnum.SETTLEMENT.getValue());
            BaseBuildEntityUtil.buildUpdate(item);
        });
        baseMapper.batchUpdate(detailList);

        // 关联对应的汇总数据状态
        PigxUser user = SecurityUtils.getUser();
        Map<Long, Integer> calculationSettlementMap =
                getCalculationStatusMap(SETTLEMENT_TYPE, detailList.stream().map(CostIncomeCalculationDetail::getCalculationId).collect(Collectors.toList()));
        costIncomeCalculationMapper.batchUpdateStatusByDetails(calculationSettlementMap, SETTLEMENT_TYPE, user.getId(), user.getName());

        return detailList.stream().map(CostIncomeCalculationDetail::getId).collect(Collectors.toList());
    }

    /**
     * 获取明细对应汇总数据状态集合
     *
     * @param statusType     状态类型 0-确认状态 1-结算状态
     * @param calculationIds 汇总ID集合
     * @return
     */
    public Map<Long, Integer> getCalculationStatusMap(Integer statusType, List<Long> calculationIds) {
        List<CostIncomeCalculationDetail> detailList =
                Optional.ofNullable(baseMapper.findList(CostIncomeCalculationDTO.builder().calculationIds(calculationIds).build()))
                        .orElse(ListUtil.empty());
        return CONFIRM_TYPE == statusType
                ? detailList.stream().collect(Collectors.groupingBy(CostIncomeCalculationDetail::getCalculationId,
                Collectors.mapping(CostIncomeCalculationDetail::getConfirmStatus,
                        Collectors.collectingAndThen(Collectors.toList(), statuses -> {
                                    Set<Integer> uniqueValues = new HashSet<>(statuses);
                                    return uniqueValues.size() == 1 ? uniqueValues.iterator().next() : ConfirmStatusEnum.PART_CONFIRM.getValue();
                                }
                        ))))
                : detailList.stream().collect(Collectors.groupingBy(CostIncomeCalculationDetail::getCalculationId,
                Collectors.mapping(CostIncomeCalculationDetail::getSettlementStatus,
                        Collectors.collectingAndThen(Collectors.toList(), statuses -> {
                                    Set<Integer> uniqueValues = new HashSet<>(statuses);
                                    return uniqueValues.size() == 1 ? uniqueValues.iterator().next() : SettlementStatusEnum.PART_SETTLEMENT.getValue();
                                }
                        ))));
    }

}
