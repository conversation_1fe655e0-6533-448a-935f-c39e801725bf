package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工单异常类型枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TaskAbnormalTypeEnum implements ValueEnum<Integer> {

    /**
     * 未提交
     */
    UN_SUBMITTED(1, "未提交"),
    /**
     * 未审核
     */
    UN_REVIEWED(2, "未审核"),
    /**
     * 未拆解
     */
    UN_DECOMPOSED(3, "未拆解"),
    /**
     * 未评价
     */
    UN_EVALUATED(4, "未评价"),


    ;

    private final Integer value;
    private final String name;
} 