package com.gok.pboot.pms.entity.vo;

import com.gok.base.admin.vo.PaymentPlatformVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 项目回款-列表查询条件
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectQueryFormVO {

    /**
     * 业务板块
     */
    private List<BusinessBlockVO> businessBlockVoList;

    /**
     * 归属业务线
     */
    private List<BusinessLineVO> businessLineVoList;

    /**
     * 收款公司
     */
    private List<AttributableSubjectVO> attributableSubjectVoList;

    /**
     * 单据状态
     */
    private List<DocumentStatusVO> documentStatusVoList;

    /**
     * 收款平台
     */
    private List<PaymentPlatformVO> paymentPlatformVoList;
}
