package com.gok.pboot.pms.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 复用交付工时统计dto
 *
 * <AUTHOR>
 * @date 2023/10/31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DailyReviewTotalDTO {

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 审批状态 0待审批 1已审批
     */
    private Integer auditStatus;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 人员姓名
     */
    private String username;

}