package com.gok.pboot.pms.entity.vo;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 任务
 *
 * <AUTHOR>
 * @since 2022-08-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TaskInfoVO {
    /**
     * 任务ID
     */
    private Long id;
    /**
    * 任务名称
    */
    private String taskName;
    /**
    * 任务类型（0=默认任务，1=手动添加，2=后期维保）
    */
    private Integer taskType;
    /**
     * 任务类型名称（0=默认任务，1=手动添加，2=后期维保）
     */
    private String taskTypeName;
    /**
    * 任务状态（0=正常，1=关闭）
    */
    private Integer taskStatus;
    /**
     * 任务状态名称（0=正常，1=关闭）
     */
    private String taskStatusName;
    /**
    * 实际工时
    */
    private BigDecimal actualHour;
    /**
    * 正常工时
    */
    private BigDecimal normalHour;
    /**
    * 加班工时
    */
    private BigDecimal addedHour;
    /**
    * 所属项目ID
    */
    private Long projectId;
    /**
    * 任务-人员列表
    */
    private List<TaskUserInfoVO> TaskUserInfoVoList;



}
