package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.domain.CostBaselineVersionRecord;
import com.gok.pboot.pms.cost.entity.vo.CostBaselineVersionRecordVO;

/**
 * 基线版本记录服务
 *
 * <AUTHOR>
 * @date 2025/01/15
 */
public interface ICostBaselineVersionRecordService extends IService<CostBaselineVersionRecord> {

    /**
     * 获取版本记录分页列表
     *
     * @param projectId   项目 ID
     * @param pageRequest 页面请求
     * @return {@link Page }<{@link CostBaselineVersionRecordVO }>
     */
    Page<CostBaselineVersionRecordVO> getVersionRecordPage(Long projectId, PageRequest pageRequest);

    /**
     * 同步基准版本记录
     *
     * @param projectId   项目 ID
     * @param requestName 请求名称
     * @param budgetType  预算类型
     */
    void syncCostBaselineVersionRecord(Long projectId, String requestName, Integer budgetType);

}
