package com.gok.pboot.pms.cost.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.cost.entity.dto.CostDeliverPurchasePlanDTO;
import com.gok.pboot.pms.cost.entity.dto.DeliverCostBudgetListDto;
import com.gok.pboot.pms.cost.entity.dto.DeliverExpensesReimburseListDto;
import com.gok.pboot.pms.cost.entity.vo.CostDeliverPurchasePlanListVO;
import com.gok.pboot.pms.cost.entity.vo.CostDeliverPurchasePlanVO;
import com.gok.pboot.pms.cost.entity.vo.DeliverCostBudgetListVO;
import com.gok.pboot.pms.cost.entity.vo.DeliverExpensesReimburseListVO;
import com.gok.pboot.pms.cost.service.ICostDeliverExpensesReimburseService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 交付管理费用报销管理
 *
 * <AUTHOR>
 * @menu 成本交付费用报销管理
 * @since 2025-01-07
 */
@RestController
@RequestMapping("/costDeliverExpensesReimburse")
@AllArgsConstructor
public class CostDeliverExpensesReimburseController {

    private final ICostDeliverExpensesReimburseService service;

    /**
     * 交付管理费用预算列表
     *
     * @param dto
     * @return {@link ApiResult}<{@link List}<{@link com.gok.pboot.pms.cost.entity.vo.CostManageListVO}>>
     */
    @PostMapping("/findDeliverCostBudget")
    public ApiResult<List<DeliverCostBudgetListVO>> findDeliverCostBudget(@RequestBody DeliverCostBudgetListDto dto) {
        return ApiResult.success(service.findDeliverCostBudget(dto), "获取成功");
    }

    /**
     * 交付管理费用报销台账
     *
     * @param reimburseListDto
     * @return {@link ApiResult}<{@link List}<{@link com.gok.pboot.pms.cost.entity.vo.CostManageListVO}>>
     */
    @PostMapping("/findExpensesReimburse")
    public ApiResult<Page<DeliverExpensesReimburseListVO>> findExpensesReimburse(@RequestBody DeliverExpensesReimburseListDto reimburseListDto) {
        return ApiResult.success(service.findExpensesReimburse(reimburseListDto), "获取成功");
    }

    /**
     * 采购计划列表
     *
     * @param projectId
     * @return {@link R}<{@link Page}<{@link CostDeliverPurchasePlanListVO}>>
     */
    @GetMapping("/planList/{projectId}")
    public ApiResult<List<CostDeliverPurchasePlanListVO>> findPlanList(@PathVariable Long projectId) {
        return ApiResult.success(service.findPlanList(projectId));
    }

    /**
     * 查看采购计划详情
     *
     * @return {@link CostDeliverPurchasePlanVO}
     */
    @GetMapping("getPlan/{id}")
    public ApiResult<CostDeliverPurchasePlanVO> getPlan(@PathVariable Long id) {
        return ApiResult.success(service.getPlan(id));
    }

    /**
     * 新增采购计划
     *
     * @param dto {@link CostDeliverPurchasePlanDTO}
     * @return {@link R}<{@link Void>
     */
    @PostMapping("/add-editPlan")
    public ApiResult<String> addOrEditPlan(@RequestBody @Valid CostDeliverPurchasePlanDTO dto) {
        return service.addOrEditPlan(dto);
    }

    /**
     * 删除采购计划
     *
     * @param id
     * @return {@link R}<{@link Void>
     */
    @DeleteMapping("/delPlan/{id}")
    public ApiResult<String> delPlan(@PathVariable Long id) {
        return service.delPlan(id);
    }
}
