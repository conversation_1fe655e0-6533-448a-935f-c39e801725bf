package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 合同转态
 *
 * <AUTHOR>
 * @date 13/12/2023
 */
@Getter
@AllArgsConstructor
public enum ContractStatusEnum implements ValueEnum<Integer> {
    /**
     * 履约中
     */
    IN_PERFORMANCE(0, "履约中"),

    /**
     * 履行完毕
     */
    FULFILLMENT_COMPLETED(1, "履约完毕"),
    /**
     * 未生效
     */
    NOT_EFFECTIVE(2, "未生效"),
    /**
     * 解除
     */
    SECURE(3, "解除");

    private final Integer value;

    private final String name;
}
