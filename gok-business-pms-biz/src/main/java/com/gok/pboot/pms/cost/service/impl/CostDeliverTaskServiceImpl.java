package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.admin.vo.DictKvVo;
import com.gok.bcp.message.entity.enums.ChannelEnum;
import com.gok.bcp.message.entity.enums.MsgTypeEnum;
import com.gok.bcp.message.entity.enums.TargetTypeEnum;
import com.gok.bcp.upms.feign.RemoteUserService;
import com.gok.bcp.upms.vo.SysUserVo;
import com.gok.components.common.user.PigxUser;
import com.gok.components.common.user.UserUtils;
import com.gok.module.file.entity.SysFile;
import com.gok.module.file.service.SysFileService;
import com.gok.pboot.common.secret.AESEncryptor;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.CollectionUtils;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.Util.PageUtils;
import com.gok.pboot.pms.common.base.BaseEntity;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.common.validate.validator.ManHourValidator;
import com.gok.pboot.pms.cost.entity.domain.CostDeliverTask;
import com.gok.pboot.pms.cost.entity.domain.CostManageVersion;
import com.gok.pboot.pms.cost.entity.domain.CostTaskCategoryManagement;
import com.gok.pboot.pms.cost.entity.domain.CostTaskDailyPaperEntry;
import com.gok.pboot.pms.cost.entity.dto.*;
import com.gok.pboot.pms.cost.entity.vo.*;
import com.gok.pboot.pms.cost.enums.*;
import com.gok.pboot.pms.cost.mapper.CostDeliverTaskMapper;
import com.gok.pboot.pms.cost.mapper.CostManageEstimationResultsMapper;
import com.gok.pboot.pms.cost.mapper.CostTaskDailyPaperEntryMapper;
import com.gok.pboot.pms.cost.service.*;
import com.gok.pboot.pms.cost.util.SupportTaskTreeProcessor;
import com.gok.pboot.pms.entity.ProjectCollect;
import com.gok.pboot.pms.entity.domain.ContractLedger;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.domain.Roster;
import com.gok.pboot.pms.entity.dto.BaseSendMsgDTO;
import com.gok.pboot.pms.enumeration.*;
import com.gok.pboot.pms.eval.enums.EvalTaskStatusEnum;
import com.gok.pboot.pms.eval.service.IEvalTaskService;
import com.gok.pboot.pms.mapper.ContractLedgerMapper;
import com.gok.pboot.pms.mapper.ProjectCollectMapper;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import com.gok.pboot.pms.service.BcpMessageService;
import com.gok.pboot.pms.service.IDictService;
import com.gok.pboot.pms.service.RosterService;
import com.gok.pboot.service.entity.rem.vo.RemSalarySocialFundDataVO;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.ValidationException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.gok.pboot.pms.Util.DateUtil.YEAR_MONTH_DAY;
import static com.gok.pboot.pms.cost.entity.vo.CostViewCorroborationVO.buildCorroborationVo;
import static com.gok.pboot.pms.cost.enums.CostTaskStatusEnum.getNextValByEnum;
import static com.gok.pboot.pms.cost.enums.CostTaskStatusEnum.getValByTaskLevel;

/**
 * 交付管理工单服务实现
 *
 * <AUTHOR>
 * @date 2025/01/15
 */
@Log4j2
@Service
public class CostDeliverTaskServiceImpl extends ServiceImpl<CostDeliverTaskMapper, CostDeliverTask> implements ICostDeliverTaskService {
    @Resource
    private ProjectInfoMapper projectInfoMapper;
    @Resource
    private RemoteUserService remoteUserService;
    @Resource
    private SysFileService sysFileService;
    @Resource
    private ICostManageVersionService costManageVersionService;
    @Resource
    private BcpMessageService bcpMessageService;
    @Resource
    private CostManageEstimationResultsMapper costManageEstimationResultsMapper;
    @Resource
    private ICostDeliverExpensesReimburseService costDeliverExpensesReimburseService;
    @Resource
    private IDictService idictService;
    @Resource
    private ICostConfigLevelPriceService costConfigLevelPriceService;

    @Resource
    private ICostTaskCategoryManagementService costTaskCategoryManagementService;
    @Resource
    private ContractLedgerMapper contractLedgerMapper;
    @Resource
    private CostTaskDailyPaperEntryMapper costTaskDailyPaperEntryMapper;
    @Resource
    private ProjectCollectMapper projectCollectMapper;
    @Resource
    private RosterService rosterService;
    @Resource
    private ICostSalaryDetailsService costSalaryDetailsService;
    @Resource
    private RedisTemplate<String, Integer> redisTemplate;

    @Resource
    private IEvalTaskService evalTaskService;
    @Value("${oa.xsfyOaId:62}")
    private Long xsfyOaId;

    @Value("${oa.xmglfysqrgOaId:8102}")
    private Long xmglfysqrgOaId;


    /**
     * 待审核
     */
    private static final List<CostTaskStatusEnum> TO_BE_REVIEWED = Arrays.asList(CostTaskStatusEnum.DEJFZRSH, CostTaskStatusEnum.DYJFZRSH, CostTaskStatusEnum.DXMJLSH);
    /**
     * 完成佐证文件最大数
     */
    private final static int COMPLETE_FILES_MAX_NUM = 10;

    public final static int TASK_FIRST_LEVEL = 1;

    public final static int TASK_SECOND_LEVEL = 2;

    public final static int TASK_MAX_LEVEL = 3;
    /**
     * 文件最大值
     */
    private static final long FILE_MAX_SIZE = 50L * 1024 * 1024;
    public static final String PARAM_PROJECT_ID = "projectId";
    public static final String PARAM_TASK_TYPE = "taskType";
    public static final String QT = "qt";


    @Override
    public Page<CostDeliverTaskVO> findPage(PageRequest pageRequest, Map<String, Object> filter) {
        Long projectId = (Long) filter.get(PARAM_PROJECT_ID);
        if (null == projectId) {
            return PageUtils.page(new ArrayList<>(), pageRequest);
        }
        Integer taskType = (Integer) filter.get(PARAM_TASK_TYPE);

        // 获取所有工单数据(已经构建好父子层级关系)
        List<CostDeliverTaskVO> taskVOList = findAll(projectId, taskType);
        if (CollUtil.isEmpty(taskVOList)) {
            return PageUtils.page(new ArrayList<>(), pageRequest);
        }

        // 获取工单类别信息
        Map<Integer, CostTaskCategoryManagement> costTaskCategoryMap = costTaskCategoryManagementService.getCostTaskCategoryMap();
        if (CollUtil.isEmpty(costTaskCategoryMap)) {
            return PageUtils.page(taskVOList, pageRequest);
        }

        // 按工单类别分组(只对一级工单进行分组)
        Map<Integer, List<CostDeliverTaskVO>> tasksByCategory = taskVOList.stream()
                .filter(task -> task.getTaskLevel() == TASK_FIRST_LEVEL)
                .collect(Collectors.groupingBy(CostDeliverTaskVO::getTaskCategory));

        // 构建类别树节点
        List<CostDeliverTaskVO> categoryTreeNodes = new ArrayList<>();

        costTaskCategoryMap.forEach((categoryId, category) -> {
            List<CostDeliverTaskVO> tasksInCategory = tasksByCategory.getOrDefault(categoryId, new ArrayList<>());
            if (CollUtil.isNotEmpty(tasksInCategory)) {
                // 创建类别节点
                CostDeliverTaskVO categoryNode = new CostDeliverTaskVO();
                categoryNode.setTaskName(category.getTaskCategoryName());
                categoryNode.setTaskCategory(categoryId);
                categoryNode.setTaskCategoryTxt(category.getTaskCategoryName());
                categoryNode.setChildren(tasksInCategory);
                categoryTreeNodes.add(categoryNode);
            }
        });

        // 根据类别sort值排序
        categoryTreeNodes.sort((a, b) -> sortByTaskCategory(costTaskCategoryMap, a, b));

        return PageUtils.page(categoryTreeNodes, pageRequest);
    }

    @Override
    public Page<CostDeliverTaskVO> getChildrenPage(PageRequest pageRequest, Long taskId, JSONObject requestObject) {
        // 获取能查看的子工单
        Set<Long> childrenIds = requestObject.getObject("childrenIds", new TypeReference<Set<Long>>() {
        });
        CostDeliverTask costDeliverTask = getById(taskId);
        ProjectInfo projectInfo = projectInfoMapper.selectById(costDeliverTask.getProjectId());
        // 查询所有子工单
        List<CostDeliverTask> allChildTasks = getAllChildTasks(taskId);
        List<CostDeliverTaskVO> taskVOList = allChildTasks.stream()
                .map(CostDeliverTaskVO::buildVo)
                .collect(Collectors.toList());
        taskVOList = buildCostDeliverTaskVOList(taskVOList, Collections.singletonList(projectInfo), true);
        // 为null时查看所有下级工单，空数组时则无权限
        if (childrenIds != null) {
            // 过滤出能查看的工单
            List<CostDeliverTaskVO> filterList = new ArrayList<>();
            taskVOList.forEach(t -> {
                if (childrenIds.contains(t.getId())) {
                    t.setChildren(CollUtil.emptyIfNull(t.getChildren()).stream()
                            .filter(c -> childrenIds.contains(c.getId()))
                            .collect(Collectors.toList()));
                    filterList.add(t);
                }
            });
            taskVOList = filterList;
        }
        return PageUtils.page(taskVOList, pageRequest);
    }

    @Override
    public List<CostDeliverTaskVO> findAll(Long projectId, Integer taskType) {
        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
        // 查询所有工单
        CostDeliverTaskDTO query = CostDeliverTaskDTO.builder().projectId(projectId).build();
        List<CostDeliverTaskVO> taskVOList = baseMapper.findByCostDeliverTaskDTO(query);
        if (null != taskType) {
            taskVOList = taskVOList.stream().filter(vo -> taskType.equals(vo.getTaskType())).collect(Collectors.toList());
        }
        if (CollUtil.isEmpty(taskVOList)) {
            return new ArrayList<>();
        }
        // 工单数据处理
        return buildCostDeliverTaskVOList(taskVOList, Collections.singletonList(projectInfo), false);
    }

    /**
     * 构建包含父子关系的工单VO集合
     *
     * @param tasks           无父子关系的工单VO集合
     * @param projectInfoList 项目信息集合
     * @return {@link List }<{@link CostDeliverTaskVO }>
     */
    private List<CostDeliverTaskVO> buildCostDeliverTaskVOList(Collection<CostDeliverTaskVO> tasks, List<ProjectInfo> projectInfoList, boolean desc) {
        if (CollUtil.isEmpty(tasks)) {
            return Collections.emptyList();
        }
        // 去重
        Collection<CostDeliverTaskVO> distinctTasks = tasks.stream()
                .collect(Collectors.toMap(CostDeliverTaskVO::getId, task -> task,
                        (existing, replacement) -> existing)).values();
        List<CostDeliverTaskVO> taskVOList = new ArrayList<>(distinctTasks);

        // 售前工单id集合
        List<Long> supportTaskIds = taskVOList.stream()
                .filter(vo -> EnumUtils.valueEquals(vo.getTaskType(), ProjectTaskKindEnum.PRE_SALES_SUPPORT))
                .map(CostDeliverTaskVO::getId)
                .collect(Collectors.toList());

        // 查询售前经理的上级信息
        Map<Long, Roster> preSaleLeaderMap;

        // 查询售前工单未审核工时
        Map<Long, BigDecimal> waitReviewSupportHoursMap;

        // 存在售前工单
        if (CollUtil.isNotEmpty(supportTaskIds)) {
            // 填充是否有填报工时
            fillWorkHoursStatus(taskVOList);
            waitReviewSupportHoursMap = getWaitReviewSupportHoursMap(supportTaskIds);
            preSaleLeaderMap = rosterService.findUserLeaderMap(CollStreamUtil.toSet(projectInfoList, ProjectInfo::getSalesmanUserId));
        } else {
            preSaleLeaderMap = Collections.emptyMap();
            waitReviewSupportHoursMap = Collections.emptyMap();
        }

        //查询税率字典
        final Map<Integer, String> taxRateMap = idictService.getDictKvList("税率").getData().stream()
                .collect(Collectors.toMap(key -> Integer.parseInt(key.getValue()), DictKvVo::getName,
                        (existing, replacement) -> existing));

        // 查询工单类别
        final Map<Integer, CostTaskCategoryManagement> costTaskCategoryMap = costTaskCategoryManagementService.getCostTaskCategoryMap();

        // 获取项目Map
        final Map<Long, ProjectInfo> projetcInfoMap = CollStreamUtil.toMap(projectInfoList, ProjectInfo::getId, e -> e);

        // 排序
        taskVOList.sort(Comparator.comparing(CostDeliverTaskVO::getId));
        if (desc) {
            Collections.reverse(taskVOList);
        }
        // 根据级别将工单分组
        Map<Integer, List<CostDeliverTaskVO>> taskVoMap = taskVOList.stream()
                .peek(t -> {
                    // 设置异常标记
                    t.setTaskAbnormal();
                    t.calculateTotalHours();
                    if (EnumUtils.valueEquals(t.getDisassemblyType(), CostTaskDisassemblyTypeEnum.STANDARD_WORK_ORDER)
                            && EnumUtils.valueEquals(t.getTaskType(), ProjectTaskKindEnum.PRE_SALES_SUPPORT)) {
                        t.setWaitReviewHours(waitReviewSupportHoursMap.getOrDefault(t.getId(), BigDecimal.ZERO));
                    }
                    if (!t.getTaskStatus().equals(CostTaskStatusEnum.YWC.getValue())
                            && EnumUtils.valueEquals(t.getTaskType(), ProjectTaskKindEnum.AFTER_SALES_DELIVERY)) {
                        t.setActualLaborCost(null);
                        t.setActualLaborCostStr(null);
                    }
                })
                .collect(Collectors.groupingBy(CostDeliverTaskVO::getTaskLevel));

        List<CostDeliverTaskVO> thirdTaskList = taskVoMap.getOrDefault(TASK_MAX_LEVEL, Collections.emptyList());
        List<CostDeliverTaskVO> secondTaskList = taskVoMap.getOrDefault(TASK_SECOND_LEVEL, Collections.emptyList());
        List<CostDeliverTaskVO> firstTaskList = taskVoMap.getOrDefault(TASK_FIRST_LEVEL, Collections.emptyList());

        // 三级工单处理
        thirdTaskList.forEach(e -> assembleCostDeliverTaskVO(e, null,
                projetcInfoMap.get(e.getProjectId()), taxRateMap, costTaskCategoryMap, Collections.emptyMap()));
        Map<Long, List<CostDeliverTaskVO>> thirdTaskMap = CollStreamUtil.groupByKey(thirdTaskList, CostDeliverTaskVO::getParentId);

        if (CollUtil.isEmpty(firstTaskList) && CollUtil.isEmpty(secondTaskList)) {
            thirdTaskList.forEach(CostDeliverTaskServiceImpl::calculateData);
            return thirdTaskList;
        }

        // 二级工单处理
        secondTaskList.forEach(e -> assembleCostDeliverTaskVO(e, thirdTaskMap,
                projetcInfoMap.get(e.getProjectId()), taxRateMap, costTaskCategoryMap, Collections.emptyMap()));

        Map<Long, List<CostDeliverTaskVO>> secondTaskMap = CollStreamUtil.groupByKey(secondTaskList, CostDeliverTaskVO::getParentId);

        if (CollUtil.isEmpty(firstTaskList)) {
            secondTaskList.forEach(CostDeliverTaskServiceImpl::calculateData);
            return secondTaskList;
        }

        // 一级工单处理
        firstTaskList.forEach(e -> assembleCostDeliverTaskVO(e, secondTaskMap,
                projetcInfoMap.get(e.getProjectId()), taxRateMap, costTaskCategoryMap, preSaleLeaderMap));
        // 计算工时、成本和产值
        firstTaskList.forEach(CostDeliverTaskServiceImpl::calculateData);
        return firstTaskList;
    }

    private static void calculateData(CostDeliverTaskVO task) {
        // 计算实际总工时
        task.calculateTotalHours();
        // 计算子工单的实际人工成本总和
        sumActualLaborCost(task);
        // 计算分配产值和结算产值
        BigDecimal income = task.getIncome();
        if (income != null) {
            if (CostTaskStatusEnum.YWC.getValue().equals(task.getTaskStatus())) {
                // 已完成状态的工单,产值为结算产值
                task.setSettledIncome(income);
            } else {
                // 非已完成状态的工单,产值为分配产值
                task.setAllocatedIncome(income);
            }
        }
    }

    private void fillWorkHoursStatus(List<CostDeliverTaskVO> taskVOList) {
        if (CollUtil.isEmpty(taskVOList)) {
            return;
        }

        // 1. 收集所有工单ID
        Set<Long> taskIds = taskVOList.stream().map(CostDeliverTaskVO::getId).collect(Collectors.toSet());

        // 2. 获取所有子工单
        List<CostDeliverTask> childTasks = lambdaQuery().in(CostDeliverTask::getParentId, taskIds).list();
        List<Long> secondTask = childTasks.stream()
                .filter(t -> t.getTaskLevel() == TASK_SECOND_LEVEL)
                .map(CostDeliverTask::getParentId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(secondTask)) {
            childTasks.addAll(lambdaQuery().in(CostDeliverTask::getParentId, secondTask).list());
        }
        taskIds.addAll(CollStreamUtil.toSet(childTasks, CostDeliverTask::getId));

        // 3. 查询工单是否有填报工时
        List<CostTaskDailyPaperEntry> paperEntryList = costTaskDailyPaperEntryMapper.selectList(
                Wrappers.lambdaQuery(CostTaskDailyPaperEntry.class)
                        .in(CostTaskDailyPaperEntry::getTaskId, taskIds));

        // 4. 获取有填写工时的工单ID集合
        Set<Long> hasHoursTaskIds = paperEntryList.stream()
                .map(CostTaskDailyPaperEntry::getTaskId)
                .collect(Collectors.toSet());

        // 5. 构建工单ID与其子工单ID的映射关系
        Map<Long, Set<Long>> parentChildMap = new HashMap<>();
        for (CostDeliverTask childTask : childTasks) {
            Long parentId = childTask.getParentId();
            parentChildMap.computeIfAbsent(parentId, k -> new HashSet<>()).add(childTask.getId());
        }

        // 6. 设置工单的fillWorkHours状态
        for (CostDeliverTaskVO taskVO : taskVOList) {
            Long taskId = taskVO.getId();
            Set<Long> childTaskIds = parentChildMap.getOrDefault(taskId, Collections.emptySet());
            // 初始化为当前子任务集合
            Set<Long> allChildIds = new HashSet<>(childTaskIds);
            // 将孙任务加入总集合
            for (Long childTaskId : childTaskIds) {
                Set<Long> grandChildIds = parentChildMap.getOrDefault(childTaskId, Collections.emptySet());
                allChildIds.addAll(grandChildIds);
            }
            // 合并所有子任务和孙任务
            childTaskIds.addAll(allChildIds);
            // 检查当前工单或其子工单是否有填写工时
            boolean hasFillWorkHours = hasHoursTaskIds.contains(taskId) ||
                    childTaskIds.stream().anyMatch(hasHoursTaskIds::contains);

            taskVO.setFillWorkHours(hasFillWorkHours);
        }
    }


    /**
     * 获取待审核的售前工时
     *
     * @param waitReviewSupportTaskIds 等待审核支持任务 ID
     * @return {@link Map }<{@link Long }, {@link BigDecimal }>
     */
    private Map<Long, BigDecimal> getWaitReviewSupportHoursMap(List<Long> waitReviewSupportTaskIds) {
        if (CollUtil.isEmpty(waitReviewSupportTaskIds)) {
            return Collections.emptyMap();
        }
        List<CostTaskDailyPaperEntry> taskDailyPaperEntries = getCostTaskDailyPaperEntries(waitReviewSupportTaskIds);
        Map<Long, BigDecimal> resultMap = new HashMap<>(0);
        taskDailyPaperEntries.forEach(e -> {
            BigDecimal hours = resultMap.getOrDefault(e.getTaskId(), BigDecimal.ZERO);
            BigDecimal normalHours = e.getNormalHours();
            if (null != normalHours) {
                hours = hours.add(normalHours);
            }
            BigDecimal addedHours = e.getAddedHours();
            if (null != addedHours) {
                hours = hours.add(addedHours);
            }
            resultMap.put(e.getTaskId(), hours);
        });
        return resultMap;
    }

    private List<CostTaskDailyPaperEntry> getCostTaskDailyPaperEntries(List<Long> waitReviewSupportTaskIds) {
        if (CollUtil.isEmpty(waitReviewSupportTaskIds)) {
            return Collections.emptyList();
        }
        return costTaskDailyPaperEntryMapper.selectList(Wrappers.lambdaQuery(CostTaskDailyPaperEntry.class)
                .eq(CostTaskDailyPaperEntry::getApprovalStatus, ApprovalStatusEnum.DSH.getValue())
                .in(CostTaskDailyPaperEntry::getTaskId, waitReviewSupportTaskIds));
    }

    /**
     * 补全工单VO字段
     *
     * @param task                上级工单
     * @param nextTaskMap         下级工单集合
     * @param projectInfo         项目信息
     * @param taxRateMap          税率字典
     * @param costTaskCategoryMap 工单类别集合
     * @param preSaleLeaderMap    preSaleLeaderMap
     * @return 包含父子关联关系的工单集合
     */
    private CostDeliverTaskVO assembleCostDeliverTaskVO(CostDeliverTaskVO task,
                                                        Map<Long, List<CostDeliverTaskVO>> nextTaskMap,
                                                        ProjectInfo projectInfo,
                                                        Map<Integer, String> taxRateMap,
                                                        Map<Integer, CostTaskCategoryManagement> costTaskCategoryMap,
                                                        Map<Long, Roster> preSaleLeaderMap) {
        if (null == task) {
            return null;
        }

        nextTaskMap = Optional.ofNullable(nextTaskMap).orElse(Collections.emptyMap());
        List<CostDeliverTaskVO> children = nextTaskMap.get(task.getId());
        if (CollUtil.isNotEmpty(children)) {
            children.forEach(e -> {
                e.setParentTaskStatus(task.getTaskStatus());
                e.setHigherManagerId(task.getManagerId());
                e.setHigherManagerName(task.getManagerName());
            });
            task.setChildren(children);

            BigDecimal normalHours = children.stream()
                    .map(CostDeliverTaskVO::getNormalHours)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal workOvertimeHours = children.stream()
                    .map(CostDeliverTaskVO::getWorkOvertimeHours)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal restOvertimeHours = children.stream()
                    .map(CostDeliverTaskVO::getRestOvertimeHours)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal holidayOvertimeHours = children.stream()
                    .map(CostDeliverTaskVO::getHolidayOvertimeHours)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            task.setNormalHours(normalHours);
            task.setWorkOvertimeHours(workOvertimeHours);
            task.setRestOvertimeHours(restOvertimeHours);
            task.setHolidayOvertimeHours(holidayOvertimeHours);

        }

        // 总成工单不展示工时
        if (CostTaskDisassemblyTypeEnum.TOTAL_WORK_ORDER.getValue().equals(task.getDisassemblyType())) {
            task.setNormalHours(null);
            task.setWorkOvertimeHours(null);
            task.setRestOvertimeHours(null);
            task.setHolidayOvertimeHours(null);
            // 设置异常标记
            task.setTaskAbnormal();
        }
        if (task.getTaskLevel() == TASK_FIRST_LEVEL && null != projectInfo) {
            setFirstHigherManager(task, projectInfo, preSaleLeaderMap);
        }

        if (task.getStartDate() != null && task.getEndDate() != null) {
            // 进度计算
            long duration = ChronoUnit.DAYS.between(task.getStartDate(), task.getEndDate()) + 1;
            task.setDuration(duration);
            if (LocalDate.now().isAfter(task.getStartDate())) {
                long progressing = ChronoUnit.DAYS.between(task.getStartDate(), LocalDate.now()) + 1;
                task.setProgress(BigDecimal.valueOf(progressing).divide(BigDecimal.valueOf(duration), 2, RoundingMode.HALF_UP));
            }
        }

        // 产值计算
        BigDecimal income = task.getIncome();
        if (income != null) {
            if (CostTaskStatusEnum.YWC.getValue().equals(task.getTaskStatus())) {
                // 已完成状态的工单,产值为结算产值
                task.setSettledIncome(income);
            } else {
                // 非已完成状态的工单,产值为分配产值
                task.setAllocatedIncome(income);
            }
        }

        // 字典值处理
        task.setTaxRateTxt(CollUtil.isNotEmpty(taxRateMap) ? taxRateMap.getOrDefault(task.getTaxRate(), StringUtils.EMPTY) : StringUtils.EMPTY);
        task.setTaskTypeTxt(EnumUtils.getNameByValue(ProjectTaskKindEnum.class, task.getTaskType()));
        task.setTaskStatusTxt(EnumUtils.getNameByValue(CostTaskStatusEnum.class, task.getTaskStatus()));
        task.setDisassemblyTypeTxt(EnumUtils.getNameByValue(CostTaskDisassemblyTypeEnum.class, task.getDisassemblyType()));
        task.setTaskCategoryTxt(costTaskCategoryMap.getOrDefault(task.getTaskCategory(), new CostTaskCategoryManagement()).getTaskCategoryName());

        // 解密计算
        try {
            if (task.getBudgetCost() == null) {
                task.setBudgetCost(StrUtil.isNotBlank(task.getBudgetCostStr()) && StrUtil.isNotBlank(AESEncryptor.justDecrypt(task.getBudgetCostStr()))
                        ? new BigDecimal(AESEncryptor.justDecrypt(task.getBudgetCostStr()))
                        : BigDecimal.ZERO);
            }
            if (EnumUtils.valueEquals(task.getTaskType(), ProjectTaskKindEnum.PRE_SALES_SUPPORT)) {
                if (task.getActualLaborCost() == null) {
                    task.setActualLaborCost(StrUtil.isNotEmpty(task.getActualLaborCostStr()) && CollUtil.isEmpty(children) && StrUtil.isNotBlank(AESEncryptor.justDecrypt(task.getActualLaborCostStr()))
                            ? new BigDecimal(AESEncryptor.justDecrypt(task.getActualLaborCostStr()))
                            : BigDecimal.ZERO);
                }
            } else {
                if (task.getTaskStatus().equals(CostTaskStatusEnum.YWC.getValue()) && StrUtil.isNotBlank(AESEncryptor.justDecrypt(task.getActualLaborCostStr()))) {
                    task.setActualLaborCost(new BigDecimal(AESEncryptor.justDecrypt(task.getActualLaborCostStr())));
                }
            }
        } catch (Exception e) {
            log.error("解密失败", e);
        }
        return task;
    }

    private static void setFirstHigherManager(CostDeliverTaskVO task, ProjectInfo projectInfo, Map<Long, Roster> preSaleLeaderMap) {
        // 获取上级信息

        if (ProjectTaskKindEnum.AFTER_SALES_DELIVERY.getValue().equals(task.getTaskType())) {
            task.setHigherManagerId(projectInfo.getManagerUserId());
            task.setHigherManagerName(projectInfo.getManagerUserName());
        } else {
            Long preSaleUserId = projectInfo.getPreSaleUserId();

            if (task.getManagerId().equals(preSaleUserId)) {
                // 获取上级领导信息
                Roster preSaleLeader = preSaleLeaderMap.getOrDefault(task.getManagerId(), new Roster());
                task.setHigherManagerId(preSaleLeader.getId());
                task.setHigherManagerName(preSaleLeader.getAliasName());
            } else {
                task.setHigherManagerId(preSaleUserId);
                task.setHigherManagerName(projectInfo.getPreSaleUserName());
            }
        }

    }

    private static int sortByTaskCategory(Map<Integer, CostTaskCategoryManagement> costTaskCategoryMap, CostDeliverTaskVO a, CostDeliverTaskVO b) {
        // 根据工单类别排序
        CostTaskCategoryManagement categoryA = costTaskCategoryMap.get(a.getTaskCategory());
        CostTaskCategoryManagement categoryB = costTaskCategoryMap.get(b.getTaskCategory());

        // 如果类别不存在,排在最后
        if (categoryA == null && categoryB == null) {
            return 0;
        }
        if (categoryA == null) {
            return 1;
        }
        if (categoryB == null) {
            return -1;
        }

        // 按sort字段升序排序
        int sortCompare = categoryA.getSort().compareTo(categoryB.getSort());
        if (sortCompare != 0) {
            return sortCompare;
        }

        // 最后按ID排序
        return a.getId().compareTo(b.getId());
    }

    /**
     * 批量创建一级工单
     *
     * @param projectId                     项目 id
     * @param costDeliverTaskAddEditDTOList 成本 交付任务 添加 编辑 dto list
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchCreateFirstDeliverTask(Long projectId, List<CostDeliverTaskAddEditDTO> costDeliverTaskAddEditDTOList) {
        if (CollUtil.isEmpty(costDeliverTaskAddEditDTOList)) {
            throw new ValidationException("请输入工单信息");
        }
        ProjectInfo projectInfo = checkFirstLevelAuth(projectId, ProjectTaskKindEnum.AFTER_SALES_DELIVERY);

        String projectName = StringUtils.defaultIfEmpty(projectInfo.getItemName(), StringUtils.EMPTY);
        // 工单集合
        List<CostDeliverTask> costDeliverTaskList = new ArrayList<>();
        // 获取最新预算成本
        final Map<String, DeliverCostBudgetListVO> latestCostBudgetMap = getLatestCostBudgetMap(projectId);
        // 工单类别集合
        final Map<Integer, CostTaskCategoryManagement> costTaskCategoryMap = costTaskCategoryManagementService.getCostTaskCategoryMap();
        for (CostDeliverTaskAddEditDTO taskAddEditDTO : costDeliverTaskAddEditDTOList) {
            CostDeliverTask costDeliverTask = BeanUtil.copyProperties(taskAddEditDTO, CostDeliverTask.class);
            // 工单编号
            final String taskNo = generateTaskNo(ProjectTaskKindEnum.AFTER_SALES_DELIVERY, costTaskCategoryMap,
                    taskAddEditDTO.getTaskCategory(), taskAddEditDTO.getManagerName());
            costDeliverTask.setParentId(null)
                    .setTaskNo(taskNo)
                    .setTaskType(ProjectTaskKindEnum.AFTER_SALES_DELIVERY.getValue())
                    .setProjectId(projectId)
                    .setProjectName(projectName)
                    .setAccountOaId(taskAddEditDTO.getAccountOaId())
                    .setTaxRate(taskAddEditDTO.getTaxRate())
                    .setTaskLevel(NumberUtils.INTEGER_ONE);
            initTaskStatus(costDeliverTask);

            final String key = getCostBudgetMapKey(costDeliverTask);
            DeliverCostBudgetListVO costBudgetListVO = latestCostBudgetMap.getOrDefault(key, new DeliverCostBudgetListVO());
            costDeliverTask.setAccountId(costBudgetListVO.getAccountId()).setAccountName(costBudgetListVO.getAccountName());

            costDeliverTaskList.add(BaseBuildEntityUtil.buildInsert(costDeliverTask));
            // 验证预计工时
            verifyEstimatedHours(costDeliverTask);
            // 验证产值
            verifyIncome(taskAddEditDTO.getIncome());
        }
        // 计算预估人工成本
        calculateBudgetCost(costDeliverTaskList);
        // 验证数据
        createDeliverFirstVerify(projectId, costDeliverTaskList, latestCostBudgetMap);
        // 批量创建工单
        costDeliverTaskList.forEach(CostDeliverTask::encrypt);
        saveBatch(costDeliverTaskList);
        // 批量发送消息
        List<BaseSendMsgDTO> addTaskMsgList = CollStreamUtil.toList(costDeliverTaskList, CostDeliverTaskServiceImpl::buildMsgDtoByAddTask);
        bcpMessageService.batchSendMsg(addTaskMsgList);
    }

    /**
     * 计算预算成本
     *
     * @param costDeliverTaskList 成本交付任务列表
     */
    private void calculateBudgetCost(List<CostDeliverTask> costDeliverTaskList) {
        List<CalculateLaborCostDTO> laborCostDTOList = new ArrayList<>();
        costDeliverTaskList.forEach(task -> {
            if (EnumUtils.valueEquals(task.getDisassemblyType(), CostTaskDisassemblyTypeEnum.STANDARD_WORK_ORDER)) {
                CalculateLaborCostDTO calculateLaborCostDTO = new CalculateLaborCostDTO()
                        .setId(task.getId())
                        .setRelateId(task.getId())
                        .setRelateTypeEnum(CostSalaryRelateTypeEnum.AFTER_TASK_ESTIMATE)
                        .setNormalHours(task.getEstimatedHours())
                        .setUserId(task.getManagerId());
                laborCostDTOList.add(calculateLaborCostDTO);
            }
        });
        if (CollUtil.isEmpty(laborCostDTOList)) {
            return;
        }
        // 计算人工成本
        Map<Long, CostSalaryDTO> salaryMap = costConfigLevelPriceService.batchCalculateLaborCost(laborCostDTOList);
        costDeliverTaskList.forEach(task -> {
            if (EnumUtils.valueEquals(task.getDisassemblyType(), CostTaskDisassemblyTypeEnum.STANDARD_WORK_ORDER)) {
                CostSalaryDTO salaryDTO = salaryMap.getOrDefault(task.getId(), CostSalaryDTO.empty());
                task.setBudgetCost(salaryDTO.getLaborCost().toString());
            }
        });
        // 记录成本薪资明细
        costSalaryDetailsService.batchSaveOrUpdate(salaryMap.values());
    }

    private void calculateBudgetCost(List<CostDeliverTask> aList, List<CostDeliverTask> bList) {
        List<CostDeliverTask> allList = new ArrayList<>();
        allList.addAll(aList);
        allList.addAll(bList);
        calculateBudgetCost(allList);
        Map<Long, String> budgetCostMap = CollStreamUtil.toMap(allList, CostDeliverTask::getId, CostDeliverTask::getBudgetCost);
        bList.forEach(task -> {
            if (EnumUtils.valueEquals(task.getDisassemblyType(), CostTaskDisassemblyTypeEnum.STANDARD_WORK_ORDER)) {
                task.setBudgetCost(budgetCostMap.getOrDefault(task.getId(), BigDecimal.ZERO.toString()));
            }

        });
        aList.forEach(task -> {
            if (EnumUtils.valueEquals(task.getDisassemblyType(), CostTaskDisassemblyTypeEnum.STANDARD_WORK_ORDER)) {
                task.setBudgetCost(budgetCostMap.getOrDefault(task.getId(), BigDecimal.ZERO.toString()));
            }
        });
    }


    /**
     * 工单头部统计信息
     *
     * @return {@link CostTaskTopCountMsgVO }
     */
    @Override
    public CostTaskTopCountMsgVO taskTopCountMsg(Integer taskType) {
        // 获取当前用户信息
        PigxUser user = getUser();
        Long managerId = user.getId();
        // 获取该工单负责人信息
        SysUserVo userVo = BaseBuildEntityUtil.apiResult(remoteUserService.getUserById(managerId));
        // 获取该工单负责人的工单列表
        List<CostDeliverTaskVO> deliverTaskList = getPersonalTasks(taskType);

        deliverTaskList.forEach(CostDeliverTaskVO::decrypt);
        if (CollUtil.isEmpty(deliverTaskList)) {
            return new CostTaskTopCountMsgVO(userVo);
        }
        if (EnumUtils.valueEquals(taskType, ProjectTaskKindEnum.PRE_SALES_SUPPORT)) {
            return getSupportTaskCountVO(deliverTaskList, userVo);
        }

        return getDeliverTaskCountVO(deliverTaskList, userVo);
    }

    private CostTaskTopCountMsgVO getSupportTaskCountVO(List<CostDeliverTaskVO> deliverTaskList, SysUserVo userVo) {
        List<Long> taskIds = CollStreamUtil.toList(deliverTaskList, CostDeliverTaskVO::getId);
        Map<Long, BigDecimal> waitReviewSupportHoursMap = getWaitReviewSupportHoursMap(taskIds);
        // 获取该工单负责人的本月确认成本
        BigDecimal thisMonthConfirmCost = getSupportTaskConfirmCostByYearMonth(YearMonth.now(),
                CollStreamUtil.toList(deliverTaskList, CostDeliverTaskVO::getId));
        return new CostTaskTopCountMsgVO(userVo)
                .setToBeReviewedCount(Math.toIntExact(waitReviewSupportHoursMap.size()))
                .setThisMonthConfirmCost(thisMonthConfirmCost);
    }

    private BigDecimal getSupportTaskConfirmCostByYearMonth(YearMonth yearMonth, List<Long> taskIds) {
        if (CollUtil.isEmpty(taskIds)) {
            return BigDecimal.ZERO;
        }
        List<CostTaskDailyPaperEntry> taskDailyPaperEntries = costTaskDailyPaperEntryMapper.selectList(Wrappers.lambdaQuery(CostTaskDailyPaperEntry.class)
                .likeRight(CostTaskDailyPaperEntry::getSubmissionDate, yearMonth.toString())
                .in(CostTaskDailyPaperEntry::getTaskId, taskIds)
                .eq(CostTaskDailyPaperEntry::getApprovalStatus, ApprovalStatusEnum.YTG.getValue()));
        return taskDailyPaperEntries.stream()
                .peek(CostTaskDailyPaperEntry::decrypt)
                .map(p -> {
                    String actualLaborCost = p.getActualLaborCost();
                    return StringUtils.isNotBlank(actualLaborCost) ? new BigDecimal(actualLaborCost) : BigDecimal.ZERO;
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @NotNull
    private static CostTaskTopCountMsgVO getDeliverTaskCountVO(List<CostDeliverTaskVO> deliverTaskList, SysUserVo userVo) {
        final Integer zero = NumberUtils.INTEGER_ZERO;
        // 获取该工单负责人的本月确认成本
        BigDecimal thisMonthConfirmCost = getDeliverTaskConfirmCostByYearMonth(deliverTaskList, YearMonth.now());
        // 获取该工单负责人的工单状态分组计数
        Map<Integer, Integer> taskStatusCountMap = deliverTaskList.stream()
                .collect(Collectors.groupingBy(CostDeliverTaskVO::getTaskStatus, Collectors.summingInt(e -> NumberUtils.INTEGER_ONE)));

        // 初始化一个包含所有状态且值为0的Map
        Map<CostTaskCountEnum, Integer> taskCountMap = Arrays.stream(CostTaskCountEnum.values())
                .collect(Collectors.toMap(taskCountEnum -> taskCountEnum, taskCount -> zero));
        // 如果Map为空，则返回默认值
        if (CollUtil.isEmpty(taskStatusCountMap)) {
            return new CostTaskTopCountMsgVO(userVo).setThisMonthConfirmCost(thisMonthConfirmCost);
        }
        // 遍历Map，将每个状态的值设置为对应状态的计数
        taskStatusCountMap.forEach((taskStatus, count) -> {
            List<CostTaskCountEnum> taskCountEnums = CostTaskCountEnum.getTaskCountEnumsByValue(EnumUtils.getEnumByValue(CostTaskStatusEnum.class, taskStatus));
            taskCountEnums.forEach(enumItem -> taskCountMap.computeIfPresent(enumItem, (k, oldCount) -> oldCount + count));
        });
        // 返回结果
        return new CostTaskTopCountMsgVO(userVo)
                .setUnfinishedCount(taskCountMap.get(CostTaskCountEnum.UNFINISHED))
                .setToBeDisassembledCount(taskCountMap.get(CostTaskCountEnum.TO_BE_DISASSEMBLED))
                .setToBeReviewedCount(taskCountMap.get(CostTaskCountEnum.TO_BE_REVIEWED))
                .setThisMonthConfirmCost(thisMonthConfirmCost);
    }

    @NotNull
    private static BigDecimal getDeliverTaskConfirmCostByYearMonth(List<CostDeliverTaskVO> deliverTaskList, YearMonth currentYearMonth) {
        return deliverTaskList.stream()
                .filter(item -> CostTaskDisassemblyTypeEnum.STANDARD_WORK_ORDER.getValue().equals(item.getDisassemblyType()))
                .filter(item -> item.getCompletionTime() != null)
                .filter(item -> YearMonth.from(item.getCompletionTime()).equals(currentYearMonth))
                .map(CostDeliverTaskVO::getActualLaborCost)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 通过工单
     *
     * @param id id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void passTask(Long id) {
        // 获取工单信息
        CostDeliverTask task = getCostDeliverTask(id);
        // 判断工单是否为待审核状态
        CostTaskStatusEnum taskStatusEnum = getCostTaskStatusEnum(task);
        LocalDateTime nowTime = LocalDateTime.now();
        Integer taskStatus = getNextValByEnum(taskStatusEnum);
        Set<String> errorMessages = new HashSet<>();
        try {
            // 校验实际人工成本是否超出预算
            verifyActualLaborCost(task);
        } catch (ServiceException e) {
            errorMessages.add(e.getMessage());
        }
        if (!errorMessages.isEmpty()) {
            throw new ServiceException(String.join("\n", errorMessages) + "对应的成本科目预算将超支，请联系项目经理处理～");
        }
        // 当工单状态为已完成并且为1级工单时批量更新子节点工单
        if (CostTaskStatusEnum.YWC.getValue().equals(taskStatus) && NumberUtils.INTEGER_ONE.equals(task.getTaskLevel())) {
            List<CostDeliverTask> deliverTaskList = updateTaskLeafNode(task, nowTime, taskStatus);
            deliverTaskList.forEach(CostDeliverTask::encrypt);
            this.updateBatchById(deliverTaskList);
            return;
        }
        // 否则只更新子节点工单
        this.updateById(buildTaskUpdateData(nowTime, task, taskStatus).encrypt());
    }

    /**
     * 更新子节点状态为已完成（共三级节点）
     *
     * @param task    工单
     * @param nowTime 现在时间
     * @return {@link List }<{@link CostDeliverTask }>
     */
    private List<CostDeliverTask> updateTaskLeafNode(CostDeliverTask task, LocalDateTime nowTime, Integer taskStatus) {
        List<CostDeliverTask> deliverTaskList = getAllChildTasks(task.getId());
        deliverTaskList.add(task);
        return deliverTaskList.stream()
                .map(item -> buildTaskUpdateData(nowTime, item, taskStatus))
                .collect(Collectors.toList());
    }


    /**
     * 获取所有子任务
     * (会对成本解密)
     *
     * @param id id
     * @return {@link List }<{@link CostDeliverTask }>
     */
    private List<CostDeliverTask> getAllChildTasks(Long id) {
        // 获取第二级子节点列表
        List<CostDeliverTask> secondChildrenList = getChildrenByParentIds(Collections.singleton(id));
        // 组装二级子节点集合
        List<CostDeliverTask> deliverTaskList = new ArrayList<>(secondChildrenList);
        // 获取第三级子节点列表
        List<CostDeliverTask> thirdChildrenList = getChildrenByParentIds(CollStreamUtil.toSet(secondChildrenList, CostDeliverTask::getId));
        if (CollUtil.isNotEmpty(thirdChildrenList)) {
            // 组装三级子节点集合
            deliverTaskList.addAll(thirdChildrenList);
        }
        return deliverTaskList;
    }


    private List<CostDeliverTask> getChildrenByParentIds(Set<Long> parentIds) {
        if (CollUtil.isEmpty(parentIds)) {
            return new ArrayList<>();
        }
        return lambdaQuery().in(CostDeliverTask::getParentId, parentIds).list()
                .stream().map(CostDeliverTask::decrypt).collect(Collectors.toList());
    }

    /**
     * 构建任务更新数据
     *
     * @param nowTime    现在时间
     * @param task       任务
     * @param taskStatus 任务状态
     * @return {@link CostDeliverTask }
     */
    private static CostDeliverTask buildTaskUpdateData(LocalDateTime nowTime, CostDeliverTask task, Integer taskStatus) {
        return BaseBuildEntityUtil.buildUpdate(task.setTaskStatus(taskStatus)
                .setCompletionTime(completionTime(nowTime, task, taskStatus))
                .setAuditTime(auditTime(nowTime, task, taskStatus))
                .setReturnReason(null)
                .setReturnTime(null)
                .setReturnUserId(null)
                .setReturnUserName(null));
    }

    private static LocalDateTime auditTime(LocalDateTime nowTime, CostDeliverTask task, Integer taskStatus) {
        return NumberUtils.INTEGER_ONE.equals(task.getTaskLevel()) || !CostTaskStatusEnum.YWC.getValue().equals(taskStatus)
                ? nowTime : task.getAuditTime();
    }

    private static LocalDateTime completionTime(LocalDateTime nowTime, CostDeliverTask task, Integer taskStatus) {
        return CostTaskStatusEnum.YWC.getValue().equals(taskStatus) ? nowTime : task.getCompletionTime();
    }

    /**
     * 退回工单
     *
     * @param id  id
     * @param dto DTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void returnTask(Long id, CostTaskReturnReasonDTO dto) {
        CostDeliverTask task = getCostDeliverTask(id);
        // 已完成状态的无法退回
        if (CostTaskStatusEnum.YWC.getValue().equals(task.getTaskStatus())) {
            throw new ServiceException("该工单已完成,不能退回");
        }
        // 获取当前用户信息
        PigxUser user = getUser();
        LocalDateTime nowTime = LocalDateTime.now();
        task.setTaskStatus(CostTaskStatusEnum.BTH.getValue())
                .setAuditTime(nowTime)
                .setReturnReason(dto.getReturnReason())
                .setReturnTime(nowTime)
                .setReturnUserId(user.getId())
                .setReturnUserName(user.getName())
                .setEvaluationStatus(EvalTaskStatusEnum.DPJ.getValue());
        baseMapper.updateById(BaseBuildEntityUtil.buildUpdate(task).encrypt());
        if (EnumUtils.valueEquals(task.getDisassemblyType(), CostTaskDisassemblyTypeEnum.STANDARD_WORK_ORDER)) {
            // 删除工资明细记录
            costSalaryDetailsService.removeBatchByRelateIds(Collections.singleton(task.getId()), CostSalaryRelateTypeEnum.AFTER_TASK_ACTUAL_COST);
        }
        // 发送消息通知
        bcpMessageService.sendMsg(buildMsgDtoByReturnTask(task));

        //删除工单评价
        evalTaskService.deleteEvaluation(id);
    }

    /**
     * 查看退回信息
     *
     * @param id id
     * @return {@link CostTaskReturnInfoVO }
     */
    @Override
    public CostTaskReturnInfoVO returnInfo(Long id) {
        CostDeliverTask task = getCostDeliverTask(id);
        if (!CostTaskStatusEnum.BTH.getValue().equals(task.getTaskStatus())) {
            throw new ServiceException("该工单状态不是退回状态,没有退回信息");
        }
        return new CostTaskReturnInfoVO(task.getTaskName(), task.getReturnUserName(), task.getReturnReason(), task.getReturnTime());
    }

    /**
     * 提交完成佐证
     *
     * @param id  id
     * @param dto DTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void taskFinishCorroboration(Long id, CostFinishCorroborationDTO dto) {
        CostDeliverTask task = getCostDeliverTask(id);
        String deliveryDesc = dto.getDeliveryDesc();
        String completeFiles = dto.getCompleteFiles();
        BigDecimal normalHours = dto.getNormalHours();
        BigDecimal workOverTimeHours = dto.getWorkOverTimeHours();
        BigDecimal restOverTimeHours = dto.getRestOverTimeHours();
        BigDecimal holidayOverTimeHours = dto.getHolidayOverTimeHours();

        // 验证数据
        verifyData(completeFiles, deliveryDesc, task, workOverTimeHours, restOverTimeHours, holidayOverTimeHours, normalHours);

        task.setSubmitCompletionTime(LocalDateTime.now())
                .setTaskStatus(getValByTaskLevel(task.getTaskLevel()))
                .setDeliverDesc(deliveryDesc)
                .setCompleteFiles(completeFiles)
                .setWorkOvertimeHours(workOverTimeHours)
                .setRestOvertimeHours(restOverTimeHours)
                .setHolidayOvertimeHours(holidayOverTimeHours)
                .setNormalHours(normalHours);
        // 标准工单才需要计算人工成本
        if (CostTaskDisassemblyTypeEnum.STANDARD_WORK_ORDER.getValue().equals(task.getDisassemblyType())) {
            CostSalaryDTO costSalaryDTO = costConfigLevelPriceService.calculateLaborCost(
                    new CalculateLaborCostDTO()
                            .setId(task.getId())
                            .setRelateId(task.getId())
                            .setRelateTypeEnum(CostSalaryRelateTypeEnum.AFTER_TASK_ACTUAL_COST)
                            .setUserId(task.getManagerId())
                            .setNormalHours(normalHours)
                            .setWorkOvertimeHours(workOverTimeHours)
                            .setRestOvertimeHours(restOverTimeHours)
                            .setHolidayOvertimeHours(holidayOverTimeHours));
            // 保存人工成本明细
            costSalaryDetailsService.batchSaveOrUpdate(Collections.singleton(costSalaryDTO));
            BigDecimal actualLaborCost = costSalaryDTO.getLaborCost();
            task.setActualLaborCost(actualLaborCost.toString());
        }
        task.encrypt();
        baseMapper.updateById(BaseBuildEntityUtil.buildUpdate(task));
        if (null != task.getParentId()) {
            CostDeliverTask parentTask = getById(task.getParentId());
            // 当前项目下所有工单集合
            parentTask.decrypt();
            final List<CostDeliverTask> allTaskListByProject = getTasksByProjectId(parentTask.getProjectId());
            // 当前项目下所有工单Map
            final Map<Long, CostDeliverTask> allTaskMap = new HashMap<>(allTaskListByProject.size());
            allTaskListByProject.forEach(costDeliverTask -> {
                allTaskMap.put(costDeliverTask.getId(), costDeliverTask);
            });
            updateParentActualLaborCost(task, parentTask, allTaskListByProject, allTaskMap);
        }
    }

    /**
     * 验证数据
     *
     * @param completeFiles        完整文件
     * @param deliveryDesc         交货描述
     * @param task                 任务
     * @param workOverTimeHours    加班时间
     * @param restOverTimeHours    休息时间
     * @param holidayOverTimeHours 节假日营业时间
     * @param normalHours          正常工时
     */
    private void verifyData(String completeFiles, String deliveryDesc, CostDeliverTask task,
                            BigDecimal workOverTimeHours, BigDecimal restOverTimeHours,
                            BigDecimal holidayOverTimeHours, BigDecimal normalHours) {
        // 获取所有子任务
        List<CostDeliverTask> allChildTaskList = getAllChildTasks(task.getId());
        if (CollUtil.isNotEmpty(allChildTaskList)) {
            allChildTaskList.stream()
                    .filter(Objects::nonNull)
                    .map(CostDeliverTask::getTaskStatus)
                    .filter(status -> !CostTaskStatusEnum.EJFZRYTG.getValue().equals(status) && !CostTaskStatusEnum.YJFZRYTG.getValue().equals(status))
                    .findFirst()
                    .ifPresent(status -> {
                        throw new ServiceException("存在子工单未通过审核,不允许提交完成佐证");
                    });
        }
        if (CostTaskStatusEnum.DCJ.getValue().equals(task.getTaskStatus())) {
            throw new ServiceException("该工单状态为待拆解状态,不允许提交完成佐证");
        }
        // 获取文件map
        Map<Long, SysFile> fileMap = getFileMap(completeFiles);
        if (StrUtil.isAllBlank(deliveryDesc, completeFiles)) {
            throw new ServiceException("交付说明和完成佐证必须必填其中一个,不允许都为空");
        }
        if (fileMap.size() > COMPLETE_FILES_MAX_NUM) {
            throw new ServiceException("完成佐证不能超过10个文件");
        }
        fileMap.values().forEach(sysFile -> {
            if (sysFile.getFileSize() > FILE_MAX_SIZE) {
                throw new ServiceException("文件大小不能超过50M");
            }
        });

        // 计算工单周期天数
        long periodDays = ChronoUnit.DAYS.between(task.getStartDate(), task.getEndDate()) + 1;

        // 验证正常工时不超过周期天数*7小时
        if (normalHours != null && normalHours.compareTo(BigDecimal.valueOf(periodDays * 7)) > 0) {
            throw new ServiceException("正常工时不能超过周期天数*7小时");
        }

        // 计算实际总工时
        BigDecimal actualTotalHours = BigDecimal.ZERO;
        if (normalHours != null) {
            actualTotalHours = actualTotalHours.add(normalHours);
        }
        if (workOverTimeHours != null) {
            actualTotalHours = actualTotalHours.add(workOverTimeHours);
        }
        if (restOverTimeHours != null) {
            actualTotalHours = actualTotalHours.add(restOverTimeHours);
        }
        if (holidayOverTimeHours != null) {
            actualTotalHours = actualTotalHours.add(holidayOverTimeHours);
        }

        // 验证实际总工时不超过周期天数*24小时
        if (actualTotalHours.compareTo(ManHourValidator.TWENTY_FOUR_DECIMAL.multiply(BigDecimal.valueOf(periodDays))) > 0) {
            throw new ServiceException("实际总工时不能超过 周期天数 × " + ManHourValidator.TWENTY_FOUR_DECIMAL + "小时");
        }

        if (CostTaskDisassemblyTypeEnum.TOTAL_WORK_ORDER.getValue().equals(task.getDisassemblyType())) {
            if (isOvertimeHoursPresent(workOverTimeHours, restOverTimeHours, holidayOverTimeHours)) {
                throw new ServiceException("总成工单不可提交加班工时");
            }
        }
    }

    /**
     * 存在加班时间
     *
     * @param workOverTimeHours    加班时间
     * @param restOverTimeHours    休息时间
     * @param holidayOverTimeHours 节假日营业时间
     * @return boolean
     */
    private static boolean isOvertimeHoursPresent(BigDecimal workOverTimeHours, BigDecimal restOverTimeHours, BigDecimal holidayOverTimeHours) {
        final BigDecimal zero = BigDecimal.ZERO;
        return Objects.nonNull(workOverTimeHours) && !Objects.equals(workOverTimeHours, zero) ||
                Objects.nonNull(restOverTimeHours) && !Objects.equals(restOverTimeHours, zero) ||
                Objects.nonNull(holidayOverTimeHours) && !Objects.equals(holidayOverTimeHours, zero);
    }


    /**
     * 一级工单编辑
     *
     * @param costDeliverTaskAddEditDTO 成本 交付任务 添加编辑 DTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editFirstDeliverTask(CostDeliverTaskAddEditDTO costDeliverTaskAddEditDTO) {
        Long id = costDeliverTaskAddEditDTO.getId();
        if (id == null) {
            throw new ServiceException("工单id不能为空~");
        }
        CostDeliverTask originalTask = getById(id).decrypt();
        if (!NumberUtils.INTEGER_ONE.equals(originalTask.getTaskLevel())) {
            throw new ServiceException("一级任务才可进行此编辑~");
        }
        ProjectInfo projectInfo = checkFirstLevelAuth(originalTask.getProjectId(), ProjectTaskKindEnum.AFTER_SALES_DELIVERY);
        if (CostTaskStatusEnum.isSubmitted(originalTask.getTaskStatus())) {
            throw new ServiceException("工单待拆解、未完成状态才可进行编辑~");
        }
        if (originalTask.getEndDate().isBefore(LocalDate.now())) {
            throw new ServiceException("工单已到期不可编辑~");
        }
        // 默认配置的工单不可编辑
        if (originalTask.getDefaultConf()) {
            throw new ServiceException("默认配置的工单不可编辑~");
        }
        // 子级工单
        final List<CostDeliverTask> childTaskList = lambdaQuery()
                .in(CostDeliverTask::getParentId, id).list()
                .stream().map(CostDeliverTask::decrypt)
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(childTaskList)) {
            // 校验预算成本是否低于已经拆解的子级工单预算总额
            BigDecimal budgetCost = costDeliverTaskAddEditDTO.getBudgetCost();
            BigDecimal childCostTotal = childTaskList.stream().map(CostDeliverTask::getBudgetCost).filter(Objects::nonNull)
                    .map(BigDecimal::new)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (budgetCost.compareTo(childCostTotal) < NumberUtils.INTEGER_ZERO) {
                throw new ServiceException("预算成本不能低于子级工单成本总额！");
            }
            if (!CostTaskDisassemblyTypeEnum.TOTAL_WORK_ORDER.getValue().equals(costDeliverTaskAddEditDTO.getDisassemblyType())) {
                throw new ServiceException("已拆解的总成工单不能编辑拆解类型~");
            }
        }
        // 获取当前项目对应预算的所有一级工单集合（除去正在编辑的工单）
        final List<CostDeliverTask> cretedTaskList = lambdaQuery().ne(CostDeliverTask::getId, id)
                .eq(CostDeliverTask::getTaskLevel, NumberUtils.INTEGER_ONE)
                .eq(CostDeliverTask::getProjectId, costDeliverTaskAddEditDTO.getProjectId())
                .eq(CostDeliverTask::getAccountOaId, costDeliverTaskAddEditDTO.getAccountOaId())
                .eq(CostDeliverTask::getTaxRate, costDeliverTaskAddEditDTO.getTaxRate()).list()
                .stream().map(CostDeliverTask::decrypt).collect(Collectors.toList());
        BigDecimal budgetCostTotal = BigDecimal.ZERO;
        BigDecimal incomeTotal = BigDecimal.ZERO;
        for (CostDeliverTask item : cretedTaskList) {
            // 预算 已完成取实际成本，未完成取预算成本
            if (EnumUtils.valueEquals(item.getTaskStatus(), CostTaskStatusEnum.YWC)) {
                budgetCostTotal = budgetCostTotal.add(new BigDecimal(item.getActualLaborCost()));
            } else {
                budgetCostTotal = budgetCostTotal.add(new BigDecimal(item.getBudgetCost()));
            }
            BigDecimal income = item.getIncome();
            if (income != null) {
                incomeTotal = incomeTotal.add(income);
            }
        }
        CalculateLaborCostDTO laborCostDTO = new CalculateLaborCostDTO()
                .setId(costDeliverTaskAddEditDTO.getId())
                .setRelateId(costDeliverTaskAddEditDTO.getId())
                .setRelateTypeEnum(CostSalaryRelateTypeEnum.AFTER_TASK_ESTIMATE)
                .setNormalHours(costDeliverTaskAddEditDTO.getEstimatedHours())
                .setUserId(costDeliverTaskAddEditDTO.getManagerId());
        // 计算人工成本
        final CostSalaryDTO costSalaryDto = costConfigLevelPriceService.calculateLaborCost(laborCostDTO);
        budgetCostTotal = budgetCostTotal.add(costSalaryDto.getLaborCost());
        incomeTotal = incomeTotal.add(costDeliverTaskAddEditDTO.getIncome());
        // 获取最新预算成本
        final Map<String, DeliverCostBudgetListVO> latestCostBudgetMap = getLatestCostBudgetMap(originalTask.getProjectId());
        // 当前科目的总预算 (注意使用新的科目的oaId)
        final String costBudgetMapKey = getCostBudgetMapKey(costDeliverTaskAddEditDTO.getAccountOaId(), costDeliverTaskAddEditDTO.getTaxRate());
        final DeliverCostBudgetListVO costBudgetVO = latestCostBudgetMap.get(costBudgetMapKey);
        if (costBudgetVO == null) {
            throw new ServiceException("当前科目不存在");
        }
        BigDecimal budgetAmount = Optional.ofNullable((costBudgetVO).getBudgetAmountIncludedTax()).orElse(BigDecimal.ZERO);
        if (budgetCostTotal.compareTo(budgetAmount) > NumberUtils.INTEGER_ZERO) {
            throw new ServiceException("预算成本+当前关联科目对应其他工单预算，不能超过该科目的总预算！");
        }
        BigDecimal estimatedTotalIncome = costDeliverExpensesReimburseService.getEstimatedTotalIncome(originalTask.getProjectId());
        if (incomeTotal.compareTo(estimatedTotalIncome) > NumberUtils.INTEGER_ZERO) {
            throw new ServiceException("工单产值不能超过项目总产值！");
        }
        Integer originalDisassemblyType = originalTask.getDisassemblyType();
        // 更新工单信息
        originalTask.setTaskName(costDeliverTaskAddEditDTO.getTaskName())
                .setProjectName(projectInfo.getItemName())
                .setTaskDesc(costDeliverTaskAddEditDTO.getTaskDesc())
                .setAccountId(costBudgetVO.getAccountId())
                .setAccountName(costBudgetVO.getAccountName())
                .setAccountOaId(costBudgetVO.getAccountOaId())
                .setTaxRate(costBudgetVO.getTaxRate())
                .setEstimatedHours(costDeliverTaskAddEditDTO.getEstimatedHours())
                .setIncome(costDeliverTaskAddEditDTO.getIncome())
                .setBudgetCost(costDeliverTaskAddEditDTO.getBudgetCost().toString())
                .setManagerId(costDeliverTaskAddEditDTO.getManagerId())
                .setManagerName(costDeliverTaskAddEditDTO.getManagerName())
                .setStartDate(costDeliverTaskAddEditDTO.getStartDate())
                .setEndDate(costDeliverTaskAddEditDTO.getEndDate())
                .setTaskCategory(costDeliverTaskAddEditDTO.getTaskCategory());
        // 待拆解的工单或标准工单 可以编辑拆解类型
        if (EnumUtils.valueEquals(originalTask.getTaskStatus(), CostTaskStatusEnum.DCJ)
                || EnumUtils.valueEquals(originalDisassemblyType, CostTaskDisassemblyTypeEnum.STANDARD_WORK_ORDER)) {
            originalTask.setDisassemblyType(costDeliverTaskAddEditDTO.getDisassemblyType());
            initTaskStatus(originalTask);
        }
        // 工单拆解时间校验
        verifyCostTaskDate(originalTask);
        // 验证预计工时
        verifyEstimatedHours(originalTask);
        // 验证产值
        verifyIncome(costDeliverTaskAddEditDTO.getIncome());
        // 更新工单
        baseMapper.updateById(BaseBuildEntityUtil.buildUpdate(originalTask.encrypt()));
        // 保存人工成本明细
        costSalaryDetailsService.batchSaveOrUpdate(Collections.singleton(costSalaryDto));
        // 发送消息通知
        bcpMessageService.sendMsg(buildMsgDtoByEditTask(originalTask));
    }


    /**
     * 工单拆解
     *
     * @param parentId                      父工单 ID
     * @param costDeliverTaskAddEditDTOList 成本 交付任务 添加 编辑 dto list
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void decomposition(Long parentId, List<CostDeliverTaskAddEditDTO> costDeliverTaskAddEditDTOList) {
        if (CollUtil.isEmpty(costDeliverTaskAddEditDTOList)) {
            throw new ServiceException("子级工单数据不能为空~");
        }
        // 验证带拆解的售后交付父级工单
        CostDeliverTask parentTask = verifyDecompositionParentTask(parentId);
        // 验证编辑权限
        checkEditAuth(parentId, parentTask.getProjectId(), ProjectTaskKindEnum.AFTER_SALES_DELIVERY);
        // 当前项目下所有工单集合
        final List<CostDeliverTask> allTaskListByProject = getTasksByProjectId(parentTask.getProjectId());
        // 已创建的工单Map
        final Map<Long, CostDeliverTask> createdTaskMap = new HashMap<>(costDeliverTaskAddEditDTOList.size());
        // 当前项目的子工单Map
        final Map<Long, List<CostDeliverTask>> childTasksMap = new HashMap<>(0);
        // 当前项目下所有工单Map
        final Map<Long, CostDeliverTask> allTaskMap = new HashMap<>(allTaskListByProject.size());
        allTaskListByProject.forEach(costDeliverTask -> {
            allTaskMap.put(costDeliverTask.getId(), costDeliverTask);
            Long pid = costDeliverTask.getParentId();
            if (costDeliverTask.getParentId() != null) {
                childTasksMap.computeIfAbsent(pid, k -> new ArrayList<>()).add(costDeliverTask);
                if (pid.equals(parentId)) {
                    createdTaskMap.put(costDeliverTask.getId(), costDeliverTask);
                }
            }
        });
        // 新的完整子工单集合
        List<CostDeliverTask> newTaskList = new ArrayList<>();
        // 删除、更新、新增的子工单集合
        List<CostDeliverTask> removeTasks = new ArrayList<>();
        List<CostDeliverTask> updateTasks = new ArrayList<>();
        List<CostDeliverTask> addTasks = new ArrayList<>();
        //前端传回的工单id集合
        Set<Long> taskIds = new HashSet<>();
        final LocalDate nowDate = LocalDate.now();
        for (CostDeliverTaskAddEditDTO taskAddEditDTO : costDeliverTaskAddEditDTOList) {
            verifyChildTask(taskAddEditDTO, taskIds, parentTask, addTasks, createdTaskMap, nowDate, updateTasks, newTaskList);
        }
        // 遍历已创建的工单Map
        createdTaskMap.forEach((id, task) -> {
            if (!taskIds.contains(id)) {
                // 不存在入参集合中则为删除
                removeTasks.add(task);
                // 删除工单时，删除对应子工单
                removeTasks.addAll(childTasksMap.getOrDefault(task.getId(), Collections.emptyList()));
            } else if (!updateTasks.contains(task)) {
                // 存在入参集合，不存在更新集合中则为不变的工单
                newTaskList.add(task);
            }
        });

        // 校验删除的工单
        removeTasks.forEach(CostDeliverTaskServiceImpl::removeDeliverTaskVerify);

        // 子工单总产值 校验
        verifyChildTaskTotalIncome(newTaskList, parentTask.getIncome());

        // 计算预估人工成本
        calculateBudgetCost(addTasks, updateTasks);

        // 子级工单总成本校验并更新父级工单预算
        updateParentTaskBudget(parentTask, newTaskList, allTaskMap);

        // 删除工单对应成本明细
        Set<Long> removeIds = CollStreamUtil.toSet(removeTasks, CostDeliverTask::getId);
        costSalaryDetailsService.removeBatchByRelateIds(removeIds, CostSalaryRelateTypeEnum.AFTER_TASK_ESTIMATE);

        final Map<Integer, CostTaskCategoryManagement> costTaskCategoryMap = costTaskCategoryManagementService.getCostTaskCategoryMap();
        // 批量保存、更新、删除工单
        // 生成编号并加密
        addTasks.forEach(t -> {
            // 工单编号
            final String taskNo = generateTaskNo(ProjectTaskKindEnum.AFTER_SALES_DELIVERY, costTaskCategoryMap,
                    t.getTaskCategory(), t.getManagerName());
            t.setTaskNo(taskNo);
            t.encrypt();
        });
        updateTasks.forEach(CostDeliverTask::encrypt);
        saveBatch(addTasks);
        updateBatchById(updateTasks);
        removeBatchByIds(removeTasks);

        // 更新父级工单状态
        lambdaUpdate().eq(CostDeliverTask::getId, parentId)
                .set(CostDeliverTask::getTaskStatus, CostTaskStatusEnum.WWC.getValue())
                .update();
        // 发送消息通知
        List<BaseSendMsgDTO> noticeMsgList = new ArrayList<>();
        addTasks.forEach(task -> noticeMsgList.add(buildMsgDtoByAddTask(task)));
        updateTasks.forEach(task -> noticeMsgList.add(buildMsgDtoByEditTask(task)));
        removeTasks.forEach(task -> noticeMsgList.add(buildMsgDtoByDelTask(task)));
        bcpMessageService.batchSendMsg(noticeMsgList);
    }

    /**
     * 批量新增编辑售前支撑工单
     *
     * @param supportTaskDTO 成本支持任务添加编辑 DTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAddEditSupportTask(CostSupportTaskAddEditDTO supportTaskDTO, boolean add) {
        Long projectId = supportTaskDTO.getProjectId();
        // 校验项目权限
        ProjectInfo projectInfo;
        if (add) {
            projectInfo = checkFirstLevelAuth(projectId, ProjectTaskKindEnum.PRE_SALES_SUPPORT);
        } else {
            projectInfo = checkEditAuth(supportTaskDTO.getId(), projectId, ProjectTaskKindEnum.PRE_SALES_SUPPORT);
        }
        // 创建工单树处理器
        SupportTaskTreeProcessor processor = new SupportTaskTreeProcessor();
        Long firstLevelTaskId = supportTaskDTO.getId();
        CostDeliverTask originalParentTask;
        // 如果工单id为空则创建父工单
        if (firstLevelTaskId == null) {
            CostDeliverTask costDeliverTask = new CostDeliverTask();
            costDeliverTask.setTaskLevel(TASK_FIRST_LEVEL);
            BeanUtil.copyProperties(supportTaskDTO, costDeliverTask);
            originalParentTask = BaseBuildEntityUtil.buildInsert(costDeliverTask);
            firstLevelTaskId = costDeliverTask.getId();
        } else {
            originalParentTask = getById(firstLevelTaskId).decrypt();
            if (Boolean.TRUE.equals(originalParentTask.getDefaultConf())) {
                throw new ServiceException("默认配置的工单不能进行编辑~");
            }
            if (EnumUtils.valueEquals(originalParentTask.getTaskStatus(), CostTaskStatusEnum.JS)) {
                throw new ServiceException("已结束的工单不能进行编辑~");
            }
        }
        // 售前成本
        DeliverCostBudgetListVO supportCostBudget = getSupportCostBudget(projectId);
        //  验证项目合同会签流程是否归档
        verifyProjectContract(projectId);
        // 当前父工单下集合
        List<CostDeliverTask> originalAllTasks = getAllChildTasks(firstLevelTaskId);
        originalAllTasks.add(originalParentTask);
        Map<Long, CostDeliverTask> originalTaskMap = CollStreamUtil.toMap(originalAllTasks, CostDeliverTask::getId, e -> e);
        // 校验工单编辑权限
        verifySupportTaskEdit(originalParentTask, originalTaskMap, supportTaskDTO);
        // 处理父工单
        OperateEnum operateEnum = supportTaskDTO.getOperateEnum();
        if (EnumUtils.enumOrEquals(operateEnum, OperateEnum.UPDATE, OperateEnum.ADD)) {
            processor.getProcessedTaskIds().add(firstLevelTaskId);
            originalParentTask.setProjectId(projectId)
                    .setProjectName(projectInfo.getItemName())
                    .setTaskName(supportTaskDTO.getTaskName())
                    .setTaskDesc(supportTaskDTO.getTaskDesc())
                    .setDisassemblyType(supportTaskDTO.getDisassemblyType())
                    .setManagerId(supportTaskDTO.getManagerId())
                    .setManagerName(supportTaskDTO.getManagerName())
                    .setTaskCategory(supportTaskDTO.getTaskCategory())
                    .setTaskStatus(CostTaskStatusEnum.ZC.getValue())
                    .setAccountId(supportCostBudget.getAccountId())
                    .setTaxRate(supportCostBudget.getTaxRate())
                    .setAccountOaId(supportCostBudget.getAccountOaId())
                    .setAccountName(supportCostBudget.getAccountName());
            if (OperateEnum.UPDATE.equals(operateEnum)) {
                processor.getUpdateTasks().add(BaseBuildEntityUtil.buildUpdate(originalParentTask));
            } else {
                processor.getAddTasks().add(originalParentTask);
            }
        }
        // 递归处理工单树
        if (CollUtil.isNotEmpty(supportTaskDTO.getChildren())
                && EnumUtils.valueEquals(originalParentTask.getDisassemblyType(), CostTaskDisassemblyTypeEnum.TOTAL_WORK_ORDER)) {
            for (CostSupportTaskAddEditDTO childDTO : supportTaskDTO.getChildren()) {
                childDTO.setTaskLevel(originalParentTask.getTaskLevel() + 1);
                childDTO.setParentId(originalParentTask.getId());
                processor.processTaskTree(childDTO, originalParentTask, originalTaskMap);
            }
        }
        // 找出需要删除的工单
        originalTaskMap.forEach((id, task) -> {
            if (!processor.getProcessedTaskIds().contains(id) && !id.equals(originalParentTask.getId())) {
                processor.getRemoveTasks().add(task);
            }
        });
        final Map<Integer, CostTaskCategoryManagement> costTaskCategoryMap = costTaskCategoryManagementService.getCostTaskCategoryMap();
        // 工单删除校验
        removeSupportTaskVerify(processor.getRemoveTasks());
        // 生成编号并加密
        processor.getAddTasks().forEach(t -> {
            // 工单编号
            final String taskNo = generateTaskNo(ProjectTaskKindEnum.PRE_SALES_SUPPORT, costTaskCategoryMap,
                    t.getTaskCategory(), t.getManagerName());
            t.setTaskNo(taskNo);
            t.encrypt();
        });
        processor.getUpdateTasks().forEach(CostDeliverTask::encrypt);
        // 批量保存、更新、删除工单
        saveBatch(processor.getAddTasks());
        updateBatchById(processor.getUpdateTasks());
        removeBatchByIds(processor.getRemoveTasks());
        // 发送消息通知
        List<BaseSendMsgDTO> noticeMsgList = new ArrayList<>();
        processor.getAddTasks().forEach(task -> noticeMsgList.add(buildMsgDtoByAddTask(task)));
        processor.getUpdateTasks().forEach(task -> noticeMsgList.add(buildMsgDtoByEditTask(task)));
        processor.getRemoveTasks().forEach(task -> noticeMsgList.add(buildMsgDtoByDelTask(task)));
        bcpMessageService.batchSendMsg(noticeMsgList);
    }

    private ProjectInfo checkEditAuth(Long taskId, Long projectId, ProjectTaskKindEnum taskTypeEnum) {
        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);

        if (projectInfo == null) {
            throw new ValidationException("项目不存在");
        }
        // 售前经理Id
        Long preSaleUserId = projectInfo.getPreSaleUserId();
        // 项目经理
        Long managerId = projectInfo.getManagerUserId();
        PigxUser user = getUser();
        Long userId = user.getId();
        switch (taskTypeEnum) {
            case AFTER_SALES_DELIVERY:
                if (Objects.equals(userId, managerId)) {
                    return projectInfo;
                }
                break;
            case PRE_SALES_SUPPORT:
                if (Objects.equals(userId, preSaleUserId)) {
                    return projectInfo;
                }
                break;
            default:
        }
        if (taskId == null) {
            throw new ServiceException("当前用户没有权限进行编辑~");
        }
        CostDeliverTask task = getById(taskId);
        if (task == null) {
            throw new ServiceException("工单不存在~");
        }
        if (userId.equals(task.getManagerId()) && EnumUtils.valueEquals(task.getDisassemblyType(), CostTaskDisassemblyTypeEnum.TOTAL_WORK_ORDER)) {
            return projectInfo;
        }
        Set<Long> manageIds = getManageIds(task);
        if (manageIds.contains(userId)) {
            return projectInfo;
        }
        throw new ServiceException("当前用户没有权限进行编辑~");
    }


    private Set<Long> getManageIds(CostDeliverTask task) {
        if (task == null) {
            throw new ServiceException("工单不存在~");
        }
        Set<Long> manageIds = new HashSet<>();
        int i = 0;
        while (task.getParentId() != null && i < 4) {
            task = getById(task.getParentId());
            if (task == null) {
                break;
            }
            manageIds.add(task.getManagerId());
            i++;
        }
        return manageIds;
    }


    /**
     * 校验支撑任务编辑权限
     *
     * @param originalParentTask 父工单
     * @param originalTaskMap    原工单Map
     * @param editDTO            编辑DTO
     */
    private void verifySupportTaskEdit(CostDeliverTask originalParentTask,
                                       Map<Long, CostDeliverTask> originalTaskMap,
                                       CostSupportTaskAddEditDTO editDTO) {
        // 参数校验
        if (originalParentTask == null || originalTaskMap == null || editDTO == null) {
            throw new ValidationException("参数缺失");
        }

        // 校验父工单
        if (OperateEnum.UPDATE.equals(editDTO.getOperateEnum())) {
            checkTaskHours(originalParentTask, originalTaskMap);
            if (EnumUtils.valueEquals(originalParentTask.getTaskStatus(), CostTaskStatusEnum.JS)) {
                throw new ServiceException("工单「" + originalParentTask.getTaskName() + "」 已结束，不能编辑！");
            }
        }

        // 校验子工单
        List<CostSupportTaskAddEditDTO> children = editDTO.getChildren();
        if (CollUtil.isNotEmpty(children)) {
            for (CostSupportTaskAddEditDTO childDTO : children) {
                // 跳过不需要编辑的工单
                if (childDTO.getOperateEnum() == null || OperateEnum.DEFAULT.equals(childDTO.getOperateEnum())) {
                    continue;
                }

                // 对于更新操作，需要校验原工单
                if (OperateEnum.UPDATE.equals(childDTO.getOperateEnum())) {
                    Long childTaskId = childDTO.getId();
                    if (childTaskId == null) {
                        throw new ServiceException("编辑的工单ID不能为空~");
                    }
                    CostDeliverTask originalTask = originalTaskMap.get(childTaskId);
                    if (originalTask == null) {
                        throw new ServiceException("工单「" + childDTO.getTaskName() + "」 不存在，需进行新增！");
                    }
                    checkTaskHours(originalTask, originalTaskMap);
                }

                // 递归校验子工单
                if (CollUtil.isNotEmpty(childDTO.getChildren())) {
                    verifySupportTaskEdit(originalParentTask, originalTaskMap, childDTO);
                }
            }
        }
    }

    /**
     * 校验工单是否已填报工时
     *
     * @param task    当前工单
     * @param taskMap 工单Map
     */
    private void checkTaskHours(CostDeliverTask task, Map<Long, CostDeliverTask> taskMap) {
        if (CostTaskDisassemblyTypeEnum.STANDARD_WORK_ORDER.getValue().equals(task.getDisassemblyType())) {
            // 标准工单直接判断是否已经存在工时数据
            if (isExistHours(task)) {
                throw new ServiceException("工单【" + task.getTaskName() + "】 已填报工时，不能编辑！");
            }
        } else if (CostTaskDisassemblyTypeEnum.TOTAL_WORK_ORDER.getValue().equals(task.getDisassemblyType())) {
            // 总成工单需要判断子级标准工单是否存在已填报工时的情况
            boolean hasReportedHours = taskMap.values().stream()
                    .filter(subTask -> task.getId().equals(subTask.getParentId()))
                    .filter(subTask -> CostTaskDisassemblyTypeEnum.STANDARD_WORK_ORDER.getValue().equals(subTask.getDisassemblyType()))
                    .anyMatch(CostDeliverTaskServiceImpl::isExistHours);

            if (hasReportedHours) {
                throw new ServiceException("总成工单【" + task.getTaskName() + "】 下存在已填报工时的标准工单，不能编辑！");
            }
        }
    }


    private static void verifyChildTaskTotalIncome(List<CostDeliverTask> newTaskList, BigDecimal incomeTotal) {
        BigDecimal totalIncome = newTaskList.stream().map(CostDeliverTask::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (incomeTotal.compareTo(totalIncome) < 0) {
            throw new ServiceException("子级工单的总产值不能大于父级工单的产值~");
        }
    }

    private static void verifyChildTask(CostDeliverTaskAddEditDTO taskAddEditDTO, Set<Long> taskIds, CostDeliverTask parentTask,
                                        List<CostDeliverTask> addTasks, Map<Long, CostDeliverTask> createdTaskMap,
                                        LocalDate nowDate, List<CostDeliverTask> updateTasks, List<CostDeliverTask> newTaskList) {
        Optional.ofNullable(taskAddEditDTO.getId()).ifPresent(taskIds::add);
        OperateEnum operateEnum = taskAddEditDTO.getOperateEnum();
        if (operateEnum == null || OperateEnum.DEFAULT.equals(operateEnum)) {
            return;
        }
        // 子级工单的周期校验（子级工单时间跨度不可超过父级工单的周期）
        if (taskAddEditDTO.getStartDate().isBefore(parentTask.getStartDate()) || taskAddEditDTO.getEndDate().isAfter(parentTask.getEndDate())) {
            throw new ServiceException("【" + taskAddEditDTO.getTaskName() + "】子级工单的起止时间不能超过父级工单的起止时间~");
        }
        CostDeliverTask newCostDeliverTask;
        switch (operateEnum) {
            case ADD:
                newCostDeliverTask = buildNewDeliverChildTask(parentTask, taskAddEditDTO);
                addTasks.add(newCostDeliverTask);
                break;
            case UPDATE:
                // 获取原工单
                CostDeliverTask originalTask = createdTaskMap.get(taskAddEditDTO.getId());
                // 已创建工单校验
                createdTaskVerify(taskAddEditDTO, originalTask, nowDate);
                newCostDeliverTask = updateDeliverTask(originalTask, taskAddEditDTO, parentTask);
                updateTasks.add(newCostDeliverTask);
                break;
            default:
                return;
        }
        // 工单拆解时间校验
        verifyCostTaskDate(newCostDeliverTask);
        // 验证预计工时
        verifyEstimatedHours(newCostDeliverTask);
        // 验证产值
        verifyIncome(taskAddEditDTO.getIncome());
        newTaskList.add(newCostDeliverTask);
    }

    /**
     * 校验拆解的父工单
     *
     * @param parentId 父 ID
     * @return {@link CostDeliverTask }
     */
    @NotNull
    private CostDeliverTask verifyDecompositionParentTask(Long parentId) {
        CostDeliverTask parentTask = getById(parentId).decrypt();
        if (parentTask == null) {
            throw new ServiceException("父工单不存在~");
        }
        if (NumberUtils.INTEGER_TWO < parentTask.getTaskLevel()) {
            throw new ServiceException("第三级工单不可拆解~");
        }
        if (!CostTaskDisassemblyTypeEnum.TOTAL_WORK_ORDER.getValue().equals(parentTask.getDisassemblyType())) {
            throw new ServiceException("拆解类型为总成工单才能进行拆解~！");
        }
        return parentTask;
    }


    private List<CostDeliverTask> getTasksByProjectId(Long projectId) {
        return lambdaQuery().eq(CostDeliverTask::getProjectId, projectId).list()
                .stream().map(CostDeliverTask::decrypt).collect(Collectors.toList());
    }


    private static CostDeliverTask updateDeliverTask(CostDeliverTask originalTask, CostDeliverTaskAddEditDTO dto, CostDeliverTask parentTask) {
        if (originalTask == null) {
            throw new ServiceException("工单「" + dto.getTaskName() + "」不存在,需进行新增！");
        }
        originalTask.setTaskName(dto.getTaskName())
                .setIncome(dto.getIncome())
                .setTaskCategory(dto.getTaskCategory())
                .setEstimatedHours(dto.getEstimatedHours())
                .setDisassemblyType(dto.getDisassemblyType())
                .setParentId(parentTask.getId())
                .setTaskDesc(dto.getTaskDesc())
                .setTaskType(dto.getTaskType())
                .setBudgetCost(dto.getBudgetCost().toString())
                .setManagerId(dto.getManagerId())
                .setManagerName(dto.getManagerName())
                .setStartDate(dto.getStartDate())
                .setEndDate(dto.getEndDate())
                .setAccountId(parentTask.getAccountId())
                .setAccountOaId(parentTask.getAccountOaId())
                .setAccountName(parentTask.getAccountName())
                .setTaxRate(parentTask.getTaxRate())
                .setProjectId(parentTask.getProjectId())
                .setProjectName(parentTask.getProjectName());
        initTaskStatus(originalTask);
        return BaseBuildEntityUtil.buildUpdate(originalTask);
    }


    private static CostDeliverTask buildNewDeliverChildTask(CostDeliverTask parentTask, CostDeliverTaskAddEditDTO dto) {
        CostDeliverTask costDeliverTask = new CostDeliverTask();
        BeanUtil.copyProperties(dto, costDeliverTask);
        costDeliverTask.setParentId(parentTask.getId());
        costDeliverTask.setTaskLevel(parentTask.getTaskLevel() + 1);
        // 自动继承父级工单的数据
        costDeliverTask.setTaskType(parentTask.getTaskType());
        costDeliverTask.setProjectId(parentTask.getProjectId());
        costDeliverTask.setProjectName(parentTask.getProjectName());

        costDeliverTask.setAccountId(parentTask.getAccountId());
        costDeliverTask.setTaxRate(parentTask.getTaxRate());
        costDeliverTask.setAccountName(parentTask.getAccountName());
        costDeliverTask.setAccountOaId(parentTask.getAccountOaId());

        initTaskStatus(costDeliverTask);
        return BaseBuildEntityUtil.buildInsert(costDeliverTask);
    }

    private static void createdTaskVerify(CostDeliverTaskAddEditDTO costDeliverTaskAddEditDTO, CostDeliverTask originalTask, LocalDate nowDate) {
        if (originalTask != null) {
            // 校验状态是否变更
            if (!originalTask.getTaskStatus().equals(costDeliverTaskAddEditDTO.getTaskStatus())
                    && CostTaskStatusEnum.isSubmitted(originalTask.getTaskStatus())) {
                throw new ServiceException("「" + originalTask.getTaskName() + "」状态发生变更，请重新编辑~");
            }
            Integer taskStatus = originalTask.getTaskStatus();
            if (!CostTaskStatusEnum.DCJ.getValue().equals(taskStatus) && !CostTaskStatusEnum.WWC.getValue().equals(taskStatus)) {
                throw new ServiceException("待拆解、未完成状态的子工单才可进行编辑~");
            }
            // 子工单状态为未完成
            if (CostTaskStatusEnum.WWC.getValue().equals(taskStatus)) {
                // 标准工单类型：超过结束日期不可编辑
                if (EnumUtils.valueEquals(originalTask.getDisassemblyType(), CostTaskDisassemblyTypeEnum.STANDARD_WORK_ORDER)
                        && originalTask.getEndDate().isBefore(nowDate)) {
                    throw new ServiceException("「" + originalTask.getTaskName() + "」标准工单类型，超过结束日期，不可编辑~");
                }
                //总成工单类型：不可编辑（因为已经拆解完成，会影响子工单）
                if (EnumUtils.valueEquals(originalTask.getDisassemblyType(), CostTaskDisassemblyTypeEnum.TOTAL_WORK_ORDER)) {
                    throw new ServiceException("「" + originalTask.getTaskName() + "」总成工单类型，已经拆解完成，不可编辑~");
                }
            }
        }
    }

    /**
     * 查看佐证
     *
     * @param id id
     * @return {@link CostViewCorroborationVO }
     */
    @Override
    public CostViewCorroborationVO taskViewCorroboration(Long id) {
        CostDeliverTask task = getCostDeliverTask(id);
        // 获取文件map
        Map<Long, SysFile> fileMap = getFileMap(task.getCompleteFiles());
        // 获取文件列表
        List<CostCompletionFilesVO> completeFileList = fileMap.values().stream()
                .map(file -> new CostCompletionFilesVO().setFileId(file.getId())
                        .setFileName(file.getOriginal())
                        .setFileUrl(file.getFileUrl()))
                .collect(Collectors.toList());
        // 查询售前工单未审核工时
        Map<Long, BigDecimal> waitReviewSupportHoursMap = getWaitReviewSupportHoursMap(Collections.singletonList(id));
        // 获取上级负责人信息
        CostDeliverTask higherManagerInfo = getHigherManagerInfo(task);
        // 查询工单类别
        Map<Integer, CostTaskCategoryManagement> costTaskCategoryMap = costTaskCategoryManagementService.getCostTaskCategoryMap();

        // 构建返回值
        CostViewCorroborationVO viewCorroborationVO = buildCorroborationVo(task, completeFileList, costTaskCategoryMap);
        if (EnumUtils.valueEquals(task.getTaskType(), ProjectTaskKindEnum.PRE_SALES_SUPPORT)
                && EnumUtils.valueEquals(task.getDisassemblyType(), CostTaskDisassemblyTypeEnum.TOTAL_WORK_ORDER)) {
            // 售前支持总成工单-工时为所有子集之合
            List<CostDeliverTask> allChildTasks = getAllChildTasks(task.getId());
            viewCorroborationVO.setNormalHours(allChildTasks.stream()
                    .map(CostDeliverTask::getNormalHours)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            viewCorroborationVO.setWorkOverTimeHours(allChildTasks.stream()
                    .map(CostDeliverTask::getWorkOvertimeHours)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            viewCorroborationVO.setRestOverTimeHours(allChildTasks.stream()
                    .map(CostDeliverTask::getRestOvertimeHours)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            viewCorroborationVO.setHolidayOverTimeHours(allChildTasks.stream()
                    .map(CostDeliverTask::getHolidayOvertimeHours)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        return viewCorroborationVO
                .setHigherManagerId(higherManagerInfo.getManagerId())
                .setHigherManagerName(higherManagerInfo.getManagerName())
                .setWaitReviewHours(waitReviewSupportHoursMap.getOrDefault(task.getId(), BigDecimal.ZERO));
    }

    private CostDeliverTask getHigherManagerInfo(CostDeliverTask task) {
        CostDeliverTask higherTask = new CostDeliverTask();
        if (task == null) {
            return higherTask;
        }

        // 根据工单级别获取上级负责人
        if (task.getTaskLevel() != TASK_FIRST_LEVEL) {
            // 三级工单的上级负责人为二级工单的负责人
            CostDeliverTask parentTask = getById(task.getParentId());
            if (parentTask != null) {
                return parentTask;
            }
        } else {
            ProjectInfo projectInfo = projectInfoMapper.selectById(task.getProjectId());
            if (ProjectTaskKindEnum.AFTER_SALES_DELIVERY.getValue().equals(task.getTaskType())) {

                higherTask.setManagerId(projectInfo.getManagerUserId());
                higherTask.setManagerName(projectInfo.getManagerUserName());
            } else {
                Long preSaleUserId = projectInfo.getPreSaleUserId();

                if (task.getManagerId().equals(preSaleUserId)) {
                    // 获取上级领导信息
                    Map<Long, Roster> userLeaderMap = rosterService.findUserLeaderMap(Collections.singleton(preSaleUserId));
                    Roster preSaleLeader = userLeaderMap.getOrDefault(preSaleUserId, new Roster());
                    higherTask.setManagerId(preSaleLeader.getId());
                    higherTask.setManagerName(preSaleLeader.getAliasName());
                } else {
                    higherTask.setManagerId(preSaleUserId);
                    higherTask.setManagerName(projectInfo.getPreSaleUserName());
                }
            }
        }
        return higherTask;
    }

    /**
     * 查询最新已确认的人工成本预算
     *
     * @param projectId 项目id
     * @return {@link List }<{@link DeliverCostBudgetListVO }>
     */
    @Override
    public List<DeliverCostBudgetListVO> getConfirmedCost(Long projectId) {
        CostManageVersionVO costManageVersionVO = getLatestConfirmedCostManageVersionVO(projectId, null);
        if (costManageVersionVO == null || costManageVersionVO.getVersionId() == null) {
            return Collections.emptyList();
        }
        //查询版本对应人工成本预算明细
        List<DeliverCostBudgetListVO> costBudgetListList =
                costManageEstimationResultsMapper.findCostBudgetByVersionId(costManageVersionVO.getVersionId(), CostTypeEnum.RGCB.getValue());

        //通过工单查询人工成本预算明细对应已用预算、剩余预算、已确认成本
        getUsedCostByTask(projectId, costBudgetListList);
        return costBudgetListList;
    }

    /**
     * 查询项目最新已确认人工成本预算版本信息
     *
     * @param projectId 项目 ID
     * @return {@link CostManageVersionVO }
     */
    private CostManageVersionVO getLatestConfirmedCostManageVersionVO(Long projectId, ProjectTaskKindEnum taskType) {
        List<CostManageVersionVO> costManageVersionVOList = costManageVersionService.getHistoryVersions(
                new PageRequest(1, 1000),
                CostManageVersionDTO.builder().projectId(projectId).build()).getRecords();
        Stream<CostManageVersionVO> voStream = CollUtil.emptyIfNull(costManageVersionVOList).stream()
                .filter(costManageVersion -> EnumUtils.valueEquals(costManageVersion.getStatus(), CostManageStatusEnum.CONFIRMED))
                .sorted(Comparator.comparing(CostManageVersionVO::getCostBudgetType).reversed()
                        .thenComparing(Comparator.comparing(CostManageVersionVO::getVersionId).reversed()));
        if (taskType == null) {
            return voStream.findFirst().orElse(null);
        }
        switch (taskType) {
            // 售后交付
            case AFTER_SALES_DELIVERY:
                voStream = voStream.filter(costManageVersion -> !EnumUtils.valueEquals(costManageVersion.getCostBudgetType(), CostBudgetTypeEnum.SQCB));
                break;
            case PRE_SALES_SUPPORT:
                voStream = voStream.filter(costManageVersion -> EnumUtils.valueEquals(costManageVersion.getCostBudgetType(), CostBudgetTypeEnum.SQCB));
                break;

            default:
        }

        return voStream.findFirst().orElse(null);
    }

    /**
     * 获取最新剩余可分配预算Map(key: 科目OA - 税率, value: 费用预算VO类)
     *
     * @return {@link Map }<{@link String },{@link DeliverCostBudgetListVO }>
     */
    private Map<String, DeliverCostBudgetListVO> getLatestCostBudgetMap(Long projectId) {
        CostManageVersionVO costManageVersionVO = getLatestConfirmedCostManageVersionVO(projectId, ProjectTaskKindEnum.AFTER_SALES_DELIVERY);
        if (costManageVersionVO == null || costManageVersionVO.getVersionId() == null) {
            return Collections.emptyMap();
        }
        //查询版本对应人工成本预算明细
        List<DeliverCostBudgetListVO> costBudgetListList = costManageEstimationResultsMapper.findCostBudgetByVersionId(costManageVersionVO.getVersionId(), CostTypeEnum.RGCB.getValue());
        getUsedCostByTask(projectId, costBudgetListList);
        // 获取最新成本预算Map(key: 科目OA - oa税率id, value: 费用预算VO类)
        return costBudgetListList.stream().collect(Collectors.toMap(
                e -> getCostBudgetMapKey(e.getAccountOaId(), e.getTaxRate()),
                e -> e, (e1, e2) -> e1));
    }

    /**
     * 获取支持成本预算
     *
     * @param projectId 项目 ID
     * @return {@link DeliverCostBudgetListVO }
     */
    @Override
    public DeliverCostBudgetListVO getSupportCostBudget(Long projectId) {
        CostManageVersionVO costManageVersionVO = getLatestConfirmedCostManageVersionVO(projectId, ProjectTaskKindEnum.PRE_SALES_SUPPORT);
        if (costManageVersionVO == null || costManageVersionVO.getVersionId() == null) {
            throw new ServiceException("未找到最新的已确认成本预算");
        }
        //查询版本对应人工成本预算明细
        List<DeliverCostBudgetListVO> costBudgetListList = costManageEstimationResultsMapper.findCostBudgetByVersionId(costManageVersionVO.getVersionId(), CostTypeEnum.RGCB.getValue());
        DeliverCostBudgetListVO costBudgetListVO = costBudgetListList.stream()
                .filter(e -> EnumUtils.valueEquals(e.getCostBudgetType(), CostBudgetTypeEnum.SQCB))
                .findFirst().orElse(null);
        if (costBudgetListVO == null) {
            throw new ServiceException("未找到最新的已确认售前人工成本");
        }
        return costBudgetListVO;
    }

    /**
     * 通过工单查询人工成本预算明细对应已用预算、剩余预算、已确认成本
     *
     * @param projectId          项目id
     * @param costBudgetListList 人工成本预算明细
     */
    @Override
    public void getUsedCostByTask(Long projectId, List<DeliverCostBudgetListVO> costBudgetListList) {
        //查询项目所有级工单
        List<CostDeliverTaskVO> costDeliverTaskList = findAll(projectId, null);
        //查询税率字典
        Map<Integer, String> taxRateMap = idictService.getDictKvList("税率").getData().stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName));
        costBudgetListList.forEach(costBudget -> {
            String taxRateTxt = null != costBudget.getTaxRate() ? taxRateMap.get(costBudget.getTaxRate()) : null;
            costBudget.setTaxRateTxt(taxRateTxt + ("0".equals(taxRateTxt) ? StringPool.PERCENT : StringPool.EMPTY));
            List<CostDeliverTaskVO> subTaskList = costDeliverTaskList.stream()
                    .filter(e -> Objects.equals(costBudget.getAccountOaId(), e.getAccountOaId())
                            && Objects.equals(costBudget.getTaxRate(), e.getTaxRate())
                            && e.getTaskLevel() == 1)
                    .sorted(Comparator.comparing(CostDeliverTaskVO::getTaskCategory, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(CostDeliverTaskVO::getCtime, Comparator.nullsFirst(Comparator.reverseOrder()))
                            .thenComparing(CostDeliverTaskVO::getId))
                    .collect(Collectors.toList());
            BigDecimal usedBudget = subTaskList.stream()
                    .map(CostDeliverTaskVO::getBudgetCost)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            costBudget.setUsedBudget(usedBudget);
            BigDecimal budgetAmountIncludedTax = Optional.ofNullable(costBudget.getBudgetAmountIncludedTax()).orElse(BigDecimal.ZERO);
            costBudget.setRemainBudget(budgetAmountIncludedTax.subtract(usedBudget));
            costBudget.setTaskList(subTaskList);

            // 1. 计算已完成状态工单的实际人工成本总和
            BigDecimal confirmedBudget = subTaskList.stream()
                    .filter(task -> ProjectTaskKindEnum.PRE_SALES_SUPPORT.getValue().equals(task.getTaskType()) || CostTaskStatusEnum.YWC.getValue().equals(task.getTaskStatus()))
                    .map(CostDeliverTaskVO::getActualLaborCost)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            costBudget.setConfirmedBudget(confirmedBudget);

            // 2. 计算其他状态工单的预算成本总和
            BigDecimal otherTasksBudgetCost = subTaskList.stream()
                    .filter(task -> !CostTaskStatusEnum.YWC.getValue().equals(task.getTaskStatus()))
                    .map(CostDeliverTaskVO::getBudgetCost)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 3. 计算剩余可分配预算
            costBudget.setRemainAllocatableBudget(budgetAmountIncludedTax.subtract(confirmedBudget).subtract(otherTasksBudgetCost));
        });
    }

    /**
     * 查询所有（人工成本、费用报销、外采费用）成本预算明细对应已用预算、剩余预算、已确认成本
     *
     * @param costManageVersion 成本管理版本信息
     */
    @Override
    public Map<String, DeliverCostBudgetListVO> getUsedAllCostBudget(CostManageVersion costManageVersion) {
        if (null == costManageVersion || null == costManageVersion.getId()) {
            return new HashMap<>(0);
        }
        Long projectId = costManageVersion.getProjectId();
        List<DeliverCostBudgetListVO> estimationResultList =
                costManageEstimationResultsMapper.findCostBudgetByVersionId(costManageVersion.getId(), null);

        //查询项目所有一级工单已确认成本总和
        List<CostDeliverTask> taskList = lambdaQuery()
                .eq(CostDeliverTask::getProjectId, projectId)
                .eq(CostDeliverTask::getTaskLevel, 1)
                .eq(CostDeliverTask::getTaskStatus, CostTaskStatusEnum.YWC.getValue())
                .list().stream()
                .map(CostDeliverTask::decrypt).collect(Collectors.toList());

        // 销售费用-差旅费
        DeliverCostBudgetListDto costBudgetListQuery = DeliverCostBudgetListDto.builder().projectId(projectId).costType(1).build();
        List<DeliverCostBudgetListVO> travelExpensesList = costDeliverExpensesReimburseService.findDeliverCostBudget(costBudgetListQuery)
                .stream().filter(e -> xsfyOaId.equals(e.getAccountOaId()))
                .collect(Collectors.toList());

        // 费用报销科目的已用预算取值交付管理-费用报销的「已用预算」总和
        List<DeliverCostBudgetListVO> expensesReimburseList = costDeliverExpensesReimburseService.findDeliverCostBudget(costBudgetListQuery)
                .stream()
                .collect(Collectors.toList());

        // 外采成本科目的已用预算取值交付管理-采购管理的「已用预算」总和
        costBudgetListQuery.setCostType(2);
        List<DeliverCostBudgetListVO> deliverCostList = costDeliverExpensesReimburseService.findDeliverCostBudget(costBudgetListQuery)
                .stream()
                .collect(Collectors.toList());

        // 交付管理-工单管理-售前支撑工单
        List<DeliverCostBudgetListVO> preSalesTaskList = getConfirmedCost(projectId);

        estimationResultList.forEach(resultItem -> {
            // 初始化已用预算为0
            BigDecimal totalActualLaborCost = BigDecimal.ZERO;

            // 1. 处理【项目-管理费用-售前人工】科目 (accountOaId = 8102L)
            if (xmglfysqrgOaId.equals(resultItem.getAccountOaId())) {
                totalActualLaborCost = preSalesTaskList.stream()
                        .filter(task -> Objects.equals(task.getAccountOaId(), resultItem.getAccountOaId())
                                && Objects.equals(task.getTaxRate(), resultItem.getTaxRate()))
                        .map(DeliverCostBudgetListVO::getConfirmedBudget)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            // 2. 处理【销售费用】科目 (accountOaId = 62)
            else if (xsfyOaId.equals(resultItem.getAccountOaId())) {
                Optional<DeliverCostBudgetListVO> matchingTravel = travelExpensesList.stream()
                        .filter(e -> Objects.equals(e.getAccountOaId(), resultItem.getAccountOaId())
                                && Objects.equals(e.getTaxRate(), resultItem.getTaxRate()))
                        .findFirst();

                totalActualLaborCost = matchingTravel
                        .map(DeliverCostBudgetListVO::getUsedBudget)
                        .orElse(BigDecimal.ZERO);
            }
            // 3. 处理其他人工成本科目
            else if (Objects.equals(resultItem.getCostType(), CostTypeEnum.RGCB.getValue())) {
                totalActualLaborCost = taskList.stream()
                        .filter(task -> Objects.equals(task.getAccountOaId(), resultItem.getAccountOaId())
                                && Objects.equals(task.getTaxRate(), resultItem.getTaxRate()))
                        .map(e -> StrUtil.isNotBlank(e.getActualLaborCost()) ? new BigDecimal(e.getActualLaborCost()) : null)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            // 4. 处理外采成本科目
            else if (Objects.equals(resultItem.getCostType(), CostTypeEnum.WCFY.getValue())) {
                totalActualLaborCost = deliverCostList.stream()
                        .filter(cost -> Objects.equals(cost.getAccountOaId(), resultItem.getAccountOaId())
                                && Objects.equals(cost.getTaxRate(), resultItem.getTaxRate()))
                        .map(DeliverCostBudgetListVO::getUsedBudget)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            // 5. 处理费用报销科目
            else if (Objects.equals(resultItem.getCostType(), CostTypeEnum.FYBX.getValue())) {
                totalActualLaborCost = expensesReimburseList.stream()
                        .filter(expense -> Objects.equals(expense.getAccountOaId(), resultItem.getAccountOaId())
                                && Objects.equals(expense.getTaxRate(), resultItem.getTaxRate()))
                        .map(DeliverCostBudgetListVO::getUsedBudget)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            // 设置已用预算
            resultItem.setUsedBudget(totalActualLaborCost);
            // 设置预算金额(含税)，如果为null则默认为0
            resultItem.setBudgetAmountIncludedTax(Optional.ofNullable(resultItem.getBudgetAmountIncludedTax())
                    .orElse(BigDecimal.ZERO));
            // 计算剩余预算 = 预算金额(含税) - 已用预算
            resultItem.setRemainBudget(resultItem.getBudgetAmountIncludedTax().subtract(totalActualLaborCost));
        });

        return CollUtil.isNotEmpty(estimationResultList)
                ? estimationResultList.stream().collect(Collectors.toMap(e -> StrUtil.format("{}-{}", e.getAccountOaId(), e.getTaxRate()), e -> e, (e1, e2) -> e1))
                : new HashMap<>(0);
    }

    /**
     * 获取文件映射
     *
     * @param completeFiles 完整文件
     * @return {@link Map }<{@link Long }, {@link SysFile }>
     */
    private Map<Long, SysFile> getFileMap(String completeFiles) {
        List<Long> fileIds = handleFilesToList(completeFiles);
        if (CollUtil.isEmpty(fileIds)) {
            return Collections.emptyMap();
        }
        return sysFileService.lambdaQuery().in(SysFile::getId, fileIds).list().stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(SysFile::getId, file -> file, (a, b) -> a));
    }

    /**
     * 转换字符串文件ids为列表
     *
     * @param files 文件
     * @return {@link List }<{@link Long }>
     */
    private static List<Long> handleFilesToList(String files) {
        if (StrUtil.isBlank(files)) {
            return Collections.emptyList();
        }
        return Arrays.stream(StringUtils.split(files, StrPool.COMMA))
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }

    /**
     * 获取工单信息
     *
     * @param id id
     */
    private CostDeliverTask getCostDeliverTask(Long id) {
        CostDeliverTask costDeliverTask = getById(id);
        if (costDeliverTask == null) {
            throw new ServiceException("工单不存在");
        }
        return costDeliverTask.decrypt();
    }

    /**
     * 判断工单状态枚举
     *
     * @param task 任务
     * @return {@link CostTaskStatusEnum }
     */
    private static CostTaskStatusEnum getCostTaskStatusEnum(CostDeliverTask task) {
        CostTaskStatusEnum taskStatusEnum = EnumUtils.getEnumByValue(CostTaskStatusEnum.class, task.getTaskStatus());
        if (taskStatusEnum == null) {
            throw new ServiceException("工单状态不能为空");
        }
        if (!TO_BE_REVIEWED.contains(taskStatusEnum)) {
            throw new ServiceException("该工单状态不允许通过");
        }
        return taskStatusEnum;
    }


    private ProjectInfo checkFirstLevelAuth(Long projectId, ProjectTaskKindEnum taskTypeEnum) {
        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
        if (projectInfo == null) {
            throw new ValidationException("项目不存在");
        }
        // 售前经理Id
        Long preSaleUserId = projectInfo.getPreSaleUserId();
        // 项目经理
        Long managerId = projectInfo.getManagerUserId();
        PigxUser user = getUser();
        Long userId = user.getId();
        switch (taskTypeEnum) {
            case AFTER_SALES_DELIVERY:
                if (!Objects.equals(userId, managerId)) {
                    throw new ServiceException("您不是该项目的项目经理，没有权限~");
                }
                break;
            case PRE_SALES_SUPPORT:
                if (!Objects.equals(userId, preSaleUserId)) {
                    throw new ServiceException("您不是该项目的售前经理，没有权限~");
                }
                break;
            default:
        }
        return projectInfo;
    }


    /**
     * 批量通过工单
     *
     * @param ids ids
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchPassTask(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        List<CostDeliverTask> updateList = new ArrayList<>();
        List<CostDeliverTask> costDeliverTasks = listByIds(ids);
        Map<Long, CostDeliverTask> costDeliverTaskMap = costDeliverTasks
                .stream().map(CostDeliverTask::decrypt)
                .collect(Collectors.toMap(CostDeliverTask::getId, e -> e,
                        (e1, e2) -> e1));
        // 校验实际人工成本是否超出预算
        StrBuilder strBuilder = batchVerifyActualLaborCost(costDeliverTasks);
        if (!strBuilder.isEmpty()) {
            throw new ServiceException(String.join("\n", strBuilder) + "对应的成本科目预算将超支，请联系项目经理处理～");
        }
        for (Long id : ids) {
            // 获取工单信息
            CostDeliverTask task = costDeliverTaskMap.get(id);
            if (task == null) {
                continue;
            }
            CostTaskStatusEnum taskStatusEnum = EnumUtils.getEnumByValue(CostTaskStatusEnum.class, task.getTaskStatus());
            // 若工单状态不是可审核状态，默认不通过
            if (!TO_BE_REVIEWED.contains(taskStatusEnum)) {
                continue;
            }
            LocalDateTime nowTime = LocalDateTime.now();
            Integer taskStatus = getNextValByEnum(taskStatusEnum);
            // 当工单状态为已完成并且为1级工单时批量更新子节点工单
            if (CostTaskStatusEnum.YWC.getValue().equals(taskStatus) && NumberUtils.INTEGER_ONE.equals(task.getTaskLevel())) {
                List<CostDeliverTask> deliverTaskList = updateTaskLeafNode(task, nowTime, taskStatus);
                //添加需要所有更新的子节点
                updateList.addAll(deliverTaskList);
            } else {
                // 添加当前需要更新的子节点
                updateList.add(buildTaskUpdateData(nowTime, task, taskStatus));
            }
        }

        if (!updateList.isEmpty()) {
            updateList.forEach(CostDeliverTask::encrypt);
            this.updateBatchById(updateList);
        }
    }

    private StrBuilder batchVerifyActualLaborCost(List<CostDeliverTask> costDeliverTasks) {
        StrBuilder error = new StrBuilder();
        //查询批量通过的工单的实际人工成本总和
        Table<Long, Integer, BigDecimal> actualLaborCostTable = HashBasedTable.create();
        costDeliverTasks.forEach(a -> {
            Integer taxRate = a.getTaxRate();
            Long accountOaId = a.getAccountOaId();
            BigDecimal actualLaborCost = new BigDecimal(a.getActualLaborCost());
            BigDecimal actualLaborCostSum = actualLaborCostTable.get(accountOaId, taxRate);
            if (actualLaborCostSum == null) {
                actualLaborCostTable.put(accountOaId, taxRate, actualLaborCost);
            } else {
                BigDecimal add = actualLaborCostSum.add(actualLaborCost);
                actualLaborCostTable.put(accountOaId, taxRate, add);
            }
        });
        //查询项目所有一级工单已确认成本总和
        List<CostDeliverTask> subTaskList = lambdaQuery()
                .eq(CostDeliverTask::getProjectId, costDeliverTasks.get(0).getProjectId())
                .list().stream()
                .filter(e -> e.getTaskLevel() == 1 && CostTaskStatusEnum.YWC.getValue().equals(e.getTaskStatus()))
                .map(CostDeliverTask::decrypt).collect(Collectors.toList());
        Table<Long, Integer, BigDecimal> confirmedBudgetTable = HashBasedTable.create();
        subTaskList.forEach(a -> {
            Integer taxRate = a.getTaxRate();
            Long accountOaId = a.getAccountOaId();
            BigDecimal actualLaborCost = new BigDecimal(StrUtil.isNotEmpty(a.getActualLaborCost()) ? a.getActualLaborCost() : "0");
            BigDecimal confirmedBudget = confirmedBudgetTable.get(accountOaId, taxRate);
            if (confirmedBudget == null) {
                confirmedBudgetTable.put(accountOaId, taxRate, actualLaborCost);
            } else {
                BigDecimal add = confirmedBudget.add(actualLaborCost);
                confirmedBudgetTable.put(accountOaId, taxRate, add);
            }
        });

        costDeliverTasks.forEach(task -> {
            // 只对一级工单进行校验
            if (task.getTaskLevel() != 1) {
                return;
            }
            //获取已确认人工成本预算
            DeliverCostBudgetListVO matchedBudget = getCostBudgetVO(task);
            if (matchedBudget == null) {
                return;
            }
            BigDecimal actualLaborCostNum = actualLaborCostTable.get(task.getAccountOaId(), task.getTaxRate()) != null ? actualLaborCostTable.get(task.getAccountOaId(), task.getTaxRate()) : BigDecimal.ZERO;
            BigDecimal confirmedBudget = confirmedBudgetTable.get(task.getAccountOaId(), task.getTaxRate()) != null ? confirmedBudgetTable.get(task.getAccountOaId(), task.getTaxRate()) : BigDecimal.ZERO;
            // 检查是否超出预算
            if (actualLaborCostNum.add(confirmedBudget).compareTo(matchedBudget.getBudgetAmountIncludedTax()) > 0) {
                error.append("【").append(task.getTaskName()).append("】");
            }
        });
        return error;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeTask(Long id) {
        CostDeliverTask costDeliverTask = getById(id).decrypt();
        if (costDeliverTask == null) {
            throw new ServiceException("工单不存在");
        }
        // 校验权限
        ProjectTaskKindEnum taskTypeEnum = EnumUtils.getEnumByValue(ProjectTaskKindEnum.class, costDeliverTask.getTaskType());
        checkFirstLevelAuth(costDeliverTask.getProjectId(), taskTypeEnum);
        // 只针对一级任务,删除一级同步删除子级任务，删除需要推送相关负责人。已经存在提交过的数据的任务不可删除
        if (!NumberUtils.INTEGER_ONE.equals(costDeliverTask.getTaskLevel())) {
            throw new ServiceException("只能删除一级任务~");
        }
        // 默认配置的工单不可删除
        if (costDeliverTask.getDefaultConf()) {
            throw new ServiceException("默认配置的工单不可删除~");
        }

        // 获取所有子工单
        List<CostDeliverTask> allChildTasks = getAllChildTasks(costDeliverTask.getId());
        allChildTasks.add(costDeliverTask);

        // 删除工单校验
        if (ProjectTaskKindEnum.PRE_SALES_SUPPORT.getValue().equals(costDeliverTask.getTaskType())) {
            //  验证项目合同会签流程是否归档
            verifyProjectContract(costDeliverTask.getProjectId());
            // 工单删除校验
            removeSupportTaskVerify(allChildTasks);
        } else {
            allChildTasks.forEach(CostDeliverTaskServiceImpl::removeDeliverTaskVerify);
            // 批量删除工单关联的工单工资明细
            List<Long> taskIds = CollStreamUtil.toList(allChildTasks, CostDeliverTask::getId);
            costSalaryDetailsService.removeBatchByRelateIds(taskIds, CostSalaryRelateTypeEnum.AFTER_TASK_ESTIMATE);
        }
        // 批量删除工单
        removeBatchByIds(allChildTasks);

        // 批量发送消息
        List<BaseSendMsgDTO> delMsgDTOList = CollStreamUtil.toList(allChildTasks, CostDeliverTaskServiceImpl::buildMsgDtoByDelTask);
        bcpMessageService.batchSendMsg(delMsgDTOList);
    }

    /**
     * 删除售后交付工单验证
     *
     * @param costDeliverTask 成本交付任务
     */
    private static void removeDeliverTaskVerify(CostDeliverTask costDeliverTask) {
        // 是否已提交
        boolean isSubmit = CostTaskStatusEnum.isSubmitted(costDeliverTask.getTaskStatus());
        if (costDeliverTask.getEndDate().isBefore(LocalDate.now()) || isSubmit) {
            throw new ServiceException(costDeliverTask.getTaskLevel() + "级工单【" + costDeliverTask.getTaskName() + "】已到期或已提交数据无法删除~");
        }
    }

    /**
     * 删除售前支撑工单验证
     *
     * @param costDeliverTask 成本交付任务
     */
    private static void removeSupportTaskVerify(CostDeliverTask costDeliverTask) {
        if (costDeliverTask.getDefaultConf()) {
            throw new ServiceException("默认配置的工单不可删除~");
        }
        boolean existHours = isExistHours(costDeliverTask);
        if (existHours) {
            throw new ServiceException("工单【" + costDeliverTask.getTaskName() + "】已提交工时无法删除~");
        }

    }

    private void removeSupportTaskVerify(List<CostDeliverTask> costDeliverTaskList) {
        if (CollUtil.isEmpty(costDeliverTaskList)) {
            return;
        }
        List<Long> taskIds = new ArrayList<>();
        costDeliverTaskList.forEach(
                costDeliverTask -> {
                    removeSupportTaskVerify(costDeliverTask);
                    taskIds.add(costDeliverTask.getId());
                });

        // 查询工单是否有填报工时
        List<CostTaskDailyPaperEntry> paperEntryList = costTaskDailyPaperEntryMapper.selectList(
                Wrappers.lambdaQuery(CostTaskDailyPaperEntry.class)
                        .in(CostTaskDailyPaperEntry::getTaskId, taskIds));

        if (CollUtil.isNotEmpty(paperEntryList)) {
            String tasks = paperEntryList.stream()
                    .map(item -> "【" + item.getTaskName() + "】")
                    .collect(Collectors.joining(StrPool.COMMA));
            throw new ServiceException("工单" + tasks + "已填报工时无法删除~");
        }
    }


    private static boolean isExistHours(CostDeliverTask costDeliverTask) {
        BigDecimal normalHours = costDeliverTask.getNormalHours();
        BigDecimal holidayOvertimeHours = costDeliverTask.getHolidayOvertimeHours();
        BigDecimal workOvertimeHours = costDeliverTask.getWorkOvertimeHours();
        BigDecimal restOvertimeHours = costDeliverTask.getRestOvertimeHours();
        return (normalHours != null && normalHours.compareTo(BigDecimal.ZERO) > 0)
                || (holidayOvertimeHours != null && holidayOvertimeHours.compareTo(BigDecimal.ZERO) > 0)
                || (workOvertimeHours != null && workOvertimeHours.compareTo(BigDecimal.ZERO) > 0)
                || (restOvertimeHours != null && restOvertimeHours.compareTo(BigDecimal.ZERO) > 0);
    }

    @Override
    public CostDeliverTaskVO findById(Long id) {
        CostDeliverTask entity = getById(id);
        if (null == entity) {
            return new CostDeliverTaskVO();
        }
        // 查询工单类别
        Map<Integer, CostTaskCategoryManagement> costTaskCategoryMap = costTaskCategoryManagementService.getCostTaskCategoryMap();
        CostDeliverTaskVO vo = CostDeliverTaskVO.buildVo(entity, costTaskCategoryMap);
        vo.setParentId(null);
        ProjectInfo projectInfo = projectInfoMapper.selectById(entity.getProjectId());

        //查询税率字典
        Map<Integer, String> taxRateMap = idictService.getDictKvList("税率").getData().stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName));

        //  查询二级和三级数据信息
        List<CostDeliverTaskVO> secondList =
                CollUtil.emptyIfNull(baseMapper.findByIdList(null, Collections.singletonList(vo.getId()), entity.getTaskType(), null));
        List<Long> secondIds = secondList.stream().map(CostDeliverTaskVO::getId).collect(Collectors.toList());
        List<CostDeliverTaskVO> thirdList = CollUtil.isNotEmpty(secondList)
                ? CollUtil.emptyIfNull(baseMapper.findByIdList(null, secondIds, entity.getTaskType(), null))
                : new ArrayList<>(0);

        thirdList = thirdList.stream().peek(e -> assembleCostDeliverTaskVO(e, null, projectInfo, taxRateMap, costTaskCategoryMap, Collections.emptyMap()))
                .collect(Collectors.toList());
        Map<Long, List<CostDeliverTaskVO>> thirdMap =
                thirdList.stream().collect(Collectors.groupingBy(CostDeliverTaskVO::getParentId));

        secondList = secondList.stream()
                .peek(e -> assembleCostDeliverTaskVO(e, thirdMap, projectInfo, taxRateMap, costTaskCategoryMap, Collections.emptyMap()))
                .collect(Collectors.toList());
        Map<Long, List<CostDeliverTaskVO>> secondMap =
                secondList.stream().collect(Collectors.groupingBy(CostDeliverTaskVO::getParentId));

        // 查询售前经理的上级信息
        Map<Long, Roster> preSaleLeaderMap = rosterService.findUserLeaderMap(Collections.singleton(projectInfo.getPreSaleUserId()));

        // 查询上级信息
        CostDeliverTask higherManagerInfo = getHigherManagerInfo(entity);
        vo.setHigherManagerId(higherManagerInfo.getManagerId());
        vo.setHigherManagerName(higherManagerInfo.getManagerName());
        if (!vo.getTaskStatus().equals(CostTaskStatusEnum.YWC.getValue())) {
            vo.setActualLaborCost(null);
            vo.setActualLaborCostStr(null);
        }
        return assembleCostDeliverTaskVO(vo, secondMap, projectInfo, taxRateMap, costTaskCategoryMap, preSaleLeaderMap);
    }

    /**
     * 我的工单分页查询
     *
     * @param pageRequest 页面请求
     * @param request     请求
     * @return {@link Page }<{@link CostDeliverTaskVO }>
     */
    @Override
    public Page<CostDeliverTaskVO> findPersonalTask(PageRequest pageRequest, CostDeliverTaskDTO request) {
        // 获取当前我的工单
        List<CostDeliverTaskVO> resultList = getPersonalTasks(request.getTaskType());

        // 根据条件过滤工单
        List<CostDeliverTaskVO> filterList = filterResult(request, resultList);

        if (CollUtil.isEmpty(resultList)) {
            return new Page<>();
        }
        // 补充上级工单
        resultList = supplementParentTask(resultList);

        // 补全对应的项目信息
        Set<Long> projectIds = CollStreamUtil.toSet(resultList, CostDeliverTaskVO::getProjectId);
        List<ProjectInfo> projectList = projectInfoMapper.selectBatchIds(projectIds);

        //  构建包含父子关系的工单VO集合
        resultList = buildCostDeliverTaskVOList(resultList, projectList, true);

        // 递归筛选要看的工单
        resultList = recursiveFiltering(resultList, CollStreamUtil.toSet(filterList, CostDeliverTaskVO::getId));

        return PageUtils.page(resultList, pageRequest);
    }


    /**
     * 递归过滤
     * （保留白名单任务及其所有上级节点的树结构，采用自底向上的筛选方式）
     *
     * @param resultList 结果列表
     * @param whiteList  白名单
     * @return {@link List }<{@link CostDeliverTaskVO }>
     */
    private List<CostDeliverTaskVO> recursiveFiltering(List<CostDeliverTaskVO> resultList, Set<Long> whiteList) {
        if (CollUtil.isEmpty(resultList) || CollUtil.isEmpty(whiteList)) {
            return new ArrayList<>();
        }

        return resultList.stream()
                .map(task -> {
                    // 先递归处理子节点
                    List<CostDeliverTaskVO> filteredChildren = recursiveFiltering(task.getChildren(), whiteList);

                    // 创建副本避免污染原数据
                    CostDeliverTaskVO cloned = BeanUtil.copyProperties(task, CostDeliverTaskVO.class);
                    cloned.setChildren(filteredChildren);

                    // 保留条件：当前节点在白名单，或者子节点有需要保留的节点
                    return (whiteList.contains(cloned.getId()) || CollUtil.isNotEmpty(filteredChildren))
                            ? cloned
                            : null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    /**
     * 补充父级工单（至顶级）
     *
     * @param taskVOList 任务 vo list
     * @return {@link List }<{@link CostDeliverTaskVO }>
     */
    private List<CostDeliverTaskVO> supplementParentTask(List<CostDeliverTaskVO> taskVOList) {
        if (CollUtil.isEmpty(taskVOList)) {
            return taskVOList;
        }
        List<CostDeliverTaskVO> resultList = new ArrayList<>(taskVOList);
        // 已查询出的工单id集合
        Set<Long> existTaskIds = CollStreamUtil.toSet(taskVOList, CostDeliverTaskVO::getId);
        // 需要查询的工单id集合
        Set<Long> willFindIds = new HashSet<>();
        for (CostDeliverTaskVO taskVO : taskVOList) {
            Long parentId = taskVO.getParentId();
            if (parentId != null && !existTaskIds.contains(parentId)) {
                willFindIds.add(parentId);
            }
            Long topLevelId = taskVO.getTopLevelId();
            if (topLevelId != null && !existTaskIds.contains(topLevelId)) {
                willFindIds.add(topLevelId);
            }
        }
        if (CollUtil.isNotEmpty(willFindIds)) {
            List<CostDeliverTaskVO> parentTaskList = baseMapper.findByIdList(willFindIds, null, null, null);
            resultList.addAll(parentTaskList);
        }
        return resultList;
    }


    /**
     * 获取我的工单
     * 权限范围：
     * 负责人是我的工单
     * 负责人是我的工单的子工单（不包括子子级）
     * 我负责的项目的工单
     *
     * @param taskType 任务类型
     * @return {@link List }<{@link CostDeliverTaskVO }>
     */
    private List<CostDeliverTaskVO> getPersonalTasks(Integer taskType) {
        Long userId = getUser().getId();

        // 获取当前用户工单
        List<CostDeliverTaskVO> resultList = baseMapper.findByCostDeliverTaskDTO
                (CostDeliverTaskDTO.builder().managerId(userId).taskType(taskType).build());

        // 获取我下级的工单
        List<Long> taskIds = CollStreamUtil.toList(resultList, CostDeliverTaskVO::getId);
        List<CostDeliverTaskVO> childrenList = getMyDecompositionTask(taskType, taskIds);
        resultList.addAll(childrenList);

        // 获取我负责的项目的工单
        List<CostDeliverTaskVO> costDeliverTasks = getMyChargeProjectTasks(taskType, userId);
        resultList.addAll(costDeliverTasks);

        // 去重
        Collection<CostDeliverTaskVO> distinctTasks = resultList.stream()
                .collect(Collectors.toMap(CostDeliverTaskVO::getId, task -> task,
                        (existing, replacement) -> existing)).values();
        return new ArrayList<>(distinctTasks);
    }

    private List<CostDeliverTaskVO> filterResult(CostDeliverTaskDTO request, List<CostDeliverTaskVO> resultList) {
        if (CollUtil.isEmpty(resultList)) {
            return resultList;
        }
        Stream<CostDeliverTaskVO> filterStream = resultList.stream();
        if (StrUtil.isNotBlank(request.getTaskName())) {
            filterStream = filterStream.filter(task -> StrUtil.contains(task.getTaskName(), request.getTaskName()));
        }
        if (StrUtil.isNotBlank(request.getProjectName())) {
            filterStream = filterStream.filter(task -> StrUtil.contains(task.getProjectName(), request.getProjectName()));
        }
        LocalDate startDate = request.getStartDate();
        LocalDate endDate = request.getEndDate();
        if (startDate != null && endDate != null) {
            filterStream = filterStream.filter(task -> {
                LocalDate taskStartDate = task.getStartDate();
                LocalDate taskEndDate = task.getEndDate();
                // 检查任务的开始日期是否在查询日期范围内
                boolean startDateInRange = taskStartDate != null &&
                        !taskStartDate.isBefore(startDate) &&
                        !taskStartDate.isAfter(endDate);

                // 检查任务的结束日期是否在查询日期范围内
                boolean endDateInRange = taskEndDate != null &&
                        !taskEndDate.isBefore(startDate) &&
                        !taskEndDate.isAfter(endDate);
                // 返回开始日期或结束日期在查询日期范围内的任务
                return startDateInRange || endDateInRange;
            });
        }
        resultList = filterStream.collect(Collectors.toList());

        // 过滤工单状态
        List<Integer> taskStatusSet = CollUtil.emptyIfNull(request.getTaskStatusList());
        resultList = filterTaskStatus(resultList, new HashSet<>(taskStatusSet));
        return resultList;
    }

    private List<CostDeliverTaskVO> filterTaskStatus(List<CostDeliverTaskVO> resultList, Set<Integer> taskStatusSet) {
        if (CollUtil.isEmpty(taskStatusSet) || CollUtil.isEmpty(resultList)) {
            return resultList;
        }
        // 过滤售前待审核工单
        boolean filterGsDsh = taskStatusSet.contains(CostTaskStatusEnum.GSDSH.getValue());
        taskStatusSet.remove(CostTaskStatusEnum.GSDSH.getValue());

        resultList = filterGsDsh ? filterWaitReviewSupportTask(resultList) : resultList;
        // 过滤其他的工单状态
        if (!taskStatusSet.isEmpty()) {
            resultList = resultList.stream().filter(e -> taskStatusSet.contains(e.getTaskStatus())).collect(Collectors.toList());
        }
        return resultList;
    }

    /**
     * 获取我负责项目的工单
     *
     * @param taskType 任务类型
     * @param userId   用户 ID
     * @return {@link List }<{@link CostDeliverTaskVO }>
     */
    private List<CostDeliverTaskVO> getMyChargeProjectTasks(Integer taskType, Long userId) {
        ProjectTaskKindEnum taskKindEnum = EnumUtils.getEnumByValue(ProjectTaskKindEnum.class, taskType);
        if (taskKindEnum == null) {
            return Collections.emptyList();
        }
        List<ProjectInfo> projectInfoList = projectInfoMapper.selectList(Wrappers.lambdaQuery(ProjectInfo.class)
                .eq(ProjectTaskKindEnum.PRE_SALES_SUPPORT.equals(taskKindEnum), ProjectInfo::getPreSaleUserId, userId)
                .eq(ProjectTaskKindEnum.AFTER_SALES_DELIVERY.equals(taskKindEnum), ProjectInfo::getManagerUserId, userId));
        if (CollUtil.isEmpty(projectInfoList)) {
            return Collections.emptyList();
        }
        List<Long> projectIds = projectInfoList.stream().map(ProjectInfo::getId).collect(Collectors.toList());
        return baseMapper.findByCostDeliverTaskDTO(CostDeliverTaskDTO.builder()
                .projectIds(projectIds).taskType(taskType).build());
    }

    private static void sumActualLaborCost(CostDeliverTaskVO task) {
        // 如果是总成工单，计算子工单的实际人工成本总和，若子工单也有总成工单先计算子工单
        if (EnumUtils.valueEquals(task.getDisassemblyType(), CostTaskDisassemblyTypeEnum.TOTAL_WORK_ORDER)) {
            List<CostDeliverTaskVO> children = task.getChildren();
            if (CollUtil.isEmpty(children)) {
                return;
            }

            // 初始化实际人工成本总和
            BigDecimal totalActualLaborCost = BigDecimal.ZERO;

            // 遍历子工单
            for (CostDeliverTaskVO child : children) {
                // 递归计算子工单的实际人工成本
                sumActualLaborCost(child);

                // 累加子工单的实际人工成本
                if (child.getActualLaborCost() != null) {
                    totalActualLaborCost = totalActualLaborCost.add(child.getActualLaborCost());
                }
            }
            // 将计算结果设置到当前任务的实际人工成本字段中
            task.setActualLaborCost(totalActualLaborCost);
        }
    }

    /**
     * 获取我的下级工单
     */
    private List<CostDeliverTaskVO> getMyDecompositionTask(Integer taskType, List<Long> taskIds) {
        List<CostDeliverTaskVO> childrenList = new ArrayList<>();
        if (CollUtil.isNotEmpty(taskIds)) {
            childrenList = baseMapper.findByIdList(null, taskIds, taskType, null);
        }
        return childrenList;
    }

    private List<CostDeliverTaskVO> filterWaitReviewSupportTask(List<CostDeliverTaskVO> userTaskList) {
        if (CollUtil.isEmpty(userTaskList)) {
            return userTaskList;
        }
        List<Long> taskIds = CollStreamUtil.toList(userTaskList, CostDeliverTaskVO::getId);
        Set<Long> waitReviewTaskIds = getCostTaskDailyPaperEntries(taskIds)
                .stream().map(CostTaskDailyPaperEntry::getTaskId).collect(Collectors.toSet());
        return userTaskList.stream()
                .filter(task -> waitReviewTaskIds.contains(task.getId()))
                .collect(Collectors.toList());
    }

    @NotNull
    private static BigDecimal getActualTotalHours(CostDeliverTaskVO task) {
        BigDecimal actualTotalHours = BigDecimal.ZERO;
        if (task.getWorkOvertimeHours() != null) {
            actualTotalHours = actualTotalHours.add(task.getWorkOvertimeHours());
        }
        if (task.getRestOvertimeHours() != null) {
            actualTotalHours = actualTotalHours.add(task.getRestOvertimeHours());
        }
        if (task.getHolidayOvertimeHours() != null) {
            actualTotalHours = actualTotalHours.add(task.getHolidayOvertimeHours());
        }
        if (task.getNormalHours() != null) {
            actualTotalHours = actualTotalHours.add(task.getNormalHours());
        }
        return actualTotalHours;
    }


    @Override
    public Page<CostDeliverApprovalVO> findTaskApprovalPage(PageRequest pageRequest, CostDeliverTaskDTO request) {
        //查询当前用户需要审核的项目
        request.setProjectManagerUserId(getUser().getId());
        request.setManagerId(getUser().getId());
        buildApprovalStatus(request);
        Page<CostDeliverApprovalVO> taskApprovalList = baseMapper.findTaskApprovalList(Page.of(pageRequest.getPageNumber(), pageRequest.getPageSize()), request);
        taskApprovalList.getRecords().forEach(e -> {
            //查询当前用户需要审核的任务
            List<CostDeliverTask> allTasks = buildAllApprovalTask(e.getProjectId(), request);
            Map<Long, List<CostDeliverTask>> taskMap = allTasks.stream()
                    .collect(Collectors.groupingBy(CostDeliverTask::getProjectId));
            taskMap.keySet().forEach(key -> {
                List<CostDeliverTask> costDeliverTasks = taskMap.get(key);
                e.setTaskNum(costDeliverTasks.size());
                e.setBudgetCost(costDeliverTasks.stream()
                        .filter(task -> task.getDisassemblyType().equals(CostTaskDisassemblyTypeEnum.STANDARD_WORK_ORDER.getValue()))
                        .map(CostDeliverTask::getBudgetCost)
                        .filter(Objects::nonNull)
                        .map(BigDecimal::new)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
            });
        });
        return taskApprovalList;
    }

    /**
     * 设置部门负责人审核状态
     *
     * @param request 请求
     */
    private void buildApprovalStatus(CostDeliverTaskDTO request) {
        if (CollectionUtil.isNotEmpty(request.getTaskStatusList())) {
            return;
        }
        List<Integer> statusList;
        Integer approvalStatus = request.getApprovalStatus();
        if (approvalStatus == null) {
            throw new ServiceException("参数缺失");
        }
        if (YesOrNoEnum.NO.getValue().equals(request.getApprovalStatus())) {
            statusList = CollUtil.newArrayList
                    (CostTaskStatusEnum.DEJFZRSH.getValue(), CostTaskStatusEnum.DYJFZRSH.getValue());
        } else {
            statusList = CollUtil.newArrayList
                    (CostTaskStatusEnum.EJFZRYTG.getValue(), CostTaskStatusEnum.YJFZRYTG.getValue(), CostTaskStatusEnum.YWC.getValue());
        }
        request.setTaskStatusList(statusList);
    }


    private List<CostDeliverTask> buildAllApprovalTask(Long projectId, CostDeliverTaskDTO request) {
        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
        //判断当前用户是否是项目经理，若是则查询一级工单是待项目经理审核的
        boolean projectManager = getUser().getId().equals(projectInfo.getManagerUserId());
        //判断当前用户是否是项目负责人
        boolean projectHeader = CollectionUtil.isNotEmpty(lambdaQuery()
                .eq(CostDeliverTask::getManagerId, getUser().getId())
                .eq(CostDeliverTask::getProjectId, projectId).list());
        List<CostDeliverTask> allTasks = new ArrayList<>();
        request.setProjectId(projectId);
        if (projectManager) {
            //当前用户是项目经理,查询当前任务级别为1，待项目经理审核且项目经理是当前用户的数据
            if (YesOrNoEnum.NO.getValue().equals(request.getApprovalStatus())) {
                request.setTaskStatus(CostTaskStatusEnum.DXMJLSH.getValue());
            } else {
                request.setTaskStatus(CostTaskStatusEnum.YWC.getValue());
            }
            request.setProjectManagerUserId(getUser().getId());
            List<CostDeliverTask> managerTasks = baseMapper.findProjectManagerTask(request)
                    .stream().map(CostDeliverTask::decrypt).collect(Collectors.toList());
            allTasks.addAll(managerTasks);
        }
        if (projectHeader) {
            //当前用户是项目负责人，查询任务级别是第2级和第3级的，父级的负责人是当前用户的数据
            CostDeliverTaskDTO query = new CostDeliverTaskDTO();
            query.setProjectId(projectId);
            query.setTaskStatusList(request.getTaskStatusList());
            query.setManagerId(getUser().getId());
            query.setTaskLevelList(CollUtil.newArrayList(NumberUtils.INTEGER_TWO, TASK_MAX_LEVEL));
            List<CostDeliverTask> headerTasks = baseMapper.findTaskAndParent(query)
                    .stream().map(CostDeliverTask::decrypt).collect(Collectors.toList());
            allTasks.addAll(headerTasks);
        }
        return allTasks;
    }


    private List<CostDeliverTaskVO> buildApprovalData(CostDeliverTaskDTO request) {
        ProjectInfo projectInfo = projectInfoMapper.selectById(request.getProjectId());
        //判断当前用户是否是项目经理，若是显示一级工单是待项目经理审核的
        boolean projectManager = getUser().getId().equals(projectInfo.getManagerUserId());
        //判断当前用户是否是工单负责人
        boolean projectHead = CollectionUtil.isNotEmpty(lambdaQuery()
                .eq(CostDeliverTask::getManagerId, getUser().getId())
                .eq(CostDeliverTask::getProjectId, request.getProjectId()).list());
        List<CostDeliverTaskVO> resultList = new ArrayList<>();
        if (projectHead) {
            //部门负责人审核数据
            buildApprovalStatus(request);
            request.setProjectHeaderId(getUser().getId());
            resultList.addAll(baseMapper.findByCostDeliverTaskDTO(request));
        }
        if (projectManager) {
            //项目经理审核
            request.setProjectManagerApproval(NumberUtils.INTEGER_ONE);
            request.setTaskStatusList(null);
            request.setProjectHeaderId(null);
            resultList.addAll(baseMapper.findByCostDeliverTaskDTO(request));
        }
        return resultList;
    }

    @Override
    public Page<CostDeliverTaskVO> findProjectApproval(PageRequest pageRequest, CostDeliverTaskDTO request) {
        if (request.getProjectId() == null) {
            throw new ServiceException("参数缺失");
        }
        //获取需要审核的最后一级数据
        List<CostDeliverTaskVO> resultList = buildApprovalData(request);
        if (CollUtil.isEmpty(resultList)) {
            return new Page<>();
        }
        // 获取对应的上层工单
        List<Long> higherIdList = new ArrayList<>();
        List<Long> taskIds = new ArrayList<>();
        resultList.forEach(e -> {
            taskIds.add(e.getId());
            if (e.getParentId() != null) {
                higherIdList.add(e.getParentId());
            }
        });
        List<Long> otherIdList = new ArrayList<>(CollUtil.subtract(higherIdList, taskIds));
        if (CollUtil.isNotEmpty(otherIdList)) {
            List<CostDeliverTaskVO> parentList = baseMapper.findByIdList(otherIdList, Collections.emptyList(), request.getTaskType(), null);
            CollUtil.addAll(resultList, parentList);
            List<Long> existHigherIdList = resultList.stream().map(CostDeliverTaskVO::getId).distinct().collect(Collectors.toList());
            List<CostDeliverTaskVO> allHigherTasks = getAllHigherTasks(higherIdList);
            //List<Long> otherIdList = new ArrayList<>(CollUtil.subtract(higherIdList, taskIds));
            if (CollUtil.isNotEmpty(allHigherTasks)) {
                //List<CostDeliverTaskVO> parentList = baseMapper.findByIdList(otherIdList, Collections.emptyList());
                resultList.addAll(allHigherTasks.stream()
                        .filter(higher -> !existHigherIdList.contains(higher.getId()))
                        .collect(Collectors.toList()));
            }
            // 获取对应的下级工单
            List<CostDeliverTaskVO> childrenList =
                    CollUtil.emptyIfNull(baseMapper.findByIdList(null, taskIds, request.getTaskType(), null));
            List<Long> existIdList = resultList.stream().map(CostDeliverTaskVO::getId).distinct().collect(Collectors.toList());
            resultList.addAll(
                    childrenList.stream()
                            .filter(child -> !existIdList.contains(child.getId()))
                            .collect(Collectors.toList())
            );

            List<ProjectInfo> projectList =
                    projectInfoMapper.findByIdListAndStatus(resultList.stream().map(CostDeliverTaskVO::getProjectId).collect(Collectors.toList()), null);
            resultList = buildCostDeliverTaskVOList(resultList, projectList, false);
            resultList.forEach(e -> e.setParentId(null));

            // 计算工时和成本
            resultList.forEach(task -> {
                // 计算实际总工时
                BigDecimal actualTotalHours = getActualTotalHours(task);
                task.setActualTotalHours(actualTotalHours);
                task.setActualLaborCost(task.getActualLaborCostStr() != null ? new BigDecimal(AESEncryptor.justDecrypt(task.getActualLaborCostStr())) : null);

                // 如果是总成工单，计算子工单的实际人工成本总和
                if (CostTaskDisassemblyTypeEnum.TOTAL_WORK_ORDER.getValue().equals(task.getDisassemblyType())) {
                    //if (CollUtil.isNotEmpty(task.getChildren())) {
                    //    BigDecimal totalActualLaborCost = task.getChildren().stream()
                    //            .map(childTask -> {
                    //                if (childTask.getActualLaborCostStr() != null) {
                    //                    // 解密实际人工成本
                    //                    String encryptedCost = childTask.getActualLaborCostStr().toString();
                    //                    return new BigDecimal(AESEncryptor.justDecrypt(encryptedCost));
                    //                }
                    //                return BigDecimal.ZERO;
                    //            })
                    //            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    //    task.setActualLaborCost(totalActualLaborCost);
                    //}
                    task.setActualTotalHours(null);
                }

                // 设置预计工时和正常工时（如果未设置）

                task.setEstimatedHours(task.getEstimatedHours());
                task.setNormalHours(task.getNormalHours());
            });
        }
        //设置审核人
        setHigherManager(resultList);
        return PageUtils.page(resultList, pageRequest);
    }

    /**
     * 获取所有父任务
     *
     * @param ids id
     * @return {@link List }<{@link CostDeliverTask }>
     */
    private List<CostDeliverTaskVO> getAllHigherTasks(List<Long> ids) {
        // 获取第二级父节点列表
        List<CostDeliverTaskVO> secondHigherList = getHigherByParentIds(ids);
        // 组装二级父节点集合
        List<CostDeliverTaskVO> deliverTaskList = new ArrayList<>(secondHigherList);
        List<Long> existIdList = deliverTaskList.stream().map(CostDeliverTaskVO::getId).distinct().collect(Collectors.toList());
        // 获取第一级父节点列表
        List<CostDeliverTaskVO> firstHigherList = getHigherByParentIds(ids);
        if (CollUtil.isNotEmpty(firstHigherList)) {
            // 组装第一级父节点集合
            deliverTaskList.addAll(firstHigherList.stream()
                    .filter(higher -> !existIdList.contains(higher.getId()))
                    .collect(Collectors.toList()));
        }
        return deliverTaskList;
    }

    private List<CostDeliverTaskVO> getHigherByParentIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return baseMapper.findByIdList(ids, Collections.emptyList(), null, null);
    }


    @Override
    public Page<CostDeliverTaskVO> findPersonalApproval(PageRequest pageRequest, CostDeliverTaskDTO request) {
        if (request.getProjectId() == null) {
            throw new ServiceException("参数缺失");
        }
        buildApprovalStatus(request);
        List<CostDeliverTask> costDeliverTasks = buildAllApprovalTask(request.getProjectId(), request);
        Page<CostDeliverTask> page = PageUtils.page(costDeliverTasks, pageRequest);
        ProjectInfo projectInfo = projectInfoMapper.selectById(request.getProjectId());
        Page<CostDeliverTaskVO> returnPage = new Page<>();
        if (CollectionUtil.isNotEmpty(page.getRecords())) {
            BeanUtils.copyProperties(page, returnPage);
            List<CostDeliverTaskVO> list = new ArrayList<>();
            List<Long> parnetIdList = page.getRecords().stream().map(CostDeliverTask::getParentId).collect(Collectors.toList());
            List<CostDeliverTaskVO> parentTaskList = baseMapper.findByIdList(parnetIdList, null, request.getTaskType(), null);
            Map<Long, Long> managerIdMap = parentTaskList.stream().collect(Collectors.toMap(CostDeliverTaskVO::getId, CostDeliverTaskVO::getManagerId));
            page.getRecords().forEach(e -> {
                CostDeliverTaskVO costDeliverTaskVO = new CostDeliverTaskVO();
                BeanUtils.copyProperties(e, costDeliverTaskVO);
                costDeliverTaskVO.setTaskTypeTxt(EnumUtils.getNameByValue(ProjectTaskKindEnum.class, e.getTaskType()));
                costDeliverTaskVO.setTaskStatusTxt(EnumUtils.getNameByValue(CostTaskStatusEnum.class, e.getTaskStatus()));
                costDeliverTaskVO.setDisassemblyTypeTxt(EnumUtils.getNameByValue(CostTaskDisassemblyTypeEnum.class, e.getDisassemblyType()));
                costDeliverTaskVO.setId(e.getId());
                costDeliverTaskVO.setActualLaborCost(e.getActualLaborCost() != null ? new BigDecimal(e.getActualLaborCost()) : null);
                costDeliverTaskVO.setBudgetCost(e.getBudgetCost() != null ? new BigDecimal(e.getBudgetCost()) : null);

                //赋值上级
                if (NumberUtils.INTEGER_ONE.equals(e.getTaskLevel())) {
                    costDeliverTaskVO.setHigherManagerId(projectInfo.getManagerUserId());
                } else {
                    costDeliverTaskVO.setHigherManagerId(managerIdMap.get(e.getParentId()));
                }
                //前端要求parentId置空
                costDeliverTaskVO.setParentId(null);
                list.add(costDeliverTaskVO);
            });
            returnPage.setRecords(list);
        }
        return returnPage;
    }

    @Override
    public List<Map<String, Object>> calculateUserHourlyWages(Set<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyList();
        }

        // 批量获取人员时薪
        Map<Long, BigDecimal> hourlyWageMap = costConfigLevelPriceService.getHourlyWageMap(userIds);
        // 计算每个用户的时薪
        List<Map<String, Object>> hourlyWages = new ArrayList<>();
        Map<Long, String> rosterMap = rosterService.lambdaQuery().in(Roster::getId, userIds).list().stream()
                .collect(Collectors.toMap(Roster::getId, Roster::getAliasName));
        for (Long userId : userIds) {
            Map<String, Object> userHourlyWage = new HashMap<>(2);
            userHourlyWage.put("userId", String.valueOf(userId));
            userHourlyWage.put("userName", rosterMap.get(userId));
            BigDecimal hourlyWage = hourlyWageMap.getOrDefault(userId, BigDecimal.ZERO);
            userHourlyWage.put("hourlyWage", hourlyWage);
            hourlyWages.add(userHourlyWage);
        }
        return hourlyWages;
    }

    /**
     * 获取工单预估产值
     *
     * @param projectId 项目 ID
     * @return {@link CostDeliverTaskIncomeVo }
     */
    @Override
    public CostDeliverTaskIncomeVo getDeliverTaskIncome(Long projectId) {
        // 获取项目预估总产值
        BigDecimal estimatedTotalIncome = costDeliverExpensesReimburseService.getEstimatedTotalIncome(projectId);
        // 获取标准工单数据 并统计
        BigDecimal currentIncome = lambdaQuery()
                .eq(CostDeliverTask::getDisassemblyType, CostTaskDisassemblyTypeEnum.STANDARD_WORK_ORDER.getValue())
                .eq(CostDeliverTask::getProjectId, projectId).list()
                .stream().map(CostDeliverTask::decrypt)
                .map(CostDeliverTask::getIncome)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return new CostDeliverTaskIncomeVo(currentIncome, estimatedTotalIncome);
    }


    private static BigDecimal getMonthlySalary(RemSalarySocialFundDataVO salarySocialFundDataVO) {
        // 固定工资
        BigDecimal fixedSalary = decryptToDecimal(salarySocialFundDataVO.getFixedSalary());
        // 企业社保金额
        BigDecimal companySocialAmount = decryptToDecimal(salarySocialFundDataVO.getCompanySocialAmount());
        // 企业公积金金额
        BigDecimal companyFundAmount = decryptToDecimal(salarySocialFundDataVO.getCompanyFundAmount());
        return fixedSalary.add(companySocialAmount).add(companyFundAmount);
    }

    /**
     * 获取解密
     *
     * @param str str
     * @return {@link String }
     */
    public static BigDecimal decryptToDecimal(String str) {
        if (StringUtils.isBlank(str)) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(AESEncryptor.justDecrypt(str));
    }

    /**
     * 获取待审核工单总数（根据用户分组）
     * key: 用户id-用户名称，value:待审核工单总数
     */
    private static Map<String, Integer> getTaskTotalMap(List<CostDeliverTask> reviewTaskList, Map<Long, ProjectInfo> projectInfoMap) {
        Map<String, Integer> taskTotalMap = new HashMap<>((int) Math.ceil(reviewTaskList.size() / 2.0));
        for (CostDeliverTask task : reviewTaskList) {
            String key;
            if (NumberUtils.INTEGER_ONE.equals(task.getTaskLevel())) {
                Long projectId = task.getProjectId();
                ProjectInfo projectInfo = projectInfoMap.get(projectId);
                if (projectInfo == null) {
                    log.error("未查询到对应项目信息，项目id：{}", projectId);
                    continue;
                }
                key = projectInfo.getManagerUserId() + StrPool.DASHED + projectInfo.getManagerUserName();
            } else {
                key = task.getManagerId() + StrPool.DASHED + task.getManagerName();
            }
            String[] keyArr = key.split(StrPool.DASHED);
            if (keyArr.length == NumberUtils.INTEGER_TWO) {
                Integer total = taskTotalMap.getOrDefault(key, NumberUtils.INTEGER_ONE);
                taskTotalMap.put(key, total + 1);
            } else {
                log.error("工单[{}]审核提醒,用户姓名参数缺失~", task.getTaskName());
            }
        }
        return taskTotalMap;
    }


    @NotNull
    private static String getCostBudgetMapKey(CostDeliverTask costDeliverTask) {
        return costDeliverTask.getAccountOaId() + StrPool.DASHED + costDeliverTask.getTaxRate();
    }

    @NotNull
    private static String getCostBudgetMapKey(Long accountOaId, Integer taxRate) {
        return accountOaId + StrPool.DASHED + taxRate;
    }

    /**
     * 初始化 任务状态
     *
     * @param costDeliverTask 成本交付任务
     */
    private static void initTaskStatus(CostDeliverTask costDeliverTask) {
        if (costDeliverTask.getTaskLevel() > NumberUtils.INTEGER_TWO) {
            costDeliverTask.setTaskStatus(CostTaskStatusEnum.WWC.getValue());
            return;
        }
        CostTaskDisassemblyTypeEnum disassemblyTypeEnum = EnumUtils.getEnumByValue(CostTaskDisassemblyTypeEnum.class, costDeliverTask.getDisassemblyType());
        if (CostTaskDisassemblyTypeEnum.TOTAL_WORK_ORDER.equals(disassemblyTypeEnum)) {
            costDeliverTask.setTaskStatus(CostTaskStatusEnum.DCJ.getValue());
        } else if (CostTaskDisassemblyTypeEnum.STANDARD_WORK_ORDER.equals(disassemblyTypeEnum)) {
            costDeliverTask.setTaskStatus(CostTaskStatusEnum.WWC.getValue());
        }
    }

    /**
     * 构建消息DTO(审核)
     *
     * @param total      total
     * @param targetId   id
     * @param targetName 姓名
     * @return {@link BaseSendMsgDTO }
     */
    private static BaseSendMsgDTO buildMsgDtoByReviewTask(int total, Long targetId, String targetName) {
        String title = "工单审核";
        String content = "您当前有" + total + "条工单任务未审核，请及时进行审核～";
        return new BaseSendMsgDTO()
                .setTitle(title)
                .setContent(content)
                .setMsgTypeEnum(MsgTypeEnum.NOTICE_MSG)
                .setTargetTypeEnum(TargetTypeEnum.USERS)
                .setChannelEnums(CollUtil.newLinkedHashSet(ChannelEnum.WECOM, ChannelEnum.MAIL))
                .setPath("/view-businesses/cooperate-office/project-work-orders/task-review/list")
                .populateSender()
                .toOneTarget(targetId, targetName);
    }

    /**
     * 构建消息DTO(未拆解)
     *
     * @param total      total
     * @param targetId   id
     * @param targetName 姓名
     * @return {@link BaseSendMsgDTO }
     */
    private static BaseSendMsgDTO buildMsgDtoByWcjTask(Integer total, Long targetId, String targetName) {
        String title = "【工单未拆解提醒】";
        String content = "您好，您目前存在" + total + "个未拆解的工单，请及时进行拆解～";
        return new BaseSendMsgDTO()
                .setTitle(title)
                .setContent(content)
                .setMsgTypeEnum(MsgTypeEnum.NOTICE_MSG)
                .setTargetTypeEnum(TargetTypeEnum.USERS)
                .setChannelEnums(CollUtil.newLinkedHashSet(ChannelEnum.WECOM, ChannelEnum.MAIL))
                .setPath("/view-businesses/cooperate-office/project-work-orders/own/list")
                .populateSender()
                .toOneTarget(targetId, targetName);
    }

    /**
     * 构建消息DTO(新增)
     *
     * @param costDeliverTask 成本交付任务
     * @return {@link BaseSendMsgDTO }
     */
    private static BaseSendMsgDTO buildMsgDtoByAddTask(CostDeliverTask costDeliverTask) {
        PigxUser user = UserUtils.getUser();
        String title = "新工单";
        String content = user.getName() + "给你下发了【" + costDeliverTask.getProjectName() + "】的工单任务【" + costDeliverTask.getTaskName() + "】，请及时查看～";
        return new BaseSendMsgDTO()
                .setTitle(title)
                .setContent(content)
                .setMsgTypeEnum(MsgTypeEnum.NOTICE_MSG)
                .setTargetTypeEnum(TargetTypeEnum.USERS)
                .setChannelEnums(CollUtil.newLinkedHashSet(ChannelEnum.WECOM, ChannelEnum.MAIL))
                .setPath("/view-businesses/cooperate-office/project-work-orders/own/list")
                .populateSender(user.getId(), user.getName())
                .toOneTarget(costDeliverTask.getManagerId(), costDeliverTask.getManagerName());
    }

    /**
     * 构建消息DTO(编辑)
     *
     * @param costDeliverTask 成本交付任务
     * @return {@link BaseSendMsgDTO }
     */
    private static BaseSendMsgDTO buildMsgDtoByEditTask(CostDeliverTask costDeliverTask) {
        PigxUser user = UserUtils.getUser();
        String title = "工单发生编辑";
        String content = user.getName() + "编辑了【" + costDeliverTask.getProjectName() + "】的工单任务【" + costDeliverTask.getTaskName() + "】，请及时查看～";
        return new BaseSendMsgDTO()
                .setTitle(title)
                .setContent(content)
                .setMsgTypeEnum(MsgTypeEnum.NOTICE_MSG)
                .setTargetTypeEnum(TargetTypeEnum.USERS)
                .setChannelEnums(CollUtil.newLinkedHashSet(ChannelEnum.WECOM, ChannelEnum.MAIL))
                .setPath("/view-businesses/cooperate-office/project-work-orders/own/list")
                .populateSender(user.getId(), user.getName())
                .toOneTarget(costDeliverTask.getManagerId(), costDeliverTask.getManagerName());
    }

    /**
     * 构建消息DTO(删除)
     *
     * @param costDeliverTask 成本交付任务
     * @return {@link BaseSendMsgDTO }
     */
    private static BaseSendMsgDTO buildMsgDtoByDelTask(CostDeliverTask costDeliverTask) {
        PigxUser user = UserUtils.getUser();
        String title = "工单被删除";
        String content = user.getName() + "删除了【" + costDeliverTask.getProjectName() + "】的工单任务【" + costDeliverTask.getTaskName() + "】，请知悉～";
        return new BaseSendMsgDTO()
                .setTitle(title)
                .setContent(content)
                .setMsgTypeEnum(MsgTypeEnum.NOTICE_MSG)
                .setTargetTypeEnum(TargetTypeEnum.USERS)
                .setChannelEnums(CollUtil.newLinkedHashSet(ChannelEnum.WECOM, ChannelEnum.MAIL))
                .setPath("/view-businesses/cooperate-office/project-work-orders/own/list")
                .populateSender(user.getId(), user.getName())
                .toOneTarget(costDeliverTask.getManagerId(), costDeliverTask.getManagerName());
    }

    /**
     * 构建消息DTO(退回)
     *
     * @param costDeliverTask 成本交付任务
     * @return {@link BaseSendMsgDTO }
     */
    private static BaseSendMsgDTO buildMsgDtoByReturnTask(CostDeliverTask costDeliverTask) {
        String title = "工单被退回";
        String content = "您【" + costDeliverTask.getProjectName() + "】的工单任务【" + costDeliverTask.getTaskName() + "】，被退回了，请调整后重新提交完成～";
        return new BaseSendMsgDTO()
                .setTitle(title)
                .setContent(content)
                .setMsgTypeEnum(MsgTypeEnum.NOTICE_MSG)
                .setTargetTypeEnum(TargetTypeEnum.USERS)
                .setChannelEnums(CollUtil.newLinkedHashSet(ChannelEnum.WECOM, ChannelEnum.MAIL))
                .setPath("/view-businesses/cooperate-office/project-work-orders/own/list")
                .populateSender()
                .toOneTarget(costDeliverTask.getManagerId(), costDeliverTask.getManagerName());
    }

    /**
     * 工单拆解时间校验
     *
     * @param costDeliverTask 成本交付任务
     */
    private static void verifyCostTaskDate(CostDeliverTask costDeliverTask) {
        CostTaskDisassemblyTypeEnum disassemblyTypeEnum = EnumUtils.getEnumByValue(CostTaskDisassemblyTypeEnum.class, costDeliverTask.getDisassemblyType());
        if (disassemblyTypeEnum == null) {
            throw new ServiceException("工单【" + costDeliverTask.getTaskName() + "】的拆解类型不存在");
        }
        // 总成工单无需校验周期
        if (CostTaskDisassemblyTypeEnum.TOTAL_WORK_ORDER.equals(disassemblyTypeEnum)) {
            return;
        }
        // 标准工单校验
        LocalDate startDate = costDeliverTask.getStartDate();
        LocalDate endDate = costDeliverTask.getEndDate();
        if (startDate == null || endDate == null) {
            throw new ServiceException("工单【" + costDeliverTask.getTaskName() + "】的起止日期不能为空");
        }
        // 计算工单周期
        long days = startDate.until(endDate, ChronoUnit.DAYS) + 1;
        // 根据工单类别判断周期限制
        Integer taskCategory = costDeliverTask.getTaskCategory();
        TaskCategoryEnum taskCategoryEnum = EnumUtils.getEnumByValue(TaskCategoryEnum.class, taskCategory);
        if (taskCategoryEnum == null) {
            throw new ServiceException("工单【" + costDeliverTask.getTaskName() + "】的工单类别不存在");
        }
        if (days > taskCategoryEnum.getLimitTime()) {
            throw new ServiceException("工单【" + costDeliverTask.getTaskName() + "】为" + taskCategoryEnum.getName()
                    + "类型，起止日期间隔不能超过" + taskCategoryEnum.getLimitTime() + "天");
        }
    }

    /**
     * 获取当前用户信息
     *
     * @return {@link PigxUser }
     */
    private static PigxUser getUser() {
        PigxUser user = UserUtils.getUser();
        if (user == null) {
            throw new ValidationException("无操作权限");
        }
        return user;
    }

    /**
     * 创建一级工单校验
     *
     * @param projectId        projectId
     * @param costDeliverTasks 新工单集合
     * @param costBudgetMap    成本预算Map
     */
    private void createDeliverFirstVerify(Long projectId, List<CostDeliverTask> costDeliverTasks, Map<String, DeliverCostBudgetListVO> costBudgetMap) {
        // 错误信息
        Set<String> errorMsg = new LinkedHashSet<>();
        List<CostDeliverTask> createdTasks = lambdaQuery()
                .eq(CostDeliverTask::getTaskLevel, NumberUtils.INTEGER_ONE)
                .eq(CostDeliverTask::getTaskType, ProjectTaskKindEnum.AFTER_SALES_DELIVERY.getValue())
                .eq(CostDeliverTask::getProjectId, projectId).list()
                .stream().peek(CostDeliverTask::decrypt).collect(Collectors.toList());

        // 获取已使用的预算成本
        Map<String, BigDecimal> usedBudgetCostMap = new HashMap<>(createdTasks.size());
        // 已用工单产值
        BigDecimal usedIncomeTotal = BigDecimal.ZERO;
        for (CostDeliverTask item : createdTasks) {
            final String costBudgetMapKey = getCostBudgetMapKey(item);
            BigDecimal budget = usedBudgetCostMap.getOrDefault(costBudgetMapKey, BigDecimal.ZERO);
            // 预算 已完成取实际成本，未完成取预算成本
            if (EnumUtils.valueEquals(item.getTaskStatus(), CostTaskStatusEnum.YWC)) {
                usedBudgetCostMap.put(costBudgetMapKey, budget.add(new BigDecimal(item.getActualLaborCost())));
            } else {
                usedBudgetCostMap.put(costBudgetMapKey, budget.add(new BigDecimal(item.getBudgetCost())));
            }
            // 产值
            BigDecimal income = item.getIncome() == null ? BigDecimal.ZERO : item.getIncome();
            usedIncomeTotal = usedIncomeTotal.add(income);
        }
        // 获取工单已使用的金额
        Map<String, BigDecimal> budgetCostTotalMap = new HashMap<>(costDeliverTasks.size());
        for (CostDeliverTask costDeliverTask : costDeliverTasks) {
            // 预算成本
            BigDecimal budgetCost = new BigDecimal(costDeliverTask.getBudgetCost());
            final String costBudgetMapKey = getCostBudgetMapKey(costDeliverTask);
            BigDecimal budgetCostTotal = budgetCostTotalMap.getOrDefault(costBudgetMapKey, BigDecimal.ZERO);
            budgetCostTotalMap.put(costBudgetMapKey, budgetCostTotal.add(budgetCost));
            try {
                // 验证工单拆解类型
                verifyCostTaskDate(costDeliverTask);
            } catch (Exception e) {
                errorMsg.add(e.getMessage());
            }
        }

        // 验证项目预算
        for (Map.Entry<String, BigDecimal> entry : budgetCostTotalMap.entrySet()) {
            BigDecimal budgetCostTotal = entry.getValue();
            String key = entry.getKey();
            DeliverCostBudgetListVO costBudgetVO = costBudgetMap.get(key);
            BigDecimal budgetAmount = Optional.ofNullable((costBudgetVO))
                    .orElseThrow(() -> new ServiceException("科目不存在~"))
                    .getBudgetAmountIncludedTax();

            if (budgetAmount == null) {
                errorMsg.add("科目【" + costBudgetVO.getAccountName() + "】的预算不存在~");
            } else {
                BigDecimal usedBudgetCost = usedBudgetCostMap.getOrDefault(key, BigDecimal.ZERO);
                budgetCostTotal = budgetCostTotal.add(usedBudgetCost);
                if (budgetCostTotal.compareTo(budgetAmount) > NumberUtils.INTEGER_ZERO) {
                    errorMsg.add("科目【" + costBudgetVO.getAccountName() + "】的预算超出~");
                }
            }
        }

        if (CollUtil.isNotEmpty(errorMsg)) {
            throw new ServiceException(String.join(",", errorMsg));
        }

        // 项目预估总产值
        BigDecimal estimatedTotalIncome = costDeliverExpensesReimburseService.getEstimatedTotalIncome(projectId);

        // 产值校验
        if (usedIncomeTotal.compareTo(estimatedTotalIncome) > NumberUtils.INTEGER_ZERO) {
            throw new ServiceException("产值超出项目预估总产值~");
        }
    }

    /**
     * 校验工单实际人工成本是否超出预算
     *
     * @param task 工单
     */
    private void verifyActualLaborCost(CostDeliverTask task) {
        // 只对一级工单进行校验
        if (task.getTaskLevel() != 1) {
            return;
        }
        //获取已确认人工成本预算
        DeliverCostBudgetListVO matchedBudget = getCostBudgetVO(task);
        if (matchedBudget == null) {
            return;
        }
        //查询项目所有级工单
        List<CostDeliverTask> deliverTaskList = lambdaQuery()
                .eq(CostDeliverTask::getProjectId, task.getProjectId())
                .list().stream().map(CostDeliverTask::decrypt)
                .collect(Collectors.toList());
        List<CostDeliverTask> subTaskList = deliverTaskList.stream()
                .filter(e -> Objects.equals(task.getAccountOaId(), e.getAccountOaId())
                        && Objects.equals(task.getTaxRate(), e.getTaxRate())
                        && e.getTaskLevel() == 1)
                .collect(Collectors.toList());

        // 1. 计算已完成状态工单的实际人工成本总和
        BigDecimal confirmedBudget = subTaskList.stream()
                .filter(f -> CostTaskStatusEnum.YWC.getValue().equals(f.getTaskStatus()))
                .map(CostDeliverTask::getActualLaborCost)
                .filter(Objects::nonNull)
                .map(BigDecimal::new)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 检查是否超出预算
        if (new BigDecimal(task.getActualLaborCost()).add(confirmedBudget).compareTo(matchedBudget.getBudgetAmountIncludedTax()) > 0) {
            throw new ServiceException("【" + task.getTaskName() + "】");
        }
    }

    public DeliverCostBudgetListVO getCostBudgetVO(CostDeliverTask task) {
        // 获取项目最新已确认人工成本预算版本信息
        CostManageVersionVO costManageVersionVO = getLatestConfirmedCostManageVersionVO(task.getProjectId(), ProjectTaskKindEnum.AFTER_SALES_DELIVERY);
        if (costManageVersionVO == null || costManageVersionVO.getVersionId() == null) {
            return null;
        }

        // 查询版本对应人工成本预算明细
        List<DeliverCostBudgetListVO> costBudgetList = costManageEstimationResultsMapper.findCostBudgetByVersionId(
                costManageVersionVO.getVersionId(), CostTypeEnum.RGCB.getValue());

        // 找到匹配的预算科目

        return costBudgetList.stream()
                .filter(budget -> Objects.equals(budget.getAccountOaId(), task.getAccountOaId())
                        && Objects.equals(budget.getTaxRate(), task.getTaxRate()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 验证预计工时
     */
    private static void verifyEstimatedHours(CostDeliverTask task) {
        if (CostTaskDisassemblyTypeEnum.STANDARD_WORK_ORDER.getValue().equals(task.getDisassemblyType())) {
            BigDecimal estimatedHours = task.getEstimatedHours();
            if (estimatedHours == null) {
                throw new ServiceException("标准工单的预计工时不能为空");
            }
            if (estimatedHours.compareTo(BigDecimal.ZERO) < 0) {
                throw new ServiceException("预计工时不能为负数");
            }
            if (estimatedHours.compareTo(new BigDecimal("500")) > 0) {
                throw new ServiceException("预计工时不能超过500小时");
            }
            // 验证最小单位0.5
            BigDecimal remainder = estimatedHours.remainder(new BigDecimal("0.5"));
            if (remainder.compareTo(BigDecimal.ZERO) != 0) {
                throw new ServiceException("预计工时最小单位为0.5小时");
            }
        }
    }

    /**
     * 验证产值
     */
    private static void verifyIncome(BigDecimal income) {
        if (income != null) {
            if (income.compareTo(BigDecimal.ZERO) < 0) {
                throw new ServiceException("产值不能为负数");
            }
            // 验证最多保留2位小数
            if (income.scale() > NumberUtils.INTEGER_TWO) {
                throw new ServiceException("产值最多保留2位小数");
            }
        }
    }

    /**
     * 更新父级工单预算
     *
     * @param parentTask  父级工单
     * @param newTaskList 完整子级工单集合
     * @param allTaskMap  allTaskMap
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateParentTaskBudget(CostDeliverTask parentTask, List<CostDeliverTask> newTaskList, Map<Long, CostDeliverTask> allTaskMap) {
        // 子级工单总成本校验
        BigDecimal childrenBudgetTotal = newTaskList.stream()
                .map(CostDeliverTask::getBudgetCost)
                .filter(Objects::nonNull)
                .map(BigDecimal::new)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal parentTaskBudgetCost = new BigDecimal(parentTask.getBudgetCost());
        // 验证子级预算总和不能大于父级预算
        if (childrenBudgetTotal.compareTo(parentTaskBudgetCost) > 0) {
            throw new ServiceException("子级预算总和不能大于父级工单预算！");
        }
        // 如果子级预算总和小于父级预算，更新父级预算
        if (childrenBudgetTotal.compareTo(parentTaskBudgetCost) < 0) {
            // 计算预算变更金额
            BigDecimal budgetDiff = parentTaskBudgetCost.subtract(childrenBudgetTotal);

            // 需要更新的工单列表
            List<CostDeliverTask> updateTaskList = new ArrayList<>();

            // 更新当前父级工单预算
            parentTask.setBudgetCost(childrenBudgetTotal.toString());
            updateTaskList.add(BaseBuildEntityUtil.buildUpdate(parentTask));

            // 如果不是一级工单,向上逐级修改
            Long currentParentId = parentTask.getParentId();
            while (currentParentId != null) {
                CostDeliverTask currentTask = allTaskMap.get(currentParentId);
                if (currentTask != null) {
                    // 非直属上级工单,修改金额为原预算-变更预算
                    currentTask.setBudgetCost(new BigDecimal(currentTask.getBudgetCost()).subtract(budgetDiff).toString());
                    updateTaskList.add(BaseBuildEntityUtil.buildUpdate(currentTask));
                    currentParentId = currentTask.getParentId();
                } else {
                    break;
                }
            }
            // 批量更新工单预算
            if (!updateTaskList.isEmpty()) {
                updateTaskList.forEach(CostDeliverTask::encrypt);
                updateBatchById(updateTaskList);
            }
        }
    }

    @Override
    public CostSalaryDTO calculateEstimateLaborCost(CostCalculateLaborCostDTO dto) {
        // 获取工单信息
        CostDeliverTask task = getCostDeliverTask(dto.getTaskId());
        CalculateLaborCostDTO costDTO = new CalculateLaborCostDTO()
                .setId(task.getId())
                .setRelateId(task.getId())
                .setRelateTypeEnum(CostSalaryRelateTypeEnum.AFTER_TASK_ESTIMATE)
                .setUserId(task.getManagerId())
                .setNormalHours(dto.getNormalHours())
                .setWorkOvertimeHours(dto.getWorkOvertimeHours())
                .setRestOvertimeHours(dto.getRestOvertimeHours())
                .setHolidayOvertimeHours(dto.getHolidayOvertimeHours());
        // 计算人工成本
        return costConfigLevelPriceService.calculateLaborCost(costDTO);
    }


    /**
     * 更新父级工单实际人工成本
     *
     * @param task                 工单
     * @param parentTask           父级工单
     * @param allTaskListByProject 工单
     * @param allTaskMap           allTaskMap
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateParentActualLaborCost(CostDeliverTask task, CostDeliverTask parentTask, List<CostDeliverTask> allTaskListByProject, Map<Long, CostDeliverTask> allTaskMap) {
        // 子级工单总成本
        BigDecimal childrenActualLaborCost = allTaskListByProject.stream()
                .filter(f -> f.getParentId() != null)
                .filter(f -> f.getParentId().equals(task.getParentId()))
                .filter(f -> f.getActualLaborCost() != null && StrUtil.isNotEmpty(f.getActualLaborCost()))
                .map(CostDeliverTask::getActualLaborCost)
                .map(BigDecimal::new)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 需要更新的工单列表
        List<CostDeliverTask> updateTaskList = new ArrayList<>();

        BigDecimal budgetDiff = new BigDecimal(StrUtil.isNotEmpty(parentTask.getActualLaborCost()) ? parentTask.getActualLaborCost() : "0").subtract(childrenActualLaborCost);

        // 更新当前父级工单实际人工成本
        parentTask.setActualLaborCost(childrenActualLaborCost.toString());
        updateTaskList.add(BaseBuildEntityUtil.buildUpdate(parentTask));

        // 如果不是一级工单,向上逐级修改
        Long currentParentId = parentTask.getParentId();
        while (currentParentId != null) {
            CostDeliverTask currentTask = allTaskMap.get(currentParentId);
            if (currentTask != null) {
                // 非直属上级工单
                currentTask.setActualLaborCost(new BigDecimal(StrUtil.isNotEmpty(currentTask.getActualLaborCost()) ? currentTask.getActualLaborCost() : "0").subtract(budgetDiff).toString());
                updateTaskList.add(BaseBuildEntityUtil.buildUpdate(currentTask));
                currentParentId = currentTask.getParentId();
            } else {
                break;
            }
        }
        // 批量更新工单预算
        if (!updateTaskList.isEmpty()) {
            updateTaskList.forEach(CostDeliverTask::encrypt);
            updateBatchById(updateTaskList);
        }
    }

    /**
     * 结束工单任务
     *
     * @param id 工单ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void endTask(Long id) {
        // 获取工单信息
        CostDeliverTask task = getById(id);

        // 验证工单类型是否为售前支撑
        if (!ProjectTaskKindEnum.PRE_SALES_SUPPORT.getValue().equals(task.getTaskType())) {
            throw new ServiceException("只能结束售前支撑类型的工单");
        }

        // 验证工单当前状态是否为正常
        if (!CostTaskStatusEnum.ZC.getValue().equals(task.getTaskStatus())) {
            throw new ServiceException("只能结束正常状态的工单");
        }

        // 验证权限
        verifyEndRestartAuth(task);

        //  验证项目合同会签流程是否归档
        verifyProjectContract(task.getProjectId());

        // 更新工单状态为结束
        task.setTaskStatus(CostTaskStatusEnum.JS.getValue());
        task.setCompletionTime(LocalDateTime.now());
        baseMapper.updateById(BaseBuildEntityUtil.buildUpdate(task));

        // 如果是父级工单，联动结束所有子级工单
        if (task.getTaskLevel() < TASK_MAX_LEVEL) {
            List<CostDeliverTask> childTasks = getAllChildTasks(task.getId());
            if (CollUtil.isNotEmpty(childTasks)) {
                List<CostDeliverTask> zcTasks = childTasks.stream()
                        .filter(f -> CostTaskStatusEnum.ZC.getValue().equals(f.getTaskStatus()))
                        .peek(childTask -> {
                            childTask.setTaskStatus(CostTaskStatusEnum.JS.getValue());
                            childTask.setCompletionTime(LocalDateTime.now());
                            BaseBuildEntityUtil.buildUpdate(childTask.encrypt());
                        }).collect(Collectors.toList());
                this.updateBatchById(zcTasks);
            }
        }
    }

    /**
     * 重启工单任务
     *
     * @param id 工单ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void restartTask(Long id) {
        // 获取工单信息
        CostDeliverTask task = getById(id);

        // 验证工单类型是否为售前支撑
        if (!ProjectTaskKindEnum.PRE_SALES_SUPPORT.getValue().equals(task.getTaskType())) {
            throw new ServiceException("只能重启售前支撑类型的工单");
        }

        // 验证工单当前状态是否为结束
        if (!CostTaskStatusEnum.JS.getValue().equals(task.getTaskStatus())) {
            throw new ServiceException("只能重启结束状态的工单");
        }

        // 验证权限
        verifyEndRestartAuth(task);

        //  验证项目合同会签流程是否归档
        verifyProjectContract(task.getProjectId());

        // 如果是子级工单，验证父级工单状态
        if (task.getTaskLevel() > TASK_FIRST_LEVEL) {
            CostDeliverTask parentTask = getById(task.getParentId());
            if (parentTask != null && CostTaskStatusEnum.JS.getValue().equals(parentTask.getTaskStatus())) {
                throw new ServiceException("父级工单已结束，无法重启子级工单");
            }
        }

        // 更新工单状态为正常
        task.setTaskStatus(CostTaskStatusEnum.ZC.getValue());
        task.setEvaluationStatus(EvalTaskStatusEnum.DPJ.getValue());
        task.setCompletionTime(null);
        baseMapper.updateById(BaseBuildEntityUtil.buildUpdate(task));

        //删除工单评价
        evalTaskService.deleteEvaluation(id);
    }

    private void verifyProjectContract(Long projectId) {
        if (contractLedgerMapper.exists(Wrappers.lambdaQuery(ContractLedger.class).eq(ContractLedger::getXmmc, projectId))) {
            throw new ServiceException("当前项目的合同会签流程已归档，工单不能进行操作~");
        }
    }


    /**
     * 验证结束/重启权限
     *
     * @param task 工单信息
     */
    private void verifyEndRestartAuth(CostDeliverTask task) {
        PigxUser currentUser = getUser();

        // 获取项目信息
        ProjectInfo projectInfo = projectInfoMapper.selectById(task.getProjectId());
        if (projectInfo == null) {
            throw new ServiceException("项目不存在");
        }

        // 验证是否为售前经理
        Long preSaleUserId = projectInfo.getPreSaleUserId();
        if (currentUser.getId().equals(preSaleUserId)) {
            // 售前经理拥有所有一级工单的结束/重启权限
            if (task.getTaskLevel() == TASK_FIRST_LEVEL) {
                return;
            }
        }

        // 验证是否为工单负责人
        if (task.getTaskLevel() > TASK_FIRST_LEVEL) {
            // 获取父级工单
            CostDeliverTask parentTask = baseMapper.selectById(task.getParentId());
            if (parentTask != null && currentUser.getId().equals(parentTask.getManagerId())) {
                // 工单负责人拥有子级工单的结束/重启权限
                return;
            }
        }
        throw new ServiceException("您没有权限执行此操作");
    }

    @Override
    public List<CostDeliverTaskInDailyPaperEntryVO> findCurrentForDailyPaperEntry(LocalDate date) {
        Long userId = getUser().getId();
        // 查询当前用户可用的工单列表
        List<CostDeliverTask> costDeliverTasks = lambdaQuery()
                .eq(CostDeliverTask::getTaskType, ProjectTaskKindEnum.PRE_SALES_SUPPORT.getValue())
                .eq(CostDeliverTask::getDisassemblyType, CostTaskDisassemblyTypeEnum.STANDARD_WORK_ORDER.getValue())
                .eq(CostDeliverTask::getManagerId, userId)
                .and(e -> e.le(CostDeliverTask::getEndDate, date)
                        .or().isNull(CostDeliverTask::getEndDate)
                ).orderByDesc(CostDeliverTask::getCtime).list();

        if (CollUtil.isEmpty(costDeliverTasks)) {
            return Collections.emptyList();
        }
        // 查询工单类别
        Map<Integer, CostTaskCategoryManagement> costTaskCategoryMap = costTaskCategoryManagementService.getCostTaskCategoryMap();
        // 按项目ID分组
        List<CostDeliverTaskVO> taskVOList = costDeliverTasks.stream()
                .map(e -> CostDeliverTaskVO.buildVo(e.decrypt(), costTaskCategoryMap))
                .collect(Collectors.toList());
        // 设置审批人信息
        setHigherManager(taskVOList);
        Map<Long, List<CostDeliverTaskVO>> taskMap = taskVOList.stream().collect(Collectors.groupingBy(CostDeliverTaskVO::getProjectId));
        List<CostDeliverTaskInDailyPaperEntryVO> vos = new ArrayList<>();
        Set<Long> projectIds = taskMap.keySet();
        // 查询项目信息
        Map<Long, ProjectInfo> projectMap = projectInfoMapper.selectBatchIds(projectIds)
                .stream().collect(Collectors.toMap(ProjectInfo::getId, Function.identity()));
        // 查询项目收藏信息
        Map<Long, ProjectCollect> projectCollectMap = projectCollectMapper.findForDailyPaperEntry(projectIds, ProjectCollectTypeEnum.TICKETS_WORKING_HOURS.getValue(), userId)
                .stream().collect(Collectors.toMap(ProjectCollect::getProjectId, pc -> pc, (a, b) -> b));
        // 构建返回结果
        taskMap.forEach((projectId, tasks) -> {
            CostDeliverTaskInDailyPaperEntryVO vo = new CostDeliverTaskInDailyPaperEntryVO();
            vo.setProjectId(projectId);
            vo.setTasks(tasks);
            // 填充项目信息
            ProjectInfo projectInfo = projectMap.getOrDefault(projectId, new ProjectInfo());
            vo.setProjectName(projectInfo.getItemName());
            if (projectInfo.getIsNotInternalProject() != null) {
                vo.setIsInsideProject(Math.toIntExact(projectInfo.getIsNotInternalProject()));
            }
            vo.setPreSaleUserName(projectInfo.getPreSaleUserName());
            vo.setSalesmanUserName(projectInfo.getProjectSalesperson());
            vo.setManagerUserName(projectInfo.getManagerUserName());
            // 填充项目收藏信息
            ProjectCollect projectCollect = projectCollectMap.getOrDefault(projectId, new ProjectCollect());
            vo.setCollectId(projectCollect.getId());
            vo.setCollectTime(projectCollect.getCtime());
            vo.setCollectUserId(projectCollect.getCreatorId());
            vos.add(vo);
        });
        // 根据CollectTime排序
        vos.sort(Comparator.comparing(
                CostDeliverTaskInDailyPaperEntryVO::getCollectTime,
                Comparator.nullsLast(Comparator.naturalOrder())
        ));
        return vos;
    }

    private void setHigherManager(List<CostDeliverTaskVO> taskVOList) {
        if (CollUtil.isEmpty(taskVOList)) {
            return;
        }
        Set<Long> projectIds = new HashSet<>();
        List<Long> parentTaskIds = new ArrayList<>();

        taskVOList.forEach(task -> {
            projectIds.add(task.getProjectId());
            if (task.getParentId() != null) {
                parentTaskIds.add(task.getParentId());
            }
        });
        // 查询父级工单的负责人
        Map<Long, CostDeliverTask> higherManagerMap = CollUtil.isEmpty(parentTaskIds) ? Collections.emptyMap() : listByIds(parentTaskIds)
                .stream().collect(Collectors.toMap(BaseEntity::getId, e -> e));

        // 查询项目信息
        List<ProjectInfo> projectInfoList = projectInfoMapper.selectBatchIds(projectIds);
        Map<Long, ProjectInfo> projectInfoMap = new HashMap<>(projectInfoList.size());
        List<Long> preSaleUserIds = new ArrayList<>();
        projectInfoList.forEach(projectInfo -> {
            projectInfoMap.put(projectInfo.getId(), projectInfo);
            preSaleUserIds.add(projectInfo.getPreSaleUserId());
        });
        // 查询售前经理的上级信息
        Map<Long, Roster> preSaleLeaderMap = rosterService.findUserLeaderMap(preSaleUserIds);

        for (CostDeliverTaskVO task : taskVOList) {
            ProjectInfo projectInfo = projectInfoMap.getOrDefault(task.getProjectId(), new ProjectInfo());
            // 获取上级信息
            if (task.getTaskLevel() == TASK_FIRST_LEVEL) {
                setFirstHigherManager(task, projectInfo, preSaleLeaderMap);
            } else {
                CostDeliverTask parentTask = higherManagerMap.getOrDefault(task.getParentId(), new CostDeliverTask());
                task.setHigherManagerId(parentTask.getManagerId());
                task.setHigherManagerName(parentTask.getManagerName());
            }
        }

    }


    /**
     * 查询异常工单列表
     *
     * @param pageRequest 分页请求
     * @param request     查询条件
     * @return 异常工单列表
     */
    @Override
    public Page<CostTaskAbnormalVO> findAbnormalPage(PageRequest pageRequest, CostTaskAbnormalDTO request) {
        // 查询异常工单数据
        Page<CostTaskAbnormalVO> page = baseMapper.findAbnormalPage(new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize()), request);
        // 处理异常工单数据
        handleAbnormalData(page.getRecords());
        return page;
    }

    /**
     * 导出异常工单
     *
     * @param request 查询条件
     * @return 异常工单列表
     */
    @Override
    public List<CostTaskAbnormalVO> exportAbnormal(CostTaskAbnormalDTO request) {
        // 查询异常工单数据
        List<CostTaskAbnormalVO> list = baseMapper.findAbnormalPage(request);
        // 处理异常工单数据
        handleAbnormalData(list);
        return list;
    }

    /**
     * 处理异常工单数据
     *
     * @param list 异常工单列表
     */
    private void handleAbnormalData(List<CostTaskAbnormalVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 查询工单类别
        Map<Integer, CostTaskCategoryManagement> costTaskCategoryMap = costTaskCategoryManagementService.getCostTaskCategoryMap();
        // 获取所有用户信息
        list.forEach(vo -> {
            vo.setAbnormalTypeTxt(EnumUtils.getNameByValue(TaskAbnormalTypeEnum.class, vo.getAbnormalType()));
            vo.setTaskStatusTxt(EnumUtils.getNameByValue(CostTaskStatusEnum.class, vo.getTaskStatus()));
            vo.setTaskCategoryTxt(costTaskCategoryMap.getOrDefault(vo.getTaskCategory(), new CostTaskCategoryManagement()).getTaskCategoryName());
            vo.setDisassemblyTypeTxt(EnumUtils.getNameByValue(CostTaskDisassemblyTypeEnum.class, vo.getDisassemblyType()));
            vo.setStartEndDate(vo.getStartDate() + " ~ " + vo.getEndDate());
        });
    }

    @Override
    public void sendAbnormalMsg(TaskAbnormalTypeEnum abnormalTypeEnum) {
        CostTaskAbnormalDTO request = new CostTaskAbnormalDTO();
        request.setAbnormalType(abnormalTypeEnum.getValue());
        // 查询异常工单数据
        List<CostTaskAbnormalVO> abnormalList = baseMapper.findAbnormalPage(request);
        // 按工单负责人分组
        Map<String, List<CostTaskAbnormalVO>> groupedByUser = CollStreamUtil.groupByKey(abnormalList, CostDeliverTaskServiceImpl::buildSenderKey);
        // 构建消息推送列表
        List<BaseSendMsgDTO> msgList = new ArrayList<>();
        // 处理每个工单负责人的消息
        buildAbnormalMsg(groupedByUser, abnormalTypeEnum, msgList);
        // 批量发送消息
        bcpMessageService.batchSendMsg(msgList);
    }


    @Override
    public void sendAbnormalMsg(CostTaskAbnormalDTO request) {
        // 查询异常工单数据
        List<CostTaskAbnormalVO> abnormalList = baseMapper.findAbnormalPage(request);

        // 按异常类型和推送对象分组
        Map<Integer, Map<String, List<CostTaskAbnormalVO>>> groupedByTypeAndUser = abnormalList.stream()
                .filter(vo -> vo.getAbnormalType() != null
                        && vo.getManagerId() != null && vo.getReviewerId() != null)
                .collect(Collectors.groupingBy(
                        CostTaskAbnormalVO::getAbnormalType,
                        Collectors.groupingBy(CostDeliverTaskServiceImpl::buildSenderKey)
                ));

        // 构建消息推送列表
        List<BaseSendMsgDTO> msgList = new ArrayList<>();

        // 处理每种异常类型的消息
        groupedByTypeAndUser.forEach((abnormalType, userMap) -> {
            TaskAbnormalTypeEnum abnormalTypeEnum = EnumUtils.getEnumByValue(TaskAbnormalTypeEnum.class, abnormalType);
            if (abnormalTypeEnum == null) {
                return;
            }
            buildAbnormalMsg(userMap, abnormalTypeEnum, msgList);
        });

        // 批量发送消息
        bcpMessageService.batchSendMsg(msgList);
    }

    @NotNull
    private static String buildSenderKey(CostTaskAbnormalVO vo) {
        // 根据异常类型确定推送对象
        TaskAbnormalTypeEnum abnormalTypeEnum = EnumUtils.getEnumByValue(TaskAbnormalTypeEnum.class, vo.getAbnormalType());
        if (abnormalTypeEnum == null) {
            return StringPool.EMPTY;
        }
        switch (abnormalTypeEnum) {
            case UN_SUBMITTED:
            case UN_DECOMPOSED:
            case UN_EVALUATED:
                return vo.getManagerId() + StringPool.COLON + vo.getManagerName();
            case UN_REVIEWED:
                return vo.getReviewerId() + StringPool.COLON + vo.getReviewerName();
            default:
                return StringPool.EMPTY;
        }
    }

    private static void buildAbnormalMsg(Map<String, List<CostTaskAbnormalVO>> userMap, TaskAbnormalTypeEnum abnormalTypeEnum, List<BaseSendMsgDTO> msgList) {
        // 根据异常类型设置不同的消息内容
        String content;
        String url;
        String title;
        switch (abnormalTypeEnum) {
            case UN_SUBMITTED:
                content = "您好，您目前存在%d个工单未提交完成，请及时处理～";
                title = "【工单未提交提醒】";
                url = "/view-businesses/cooperate-office/project-work-orders/own/list";
                break;
            case UN_REVIEWED:
                content = "您好，您目前存在%d个未审核的售后工单，请及时审核～";
                title = "【工单未审核提醒】";
                url = "/view-businesses/cooperate-office/work-order-review/work-order-time-review/index";
                break;
            case UN_DECOMPOSED:
                content = "您好，您目前存在%d个未拆解的工单，请及时审核～";
                title = "【工单未拆解提醒】";
                url = "/view-businesses/cooperate-office/project-work-orders/own/list";
                break;
            case UN_EVALUATED:
                content = "您好，您目前存在%d个未评价的工单，请及时评价～";
                title = "【工单未评价提醒】";
                url = "/view-businesses/cooperate-office/work-order-evaluation-manage/work-order-evaluation/index";
                break;
            default:
                return;
        }
        userMap.forEach((senderKey, voList) -> {
            if (CollUtil.isEmpty(voList) || StringUtils.isBlank(senderKey)) {
                return;
            }
            String[] senderSplit = senderKey.split(StringPool.COLON);
            // 根据异常类型设置不同的消息内容
            BaseSendMsgDTO baseSendMsgDTO = new BaseSendMsgDTO()
                    .setTitle(title)
                    .setContent(String.format(content, voList.size()))
                    .setMsgTypeEnum(MsgTypeEnum.NOTICE_MSG)
                    .setTargetTypeEnum(TargetTypeEnum.USERS)
                    .setChannelEnums(CollUtil.newLinkedHashSet(ChannelEnum.WECOM, ChannelEnum.MAIL))
                    .setPath(url)
                    .populateSender()
                    .toOneTarget(Long.valueOf(senderSplit[0]), senderSplit[1]);
            msgList.add(baseSendMsgDTO);
        });
    }

    @Override
    public CostTaskAbnormalCountVO getAbnormalCount(CostTaskAbnormalDTO request) {
        List<JSONObject> countList = baseMapper.countAbnormalGroupByType(request);
        int unSubmitted = 0, unReviewed = 0, unDecomposed = 0, unEvaluated = 0;
        for (JSONObject jsonObject : countList) {
            Integer abnormalType = jsonObject.getInteger("abnormalType");
            Integer count = jsonObject.getInteger("count");
            TaskAbnormalTypeEnum abnormalTypeEnum = EnumUtils.getEnumByValue(TaskAbnormalTypeEnum.class, abnormalType);
            if (abnormalTypeEnum == null) {
                continue;
            }
            switch (abnormalTypeEnum) {
                case UN_SUBMITTED:
                    unSubmitted = count;
                    break;
                case UN_REVIEWED:
                    unReviewed = count;
                    break;
                case UN_DECOMPOSED:
                    unDecomposed = count;
                    break;
                case UN_EVALUATED:
                    unEvaluated = count;
                    break;
                default:
            }
        }
        return CostTaskAbnormalCountVO.builder()
                .unSubmittedCount(unSubmitted)
                .unReviewedCount(unReviewed)
                .unDecomposedCount(unDecomposed)
                .unEvaluatedCount(unEvaluated)
                .totalCount(unSubmitted + unReviewed + unDecomposed + unEvaluated)
                .build();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void endSupportTask() {
        // 合同会签流程归档后，对应的售前工单要结束
        List<CostDeliverTask> taskList = lambdaQuery()
                .eq(CostDeliverTask::getTaskType, ProjectTaskKindEnum.PRE_SALES_SUPPORT.getValue())
                .eq(CostDeliverTask::getTaskStatus, CostTaskStatusEnum.ZC.getValue())
                .eq(CostDeliverTask::getTaskLevel, TASK_FIRST_LEVEL)
                .groupBy(CostDeliverTask::getProjectId)
                .list();
        if (CollUtil.isEmpty(taskList)) {
            return;
        }

        Set<Long> projectIds = CollStreamUtil.toSet(taskList, CostDeliverTask::getProjectId);

        List<ContractLedger> contractLedgers = contractLedgerMapper.selectList(
                Wrappers.lambdaQuery(ContractLedger.class)
                        .in(ContractLedger::getXmmc, projectIds));

        if (CollUtil.isEmpty(contractLedgers)) {
            return;
        }

        projectIds = CollStreamUtil.toSet(contractLedgers, ContractLedger::getXmmc);

        List<CostDeliverTask> endTasks = lambdaQuery()
                .eq(CostDeliverTask::getTaskType, ProjectTaskKindEnum.PRE_SALES_SUPPORT.getValue())
                .in(CostDeliverTask::getProjectId, projectIds)
                .eq(CostDeliverTask::getTaskStatus, CostTaskStatusEnum.ZC.getValue())
                .list();

        endTasks.forEach(task -> {
            task.setModifier("合同会签归档");
            task.setModifierId(0L);
            task.setMtime(new Timestamp(System.currentTimeMillis()));
            task.setTaskStatus(CostTaskStatusEnum.JS.getValue());
            task.setCompletionTime(LocalDateTime.now());
        });
        updateBatchById(endTasks);
    }

    /**
     * 按日期获取最大序列号
     *
     * @param dateStr 日期 str
     * @param prefix  前缀
     * @return {@link Integer }
     */
    @Override
    public Integer getMaxSequenceNumberByDate(String dateStr, String prefix) {
        return baseMapper.getMaxSequenceNumberByDate(dateStr, prefix);
    }

    /**
     * 生成工单编号
     * 规则：售前/售后-工单类别-工单负责人-创建日期-顺序性流水号
     * 示例：sq-xmgl-zsf-20250508-0001
     *
     * @param taskTypeEnum    工单类型
     * @param taskCategoryMap 工单类别映射
     * @param taskCategory    工单类别ID
     * @param managerName     负责人姓名
     * @return 工单编号
     */
    @Override
    public String generateTaskNo(ProjectTaskKindEnum taskTypeEnum, Map<Integer, CostTaskCategoryManagement> taskCategoryMap,
                                 Integer taskCategory, String managerName) {
        final String prefix = ProjectTaskKindEnum.PRE_SALES_SUPPORT.equals(taskTypeEnum) ? "sq" : "sh";
        // 参数校验
        if (StringUtils.isBlank(prefix) || StringUtils.isBlank(managerName)) {
            throw new ServiceException("工单前缀和负责人姓名不能为空");
        }

        // 获取工单类别缩写
        String categoryCode = getCategoryShortName(taskCategory, taskCategoryMap);

        // 获取负责人拼音缩写
        String managerCode = getManagerCode(managerName);

        // 获取当前日期
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern(YEAR_MONTH_DAY));

        // 获取序列号
        String sequenceNumber = getSequenceNumber(dateStr, prefix);

        // 组装工单编号：sq-类别-负责人-日期-序号
        return String.format("%s-%s-%s-%s-%s", prefix, categoryCode, managerCode, dateStr, sequenceNumber);
    }


    /**
     * 获取负责人姓名拼音缩写
     *
     * @param managerName 负责人姓名
     * @return 拼音缩写
     */
    private String getManagerCode(String managerName) {
        if (StringUtils.isBlank(managerName)) {
            return "unknown";
        }
        return PinyinUtil.getFirstLetter(managerName, StrUtil.EMPTY).toLowerCase();
    }

    /**
     * 获取工单类别缩写
     *
     * @param taskCategory    工单类别ID
     * @param taskCategoryMap 工单类别映射
     * @return 工单类别缩写
     */
    private String getCategoryShortName(Integer taskCategory, Map<Integer, CostTaskCategoryManagement> taskCategoryMap) {
        if (taskCategory == null || CollUtil.isEmpty(taskCategoryMap)) {
            return QT;
        }

        CostTaskCategoryManagement category = taskCategoryMap.get(taskCategory);
        if (category == null || StringUtils.isBlank(category.getTaskCategoryName())) {
            return QT;
        }

        return PinyinUtil.getFirstLetter(category.getTaskCategoryName(), StringPool.EMPTY);
    }


    /**
     * 获取序列号
     * 使用分布式锁和缓存来保证并发安全和性能
     *
     * @param dateStr 日期字符串
     * @param prefix  前缀
     * @return 4位序列号
     */

    private String getSequenceNumber(String dateStr, String prefix) {
        String lockKey = String.format("task_no_lock:%s:%s", prefix, dateStr);
        String cacheKey = String.format("task_no_seq:%s:%s", prefix, dateStr);
        // 定义最大重试次数
        final int maxRetries = 3;
        // 定义初次等待时间
        final long initialWaitTime = 100L;
        // 定义分布式锁的超时时间
        final long lockTimeout = 10L;
        // 定义缓存序列号的超时时间
        final long cacheTimeout = 24L;
        for (int retryCount = 0; retryCount < maxRetries; retryCount++) {
            try {
                // 获取分布式锁
                Boolean locked = redisTemplate.opsForValue().setIfAbsent(lockKey, 1, lockTimeout, TimeUnit.SECONDS);
                if (Boolean.TRUE.equals(locked)) {
                    try {
                        // 从缓存获取当前序列号
                        Integer currentSeq = redisTemplate.opsForValue().get(cacheKey);
                        if (currentSeq == null) {
                            // 缓存未命中，从数据库获取最大序号
                            currentSeq = getMaxSequenceNumberByDate(dateStr, prefix);
                            currentSeq = (currentSeq == null) ? 0 : currentSeq;
                        }

                        // 递增序列号并更新缓存
                        currentSeq++;
                        redisTemplate.opsForValue().set(cacheKey, currentSeq, cacheTimeout, TimeUnit.HOURS);

                        return String.format("%04d", currentSeq);
                    } finally {
                        // 释放分布式锁
                        redisTemplate.delete(lockKey);
                    }
                }

                // 使用指数退避策略等待
                Thread.sleep(initialWaitTime * (1L << retryCount));

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new ServiceException("生成序列号被中断");
            } catch (Exception e) {
                log.error("生成序列号失败: prefix={}, dateStr={}", prefix, dateStr, e);
                throw new ServiceException("生成序列号失败");
            }
        }

        throw new ServiceException("获取序列号失败，超过最大重试次数");
    }


}

