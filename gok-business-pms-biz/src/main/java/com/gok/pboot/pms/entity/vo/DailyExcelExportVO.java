package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * @Auther chenhc
 * @Date 2022-08-24 14:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DailyExcelExportVO {
    /**
     * 员工ID
     */
    @ExcelIgnore
    private Long userId;

    /**
     * 姓名
     */
    @ExcelProperty(index = 0, value = "姓名")
    private String name;
    /**
     * 员工部门ID
     */
    @ExcelIgnore
    private Long userDeptId;
    /**
     * 部门
     */
    @ExcelProperty(index = 1, value = "部门")
    private String deptName;

    /**
     * 人员状态（正式、实习）
     */
    @ExcelIgnore
    private Integer userStatus;
    /**
     * 人员状态（正式、实习）
     */
    @ExcelProperty(index = 2, value = "人员状态")
    private String userStatusStr;
    /**
     * 填报日期（日报的日期，按应填日报日期+假期加班有填的日期）
     */
    @ExcelProperty(index = 3, value = "填报日期")
    private LocalDate submissionDate;
    /**
     * 周x
     */
    @ExcelProperty(index = 4, value = "星期几")
    private String month;
    /**
     * 审核状态（0=未提交，1=已退回，2=待审核，3=不通过，4=已通过）
     */
    @ExcelIgnore
    private Integer approvalStatus;
    /**
     * 审核状态
     */
    @ExcelProperty(index = 5, value = "审核状态")
    private String approvalStatusStr;
    /**
     * 填报状态（0=正常，1=滞后）
     */
    @ExcelIgnore
    private Integer fillingState;
    /**
     * 提交状态(正常、滞后)
     */
    @ExcelProperty(index = 6, value = "提交状态")
    private String fillingStateStr;
    /**
     * 是否异常
     */
    @ExcelProperty(index = 7, value = "是否异常")
    private String isAbnormal;
    /**
     * 正常工时
     */
    @ExcelProperty(index = 8, value = "正常工时")
    private BigDecimal dailyHourCount;
    /**
     * 加班工时
     */
    @ExcelProperty(index = 9, value = "总加班工时")
    private BigDecimal addedHourCount;
    /**
     * 工作日加班
     */
    @ExcelProperty(index = 10, value = "工作日加班")
    private BigDecimal workOvertimeHours;
    /**
     * 休息日加班
     */
    @ExcelProperty(index = 11, value = "休息日加班")
    private BigDecimal restOvertimeHours;
    /**
     * 节假日加班
     */
    @ExcelProperty(index = 12, value = "节假日加班")
    private BigDecimal holidayOvertimeHours;

    /**
     * 请休假工时
     */
    @ExcelProperty(index = 13, value = "请休假工时")
    private BigDecimal leaveHours;

    /**
     * 调休工时
     */
    @ExcelProperty(index = 14, value = "调休工时")
    private BigDecimal compensatoryHourCount;

    /**
     * 是否工作日（0=否，1=是）
     */
    @ExcelIgnore
    private Integer workday;
}
