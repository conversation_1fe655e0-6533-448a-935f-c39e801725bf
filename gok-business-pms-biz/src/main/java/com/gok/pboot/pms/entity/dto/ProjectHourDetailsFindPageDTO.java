package com.gok.pboot.pms.entity.dto;

import com.gok.pboot.pms.common.base.PageRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.LocalDate;

/**
 * 项目工时汇总明细分页查询条件
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ProjectHourDetailsFindPageDTO extends PageRequest {
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 开始日期
     */
    private LocalDate startTime;
    /**
     * 结束日期
     */
    private LocalDate endTime;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 姓名
     */
    private String username;
}
