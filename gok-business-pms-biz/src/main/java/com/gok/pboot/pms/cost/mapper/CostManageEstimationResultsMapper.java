package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.cost.entity.domain.CostManageEstimationResults;
import com.gok.pboot.pms.cost.entity.vo.CostManageConfigVersionVO;
import com.gok.pboot.pms.cost.entity.vo.CostManageEstimationResultsVO;
import com.gok.pboot.pms.cost.entity.vo.CostManageSelectVO;
import com.gok.pboot.pms.cost.entity.vo.DeliverCostBudgetListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 成本管理估算结果 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Mapper
public interface CostManageEstimationResultsMapper extends BaseMapper<CostManageEstimationResults> {

    /**
     * 根据版本号获取对应的成本估算结果列表
     *
     * @param versionIds 版本ID集合
     * @return 估算结果列表
     */
    List<CostManageEstimationResultsVO> findByVersionId(@Param("versionIds") List<Long> versionIds);

    /**
     * 批量保存
     *
     * @param saveEntries 待保存的记录列表
     * @return 批量保存结果
     */
    int batchSave(@Param("saveEntries") List<CostManageEstimationResults> saveEntries);

    /**
     * 获取成本配置版本列表
     *
     * @param versionId 成本估算结果版本ID
     * @return
     */
    List<CostManageConfigVersionVO> getCostConfigVersion(@Param("versionId") Long versionId, @Param("projectId") Long projectId);

    /**
     * 根据id获取对应的成本估算结果列表
     * 包含子级id
     *
     * @param id 版本ID
     * @return 估算结果列表
     */
    List<CostManageEstimationResultsVO> findAllById(@Param("id") Long id);

    /**
     * 查询管理费用预算
     *
     * @param versionId 版本 ID
     * @param costType  成本类型
     * @return {@link List }<{@link DeliverCostBudgetListVO }>
     */
    List<DeliverCostBudgetListVO> findCostBudgetByVersionId(@Param("versionId") Long versionId, @Param("costType") Integer costType);

    /**
     * 查询项目成本预算
     *
     * @param versionId 版本 ID
     * @param accountId 帐户 ID
     * @return {@link CostManageSelectVO }
     */
    CostManageSelectVO findCostManageSelectListVO(@Param("versionId") Long versionId, @Param("accountId") Long accountId);

}
