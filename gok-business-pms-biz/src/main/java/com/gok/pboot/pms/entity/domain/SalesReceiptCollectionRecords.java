package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 销售收款计划催款记录
 *
 * <AUTHOR>
 * @since 2024-02-20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("sales_receipt_collection_records")
@ApiModel("销售收款计划催款记录")
public class SalesReceiptCollectionRecords extends Model<SalesReceiptCollectionRecords> {

    /**
     * 催款记录id
     */
    @ApiModelProperty("催款记录id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 销售计划id
     */
    @ApiModelProperty("销售计划id")
    private Long salesReceiptId;

    /**
     * 预计收款日期
     */
    @ApiModelProperty("预计收款日期")
    private LocalDateTime expectedReceiptDate;

    /**
     * 催收日期
     */
    @ApiModelProperty("催收日期")
    private LocalDateTime collectionDate;

    /**
     * 催收金额
     */
    @ApiModelProperty("催收金额")
    private BigDecimal collectionAmount;

    /**
     * 催款方式
     * {@link com.gok.pboot.pms.enumeration.CollectionMethodEnum}
     */
    @ApiModelProperty("催款方式")
    private Integer collectionMethod;

    /**
     * 催款情况
     */
    @ApiModelProperty("催款情况")
    private String collectionSituation;

    /**
     * 催款备注
     */
    @ApiModelProperty("催款备注")
    private String collectionRemarks;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic
    @ApiModelProperty("删除标记")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty("所属租户")
    private Long tenantId;
}