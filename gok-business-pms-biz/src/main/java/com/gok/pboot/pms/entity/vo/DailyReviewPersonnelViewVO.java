package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.Util.DailyPaperDateUtils;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.entity.DailyPaperEntry;
import com.gok.pboot.pms.entity.domain.TomorrowPlanPaperEntry;
import com.gok.pboot.pms.enumeration.ApprovalStatusEnum;
import com.gok.pboot.pms.enumeration.DailyPaperFillingStateEnum;
import com.gok.pboot.pms.enumeration.WorkTypeEnum;
import lombok.*;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.HashMap;

/**
 * 日报审核 人员维度
 *
 * <AUTHOR>
 * @version 1.3.2
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class DailyReviewPersonnelViewVO {

    /**
     * ID
     */
    private String id;

    /**
     * 人员姓名
     */
    private String userRealName;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 工时类型
     * @see com.gok.pboot.pms.enumeration.WorkTypeEnum
     */
    private Integer workType;

    /**
     * 工时类型名称
     */
    private String workTypeName;

    /**
     * 填报日期
     */
    private LocalDate submissionDate;
    /**
     * 填报日期带周
     */
    private String submissionDateFormatted;

    /**
     * 工作内容
     */
    private String description;

    /**
     * 昨日计划
     */
    private String yesterdayPlan;

    /**
     * 正常工时
     */
    private BigDecimal normalHours;

    /**
     * 加班工时
     */
    private BigDecimal addedHours;

    /**
     * 提交时间
     */
    private LocalDate commitDate;

    /**
     * 填报状态
     * @see com.gok.pboot.pms.enumeration.DailyPaperFillingStateEnum
     */
    private Integer fillingState;

    /**
     * 填报状态名称
     */
    private String fillingStateName;

    /**
     * 审批状态
     * @see com.gok.pboot.pms.enumeration.ApprovalStatusEnum
     */
    private Integer approvalStatus;

    /**
     * 审批状态名称
     */
    private String approvalStatusName;

    /**
     * 假期类型：1-法定节假日 0-普通休息日
     */
    private Integer holidayType;

    public static DailyReviewPersonnelViewVO of(
            DailyPaperEntry entry,
            @Nullable TomorrowPlanPaperEntry lastDayPlanEntry,
            Integer fillingState,
            HashMap<LocalDate, Integer> holidayMap
    ) {
        DailyReviewPersonnelViewVO result = new DailyReviewPersonnelViewVO();
        Integer workType = entry.getWorkType();
        Timestamp ctime = entry.getCtime();
        BigDecimal normalHours = entry.getNormalHours();
        BigDecimal addedHours = entry.getAddedHours();
        Integer approvalStatus = entry.getApprovalStatus();

        result.setId(String.valueOf(entry.getId()));
        result.setUserRealName(entry.getUserRealName());
        result.setProjectName(entry.getProjectName());
        result.setTaskName(entry.getTaskName());
        if (workType != null) {
            result.setWorkType(workType);
            result.setWorkTypeName(EnumUtils.getNameByValue(WorkTypeEnum.class, workType));
        }
        result.setSubmissionDate(entry.getSubmissionDate());
        if (ctime != null) {
            result.setCommitDate(ctime.toLocalDateTime().toLocalDate());
        }
        result.setFillingState(fillingState);
        result.setFillingStateName(EnumUtils.getNameByValue(DailyPaperFillingStateEnum.class, fillingState));
        result.setNormalHours(normalHours == null ? BigDecimal.ZERO : normalHours);
        result.setAddedHours(addedHours == null ? BigDecimal.ZERO : addedHours);
        result.setDescription(entry.getDescription());
        if (lastDayPlanEntry != null) {
            result.setYesterdayPlan(lastDayPlanEntry.getDescription());
        }
        result.setApprovalStatus(approvalStatus);
        result.setApprovalStatusName(EnumUtils.getNameByValue(ApprovalStatusEnum.class, approvalStatus));

        result.setSubmissionDateFormatted(DailyPaperDateUtils.asDateString(result.getSubmissionDate()));
        result.setHolidayType(holidayMap.get(result.getSubmissionDate()));
        return result;
    }
}
