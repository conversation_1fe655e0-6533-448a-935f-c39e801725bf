package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.cost.entity.domain.CostConfigTravelSubsidy;
import com.gok.pboot.pms.cost.entity.dto.CostConfigTravelSubsidyDTO;
import com.gok.pboot.pms.cost.entity.vo.CostConfigTravelSubsidyVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 差旅补贴标准配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
public interface ICostConfigTravelSubsidyService extends IService<CostConfigTravelSubsidy> {

    /**
     * 获取最新版本差旅住宿标准配置
     *
     * @return {@link List }<{@link CostConfigTravelSubsidyVO }>
     */
    List<CostConfigTravelSubsidyVO> getCostConfigTravelSubsidyList();

    /**
     * 编辑 差旅住宿标准配置
     *
     * @param dtoList DTO 列表
     */
    void editCostConfigTravelSubsidyList(@Param("dtoList") List<CostConfigTravelSubsidyDTO> dtoList);

    /**
     * 根据版本id获取差旅补贴标准配置
     *
     * @param versionId 版本 ID
     * @return {@link List }<{@link CostConfigTravelSubsidyVO }>
     */
    List<CostConfigTravelSubsidyVO> getCostConfigTravelSubsidyListByVersionId(Long versionId);
}
