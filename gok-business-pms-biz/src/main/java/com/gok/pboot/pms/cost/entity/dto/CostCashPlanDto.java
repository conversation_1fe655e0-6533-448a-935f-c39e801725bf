package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2025/03/06
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CostCashPlanDto {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 版本ID
     * 为空则默认取当前版本
     */
    private Long versionId;

}
