package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 项目字典
 *
 * <AUTHOR>
 * @date 2023/4/11
 */
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ProjectDictVo {

    /**
     * id
     */
    private Long id;

    /**
     * 字段id
     */
    private Integer fieldid;

    /**
     * 选项值
     */
    private Integer selectvalue;

    /**
     * 选项名称
     */
    private String selectname;


}
