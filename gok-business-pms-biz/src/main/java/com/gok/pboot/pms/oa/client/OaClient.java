package com.gok.pboot.pms.oa.client;

import com.alibaba.fastjson.JSONObject;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Var;
import com.gok.pboot.pms.oa.dto.OaGetTokenDTO;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * - OA后台 -
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@BaseRequest(
        headers = {
                "Accept-Charset: utf-8",
                "Content-Type: application/json"
        }
)
@Component
public interface OaClient {

    /**
     * 获取token
     */
    @Post(
            url = "{url}/api/ec/dev/auth/applytoken",
            maxRetryInterval = 3,
            headers = {"appid:${appid}", "secret:${secret}"}
    )
    OaGetTokenDTO getToken(
            @Var("url") String url,
            @Var("appid") String appid,
            @Var("secret") String secret
    );

    /**
     * 获取流程相关资源
     */
    @Get(
            url = "{url}/api/workflow/paService/getRequestResources?requestid={requestid}",
            headers = {"token:${token}", "appid:${appid}", "userid:${userid}"}
    )
    Map getRequestResources(
            @Var("url") String url,
            @Var("requestid") String requestid,
            @Var("token") String token,
            @Var("appid") String appid,
            @Var("userid") String userid
    );

    /**
     * 创建流程
     *
     * @param workflowId  流程ID
     * @param requestName 请求名字
     * @param mainData    主表参数
     * @param otherParams 其他参数
     * @return
     */
    @Post(url = "{url}/api/workflow/paService/doCreateRequest",
            headers = {
                "token:${token}", 
                "appid:${appid}", 
                "userid:${userid}",
                "Content-Type: application/x-www-form-urlencoded"
            },
            data = "workflowId={workflowId}&requestName={requestName}&mainData={mainData}&otherParams={otherParams}&detailData={detailData}")
    JSONObject doCreateRequest(@Var("token") String token,
                               @Var("appid") String appid,
                               @Var("userid") String userid,
                               @Var("url") String url,
                               @Var("workflowId") String workflowId, @Var("requestName") String requestName,
                               @Var("mainData") String mainData, @Var("otherParams") String otherParams,
                               @Var("detailData") String detailData);

    /**
     * 同步OA公共字典
     *
     * @param id   字典id
     * @param name 字典名称
     * @return
     */
    @Post(url = "{url}/api/workflow/formManage/publicselect/saveSelectItem?id={id}&name={name}",
            headers = {"token:${token}", "appid:${appid}", "userid:${userid}"})
    Map saveSelectItem(@Var("token") String token,
                       @Var("appid") String appid,
                       @Var("userid") String userid,
                       @Var("url") String url,
                       @Var("id") String id, @Var("name") String name);


    /**
     * 提交流程
     * @param userid
     * @param requestId
     * @param detailData
     * @param mainData
     * @param otherParams
     * @return
     */
    @Post(url = "{url}/api/workflow/paService/submitRequest",
            headers = {
                "token:${token}", 
                "appid:${appid}", 
                "userid:${userid}",
                "Content-Type: application/x-www-form-urlencoded"
            },
            data = "requestId={requestId}&detailData={detailData}&mainData={mainData}&otherParams={otherParams}"
    )
    Map submitRequest(
            @Var("token") String token,
            @Var("appid") String appid,
            @Var("userid") String userid,
            @Var("url") String url,
            @Var("requestId") String requestId,
            @Var("detailData") String detailData,
            @Var("mainData") String mainData,
            @Var("otherParams") String otherParams);
}
