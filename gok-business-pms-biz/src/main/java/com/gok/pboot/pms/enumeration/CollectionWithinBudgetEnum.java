package com.gok.pboot.pms.enumeration;

import lombok.Getter;

/**
 * 预算内回款 Enum
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Getter
public enum CollectionWithinBudgetEnum implements ValueEnum<Integer> {

    /**
     * 已回款
     */
    YES(0, "是"),

    /**
     * 未回款
     */
    NO(1, "否");

    private final Integer value;

    private final String name;

    CollectionWithinBudgetEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

}
