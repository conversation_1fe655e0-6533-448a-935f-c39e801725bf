package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 售前工单负责人角色枚举
 *
 * <AUTHOR>
 * @date 2025/01/15
 */
@AllArgsConstructor
@Getter
public enum CostPresalesTaskManagerRoleEnum implements ValueEnum<Integer> {

    /**
     * 售前经理
     */
    SQJL(0, "售前经理"),

    /**
     * 项目经理
     */
    XMJL(1, "项目经理"),

    /**
     * 客户经理
     */
    KHJL(2, "客户经理");

    private final Integer value;
    private final String name;
} 