package com.gok.pboot.pms.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.BaseController;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.base.PropertyFilters;
import com.gok.pboot.pms.entity.vo.FilingFindPageVO;
import com.gok.pboot.pms.service.IFilingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
/**
 *
 * @description 归档 前端控制器
 * <AUTHOR>
 * @since 2022-08-29
 */
@Slf4j
@RestController
@RequestMapping("filing")
public class FilingController extends BaseController {

    private IFilingService service;

    @Autowired
    public FilingController(IFilingService service) {
        this.service = service;
    }

    /**
     * 定时器
     * 自上线后开始，每月1号0点自动生成本月数据条  0 0 0 1 * ?
     * @para 选择月份:2022-06
     * @return {@link ApiResult}
     */
    @Inner(value = false)
    @GetMapping("/filingTask/job")
    public ApiResult<String> filingTask(String para) {
        log.info("定时任务触发,每月1日0点触发生成该月的未归档记录:{}，输入参数{},执行开始", LocalDateTime.now(), para);
        ApiResult<String> result = service.filingTask(para);
        log.info("定时任务触发,每月1日0点触发生成该月的未归档记录:{}，输入参数{}，执行结束", LocalDateTime.now(), para);
        return result;
    }


    /**
     * 归档
     * @param id
     * @return {@link ApiResult}
     */
    @GetMapping("/file/{id}")
    public ApiResult<String> file(@PathVariable("id") Long id) {
        return service.file(id);
    }

    /**
     * 取消归档
     * @param id
     * @return {@link ApiResult}
     */
    @GetMapping("/cancelFile/{id}")
    public ApiResult<String> cancelFile(@PathVariable("id") Long id) {
        return service.cancelFile(id);
    }

    /**
     * 分页查询归档
     *
     * @param pageRequest
     * @param request
     *@customParam filter_S_filed 0：为归档；1：已归档
     * @return {@link ApiResult<Page<FilingFindPageVO>>}
     */
    @GetMapping("/findPage")
    public ApiResult<Page<FilingFindPageVO>> findPage(PageRequest pageRequest, HttpServletRequest request) {
        return success(service.FilingFindPageVO(pageRequest, PropertyFilters.get(request)));
    }


}
