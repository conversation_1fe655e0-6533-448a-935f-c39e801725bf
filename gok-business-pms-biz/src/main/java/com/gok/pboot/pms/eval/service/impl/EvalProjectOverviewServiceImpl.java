package com.gok.pboot.pms.eval.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.message.dto.BcpMessageBatchDTO;
import com.gok.bcp.message.dto.BcpMessageTargetBatchDTO;
import com.gok.bcp.message.dto.BcpMessageTargetDTO;
import com.gok.bcp.message.entity.enums.ChannelEnum;
import com.gok.bcp.message.entity.enums.MsgTypeEnum;
import com.gok.bcp.message.entity.enums.SourceEnum;
import com.gok.bcp.message.entity.enums.TargetTypeEnum;
import com.gok.bcp.message.entity.model.BcpMessageContentModel;
import com.gok.bcp.message.entity.model.WeComBatchModel;
import com.gok.bcp.message.entity.model.WeComModel;
import com.gok.bcp.message.feign.RemoteSendMsgService;
import com.gok.components.common.constant.SecurityConstants;
import com.gok.module.file.entity.SysFile;
import com.gok.module.file.service.SysFileService;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.DbApiUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.Util.PageUtils;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.domain.CostBaselineVersionRecord;
import com.gok.pboot.pms.cost.entity.domain.CostManageVersion;
import com.gok.pboot.pms.cost.entity.vo.CostCashPlanVO;
import com.gok.pboot.pms.cost.entity.vo.CostManageEstimationResultsVO;
import com.gok.pboot.pms.cost.entity.vo.CostManageVersionVO;
import com.gok.pboot.pms.cost.enums.CostBudgetTypeEnum;
import com.gok.pboot.pms.cost.enums.CostManageVersionEnum;
import com.gok.pboot.pms.cost.mapper.CostBaselineVersionRecordMapper;
import com.gok.pboot.pms.cost.mapper.CostCashPlanMapper;
import com.gok.pboot.pms.cost.mapper.CostManageEstimationResultsMapper;
import com.gok.pboot.pms.cost.mapper.CostManageVersionMapper;
import com.gok.pboot.pms.cost.service.ICostCashPlanService;
import com.gok.pboot.pms.entity.domain.ContractLedger;
import com.gok.pboot.pms.entity.domain.ProjectBusinessMilestones;
import com.gok.pboot.pms.enumeration.*;
import com.gok.pboot.pms.eval.entity.domain.EvalCustomerSatisfactionSurvey;
import com.gok.pboot.pms.eval.entity.domain.EvalProjectDetail;
import com.gok.pboot.pms.eval.entity.domain.EvalProjectOverview;
import com.gok.pboot.pms.eval.entity.domain.EvalUserRole;
import com.gok.pboot.pms.eval.entity.dto.EvalProjectOverviewDTO;
import com.gok.pboot.pms.eval.entity.vo.EvalProjectDetailVO;
import com.gok.pboot.pms.eval.entity.vo.EvalProjectManagerVO;
import com.gok.pboot.pms.eval.entity.vo.EvalProjectOverviewVO;
import com.gok.pboot.pms.eval.entity.vo.EvalUserRoleVO;
import com.gok.pboot.pms.eval.enums.*;
import com.gok.pboot.pms.eval.mapper.EvalCustomerSatisfactionSurveyMapper;
import com.gok.pboot.pms.eval.mapper.EvalProjectOverviewMapper;
import com.gok.pboot.pms.eval.service.IEvalProjectDetailService;
import com.gok.pboot.pms.eval.service.IEvalProjectManagerService;
import com.gok.pboot.pms.eval.service.IEvalProjectOverviewService;
import com.gok.pboot.pms.eval.service.IEvalUserRoleService;
import com.gok.pboot.pms.handler.perm.PmsRetriever;
import com.gok.pboot.pms.mapper.ContractLedgerMapper;
import com.gok.pboot.pms.mapper.ProjectBusinessMilestonesMapper;
import com.google.common.base.Charsets;
import com.google.common.collect.ImmutableMap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.gok.pboot.pms.cost.service.impl.CostManageEstimationResultsServiceImpl.getCostManageStatusByCurrentNodeType;
import static java.util.stream.Collectors.toList;

/**
 * 项目经理评价表服务实现类
 *
 * <AUTHOR>
 * @create 2025/05/08
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class EvalProjectOverviewServiceImpl extends ServiceImpl<EvalProjectOverviewMapper, EvalProjectOverview> implements IEvalProjectOverviewService {

    private final CostCashPlanMapper costCashPlanMapper;
    private final ContractLedgerMapper contractLedgerMapper;
    private final CostManageVersionMapper costManageVersionMapper;
    private final ProjectBusinessMilestonesMapper projectBusinessMilestonesMapper;
    private final CostBaselineVersionRecordMapper costBaselineVersionRecordMapper;
    private final CostManageEstimationResultsMapper costManageEstimationResultsMapper;
    private final EvalCustomerSatisfactionSurveyMapper evalCustomerSatisfactionSurveyMapper;

    private final SysFileService sysFileService;
    private final ICostCashPlanService costCashPlanService;
    private final IEvalUserRoleService evalUserRoleService;
    private final IEvalProjectDetailService evalProjectDetailService;
    private final IEvalProjectManagerService evalProjectManagerService;


    private final DbApiUtil dbApiUtil;
    private final PmsRetriever pmsRetriever;
    private final RemoteSendMsgService remoteSendMsgService;

    @Value("${pushMessage.clientId}")
    private Long clientId;
    @Value("${pushMessage.redirectPrefixUrl}")
    private String redirectPrefix;
    @Value("${pushMessage.evalProjectOverviewUrlRedirect}")
    private String evalProjectOverviewUrlRedirect;

    private static final String SCORE_EVAL_PROJECT_OVERVIEW_TITLE = "项目未评价提醒";
    private static final String ARCHIVE_EVAL_PROJECT_OVERVIEW_TITLE = "项目评价抄送提醒";
    private static final String CALIBRATE_EVAL_PROJECT_OVERVIEW_TITLE = "工单评价校准提醒";

    @Override
    @Transactional
    public void autoSaveProjectOverviewEval() {
        List<EvalProjectOverviewVO> unSaveProjectInfoList = baseMapper.getUnSaveProjectInfoList();
        if (CollUtil.isEmpty(unSaveProjectInfoList)) {
            log.info("所有已结项项目均已创建项目整体评价，自动创建任务结束!");
            return;
        }

        // 避免数据量过大，分批处理
        List<List<EvalProjectOverviewVO>> unSaveProjectInfoListSplit = CollUtil.split(unSaveProjectInfoList, 50);
        for (List<EvalProjectOverviewVO> evalProjectOverviewVOList : unSaveProjectInfoListSplit) {
            List<Long> projectIds = evalProjectOverviewVOList.stream().map(EvalProjectOverviewVO::getProjectId).collect(Collectors.toList());

            // 获取项目对应的合同数据
            Map<Long, ContractLedger> contractMap =
                    CollUtil.emptyIfNull(contractLedgerMapper.selEvalProjectData(projectIds)).stream()
                            .collect(Collectors.toMap(ContractLedger::getXmmc, Function.identity(), (v1, v2) -> v1));

            // 获取项目对应的里程碑数据
            Map<Long, ProjectBusinessMilestones> businessMilestonesMap =
                    CollUtil.emptyIfNull(projectBusinessMilestonesMapper.selEvalProjectData(projectIds)).stream()
                            .collect(Collectors.toMap(ProjectBusinessMilestones::getProjectId, Function.identity(), (v1, v2) -> v1));

            List<EvalProjectOverview> saveEntries = new ArrayList<>(evalProjectOverviewVOList.size());
            evalProjectOverviewVOList.forEach(e -> {
                Long projectId = e.getProjectId();
                ContractLedger contractLedger = contractMap.getOrDefault(projectId, new ContractLedger());
                LocalDate expectedStartDate = StrUtil.isNotBlank(contractLedger.getSjhtqdrq()) ? LocalDate.parse(contractLedger.getSjhtqdrq()) : null;
                ProjectBusinessMilestones businessMilestone = businessMilestonesMap.getOrDefault(projectId, new ProjectBusinessMilestones());

                // 构造实体类
                EvalProjectOverview saveEntry = EvalProjectOverview.builder()
                        .projectId(projectId)
                        .projectName(e.getProjectName())
                        .projectNo(e.getProjectNo())
                        .deliverType(e.getDeliverType())
                        .planCompletionCycle(null != expectedStartDate && null != businessMilestone.getExpectedCompleteDate()
                                ? Convert.toInt((ChronoUnit.DAYS.between(expectedStartDate, businessMilestone.getExpectedCompleteDate())))
                                : null)
                        .actualCompletionCycle(null != expectedStartDate && null != businessMilestone.getActualCompleteDate()
                                ? Convert.toInt((ChronoUnit.DAYS.between(expectedStartDate, businessMilestone.getActualCompleteDate())))
                                : null)
                        .planDeviationRate(calcPlanDeviationRate(expectedStartDate, businessMilestone.getExpectedCompleteDate(), businessMilestone.getActualCompleteDate()))
                        .evalStatus(EvalStatusEnum.EVAL.getValue())
                        .managerEvalStatus(ManagerEvalStatusEnum.UNEVALUATED.getValue())
                        .build();
                BaseBuildEntityUtil.buildInsertNoUser(saveEntry, "admin");
                saveEntries.add(saveEntry);
            });

            this.saveBatch(saveEntries);
            // 保存对应项目评价
            evalProjectDetailService.autoSaveEvalProjectDetail(saveEntries);
            // 保存对应项目经理评价
            evalProjectManagerService.autoSaveEvalProjectManager(saveEntries);
            // 批量发送评价消息提醒
            batchSendEvalScoreMsg(evalProjectOverviewVOList);

            saveEntries.clear();
        }
    }

    @Override
    public EvalProjectOverviewVO findEvalProjectOverviewByProjectId(Long projectId) {

        getChangeCountsMap(Arrays.asList(projectId));

        // 获取项目评价基本信息
        EvalProjectOverviewDTO query = EvalProjectOverviewDTO.builder().projectIds(Arrays.asList(projectId)).build();
        List<EvalProjectOverviewVO> overviewVOList = baseMapper.findList(query);
        if (CollUtil.isEmpty(overviewVOList)) {
            return new EvalProjectOverviewVO();
        }

        // 表扬信文件处理
        EvalProjectOverviewVO evalProjectOverviewVO = overviewVOList.get(0);
        if (StrUtil.isNotBlank(evalProjectOverviewVO.getCommendationLetter())) {
            List<String> fileIds = Arrays.asList(evalProjectOverviewVO.getCommendationLetter().split(","));
            List<SysFile> fileList = sysFileService.listByIds(fileIds);
            evalProjectOverviewVO.setCommendationLetterList(fileList);
        }

        // 批量处理项目评价基本信息
        batchHandleEvalProjectOverviewVO(overviewVOList);
        // 批量处理项目评价信息
        Map<Long, List<EvalProjectDetailVO>> detailMap = evalProjectDetailService.findEvalProjectDetailVOList(overviewVOList);
        // 批量处理项目经理评价信息
        Map<Long, List<EvalProjectManagerVO>> managerMap = evalProjectManagerService.findEvalProjectManagerVOList(overviewVOList);

        calculateIndexScores(overviewVOList, detailMap, managerMap, false);

        return overviewVOList.get(0);
    }

    @Override
    public Page<EvalProjectOverviewVO> findEvalProjectOverviewList(PageRequest pageRequest, EvalProjectOverviewDTO request) {
        // 数据权限处理
        Long currentUserId = SecurityUtils.getUser().getId();
        List<EvalUserRoleVO> evalUserRoleVOList = CollUtil.emptyIfNull(evalUserRoleService.findAll());
        List<Long> allRoleIds = evalUserRoleVOList.stream()
                .map(EvalUserRoleVO::getUserId).distinct().collect(Collectors.toList());
        if (!CollUtil.contains(allRoleIds, currentUserId)) {
            List<Long> projectIdsAvailable = pmsRetriever.getProjectIdsAvailable();
            request.setProjectIds(projectIdsAvailable);
        }

        // 获取项目评价基本信息
        List<EvalProjectOverviewVO> overviewVOList = baseMapper.findList(request);
        if (CollUtil.isEmpty(overviewVOList)) {
            return PageUtils.page(ListUtil.empty(), pageRequest);
        }

        // 批量处理项目评价基本信息
        batchHandleEvalProjectOverviewVO(overviewVOList);
        // 批量处理项目评价信息
        Map<Long, List<EvalProjectDetailVO>> detailMap = evalProjectDetailService.findEvalProjectDetailVOList(overviewVOList);
        // 批量处理项目经理评价信息
        Map<Long, List<EvalProjectManagerVO>> managerMap = evalProjectManagerService.findEvalProjectManagerVOList(overviewVOList);

        // 计算各项指标得分并获取总得分
        calculateIndexScores(overviewVOList, detailMap, managerMap, true);

        // 获取所有PMO角色的用户ID
        List<Long> pmoUserIds = evalUserRoleVOList.stream()
                .filter(role -> EvalUserRoleEnum.PMO.getValue().equals(role.getRole()))
                .map(EvalUserRoleVO::getUserId)
                .collect(Collectors.toList());

        // 根据评价状态和创建时间排序
        overviewVOList.sort((a, b) -> {
            // 判断项目a是否可评价
            boolean aCanEval = false;
            if (currentUserId.equals(a.getSupportManagerId())) {
                // 当前用户是总支撑官/职能领导
                aCanEval = !ManagerEvalStatusEnum.MANAGER.getValue().equals(a.getManagerEvalStatus())
                        && !ManagerEvalStatusEnum.ALL.getValue().equals(a.getManagerEvalStatus());
            } else if (pmoUserIds.contains(currentUserId)) {
                // 当前用户是PMO
                aCanEval = !ManagerEvalStatusEnum.PMO.getValue().equals(a.getManagerEvalStatus())
                        && !ManagerEvalStatusEnum.ALL.getValue().equals(a.getManagerEvalStatus());
            }

            // 判断项目b是否可评价
            boolean bCanEval = false;
            if (currentUserId.equals(b.getSupportManagerId())) {
                // 当前用户是总支撑官/职能领导
                bCanEval = !ManagerEvalStatusEnum.MANAGER.getValue().equals(b.getManagerEvalStatus())
                        && !ManagerEvalStatusEnum.ALL.getValue().equals(b.getManagerEvalStatus());
            } else if (pmoUserIds.contains(currentUserId)) {
                // 当前用户是PMO
                bCanEval = !ManagerEvalStatusEnum.PMO.getValue().equals(b.getManagerEvalStatus())
                        && !ManagerEvalStatusEnum.ALL.getValue().equals(b.getManagerEvalStatus());
            }

            // 优先按是否可评价排序
            if (aCanEval != bCanEval) {
                return aCanEval ? -1 : 1;
            }

            // 如果评价状态相同，则按创建时间倒序排序
            return b.getCtime().compareTo(a.getCtime());
        });

        return PageUtils.page(overviewVOList, pageRequest);
    }

    /**
     * 计算各项指标得分并获取总得分
     *
     * @param overviewVOList  项目整体评价集合
     * @param detailMap       项目评价集合
     * @param managerMap      项目经理评价集合
     * @param clearDetailList 是否清空明细集合  true-清空
     */
    private void calculateIndexScores(List<EvalProjectOverviewVO> overviewVOList,
                                      Map<Long, List<EvalProjectDetailVO>> detailMap,
                                      Map<Long, List<EvalProjectManagerVO>> managerMap,
                                      boolean clearDetailList) {
        // 计算各指标类型的总权重
        Map<EvalIndexTypeEnum, BigDecimal> indexTypeWeightMap = Arrays.stream(AssessmentProjectEnum.values())
                .collect(Collectors.groupingBy(
                        AssessmentProjectEnum::getEvalIndexType,
                        Collectors.reducing(BigDecimal.ZERO, AssessmentProjectEnum::getWeight, BigDecimal::add)
                ));

        for (EvalProjectOverviewVO vo : overviewVOList) {
            List<EvalProjectDetailVO> details = detailMap.get(vo.getId());
            List<EvalProjectManagerVO> managers = managerMap.get(vo.getId());

            // 计算项目评价各指标得分集合
            if (CollUtil.isNotEmpty(details)) {
                Map<Integer, List<BigDecimal>> indexScoresMap = details.stream()
                        .collect(Collectors.groupingBy(EvalProjectDetailVO::getIndexType,
                                Collectors.mapping(EvalProjectDetailVO::getScore, Collectors.toList())));

                // 计算各指标平均分
                BigDecimal costIndexScore = calculateAverageScore(indexScoresMap.get(EvalIndexTypeEnum.COST_INDEX.getValue()));
                BigDecimal processIndexScore = calculateAverageScore(indexScoresMap.get(EvalIndexTypeEnum.PROCESS_INDEX.getValue()));
                BigDecimal qualityIndexScore = calculateAverageScore(indexScoresMap.get(EvalIndexTypeEnum.QUALITY_INDEX.getValue()));

                vo.setCostIndexScore(costIndexScore);
                vo.setProcessIndexScore(processIndexScore);
                vo.setQualityIndexScore(qualityIndexScore);

                // 计算项目总得分 = 成本指标*权重 + 进度指标*权重 + 质量指标*权重
                BigDecimal totalScore = costIndexScore.multiply(indexTypeWeightMap.get(EvalIndexTypeEnum.COST_INDEX))
                        .add(processIndexScore.multiply(indexTypeWeightMap.get(EvalIndexTypeEnum.PROCESS_INDEX)))
                        .add(qualityIndexScore.multiply(indexTypeWeightMap.get(EvalIndexTypeEnum.QUALITY_INDEX)));
                vo.setTotalScore(totalScore.setScale(2, RoundingMode.HALF_UP));
            }

            // 计算项目经理评价各指标得分
            if (CollUtil.isNotEmpty(managers)) {
                Map<Integer, List<BigDecimal>> indexScoresMap = managers.stream()
                        .collect(Collectors.groupingBy(EvalProjectManagerVO::getIndexType,
                                Collectors.mapping(EvalProjectManagerVO::getScore, Collectors.toList())));

                // 计算各指标平均分
                BigDecimal projectWorkScore = calculateAverageScore(indexScoresMap.get(EvalIndexTypeEnum.PROJECT_WORK.getValue()));
                BigDecimal professionalBehaviorScore = calculateAverageScore(indexScoresMap.get(EvalIndexTypeEnum.PROFESSIONAL_BEHAVIOR.getValue()));

                vo.setProjectWorkScore(projectWorkScore);
                vo.setProfessionalBehaviorScore(professionalBehaviorScore);

                // 计算项目经理得分 = 项目工作*权重 + 职业行为*权重
                BigDecimal managerScore = projectWorkScore.multiply(indexTypeWeightMap.get(EvalIndexTypeEnum.PROJECT_WORK))
                        .add(professionalBehaviorScore.multiply(indexTypeWeightMap.get(EvalIndexTypeEnum.PROFESSIONAL_BEHAVIOR)));
                vo.setManagerScore(managerScore.setScale(2, RoundingMode.HALF_UP));
            }

            // 计算项目评价系数
            if (vo.getTotalScore() != null) {
                if (vo.getTotalScore().compareTo(new BigDecimal("5")) == 0) {
                    vo.setEvaluationCoefficient(new BigDecimal("1.2"));
                } else if (vo.getTotalScore().compareTo(new BigDecimal("3")) >= 0) {
                    vo.setEvaluationCoefficient(new BigDecimal("1.0")); // 1.0
                } else if (vo.getTotalScore().compareTo(new BigDecimal("1")) >= 0) {
                    vo.setEvaluationCoefficient(new BigDecimal("0.8"));  // 0.8
                } else {
                    vo.setEvaluationCoefficient(BigDecimal.ZERO);   // 0
                }
            }

            // 设置项目等级
            EvalGradeEnum projectGrade = EvalGradeEnum.getGradeByScore(vo.getTotalScore());
            if (projectGrade != null) {
                vo.setProjectGrade(projectGrade.getValue());
                vo.setProjectGradeName(projectGrade.getName());
            }

            // 设置项目经理得分等级
            EvalGradeEnum managerGrade = EvalGradeEnum.getGradeByScore(vo.getManagerScore());
            if (managerGrade != null) {
                vo.setManagerGrade(managerGrade.getValue());
                vo.setManagerGradeName(managerGrade.getName());
            }

            if (clearDetailList) {
                // 清空详情列表，避免响应体过大
                vo.setEvalProjectDetailList(null);
                vo.setEvalProjectManagerList(null);
            }
        }
    }

    /**
     * 计算平均分
     */
    private BigDecimal calculateAverageScore(List<BigDecimal> scores) {
        if (CollUtil.isEmpty(scores)) {
            return BigDecimal.ZERO;
        }
        return scores.stream()
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .divide(new BigDecimal(scores.size()), 2, RoundingMode.HALF_UP);
    }

    /**
     * 批量处理项目评价基本信息
     *
     * @param sourceVOList 项目评价基本信息集合
     */
    private void batchHandleEvalProjectOverviewVO(List<EvalProjectOverviewVO> sourceVOList) {
        if (CollUtil.isEmpty(sourceVOList)) {
            return;
        }
        List<Long> projectIds = sourceVOList.stream().map(EvalProjectOverviewVO::getProjectId).collect(Collectors.toList());
        // 获取客户评价得分集合
        Map<Long, BigDecimal> customerEvalScoreMap = getCustomerEvalScoreMap(projectIds);

        // 获取项目所有的B表成本
        Map<Long, BigDecimal> budgetCostMap = getBbcbCostManageVersionMap(projectIds);

        // 统计项目变更次数（非客户原因）
        Map<Long, Integer> changeCountsMap = getChangeCountsMap(projectIds);

        sourceVOList.forEach(vo -> {
            vo.setDeliverTypeName(EnumUtils.getNameByValue(DeliverTypeEnum.class, vo.getDeliverType()));
            vo.setProjectTypeName(EnumUtils.getNameByValue(ProjectTypeEnum.class, vo.getProjectType()));
            vo.setEvalStatusName(EnumUtils.getNameByValue(EvalStatusEnum.class, vo.getEvalStatus()));
            vo.setManagerEvalStatusName(EnumUtils.getNameByValue(ManagerEvalStatusEnum.class, vo.getManagerEvalStatus()));
            vo.setCustomerEvalScore(customerEvalScoreMap.get(vo.getProjectId()));
            vo.setProjectStatusName(EnumUtils.getNameByValue(ProjectStatusEnum.class, vo.getProjectStatus()));
            vo.setChangeCount(changeCountsMap.get(vo.getProjectId()));

            // 计算成本相关
            if (!EvalStatusEnum.ARCHIVE.getValue().equals(vo.getEvalStatus())) {
                BigDecimal budgetCost = budgetCostMap.get(vo.getProjectId());
                BigDecimal actualCost = vo.getActualCost();
                vo.setBudgetCost(budgetCost);
                boolean calcCostDeviationRateFlag = budgetCost != null && BigDecimal.ZERO.compareTo(budgetCost) != 0 && null != actualCost;
                BigDecimal costDeviationRate = calcCostDeviationRateFlag
                        ? actualCost.subtract(budgetCost).divide(budgetCost, 4, RoundingMode.HALF_UP)
                        : null;
                vo.setCostDeviationRate(costDeviationRate);
            }
        });

    }

    /**
     * 获取项目变更次数集合
     *
     * @param projectIds 项目ID集合
     * @return
     */
    private Map<Long, Integer> getChangeCountsMap(List<Long> projectIds) {
        if (CollUtil.isEmpty(projectIds)) {
            return ImmutableMap.of();
        }
        List<CostBaselineVersionRecord> costBaselineVersionList =
                CollUtil.emptyIfNull(costBaselineVersionRecordMapper.getCostChangeByProjectIds(projectIds));
        List<Long> requestIds = costBaselineVersionList.stream()
                .map(CostBaselineVersionRecord::getRequestId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, Long> bglxMap = new HashMap<>(requestIds.size());
        if (CollUtil.isNotEmpty(requestIds)) {
            JSONArray xmbgByRequestIds = dbApiUtil.getXMBGByRequestIds(requestIds);
            if (!xmbgByRequestIds.isEmpty()) {
                xmbgByRequestIds.forEach(item -> {
                    JSONObject jsonObject = (JSONObject) item;
                    bglxMap.put(jsonObject.getLong("REQUESTID"), jsonObject.getLong("BGLX"));
                });
            }
        }
        return costBaselineVersionList.stream()
                .filter(e -> e.getRequestId() == null || bglxMap.get(e.getRequestId()) == null || bglxMap.get(e.getRequestId()) != 1)
                .collect(Collectors.groupingBy(CostBaselineVersionRecord::getProjectId, Collectors.reducing(0, (e) -> 1, Integer::sum)));
    }

    /**
     * 获取项目对应预算成本集合
     *
     * @param projectIds
     * @return
     */
    private Map<Long, BigDecimal> getBbcbCostManageVersionMap(List<Long> projectIds) {
        if (CollUtil.isEmpty(projectIds)) {
            return Collections.emptyMap();
        }
        // 获取项目所有的B表成本
        LambdaQueryWrapper<CostManageVersion> costManageVersionQuery = Wrappers.<CostManageVersion>lambdaQuery()
                .in(CostManageVersion::getProjectId, projectIds)
                .eq(CostManageVersion::getVersionType, CostManageVersionEnum.CBGL.getValue())
                .eq(CostManageVersion::getCostBudgetType, CostBudgetTypeEnum.BBCB.getValue())
                .eq(CostManageVersion::getDelFlag, 0);
        List<CostManageVersion> costManageVersionList = CollUtil.emptyIfNull(costManageVersionMapper.selectList(costManageVersionQuery));
        Map<Long, CostManageStatusEnum> statusEnumMap = dbApiUtil.getOaRequestStatusToObj(costManageVersionList.stream()
                        .map(CostManageVersion::getRequestId).collect(toList()), CostManageVersionVO.class).stream()
                .collect(Collectors.toMap(CostManageVersionVO::getRequestId, e -> getCostManageStatusByCurrentNodeType(e.getRequestStatus()), (a, b) -> a));
        Map<Long, CostManageVersion> costManageVersionMap = costManageVersionList.stream()
                .filter(v -> CostManageStatusEnum.CONFIRMED.equals(statusEnumMap.get(v.getRequestId()))) // 先过滤出 CONFIRMED 状态的记录
                .collect(Collectors.groupingBy(
                        CostManageVersion::getProjectId, // 按 projectId 分组
                        Collectors.minBy(Comparator.comparing(CostManageVersion::getCtime)) // 在每组中取最早的一条（根据创建时间）
                )).entrySet().stream()
                .filter(e -> e.getValue().isPresent()) // 过滤掉 value 为空的项
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().get()
                ));

        // 获取对应的成本估算结果明细数据
        List<Long> versionIds = costManageVersionMap.values().stream()
                .map(CostManageVersion::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<Long, List<CostManageEstimationResultsVO>> resultsMap = CollUtil.emptyIfNull(costManageEstimationResultsMapper.findByVersionId(versionIds)).stream()
                .collect(Collectors.groupingBy(CostManageEstimationResultsVO::getVersionId));
        if (CollUtil.isEmpty(resultsMap)) {
            return Collections.emptyMap();
        }

        // 获取现金流数据
        List<Long> cashPlanVersionIds = costManageVersionList.stream().map(CostManageVersion::getCashPlanVersionId).collect(toList());
        List<CostCashPlanVO> costCashPlanVersions = CollUtil.isNotEmpty(cashPlanVersionIds)
                ? costCashPlanMapper.getCostCashPlanList(cashPlanVersionIds)
                : ListUtil.empty();
        List<CostCashPlanVO> handleCostCashPlanVOList = costCashPlanService.getCostCashPlanList(costCashPlanVersions);

        Map<Long, BigDecimal> resultMap = new HashMap<>();
        for (Map.Entry<Long, List<CostManageEstimationResultsVO>> entry : resultsMap.entrySet()) {
            Long versionId = entry.getKey();
            List<CostManageEstimationResultsVO> results = entry.getValue();
            CostManageVersion currentVersion = costManageVersionList.stream().filter(v -> v.getId().equals(versionId)).findAny().orElse(null);
            if (null == currentVersion) {
                continue;
            }

            BigDecimal budgetCost = results.stream()
                    .map(CostManageEstimationResultsVO::getBudgetAmountExcludingTax)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 加入垫资成本
            BigDecimal cashFlowInterest = handleCostCashPlanVOList.stream()
                    .filter(v -> v.getVersionId().equals(currentVersion.getCashPlanVersionId()))
                    .map(CostCashPlanVO::getCashFlowInterest)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal budgetAmountIncludedTax = cashFlowInterest.compareTo(BigDecimal.ZERO) >= 0
                    ? BigDecimal.ZERO
                    : cashFlowInterest.abs();
            budgetCost.add(budgetAmountIncludedTax);
            resultMap.put(currentVersion.getProjectId(), budgetCost);
        }

        return resultMap;
    }

    /**
     * 计算计划偏差率
     *
     * @param expectedStartDate    计划开始日期
     * @param expectedCompleteDate 计划完成日期
     * @param actualCompleteDate   实际完成日期
     * @return 计划偏差率
     */
    private BigDecimal calcPlanDeviationRate(LocalDate expectedStartDate, LocalDate expectedCompleteDate, LocalDate actualCompleteDate) {
        if (null == expectedStartDate || null == expectedCompleteDate || null == actualCompleteDate) {
            return null;
        }
        // 实际完成日期-计划开始日期
        int actualCompletionCycle = Convert.toInt((ChronoUnit.DAYS.between(expectedStartDate, actualCompleteDate)));
        // 计划完成时间-计划开始时间
        int planCompletionCycle = Convert.toInt((ChronoUnit.DAYS.between(expectedStartDate, expectedCompleteDate)));
        BigDecimal planDeviationRate = BigDecimal.valueOf(actualCompletionCycle - planCompletionCycle)
                .divide(BigDecimal.valueOf(planCompletionCycle), 4, BigDecimal.ROUND_HALF_UP);

        return planDeviationRate;
    }

    @Override
    public List<Long> findUnevaluatedProjectIds(EvalProjectOverviewDTO request) {
        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUser().getId();

        // 获取所有用户角色
        List<EvalUserRoleVO> allRoles = CollUtil.emptyIfNull(evalUserRoleService.findAll());

        // 判断当前用户是否是PMO
        boolean isPmo = allRoles.stream()
                .anyMatch(role -> role.getUserId().equals(currentUserId)
                        && EvalUserRoleEnum.PMO.getValue().equals(role.getRole()));

        // 获取当前用户的项目评价列表
        Page<EvalProjectOverviewVO> page = findEvalProjectOverviewList(new PageRequest(1, Integer.MAX_VALUE), request);
        List<EvalProjectOverviewVO> overviewList = page.getRecords();
        if (CollUtil.isEmpty(overviewList)) {
            return ListUtil.empty();
        }

        // 根据用户角色筛选未评价的项目
        return overviewList.stream()
                .filter(overview -> {
                    // 默认为已评价
                    // 已归档的项目不需要评价
                    if (EvalStatusEnum.ARCHIVE.getValue().equals(overview.getEvalStatus())) {
                        return false;
                    }

                    if (isPmo) {
                        // PMO未评价的条件：managerEvalStatus不是PMO或ALL
                        return !ManagerEvalStatusEnum.PMO.getValue().equals(overview.getManagerEvalStatus())
                                && !ManagerEvalStatusEnum.ALL.getValue().equals(overview.getManagerEvalStatus());
                    } else {
                        // 总支撑官/职能上级未评价的条件：
                        // 1. 是该项目的总支撑官/职能上级
                        // 2. managerEvalStatus不是MANAGER或ALL
                        return currentUserId.equals(overview.getSupportManagerId())
                                && !ManagerEvalStatusEnum.MANAGER.getValue().equals(overview.getManagerEvalStatus())
                                && !ManagerEvalStatusEnum.ALL.getValue().equals(overview.getManagerEvalStatus());
                    }
                })
                .map(EvalProjectOverviewVO::getProjectId)
                .collect(Collectors.toList());
    }

    @Override
    public List<EvalProjectOverviewVO> export(EvalProjectOverviewDTO request) {
        Page<EvalProjectOverviewVO> page = findEvalProjectOverviewList(new PageRequest(1, Integer.MAX_VALUE), request);
        List<EvalProjectOverviewVO> overviewList = page.getRecords();
        if (CollUtil.isEmpty(overviewList)) {
            return ListUtil.empty();
        }
        overviewList.forEach(v -> {
            v.setPlanDeviationRateStr(covertRateToString(v.getPlanDeviationRate()));
            v.setCostDeviationRateStr(covertRateToString(v.getCostDeviationRate()));
        });
        return overviewList;
    }

    @Override
    @Transactional
    public Long archiveEval(Long projectId) {
        EvalProjectOverview evalProjectOverview = baseMapper.getByProjectId(projectId);
        if (null == evalProjectOverview || EvalStatusEnum.ARCHIVE.getValue().equals(evalProjectOverview.getEvalStatus())) {
            return null;
        }

        evalProjectOverview.setEvalStatus(EvalStatusEnum.ARCHIVE.getValue());

        // 数据归档
        EvalProjectOverviewVO evalProjectOverviewVO = findEvalProjectOverviewByProjectId(projectId);
        evalProjectOverview.setBudgetCost(evalProjectOverviewVO.getBudgetCost());
        evalProjectOverview.setActualCost(evalProjectOverviewVO.getActualCost());
        evalProjectOverview.setCostDeviationRate(evalProjectOverviewVO.getCostDeviationRate());
        evalProjectOverview.setCustomerEvalScore(evalProjectOverviewVO.getCustomerEvalScore());
        evalProjectOverview.setChangeCount(evalProjectOverviewVO.getChangeCount());

        Map<Long, EvalProjectDetailVO> projectDetailVOMap =
                evalProjectOverviewVO.getEvalProjectDetailList().stream().collect(Collectors.toMap(EvalProjectDetailVO::getId, v -> v));
        List<EvalProjectDetail> evalProjectDetailEntities =
                evalProjectDetailService.getBaseMapper().selectBatchIds(projectDetailVOMap.keySet());
        evalProjectDetailEntities.forEach(e -> {
            e.setScore(projectDetailVOMap.get(e.getId()).getScore());
            BaseBuildEntityUtil.buildUpdate(e);
        });
        evalProjectDetailService.saveOrUpdateBatch(evalProjectDetailEntities);

        BaseBuildEntityUtil.buildUpdate(evalProjectOverview);
        baseMapper.updateById(evalProjectOverview);

        // 推送消息提醒
        batchSendArchiveEvalMsg(Arrays.asList(evalProjectOverview));

        return evalProjectOverview.getId();
    }

    /**
     * 批量发送项目评价评分消息
     *
     * @param evalProjectOverviewVOList
     * @return 发送项目评价消息提醒的项目ID集合
     */
    private List<Long> batchSendEvalScoreMsg(List<EvalProjectOverviewVO> evalProjectOverviewVOList) {
        if (CollUtil.isEmpty(evalProjectOverviewVOList)) {
            log.info("项目评价信息为空，消息发送结束");
            return ListUtil.empty();
        }

        // 获取发送对象
        LambdaQueryWrapper<EvalUserRole> roleQuery = Wrappers.<EvalUserRole>lambdaQuery()
                .eq(EvalUserRole::getDelFlag, YesOrNoEnum.NO.getValue())
                .eq(EvalUserRole::getRole, EvalUserRoleEnum.PMO.getValue());
        List<EvalUserRole> evalUserRoles = evalUserRoleService.getBaseMapper().selectList(roleQuery);
        if (CollUtil.isEmpty(evalUserRoles)) {
            log.info("发送对象未配置，消息发送结束");
            return ListUtil.empty();
        }
        List<BcpMessageTargetDTO> pmoList = evalUserRoles.stream().map(
                e -> BcpMessageTargetDTO.builder()
                        .targetId(String.valueOf(e.getUserId()))
                        .targetName(e.getUserName())
                        .build()
        ).collect(toList());


        List<WeComModel> weComModelList = new ArrayList<>(evalProjectOverviewVOList.size());
        Map<String, BcpMessageContentModel> bcMessageContentMap = new HashMap<>();
        List<BcpMessageTargetBatchDTO> bcpMessageTargetList = new ArrayList<>();
        for (EvalProjectOverviewVO eval : evalProjectOverviewVOList) {
            // 封装企微消息对象
            WeComModel weComModel = new WeComModel();
            weComModel.setSource(SourceEnum.PROJECT.getValue());
            weComModel.setType(MsgTypeEnum.TEXT_MSG.getValue());
            weComModel.setTitle(SCORE_EVAL_PROJECT_OVERVIEW_TITLE);
            weComModel.setSenderId(clientId);
            weComModel.setSender(SourceEnum.PROJECT.getName());
            weComModel.setTargetType(TargetTypeEnum.USERS.getValue());

            String redirectUrl = StrUtil.format(evalProjectOverviewUrlRedirect, eval.getProjectId());
            weComModel.setRedirectUrl(redirectPrefix + Base64.encode(redirectUrl, Charsets.UTF_8));

            List<BcpMessageTargetDTO> targetList = new ArrayList<>();
            CollUtil.addAll(targetList, pmoList);
            targetList.add(BcpMessageTargetDTO.builder()
                    .targetId(String.valueOf(eval.getSupportManagerId()))
                    .targetName(eval.getSupportManagerName())
                    .build());
            List<BcpMessageTargetDTO> distinctTargetList = targetList.stream()
                    .filter(Objects::nonNull) // 防止空值
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(
                                    BcpMessageTargetDTO::getTargetId, // 按 targetId 去重
                                    dto -> dto,
                                    (existing, replacement) -> existing // 如果重复，保留第一个
                            ),
                            map -> new ArrayList<>(map.values())
                    ));
            weComModel.setTargetList(distinctTargetList);
            String content = StrUtil.format("您好，{}项目已结项，需要您进行项目评价，请及时评价～", eval.getProjectName());
            weComModel.setContent(content + "\n<a href=\"" + weComModel.getRedirectUrl() + "\">" + "查看详情</a>");
            weComModelList.add(weComModel);

            // 封装门户消息对象
            String contentId = UUID.randomUUID().toString();
            BcpMessageContentModel bcpMessageContentModel = new BcpMessageContentModel();
            bcpMessageContentModel.setTitle(weComModel.getTitle());
            bcpMessageContentModel.setContent(content);
            bcpMessageContentModel.setRedirectUrl(weComModel.getRedirectUrl());
            bcpMessageContentModel.setType(MsgTypeEnum.TEXT_MSG.getValue());
            bcMessageContentMap.put(contentId, bcpMessageContentModel);

            targetList.forEach(e -> {
                BcpMessageTargetBatchDTO bcpMessageTargetBatchDTO = new BcpMessageTargetBatchDTO();
                bcpMessageTargetBatchDTO.setContentId(contentId);
                bcpMessageTargetBatchDTO.setTargetId(e.getTargetId());
                bcpMessageTargetBatchDTO.setTargetName(e.getTargetName());
                bcpMessageTargetList.add(bcpMessageTargetBatchDTO);
            });
        }

        batchSendMessage(weComModelList, bcMessageContentMap, bcpMessageTargetList);

        return evalProjectOverviewVOList.stream().map(EvalProjectOverviewVO::getProjectId).collect(toList());
    }


    /**
     * 批量发送归档评价消息提醒
     *
     * @param evalProjectOverviewList 项目评价数据集合
     * @return 项目评价ID集合
     */
    private List<Long> batchSendArchiveEvalMsg(List<EvalProjectOverview> evalProjectOverviewList) {
        if (CollUtil.isEmpty(evalProjectOverviewList)) {
            log.info("项目评价信息为空，消息发送结束");
            return ListUtil.empty();
        }

        // 获取发送对象
        LambdaQueryWrapper<EvalUserRole> roleQuery = Wrappers.<EvalUserRole>lambdaQuery()
                .eq(EvalUserRole::getDelFlag, YesOrNoEnum.NO.getValue())
                .in(EvalUserRole::getRole, Arrays.asList(EvalUserRoleEnum.CUSTOMER_MARKET_LEADER.getValue(), EvalUserRoleEnum.SHARED_OPERATION_LEADER.getValue()));
        List<EvalUserRole> evalUserRoles = evalUserRoleService.getBaseMapper().selectList(roleQuery);
        if (CollUtil.isEmpty(evalUserRoles)) {
            log.info("发送对象未配置，消息发送结束");
            return ListUtil.empty();
        }
        List<BcpMessageTargetDTO> targetList = evalUserRoles.stream().map(
                e -> BcpMessageTargetDTO.builder()
                        .targetId(String.valueOf(e.getUserId()))
                        .targetName(e.getUserName())
                        .build()
        ).collect(toList());

        List<WeComModel> weComModelList = new ArrayList<>(evalProjectOverviewList.size());
        BcpMessageBatchDTO bcpMessageDto = new BcpMessageBatchDTO();
        Map<String, BcpMessageContentModel> bcMessageContentMap = new HashMap<>();
        List<BcpMessageTargetBatchDTO> bcpMessageTargetList = new ArrayList<>();
        for (EvalProjectOverview eval : evalProjectOverviewList) {
            // 封装企微消息对象
            WeComModel weComModel = new WeComModel();
            weComModel.setSource(SourceEnum.PROJECT.getValue());
            weComModel.setType(MsgTypeEnum.TEXT_MSG.getValue());
            weComModel.setTitle(ARCHIVE_EVAL_PROJECT_OVERVIEW_TITLE);
            weComModel.setSenderId(clientId);
            weComModel.setSender(SourceEnum.PROJECT.getName());
            weComModel.setTargetType(TargetTypeEnum.USERS.getValue());

            String redirectUrl = StrUtil.format(evalProjectOverviewUrlRedirect, eval.getProjectId());
            weComModel.setRedirectUrl(redirectPrefix + Base64.encode(redirectUrl, Charsets.UTF_8));

            weComModel.setTargetList(targetList);
            String content = StrUtil.format("您好，{}项目的项目评价已完成，请前往查看～", eval.getProjectName());
            weComModel.setContent(content + "\n<a href=\"" + weComModel.getRedirectUrl() + "\">" + "查看详情</a>");
            weComModelList.add(weComModel);

            // 封装门户消息对象
            String contentId = UUID.randomUUID().toString();
            BcpMessageContentModel bcpMessageContentModel = new BcpMessageContentModel();
            bcpMessageContentModel.setTitle(weComModel.getTitle());
            bcpMessageContentModel.setContent(content);
            bcpMessageContentModel.setRedirectUrl(weComModel.getRedirectUrl());
            bcpMessageContentModel.setType(MsgTypeEnum.TEXT_MSG.getValue());
            bcMessageContentMap.put(contentId, bcpMessageContentModel);

            evalUserRoles.forEach(e -> {
                BcpMessageTargetBatchDTO bcpMessageTargetBatchDTO = new BcpMessageTargetBatchDTO();
                bcpMessageTargetBatchDTO.setContentId(contentId);
                bcpMessageTargetBatchDTO.setTargetId(String.valueOf(e.getUserId()));
                bcpMessageTargetBatchDTO.setTargetName(e.getUserName());
                bcpMessageTargetList.add(bcpMessageTargetBatchDTO);
            });
        }
        batchSendMessage(weComModelList, bcMessageContentMap, bcpMessageTargetList);

        return evalProjectOverviewList.stream().map(EvalProjectOverview::getId).collect(toList());
    }

    /**
     * 批量发送企微和门户消息
     *
     * @param weComModelList       企微消息列表
     * @param bcMessageContentMap  门户消息内容映射
     * @param bcpMessageTargetList 门户消息目标列表
     */
    private void batchSendMessage(List<WeComModel> weComModelList,
                                  Map<String, BcpMessageContentModel> bcMessageContentMap,
                                  List<BcpMessageTargetBatchDTO> bcpMessageTargetList) {
        // 批量发送企微消息
        WeComBatchModel weComBatchModel = new WeComBatchModel();
        weComBatchModel.setData(weComModelList);
        remoteSendMsgService.sendWeComMsgBatch(SecurityConstants.FROM_IN, weComBatchModel);

        // 发送门户消息
        BcpMessageBatchDTO bcpMessageDto = new BcpMessageBatchDTO();
        bcpMessageDto.setSource(SourceEnum.PROJECT.getValue());
        bcpMessageDto.setChannel(ChannelEnum.MAIL.getValue());
        bcpMessageDto.setSenderId(clientId);
        bcpMessageDto.setSender(SourceEnum.PROJECT.getName());
        bcpMessageDto.setTargetType(TargetTypeEnum.USERS.getValue());
        bcpMessageDto.setSendTime(DateUtil.formatTime(new Date()));
        bcpMessageDto.setContentMap(bcMessageContentMap);
        bcpMessageDto.setTargetList(bcpMessageTargetList);
        remoteSendMsgService.sendMsgBatch(SecurityConstants.FROM_IN, bcpMessageDto);
    }

    private String covertRateToString(BigDecimal rate) {
        if (null == rate) {
            return StrUtil.EMPTY;
        }
        DecimalFormat percentFormat = new DecimalFormat("#.##%");
        String result = percentFormat.format(rate.doubleValue());
        return result;
    }

    @Override
    @Transactional
    public List<Long> batchCalibrateEval(List<EvalCustomerSatisfactionSurvey> surveyList) {
        if (CollUtil.isEmpty(surveyList)) {
            return ListUtil.empty();
        }
        // 获取客户评价得分集合
        Map<Long, BigDecimal> customerEvalScoreMap = surveyList.stream()
                .collect(Collectors.toMap(EvalCustomerSatisfactionSurvey::getProjectId, EvalCustomerSatisfactionSurvey::getTotalScore, (a, b) -> a));
        Set<Long> projectIds = customerEvalScoreMap.keySet();

        LambdaQueryWrapper<EvalProjectOverview> evalProjectOverviewQuery = Wrappers.<EvalProjectOverview>lambdaQuery()
                .eq(EvalProjectOverview::getDelFlag, YesOrNoEnum.NO.getValue())
                .eq(EvalProjectOverview::getEvalStatus, EvalStatusEnum.EVAL.getValue())
                .in(EvalProjectOverview::getProjectId, projectIds);
        List<EvalProjectOverview> evalProjectOverviewList = baseMapper.selectList(evalProjectOverviewQuery);
        if (CollUtil.isEmpty(evalProjectOverviewList)) {
            return ListUtil.empty();
        }

        evalProjectOverviewList.forEach(e -> {
            e.setEvalStatus(EvalStatusEnum.CALIBRATE.getValue());
            e.setCustomerEvalScore(customerEvalScoreMap.get(e.getProjectId()));
            BaseBuildEntityUtil.buildUpdate(e);
            e.setModifier("admin");
        });
        this.updateBatchById(evalProjectOverviewList);

        // 发送项目校准消息
        batchSendEvalCalibrateMsg(evalProjectOverviewList);
        return evalProjectOverviewList.stream().map(EvalProjectOverview::getId).collect(toList());
    }

    private List<Long> batchSendEvalCalibrateMsg(List<EvalProjectOverview> evalProjectOverviewList) {
        if (CollUtil.isEmpty(evalProjectOverviewList)) {
            log.info("项目评价信息为空，消息发送结束");
            return ListUtil.empty();
        }

        List<WeComModel> weComModelList = new ArrayList<>(evalProjectOverviewList.size());
        BcpMessageBatchDTO bcpMessageDto = new BcpMessageBatchDTO();
        Map<String, BcpMessageContentModel> bcMessageContentMap = new HashMap<>();
        List<BcpMessageTargetBatchDTO> bcpMessageTargetList = new ArrayList<>();
        for (EvalProjectOverview eval : evalProjectOverviewList) {
            // 封装企微消息对象
            WeComModel weComModel = new WeComModel();
            weComModel.setSource(SourceEnum.PROJECT.getValue());
            weComModel.setType(MsgTypeEnum.TEXT_MSG.getValue());
            weComModel.setTitle(CALIBRATE_EVAL_PROJECT_OVERVIEW_TITLE);
            weComModel.setSenderId(clientId);
            weComModel.setSender(SourceEnum.PROJECT.getName());
            weComModel.setTargetType(TargetTypeEnum.USERS.getValue());

            String redirectUrl = StrUtil.format(evalProjectOverviewUrlRedirect, eval.getProjectId());
            weComModel.setRedirectUrl(redirectPrefix + Base64.encode(redirectUrl, Charsets.UTF_8));

            BcpMessageTargetDTO target = BcpMessageTargetDTO.builder()
                    .targetId(String.valueOf(eval.getManagerUserId()))
                    .targetName(eval.getManagerUserName())
                    .build();
            weComModel.setTargetList(Arrays.asList(target));
            String content = StrUtil.format("您好，{}项目整体得分已得出，需要您进行人员工单评价校准，请及时前往～", eval.getProjectName());
            weComModel.setContent(content + "\n<a href=\"" + weComModel.getRedirectUrl() + "\">" + "查看详情</a>");
            weComModelList.add(weComModel);

            // 封装门户消息对象
            String contentId = UUID.randomUUID().toString();
            BcpMessageContentModel bcpMessageContentModel = new BcpMessageContentModel();
            bcpMessageContentModel.setTitle(weComModel.getTitle());
            bcpMessageContentModel.setContent(content);
            bcpMessageContentModel.setRedirectUrl(weComModel.getRedirectUrl());
            bcpMessageContentModel.setType(MsgTypeEnum.TEXT_MSG.getValue());
            bcMessageContentMap.put(contentId, bcpMessageContentModel);

            BcpMessageTargetBatchDTO bcpMessageTargetBatchDTO = new BcpMessageTargetBatchDTO();
            bcpMessageTargetBatchDTO.setContentId(contentId);
            bcpMessageTargetBatchDTO.setTargetId(target.getTargetId());
            bcpMessageTargetBatchDTO.setTargetName(target.getTargetName());
            bcpMessageTargetList.add(bcpMessageTargetBatchDTO);
        }
        // 批量发送企微消息
        WeComBatchModel weComBatchModel = new WeComBatchModel();
        weComBatchModel.setData(weComModelList);
        remoteSendMsgService.sendWeComMsgBatch(SecurityConstants.FROM_IN, weComBatchModel);

        // 发送门户消息
        bcpMessageDto.setSource(SourceEnum.PROJECT.getValue());
        bcpMessageDto.setChannel(ChannelEnum.MAIL.getValue());
        bcpMessageDto.setSenderId(clientId);
        bcpMessageDto.setSender(SourceEnum.PROJECT.getName());
        bcpMessageDto.setTargetType(TargetTypeEnum.USERS.getValue());
        bcpMessageDto.setSendTime(DateUtil.formatTime(new Date()));
        bcpMessageDto.setContentMap(bcMessageContentMap);
        bcpMessageDto.setTargetList(bcpMessageTargetList);
        remoteSendMsgService.sendMsgBatch(SecurityConstants.FROM_IN, bcpMessageDto);

        return evalProjectOverviewList.stream().map(EvalProjectOverview::getId).collect(toList());
    }

    /**
     * 获取客户评价得分集合
     *
     * @param projectIds 项目ID集合
     * @return key-项目ID  value-用户满意度得分
     */
    private Map<Long, BigDecimal> getCustomerEvalScoreMap(List<Long> projectIds) {
        if (CollUtil.isEmpty(projectIds)) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<EvalCustomerSatisfactionSurvey> customerSurveyQuery =
                Wrappers.<EvalCustomerSatisfactionSurvey>lambdaQuery().in(EvalCustomerSatisfactionSurvey::getProjectId, projectIds);
        List<EvalCustomerSatisfactionSurvey> satisfactionSurveyList =
                CollUtil.emptyIfNull(evalCustomerSatisfactionSurveyMapper.selectList(customerSurveyQuery));
        // 获取客户评价得分集合
        Map<Long, BigDecimal> customerEvalScoreMap = satisfactionSurveyList.stream()
                .filter(e -> e.getTotalScore() != null)
                .collect(Collectors.toMap(EvalCustomerSatisfactionSurvey::getProjectId, EvalCustomerSatisfactionSurvey::getTotalScore, (a, b) -> a));
        return customerEvalScoreMap;
    }

}
