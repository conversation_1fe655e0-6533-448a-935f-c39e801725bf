package com.gok.pboot.pms.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.entity.domain.OaHrmcity;
import com.gok.pboot.pms.entity.vo.OaHrmcityVO;
import com.gok.pboot.pms.mapper.OaHrmcityMapper;
import com.gok.pboot.pms.service.IOaHrmcityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class OaHrmcityServiceImpl extends ServiceImpl<OaHrmcityMapper, OaHrmcity>
        implements IOaHrmcityService {

    @Override
    public List<OaHrmcityVO> getListByName(String name) {
        QueryWrapper<OaHrmcity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<OaHrmcity> lambda = queryWrapper.lambda();
        if(StrUtil.isNotBlank(name)){
            lambda.like(OaHrmcity::getCityname, name);
        }else{
            lambda.last("limit 50");
        }
        List<OaHrmcity> oaHrmcityList = baseMapper.selectList(queryWrapper);
        return BeanUtil.copyToList(oaHrmcityList,OaHrmcityVO.class);
    }

    /**
     * 获取所有城市列表
     *
     * @return {@link List }<{@link OaHrmcityVO }>
     */
    @Override
    public List<OaHrmcityVO> getAllCityList() {
        List<OaHrmcity> oaHrmcityList = baseMapper.selectList(null);
        return BeanUtil.copyToList(oaHrmcityList,OaHrmcityVO.class);
    }
}