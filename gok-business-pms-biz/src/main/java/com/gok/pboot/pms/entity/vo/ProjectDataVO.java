package com.gok.pboot.pms.entity.vo;

import cn.hutool.core.util.StrUtil;
import com.gok.pboot.pms.entity.domain.ProjectData;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 项目核心数据
 *
 * <AUTHOR>
 * @date 2023-07-18
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ProjectDataVO {
    /**
     * ID
     */
    private String id;

    /**
     * 预算总收入（含税）
     */
    private String totalBudgetRevenueIncludeTax;

    /**
     * 预算总成本（含税）
     */
    private String totalBudgetCostIncludeTax;

    /**
     * 实际总成本（含税）
     */
    private String actualTotalBudgetCostIncludeTax;
    /**
     * 剩余可用预算
     */
    private String remainingAvailableBudget;
    /**
     * 成本进度
     */
    private String costProgress;

    /**
     * 实际总成本（不含税）
     */
    private String actualTotalBudgetCost;

    /**
     * 预估总人天
     */
    private String estimatedTotalManDays;

    /**
     * 销售合同金额（含税）
     */
    private String contractPrice;
    /**
     * 销售合同金额(不含税合计)
     */
    private String contractPriceBhsSum;

    /**
     * 管理费用
     */
    private String managementCost;

    /**
     * 销售费用
     */
    private String salesCost;

    /**
     * 已发生人天
     */
    private String manDays;
    /**
     * 剩余可用人天
     */
    private String remainingAvailableDays;

    /**
     * 售前已发生人天
     */
    private String beforeSalesManDays;

    /**
     * 售后已发生人天
     */
    private String afterSalesManDays;

    /**
     * 学员人天
     */
    private String studentManDay;
    /**
     * 创建时间
     */
    private LocalDateTime ctime;

    /**
     * 售前人工投入
     */
    private String preSalesLaborInput;

    /**
     * 售前费用投入
     */
    private String sqfytr;

    /**
     * 内部项目预算总成本
     */
    private String internalTotalBudgetCostIncludeTax;

    /**
     * 内部项目预估总人天
     */
    private String internalEstimatedTotalManDays;

    /**
     * 内部项目人工预算
     */
    private String internalLaborBudget;

    /**
     * 内部项目外采预算(含税)
     */
    private String internalOutsourcingBudgetTax;

    /**
     * 内部项目预算变更次数
     */
    private Long internalBudgetChangesNum;
    /**
     * 回款累计
     */
    private String payBackSum;

    /**
     * 当前实际总成本_不含税
     */
    private String zyywcb;

    /**
     * 销售合同金额_不含税
     */
    private String xshtjeBhs;

    /**
     * 回款累计
     */
    private String xmhk;

    /**
     * ABCD表数据
     */
    private List<ProjectDataTableVO> projectDataTableList;

    /**
     * ProjectData转换ProjectDataVo类
     *
     * @param po                  项目核心数据实体类
     * @param internalProjectFlag 是否内部项目标记
     * @return ProjectDataVO
     */
    public static ProjectDataVO of(ProjectData po, boolean internalProjectFlag) {
        ProjectDataVO result = new ProjectDataVO();
        DecimalFormat df1 = new DecimalFormat("##,##0.00");

        BigDecimal totalBudgetRevenueIncludeTax = po.getTotalBudgetRevenueIncludeTax();
        BigDecimal totalBudgetCostIncludeTax = po.getTotalBudgetCostIncludeTax();
        BigDecimal actualTotalBudgetCostIncludeTax = po.getActualTotalBudgetCostIncludeTax();
        BigDecimal actualTotalBudgetCost = po.getActualTotalBudgetCost();
        String estimatedTotalManDays = po.getEstimatedTotalManDays();
        BigDecimal contractPrice = po.getContractPrice();
        BigDecimal managementCost = po.getManagementCost();
        BigDecimal salesCost = po.getSalesCost();
        BigDecimal manDays = po.getManDays();
        BigDecimal beforeSalesManDays = po.getBeforeSalesManDays();
        BigDecimal afterSalesManDays = po.getAfterSalesManDays();
        BigDecimal studentManDay = po.getStudentManDay();
        BigDecimal preSalesLaborInput = po.getPreSalesLaborInput();
        BigDecimal sqfytr = po.getSqfytr();
        BigDecimal internalTotalBudgetCostIncludeTax = po.getInternalTotalBudgetCostIncludeTax();
        String internalEstimatedTotalManDays = po.getInternalEstimatedTotalManDays();
        BigDecimal internalLaborBudget = po.getInternalLaborBudget();
        BigDecimal internalOutsourcingBudgetTax = po.getInternalOutsourcingBudgetTax();
        Long internalBudgetChangesNum = po.getInternalBudgetChangesNum();
        BigDecimal zyywcb = po.getZyywcb();
        BigDecimal xshtjeBhs = po.getXshtjeBhs();
        BigDecimal xmhk = po.getXmhk();
        List<ProjectDataTableVO> projectDataTableList = new ArrayList<>();

        result.setId(String.valueOf(po.getId()));
        result.setTotalBudgetRevenueIncludeTax(totalBudgetRevenueIncludeTax == null ? "0" : df1.format(totalBudgetRevenueIncludeTax));
        result.setTotalBudgetCostIncludeTax(totalBudgetCostIncludeTax == null ? "0" : df1.format(totalBudgetCostIncludeTax));
        result.setActualTotalBudgetCostIncludeTax(actualTotalBudgetCostIncludeTax == null ? "0" : df1.format(actualTotalBudgetCostIncludeTax));
        result.setActualTotalBudgetCost(actualTotalBudgetCost == null ? "0" : df1.format(actualTotalBudgetCost));
        result.setEstimatedTotalManDays(StringUtils.isBlank(estimatedTotalManDays) ? "0" : estimatedTotalManDays);
        result.setContractPrice(contractPrice == null ? "0" : df1.format(contractPrice));
        result.setManagementCost(managementCost == null ? "0" : df1.format(managementCost));
        result.setSalesCost(salesCost == null ? "0" : df1.format(salesCost));
        result.setManDays(manDays == null ? "0" : manDays.toString());
        result.setZyywcb(zyywcb == null ? "0" : df1.format(zyywcb));
        result.setXshtjeBhs(xshtjeBhs == null ? "0" : df1.format(xshtjeBhs));
        result.setXmhk(xmhk == null ? "0" : df1.format(xmhk));
        if (internalProjectFlag) {
            result.setBeforeSalesManDays("");
            result.setAfterSalesManDays("");
            result.setPreSalesLaborInput("");
            result.setSqfytr("");
            result.setInternalTotalBudgetCostIncludeTax(internalTotalBudgetCostIncludeTax == null ? "0" : df1.format(internalTotalBudgetCostIncludeTax));
            result.setInternalLaborBudget(internalLaborBudget == null ? "0" : df1.format(internalLaborBudget));
            result.setInternalOutsourcingBudgetTax(internalOutsourcingBudgetTax == null ? "0" : df1.format(internalOutsourcingBudgetTax));
            result.setInternalBudgetChangesNum(Optional.ofNullable(internalBudgetChangesNum).orElse(0L));
            result.setInternalEstimatedTotalManDays(StrUtil.isBlank(internalEstimatedTotalManDays) ? "0.00" : internalEstimatedTotalManDays);
        } else {
            result.setBeforeSalesManDays(beforeSalesManDays == null ? "0" : beforeSalesManDays.toString());
            result.setAfterSalesManDays(afterSalesManDays == null ? "0" : afterSalesManDays.toString());
            result.setPreSalesLaborInput(preSalesLaborInput == null ? "0" : preSalesLaborInput.toString());
            result.setSqfytr(sqfytr == null ? "0" : sqfytr.toString());
            projectDataTableList.addAll(ProjectDataTableVO.of(po));
            result.setProjectDataTableList(projectDataTableList);
        }

        result.setStudentManDay(studentManDay == null ? "0" : studentManDay.toString());
        result.setCtime(po.getCtime());

        return result;
    }

    /**
     * 根据财务数据更新项目核心数据
     *
     * @param source 财务数据VO类
     * @param target 核心数据VO类
     */
    public static void update(FinancialDataVo source, ProjectDataVO target) {
        if (null == source || null == target) {
            return;
        }

        DecimalFormat df1 = new DecimalFormat("##,##0.00");
        source.setScale(2, RoundingMode.DOWN, df1);

        target.setId(String.valueOf(source.getId()));
        // 实际总成本(不含税)
        target.setActualTotalBudgetCost(source.getSjzcbbhs());
        target.setPayBackSum(source.getPayBackSum());
        List<ProjectDataTableVO> projectDataBTableList = null != target.getProjectDataTableList()
                ? target.getProjectDataTableList()
                : new ArrayList<>();
        target.setProjectDataTableList(projectDataBTableList);
    }

    /**
     * 根据合同数据更新项目核心数据
     *
     * @param source 合同数据VO类
     * @param target 核心数据VO类
     */
    public static void update(ProjectContractVo source, ProjectDataVO target) {
        if (null == source || null == target) {
            return;
        }
        // 销售合同金额(不含税)
        target.setContractPriceBhsSum(source.getContractPriceBhsSum());
    }

}
