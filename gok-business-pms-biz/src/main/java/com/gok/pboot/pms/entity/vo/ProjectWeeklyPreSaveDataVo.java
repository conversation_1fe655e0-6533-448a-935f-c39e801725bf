package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 周报填写前获取自动统计数据
 *
 * <AUTHOR>
 * @date 2023/09/01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectWeeklyPreSaveDataVo {

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 当期新增工时（人天）
     */
    private BigDecimal currentHours;

    /**
     * 累计工时（人天）
     */
    private BigDecimal totalHours;

}
