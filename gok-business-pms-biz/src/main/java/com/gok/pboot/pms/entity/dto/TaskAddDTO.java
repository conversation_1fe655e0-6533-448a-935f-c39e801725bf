package com.gok.pboot.pms.entity.dto;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 任务新增
 *
 * <AUTHOR>
 * @since 2022-08-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TaskAddDTO {

    /**
    * 任务名称
    */
    private String taskName;
    /**
    * 任务类型（0=默认任务，1=手动添加，2=后期维保）
    */
    private Integer taskType;
    /**
    * 任务状态（0=正常，1=关闭）
    */
    private Integer taskStatus;
    /**
    * 所属项目ID
    */
    private Long projectId;
    /**
     * 人员id
     */
    private List<Long> taskUserIds;


}
