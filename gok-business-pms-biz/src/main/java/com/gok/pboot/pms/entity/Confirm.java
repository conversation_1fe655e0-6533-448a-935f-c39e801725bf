package com.gok.pboot.pms.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 工时确认
 *
 * <AUTHOR>
 * @since 2022-08-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(Confirm.ALIAS)
public class Confirm extends BeanEntity<Long> {

    public static final String ALIAS = "mhour_confirm";
    /**
    * 年
    */
    private Integer year;
    /**
    * 月
    */
    private Integer month;
    /**
    * 确认年月对应开始日期时间（当月1号0点）
    */
    private LocalDateTime confirmStartDatetime;
    /**
    * 确认年月对应结束日期时间（次月1号0点）
    */
    private LocalDateTime confirmEndDatetime;

    /**
     * 部门id
     */
    private Long deptId;


}
