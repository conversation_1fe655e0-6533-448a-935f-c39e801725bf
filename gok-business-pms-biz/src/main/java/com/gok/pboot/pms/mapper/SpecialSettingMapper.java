package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.entity.SpecialSetting;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * - 特殊配置 -
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface SpecialSettingMapper extends BaseMapper<SpecialSetting> {

    void batchSave(@Param("poList") List<SpecialSetting> poList);

    void batchDelete(@Param("ids") List<Long> ids);

    Page<SpecialSetting> findPage(
            Page<SpecialSetting> page,
            @Param("filter") Map<String, Object> filter
    );

    Integer countByUserIds(List<Long> userIds);

    List<Long> findUserList(@Param("filter") Map<String, Object> filter);

    Set<Long> findUserIds();
}
