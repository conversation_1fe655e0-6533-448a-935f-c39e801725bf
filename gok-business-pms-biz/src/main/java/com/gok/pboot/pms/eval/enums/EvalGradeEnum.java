package com.gok.pboot.pms.eval.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import java.math.BigDecimal;

/**
 * 评价等级枚举
 *
 * <AUTHOR>
 * @create 2025/05/12
 **/
@Getter
@AllArgsConstructor
public enum EvalGradeEnum implements ValueEnum<Integer> {

    EXCELLENT(0, "优秀"),
    GOOD(1, "良好"),
    QUALIFIED(2, "合格"),
    POOR(3, "差"),
    ;

    private final Integer value;
    private final String name;

    /**
     * 根据得分获取等级
     *
     * @param score 得分
     * @return 等级枚举
     */
    public static EvalGradeEnum getGradeByScore(BigDecimal score) {
        if (score == null) {
            return null;
        }
        if (score.compareTo(new BigDecimal("5")) >= 0) {
            return EXCELLENT;
        } else if (score.compareTo(new BigDecimal("3")) >= 0) {
            return GOOD;
        } else if (score.compareTo(new BigDecimal("1")) >= 0) {
            return QUALIFIED;
        } else {
            return POOR;
        }
    }
} 