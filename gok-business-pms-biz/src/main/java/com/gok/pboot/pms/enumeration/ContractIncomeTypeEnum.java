package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 收入类型枚举
 *
 * <AUTHOR>
 * @date 13/12/2023
 */
@Getter
@AllArgsConstructor
public enum ContractIncomeTypeEnum implements ValueEnum<Integer> {

    /**
     * 产业服务
     */
    INDUSTRIAL_SERVICES(0, "产业服务"),
    /**
     * 教育服务
     */
    EDUCATION_SERVICES(1, "教育服务"),
    /**
     * 产品收入
     */
    PRODUCT_INCOME(2, "产品收入"),
    /**
     * 服务收入
     */
    SERVICE_INCOME(3, "服务收入"),
    /**
     * 教育B端
     */
    EDUCATION_B_END(4, "教育B端"),
    /**
     * 教育C端
     */
    EDUCATION_C_END(5, "教育C端"),
    /**
     * 人才服务
     */
    TALENT_SERVICES(6, "人才服务"),
    /**
     * 培训认证
     */
    TRAINING_CERTIFICATION(7, "培训认证");

    private final Integer value;

    private final String name;
}
