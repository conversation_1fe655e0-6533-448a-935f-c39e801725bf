package com.gok.pboot.pms.entity.vo;


import lombok.Data;

/**
 * 项目预估成本表Vo
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-17 11:11:29
 */
@Data
public class ProjectEstimatedCostVO{

    /**
     * id
     */
    private Long id;
	/**
	 * 项目ID
	 */
	private Long projectId;
	/**
	 * 成本费用项
	 */
	private Integer costExpenseItems;
    /**
     * 成本费用项
     */
    private String costExpenseItemsName;
	/**
	 * 成本费用项科目代码
	 */
	private String subjectCode;
	/**
	 * 成本费用项科目名称
	 */
	private String subjectName;
	/**
	 * 预算金额
	 */
	private String budgetAmount;
	/**
	 * 预算人天
	 */
	private String budgetManDays;
	/**
	 * 成本费用说明
	 */
	private String costExplanation;

}
