package com.gok.pboot.pms.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.entity.domain.ProjectCustomerState;
import com.gok.pboot.pms.entity.vo.ProjectCustomerStateVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 项目客情状态表（数仓同步）
 *
 * <AUTHOR>
 * @date 2023-07-11 17:05:26
 */
@Mapper
public interface ProjectCustomerStateMapper extends BaseMapper<ProjectCustomerState> {

    Page<ProjectCustomerStateVO> findListPage(Page<Object> objectPage, @Param("filter") Map<String, Object> filter);
}
