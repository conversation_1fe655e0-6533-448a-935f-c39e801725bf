package com.gok.pboot.pms.cost.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
    * 人力外包-费用分摊汇总
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CostExpensesShareSummaryListVO{

    /**
     * 归属月份
     */
    @ExcelProperty("归属月份")
    @ColumnWidth(value = 30)
    private String belongingMonth;

    /**
    * 科目名称id
    */
    @ExcelIgnore
    private Long accountId;

    /**
    * 科目名称
    */
    @ExcelProperty("科目名称")
    @ColumnWidth(value = 30)
    private String accountName;

    /**
    * 成本科目类别id
    */
    @ExcelIgnore
    private Long accountCategoryId;

    /**
    * 费用项类别
    */
    @ExcelProperty("费用项类别")
    @ColumnWidth(value = 30)
    private String accountCategoryName;

    /**
    * 报销金额
    */
    @ExcelProperty("报销金额")
    @ColumnWidth(value = 30)
    private String reimburseMoney;

}