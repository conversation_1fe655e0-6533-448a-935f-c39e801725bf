package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.domain.CostConfigLevelPrice;
import com.gok.pboot.pms.cost.entity.dto.CalculateLaborCostDTO;
import com.gok.pboot.pms.cost.entity.dto.CostConfigLevelPriceDTO;
import com.gok.pboot.pms.cost.entity.dto.CostSalaryDTO;
import com.gok.pboot.pms.cost.entity.vo.CostConfigLevelPriceVO;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 人员级别单价配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
public interface ICostConfigLevelPriceService extends IService<CostConfigLevelPrice> {

    /**
     * 取人员级别单价配置列表
     *
     * @return {@link List }<{@link CostConfigLevelPriceVO }>
     */
    List<CostConfigLevelPriceVO> getCostConfigLevelPriceList();

    /**
     * 编辑人员级别单价配置列表
     *
     * @param dtoList DTO 列表
     */
    void editCostConfigLevelPriceList(List<CostConfigLevelPriceDTO> dtoList);

    /**
     * 根据版本ID获取人员级别单价配置列表
     *
     * @param versionId   版本 ID
     * @param pageRequest 页面请求
     * @return {@link Page }<{@link CostConfigLevelPriceVO }>
     */
    Page<CostConfigLevelPriceVO> getLevelPriceListByVersionIdPage(Long versionId, PageRequest pageRequest);

    /**
     * 通过 Config ID 批量计算人工成本
     *
     * @param dtoList DTO 列表
     * @return {@link Map }<{@link Long }, {@link CostSalaryDTO }>
     */
    Map<Long, CostSalaryDTO> batchCalculateLaborCostByConfigId(Collection<CalculateLaborCostDTO> dtoList);

    /**
     * 批量计算人工成本
     *
     * @param dtoList DTO 列表
     * @return {@link Map }<{@link Long }, {@link BigDecimal }>
     */
    Map<Long, CostSalaryDTO> batchCalculateLaborCost(Collection<CalculateLaborCostDTO> dtoList);

    /**
     * 获取人员时薪map
     *
     * @param userIds 用户 ID
     * @return {@link Map }<{@link Long }, {@link BigDecimal }>
     */
    Map<Long, BigDecimal> getHourlyWageMap(Collection<Long> userIds);

    /**
     * 计算人工成本
     *
     * @param dto DTO
     * @return {@link BigDecimal }
     */
    CostSalaryDTO calculateLaborCost(CalculateLaborCostDTO dto);
}
