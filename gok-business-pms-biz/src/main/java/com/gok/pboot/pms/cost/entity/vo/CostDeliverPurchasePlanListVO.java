package com.gok.pboot.pms.cost.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 交付管理采购计划分页查询VO类
 *
 * <AUTHOR>
 * @create 2025/01/15
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CostDeliverPurchasePlanListVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 采购物
     */
    private String purchaseItem;

    /**
     * 销售合同ID
     */
    private Long saleContractId;

    /**
     * 销售合同名称
     */
    private String saleContractName;

    /**
     * 采购合同ID
     */
    private Long purchaseContractId;

    /**
     * 采购合同名称
     */
    private String purchaseContractName;

    /**
     * 关联成本科目ID
     */
    private Long accountId;

    /**
     * 关联成本科目名称
     */
    private String accountName;

    /**
     * 预算金额（含税）
     */
    private BigDecimal budgetAmountIncludedTax;

    /**
     * 税率中台OA字典ID
     */
    private Integer taxRate;

    /**
     * 税率文本
     */
    private String taxRateTxt;

    /**
     * 已用预算
     */
    private BigDecimal usedBudget;

    /**
     * 剩余预算
     */
    private BigDecimal remainBudget;

    /**
     * 计划到货(场)日期
     */
    private Date plannedDeliveryDate;

    /**
     * 当前状态
     */
    private String planStatus;

    /**
     * 实际到货(场)日期
     */
    private Date actualDeliveryDate;

    /**
     * 验收和付款条件
     */
    private Integer acceptanceNum;

    /**
     * 已付金额
     */
    private BigDecimal paidAmount;

}
