package com.gok.pboot.pms.entity.dto;

import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.util.Assert;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * 项目会议纪要导入数据 Dto类
 *
 * <AUTHOR>
 * @since 2023-07-13
 **/
@Data
public class ProjectMeetingImportExcelDTO {

    /**
     * 会议名称
     */
    @ExcelProperty("会议名称")
    @NotBlank(message = "会议名称不能为空")
    private String name;

    /**
     * 召集人
     */
    @ExcelProperty("召集人")
    @NotBlank(message = "召集人不能为空")
    private String convener;

    /**
     * 会议日期
     * 格式为：yyyy-MM-dd
     */
    @ExcelProperty("会议日期")
    @NotNull(message = "会议日期不能为空")
    private LocalDate meetingDate;

    /**
     * 开始时间
     */
    @ExcelProperty("起止时间")
    private String time;

    /**
     * 记录人
     */
    @ExcelProperty("记录人")
    @Length(max = 20)
    private String recorder;

    /**
     * 会议地点
     */
    @ExcelProperty("会议地点")
    @Length(max = 64)
    private String place;

    /**
     * 参会人员
     */
    @ExcelProperty("参会人员")
    @Length(max = 100)
    private String member;

    /**
     * 会议目标
     */
    @ExcelProperty("会议目标")
    @Length(max = 500)
    private String objective;

    /**
     * 会议过程
     */
    @ExcelProperty("会议过程")
    @Length(max = 2000)
    private String process;

    /**
     * 会议决议
     */
    @ExcelProperty("会议决议")
    @Length(max = 500)
    private String resolution;

    /**
     * 待办事项
     */
    @ExcelProperty("待办事项")
    @Length(max = 500)
    private String backlog;

    public void validate() {
        if (StrUtil.isNotBlank(time)) {
            String regexp = "\\d{2}:\\d{2}";
            List<String> timeArray = StrUtil.split(time, StrUtil.DASHED);
            if (StrUtil.isNotBlank(timeArray.get(0))) {
                Assert.isTrue(ReUtil.isMatch(regexp, timeArray.get(0)), "开始时间不合法");
            }
            if (StrUtil.isNotBlank(timeArray.get(1))) {
                Assert.isTrue(ReUtil.isMatch(regexp, timeArray.get(0)), "结束时间不合法");
            }
        }
    }

}
