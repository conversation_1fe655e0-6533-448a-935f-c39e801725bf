package com.gok.pboot.pms.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 项目系统动态Dto类
 *
 * <AUTHOR>
 * @since 2023-07-17
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectSystemProcessInfoDTO {

    /**
     * 会议纪要id/周报id
     */
    private Long requestId;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 申请人id
     */
    private Long applicatId;

    /**
     * 申请人
     */
    private String applicat;

    /**
     * 流程类型
     * {@link com.gok.pboot.pms.enumeration.SystemProcessTypeEnum}
     */
    private String processType;

    /**
     * 流程状态
     * {@link com.gok.pboot.pms.enumeration.ProcessInfoStatusEnum}
     */
    private String status;

    /**
     * 流程名
     */
    private String name;

}
