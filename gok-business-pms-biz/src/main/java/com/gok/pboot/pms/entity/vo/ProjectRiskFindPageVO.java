package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.entity.domain.ProjectRisk;
import com.gok.pboot.pms.enumeration.InfluenceDegreeEnum;
import com.gok.pboot.pms.enumeration.RiskLevelEnum;
import com.gok.pboot.pms.enumeration.RiskStatusEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 项目风险表 Vo
 *
 * <AUTHOR>
 * @since 2023-07-13
 **/
@Data
public class ProjectRiskFindPageVO {

    /**
     * 项目风险主键id
     */
    private Long id;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 风险描述
     */
    private String description;

    /**
     * 发生概率 (%)
     */
    private BigDecimal probability;

    /**
     * 影响程度: 极大、大、中、小 字典id
     * {@link com.gok.pboot.pms.enumeration.InfluenceDegreeEnum}
     */
    private Integer influenceDegree;

    /**
     * 风险等级: 高 中 低 字典id
     * 高风险： >60%发生风险的可能性    中风险： 30-60%发生风险的可能性    低风险：<30%发生防线的可能性
     */
    private Integer level;

    /**
     * 风险应对计划
     */
    private String responsePlan;

    /**
     * 责任人id
     */
    private Long chargeUserId;

    /**
     * 责任人
     */
    private String chargeUser;

    /**
     * 状态: 开放0、关闭1 字典id
     */
    private Integer status;

    /**
     * 状态: 开放、关闭
     */
    private String statusTxt;

    /**
     * 风险等级: 高 中 低
     */
    private String levelTxt;

    /**
     * 影响程度: 极大、大、中、小
     * {@link com.gok.pboot.pms.enumeration.InfluenceDegreeEnum}
     */
    private String influenceDegreeTxt;

    public static ProjectRiskFindPageVO of(ProjectRisk po, Map<Long, String> projectIdAndNameMap){
        ProjectRiskFindPageVO result = new ProjectRiskFindPageVO();
        Long projectId = po.getProjectId();

        result.setId(po.getId());
        result.setProjectId(projectId);
        result.setDescription(po.getDescription());
        result.setProbability(po.getProbability());
        result.setInfluenceDegree(po.getInfluenceDegree());
        result.setLevel(po.getLevel());
        result.setResponsePlan(po.getResponsePlan());
        result.setChargeUser(po.getChargeUser());
        result.setChargeUserId(po.getChargeUserId());
        result.setStatus(po.getStatus());

        result.setInfluenceDegreeTxt(EnumUtils.getNameByValue(InfluenceDegreeEnum.class,po.getInfluenceDegree()));
        result.setLevelTxt(EnumUtils.getNameByValue(RiskLevelEnum.class, po.getLevel()));
        result.setStatusTxt(EnumUtils.getNameByValue(RiskStatusEnum.class, po.getStatus()));
        result.setProjectName(projectIdAndNameMap.get(projectId));

        return result;
    }

}
