package com.gok.pboot.pms.controller;


import com.gok.bcp.admin.vo.DictKvVo;
import com.gok.bcp.admin.vo.DictLevelKvVo;
import com.gok.components.common.util.R;
import com.gok.pboot.pms.common.base.BaseController;
import com.gok.pboot.pms.entity.DictBatchDTO;
import com.gok.pboot.pms.service.IDictService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 字典前端控制器
 *
 * <AUTHOR>
 * @description 工时确认 前端控制器
 * @since 2022-08-30
 * @menu 中台字典
 */
@Slf4j
@RestController
@RequestMapping("/dict")
@RequiredArgsConstructor
public class DictController extends BaseController {

    private final IDictService dictService;

    /**
     * 批量获取数据字典kv值(字典名称咨询后端)
     */
    @PostMapping("/batchList")
    R<Map<String, List<DictKvVo>>> getDictKvBatchList(@RequestBody DictBatchDTO dto){
       return dictService.getDictKvBatchList(dto.getDictKeyList());
    }

    /**
     * 获取数据字典kv值(字典名称咨询后端)
     */
    @GetMapping("/list")
    R<List<DictKvVo>> geDictKvList(@RequestParam("dictKey") String dictKey){
        return dictService.getDictKvList(dictKey);
    }

    /**
     * 批量获取树结构字典(字典名称咨询后端)
     */
    @PostMapping("/batchTreeList")
    R<Map<String,List<DictLevelKvVo>>> batchTreeList(@RequestBody DictBatchDTO dto){
        return dictService.batchTreeList(dto.getDictKeyList());
    }
}
