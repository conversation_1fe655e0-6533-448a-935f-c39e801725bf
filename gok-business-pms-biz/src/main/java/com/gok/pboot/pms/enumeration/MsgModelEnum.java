package com.gok.pboot.pms.enumeration;

/**
 * 消息模版枚举
 *
 * @Author: wengc
 * @Date: 2023/6/12
 */
public enum MsgModelEnum implements EnumBase {

    BUSINESS_MILESTONE_ACHIEVEMENT_REMINDER(1, "商务里程碑达成提醒", "商务里程碑达成提醒",
            "【{}】的商务里程碑【{}】距离预计完成日期还有7天，请及时发起里程碑达成审批～"
            ,"【{}】的商务里程碑【{}】距离预计完成日期还有7天，请及时发起里程碑达成审批～\n<a href=\"{}\">查看详情</a>"),
    BUSINESS_MILESTONE_OVERDUE_REMINDER(2, "商务里程碑逾期提醒", "商务里程碑逾期提醒",
            "【{}】的商务里程碑【{}】即将逾期，请及时发起里程碑达成审批～"
    ,"【{}】的商务里程碑【{}】即将逾期，请及时发起里程碑达成审批~\n<a href=\"{}\">查看详情</a>"),

    ;


    private final String title;
    private final String remark;
    private final Integer value;
    private final String name;
    private final String name1;

    MsgModelEnum(Integer value, String title, String remark, String name, String name1) {
        this.title = title;
        this.remark = remark;
        this.value = value;
        this.name = name;
        this.name1 = name1;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
    public String getName1() {
        return name1;
    }


    public String getTitle() {
        return title;
    }

    public String getRemark() {
        return remark;
    }
}
