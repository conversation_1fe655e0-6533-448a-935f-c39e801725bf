package com.gok.pboot.pms.cost.entity.vo;

import com.gok.pboot.common.secret.AESEncryptor;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.cost.entity.domain.CostDeliverTask;
import com.gok.pboot.pms.cost.entity.domain.CostTaskCategoryManagement;
import com.gok.pboot.pms.cost.enums.CostTaskDisassemblyTypeEnum;
import com.gok.pboot.pms.cost.enums.CostTaskStatusEnum;
import com.gok.pboot.pms.enumeration.ProjectTaskKindEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 交付管理工单表分页查询VO类
 *
 * <AUTHOR>
 * @create 2025/01/15
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CostDeliverTaskVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 工单名称
     */
    private String taskName;

    /**
     * 工单类型（0=售前支撑，1=售后交付）
     *
     * @see com.gok.pboot.pms.enumeration.ProjectTaskKindEnum
     */
    private Integer taskType;

    private String taskTypeTxt;

    /**
     * 工单级别
     */
    private Integer taskLevel;

    /**
     * 工单描述
     */
    private String taskDesc;

    /**
     * 成本科目ID
     */
    private Long accountId;

    /**
     * 关联成本科目OA ID
     */
    private Long accountOaId;

    /**
     * 税率
     */
    private Integer taxRate;

    /**
     * 税率文本
     */
    private String taxRateTxt;

    /**
     * 关联成本科目名称
     */
    private String accountName;

    /**
     * 预算成本
     */
    private BigDecimal budgetCost;

    private String budgetCostStr;

    /**
     * 拆解类型（0=标准工单，1=总成工单）
     *
     * @see com.gok.pboot.pms.cost.enums.CostTaskDisassemblyTypeEnum
     */
    private Integer disassemblyType;

    private String disassemblyTypeTxt;

    /**
     * 工单负责人ID
     */
    private Long managerId;

    /**
     * 工单负责人姓名
     */
    private String managerName;

    /**
     * 上级工单负责人ID
     */
    private Long higherManagerId;

    /**
     * 上级工单负责人姓名
     */
    private String higherManagerName;

    /**
     * 开始时间
     */
    private LocalDate startDate;

    /**
     * 结束时间
     */
    private LocalDate endDate;

    /**
     * 工单状态
     *
     * @see com.gok.pboot.pms.cost.enums.CostTaskStatusEnum
     */
    private Integer taskStatus;

    private String taskStatusTxt;

    /**
     * 评价状态
     */
    private Integer evaluationStatus;

    /**
     * 完成时间
     */
    private LocalDateTime completionTime;

    /**
     * 提交完成时间
     */
    private LocalDateTime submitCompletionTime;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 工作日加班工时
     */
    private BigDecimal workOvertimeHours;

    /**
     * 休息日加班工时
     */
    private BigDecimal restOvertimeHours;

    /**
     * 节假日加班工时
     */
    private BigDecimal holidayOvertimeHours;

    /**
     * 总工时
     */
    private BigDecimal totalHours;

    /**
     * 产值
     */
    private BigDecimal income;

    /**
     * 分配产值
     */
    private BigDecimal allocatedIncome;

    /**
     * 结算产值
     */
    private BigDecimal settledIncome;

    /**
     * 进行时间（天）
     */
    private Long duration;

    /**
     * 进度
     */
    private BigDecimal progress;

    /**
     * 退回原因
     */
    private String returnReason;

    /**
     * 退回时间
     */
    private LocalDateTime returnTime;

    /**
     * 退回人ID
     */
    private Long returnerId;

    /**
     * 退回人姓名
     */
    private String returnerName;

    /**
     * 顶层工单ID
     */
    private Long topLevelId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建时间
     */
    private LocalDateTime ctime;

    /**
     * 预计工时
     */
    private BigDecimal estimatedHours;

    /**
     * 正常工时
     */
    private BigDecimal normalHours;

    /**
     * 实际总工时
     */
    private BigDecimal actualTotalHours;

    /**
     * 实际人工成本
     */
    private BigDecimal actualLaborCost;

    private String actualLaborCostStr;

    /**
     * 子级集合
     */
    private List<CostDeliverTaskVO> children;

    /**
     * 工单类别（来自工单类别管理）
     */
    private Integer taskCategory;

    /**
     * 工单类别文本
     */
    private String taskCategoryTxt;

    /**
     * 是否为默认配置的工单(1是 0否)
     */
    private Boolean defaultConf;

    /**
     * （售前）等待审核工时
     */
    private BigDecimal waitReviewHours;

    /**
     * 是否异常
     */
    private Boolean abnormal;

    /**
     * 父级工单状态
     */
    private Integer parentTaskStatus;

    /**
     * 是否填写工作时间
     */
    private Boolean fillWorkHours;

    /**
     * 构建 vo
     *
     * @param task                工单实体类（无解密）
     * @param costTaskCategoryMap 工单类别管理集合
     * @return {@link CostDeliverTaskVO }
     */
    public static CostDeliverTaskVO buildVo(CostDeliverTask task, Map<Integer, CostTaskCategoryManagement> costTaskCategoryMap) {
        return buildVo(task).setTaskCategoryTxt(costTaskCategoryMap.getOrDefault(task.getTaskCategory(), new CostTaskCategoryManagement()).getTaskCategoryName());
    }

    public static CostDeliverTaskVO buildVo(CostDeliverTask task) {
        return new CostDeliverTaskVO()
                .setId(task.getId())
                .setParentId(task.getParentId())
                .setProjectId(task.getProjectId())
                .setProjectName(task.getProjectName())
                .setTaskName(task.getTaskName())
                .setTaskType(task.getTaskType())
                .setTaskTypeTxt(EnumUtils.getNameByValue(ProjectTaskKindEnum.class, task.getTaskType()))
                .setTaskLevel(task.getTaskLevel())
                .setTaskDesc(task.getTaskDesc())
                .setAccountId(task.getAccountId())
                .setAccountName(task.getAccountName())
                .setAccountOaId(task.getAccountOaId())
                .setTaxRate(task.getTaxRate())
                .setBudgetCostStr(task.getBudgetCost())
                .setDisassemblyType(task.getDisassemblyType())
                .setManagerId(task.getManagerId())
                .setManagerName(task.getManagerName())
                .setStartDate(task.getStartDate())
                .setEndDate(task.getEndDate())
                .setSubmitCompletionTime(task.getSubmitCompletionTime())
                .setAuditTime(task.getAuditTime())
                .setWorkOvertimeHours(task.getWorkOvertimeHours())
                .setRestOvertimeHours(task.getRestOvertimeHours())
                .setTaskCategory(task.getTaskCategory())
                .setHolidayOvertimeHours(task.getHolidayOvertimeHours())
                .setEstimatedHours(task.getEstimatedHours())
                .setTaskStatus(task.getTaskStatus())
                .setTaskStatusTxt(EnumUtils.getNameByValue(CostTaskStatusEnum.class, task.getTaskStatus()))
                .setCompletionTime(task.getCompletionTime())
                .setActualLaborCostStr(task.getActualLaborCost())
                .setNormalHours(task.getNormalHours())
                .setIncome(task.getIncome())
                .setDefaultConf(task.getDefaultConf())
                .setAbnormal(task.getAbnormal())
                .calculateTotalHours();
    }

    /**
     * 计算总工时
     *
     * @return {@link BigDecimal }
     */
    public CostDeliverTaskVO calculateTotalHours() {
        if (EnumUtils.valueEquals( this.disassemblyType, CostTaskDisassemblyTypeEnum.TOTAL_WORK_ORDER)) {
            return this;
        }

        BigDecimal totalHours = BigDecimal.ZERO;
        if (this.workOvertimeHours != null) {
            totalHours = totalHours.add(this.workOvertimeHours);
        }
        if (this.restOvertimeHours != null) {
            totalHours = totalHours.add(this.restOvertimeHours);
        }
        if (this.holidayOvertimeHours != null) {
            totalHours = totalHours.add(this.holidayOvertimeHours);
        }
        if (this.normalHours!= null) {
            totalHours = totalHours.add(this.normalHours);
        }

        this.totalHours = totalHours;
        return this;
    }

    public void setTaskAbnormal() {
        CostDeliverTask costDeliverTask = new CostDeliverTask()
                .setDisassemblyType(this.getDisassemblyType())
                .setTaskStatus(this.getTaskStatus())
                .setStartDate(this.getStartDate())
                .setEndDate(this.getEndDate());
        this.abnormal = CostDeliverTask.isTaskAbnormal(costDeliverTask);
    }

    /**
     * 解密
     *
     * @return {@link CostDeliverTask }
     */
    public CostDeliverTaskVO decrypt() {
        String budgetCostString = AESEncryptor.justDecrypt(this.budgetCostStr);
        this.budgetCostStr = budgetCostString;
        this.budgetCost = StringUtils.isNotBlank(budgetCostString) ? new BigDecimal(budgetCostString) : BigDecimal.ZERO;

        String actualLaborCostString = AESEncryptor.justDecrypt(this.actualLaborCostStr);
        this.actualLaborCostStr = actualLaborCostString;
        this.actualLaborCost = StringUtils.isNotBlank(actualLaborCostString) ? new BigDecimal(actualLaborCostString) : BigDecimal.ZERO;
        return this;
    }
}
