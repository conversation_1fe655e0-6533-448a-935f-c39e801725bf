package com.gok.pboot.pms.enumeration;

/**
 * 项目-是否内部项目
 *
 * <AUTHOR>
 */
public enum IsNoInternalProjectEnum implements ValueEnum<Integer> {
    /**
     * 是
     */
    yes(1, "是"),
    /**
     * 否
     */
    no(2, "否");

    //值
    private Integer  value;
    //名称
    private String name;

    IsNoInternalProjectEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
    /**
     * 获取值
     *
     * @return Integer
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取名称
     *
     * @return String
     */
    @Override
    public String getName() {
        return name;
    }

    public static String getNameByVal(Integer value) {
        for (IsNoInternalProjectEnum yesOrNoEnum : IsNoInternalProjectEnum.values()) {
            if (yesOrNoEnum.value.equals(value)) {
                return yesOrNoEnum.name;
            }
        }
        return "";
    }
    public static Integer getValByName(String name) {
        for (IsNoInternalProjectEnum yesOrNoEnum : IsNoInternalProjectEnum.values()) {
            if (yesOrNoEnum.getName().equals(name)) {
                return yesOrNoEnum.getValue();
            }
        }
        return null;
    }

    public static IsNoInternalProjectEnum getYesOrNoEnum(Integer value) {
        for (IsNoInternalProjectEnum yesOrNoEnum : IsNoInternalProjectEnum.values()) {
            if (yesOrNoEnum.value.equals(value)) {
                return yesOrNoEnum;
            }
        }
        return null;
    }
}
