package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.cost.entity.domain.CostPersonnelInformation;
import com.gok.pboot.pms.cost.entity.dto.*;
import com.gok.pboot.pms.cost.entity.vo.CostPersonnelInformationVO;
import com.gok.pboot.pms.cost.entity.vo.PersonPartInfoVO;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 人员信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
public interface ICostPersonnelInformationService extends IService<CostPersonnelInformation> {

    /**
     * 保存或更新人员信息
     *
     * @param dto dto实体
     * @return {@link List}<{@link String}>
     */
    List<String> saveOrUpdatePersonInfo(CostPersonnelInformationDTO dto);

    /**
     * 根据条件获取在场/已离场人员信息
     *
     * @param dto dto实体
     * @return {@link List}<{@link CostPersonnelInformationVO}>
     */
    List<CostPersonnelInformationVO> getPersonInfoList(PersonInfoConditionDTO dto);

    /**
     * 根据条件获取含税报价信息
     *
     * @param dto dto实体
     * @return {@link BigDecimal}
     */
    BigDecimal getQuotationIncludeTax(AutoBringConditionDTO dto);

    /**
     * 根据条件自动带出人员信息列表
     *
     * @param dto dto实体
     * @return {@link List}<{@link CostPersonnelInformationVO}>
     */
    List<CostPersonnelInformationVO> getPersonAutoBringInfo(AutoBringConditionDTO dto);

    /**
     * 根据条件获取新增国科人员信息
     *
     * @param dto dto实体
     * @return {@link List}<{@link PersonPartInfoVO}>
     */
    List<PersonPartInfoVO> getPersonPartInfo(PersonPartInfoDTO dto);

    /**
     * 离场/再次入场
     *
     * @param dto dto实体
     * @return {@link Boolean}
     */
    Boolean exitOrEntry(ExitOrDeleteOrEntryPersonDTO dto);

    /**
     * 删除
     *
     * @param dto dto实体
     * @return {@link ApiResult}<{@link Boolean}>
     */
    Boolean deletePersonInfo(ExitOrDeleteOrEntryPersonDTO dto);

    /**
     * 导出
     *
     * @param response HTTP
     * @param dto      dto实体
     */
    void export(HttpServletResponse response, PersonInfoConditionDTO dto);

    /**
     * 导入
     *
     * @param projectId 项目ID
     * @param dtoList   dto列表
     * @return {@link List}<{@link String}>
     */
    List<String> importPersonData(Long projectId, List<PersonImportInfoDTO> dtoList);

    /**
     * 归档人员信息
     *
     * @return {@link Boolean}
     */
    Boolean filingPersonInfo();

}