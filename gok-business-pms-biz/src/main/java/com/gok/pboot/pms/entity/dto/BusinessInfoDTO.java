package com.gok.pboot.pms.entity.dto;

import com.gok.pboot.pms.common.base.PageRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 商机台账dto
 *
 * <AUTHOR>
 * @date 2023/11/21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessInfoDTO extends PageRequest {

    /**
     * 商机/客户关键词 (模糊搜索: 商机名称、最终客户、签约客户)
     */
    private String keyword;

    /**
     * 业务归属部门id集合
     */
    private List<Long> departmentIds;

    /**
     * 商机阶段 (商机阶段0、商机阶段1、商机阶段2、商机阶段3、商机阶段4)
     */
    private List<Long> businessStage;

    /**
     * 经营单元ID
     */
    private Long businessId;

    /**
     * 所属客户ID
     */
    private List<Long> unitId;

    /**
     * 项目里程碑 (项目未启动、需求方案编制、可研设计、招投标)
     */
    private Long businessMilestone;

    /**
     * 是否涉及外采
     */
    private String isExternalProcurement;

    /**
     * 采购类别
     */
    private String purchasingCategories;

    /**
     * 客户分级 (战略部署类、积极开拓类、潜力探索类、被动展业类、其他)
     */
    private Long customerGrade;

    /**
     * 成员id (传入成员姓名对应的id)
     */
    private Long memberId;

    /**
     * 项目所在地
     */
    private String projectLocation;

    /**
     * 客户行业
     */
    private String customerIndustry;

    /**
     * 业务板块 (通用企业管理数字化、专业主营业务数字化、教育培训)
     */
    private Long businessModule;

    /**
     * 是否需要招投标 (需要招投标、不需要招投标)
     */
    private Long isNotNeedBidding;

    /**
     * 项目签约主体
     */
    private String contractEntity;

    /**
     * 收入类型 (产业服务、教育服务)
     */
    private Long incomeType;

    /**
     * 技术类型 (ICT集成、综合运维、安全服务、软件开发、ERP交付、数据治理、其他)
     */
    private Long technologyType;

    /**
     * 结算方式 (人力结算、项目结算)
     */
    private Long settlementMethod;

    /**
     * 交付形式 (劳务派遣、劳务外包、项目外包)
     */
    private Long deliveryMethod;

    /**
     * 关键决策人支持情况 (未接触到关键决策人、部分联系且具备合作意愿、1个关键决策人支持、2个或以上关键决策人支持)
     */
    private Long supportFromKeyDecision;

    /**
     * 项目需求是否明确 (不了解具体需求、需求初步了解、需求基本明确、需求细节清晰)
     */
    private Long projectRequirementClear;

    /**
     * 预算情况 (不了解预算、了解预算概况、预算已申报、预算已批复)
     */
    private Long budgetSituation;

    /**
     * 商机创建时间排序(0升序，1倒序)
     */
    private Integer businessCtimeSort;

    /**
     * 商机更新日期排序(0升序，1倒序)
     */
    private Integer businessMtimeSort;

    /**
     * 登录用户的用户id
     */
    private Long userId;

    /**
     * 商机id集合
     */
    private List<Long> businessIds;

    /**
     * 商机状态
     *
     * @see com.gok.pboot.pms.enumeration.BusinessStatusEnum
     */
    private Integer businessStatus;
}
