package com.gok.pboot.pms.eval.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;

/**
 * 评定项目枚举
 *
 * <AUTHOR>
 * @create 2025/05/12
 **/
@Getter
@AllArgsConstructor
public enum AssessmentProjectEnum implements ValueEnum<Integer> {

    CBPCL(EvalIndexTypeEnum.COST_INDEX, 0, "成本偏差率", new BigDecimal("0.5")),
    SWLCBJHPCL(EvalIndexTypeEnum.PROCESS_INDEX, 1, "商务里程碑计划偏差率", new BigDecimal("0.1")),
    XMJXBGCS(EvalIndexTypeEnum.PROCESS_INDEX, 2, "项目基线变更次数", new BigDecimal("0.1")),
    KHMYD(EvalIndexTypeEnum.QUALITY_INDEX, 3, "客户满意度", new BigDecimal("0.3")),

    // 项目工作指标
    COMPLETION_TIMELINESS(EvalIndexTypeEnum.PROJECT_WORK, 4, "完成及时性", new BigDecimal("0.2")),
    COMPLETION_QUALITY(EvalIndexTypeEnum.PROJECT_WORK, 5, "完成质量", new BigDecimal("0.2")),
    COMPLIANCE(EvalIndexTypeEnum.PROJECT_WORK, 6, "合规性", new BigDecimal("0.2")),

    // 职业行为指标
    COLLABORATION(EvalIndexTypeEnum.PROFESSIONAL_BEHAVIOR, 7, "协作沟通", new BigDecimal("0.2")),
    WORK_ATTITUDE(EvalIndexTypeEnum.PROFESSIONAL_BEHAVIOR, 8, "工作态度", new BigDecimal("0.2")),
    ;

    /**
     * 指标类型
     */
    private final EvalIndexTypeEnum evalIndexType;
    private final Integer value;
    private final String name;
    /**
     * 权重
     */
    private final BigDecimal weight;

}
