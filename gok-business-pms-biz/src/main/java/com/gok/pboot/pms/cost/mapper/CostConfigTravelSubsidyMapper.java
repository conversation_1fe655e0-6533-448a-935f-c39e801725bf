package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.cost.entity.domain.CostConfigTravelSubsidy;
import com.gok.pboot.pms.cost.entity.vo.CostConfigTravelSubsidyVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 差旅补贴标准配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Mapper
public interface CostConfigTravelSubsidyMapper extends BaseMapper<CostConfigTravelSubsidy> {

    /**
     * 按版本 ID 获取差旅补贴
     *
     * @param versionId 版本 ID
     * @return {@link List }<{@link CostConfigTravelSubsidyVO }>
     */
    List<CostConfigTravelSubsidyVO> getTravelSubsidiesByVersionId(@Param("versionId") Long versionId);
}
