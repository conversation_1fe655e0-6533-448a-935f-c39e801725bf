package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.Util.DecimalFormatUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;

/**
 * 财务数据-追加预算总收入 分页数据
 *
 * <AUTHOR>
 * @date 2023/08/23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class AdditionalIncomePageVo {

    /**
     * 请求名称
     */
    private String requestName;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 销售内容
     */
    private String sellContent;

    /**
     * 销售金额（含税）
     */
    private String salesAmount;

    /**
     * 税率
     */
    private String taxRate;

    /**
     * 销售金额（不含税）
     */
    private String salesAmountBhs;

    /**
     * 生成日期
     */
    private LocalDate generationDate;

    /**
     * 流程类型
     */
    private Integer type;

    /**
     * 设置小数位和舍进规则
     *
     * @param newScale 小数保留位数
     * @param roundingMode 舍进规则
     * @param decimalFormat 小数
     */
    public void setScale(int newScale, RoundingMode roundingMode, DecimalFormat decimalFormat) {
        this.salesAmount = DecimalFormatUtil.setAndValidate(salesAmount, newScale, roundingMode, decimalFormat);
        this.salesAmountBhs = DecimalFormatUtil.setAndValidate(salesAmountBhs, newScale, roundingMode, decimalFormat);
    }

}
