package com.gok.pboot.pms.Util;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.text.StrPool;
import com.baomidou.mybatisplus.core.toolkit.BeanUtils;
import com.gok.pboot.pms.common.base.BaseEntity;
import com.gok.pboot.pms.common.base.TreeEntity;
import com.google.common.base.Preconditions;
import com.google.common.collect.*;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Nonnull;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * - 基础实体工具类 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/11/22 11:26
 */
@UtilityClass
public class BaseEntityUtils {

    @Nonnull
    @Deprecated // 已废弃，方法名不规范，应使用"List<ID> mapToIdList(@Nonnull Collection<T> entities)"
    public static<ID, T extends BaseEntity<ID>> List<ID> mapEntitiesToIds(@Nonnull Collection<T> entities){
        if (entities.isEmpty()){
            return new ArrayList<>(0);
        }

        return entities.stream()
                .map(BaseEntity::getId)
                .collect(Collectors.toList());
    }

    @Nonnull
    public static<ID, T extends BaseEntity<ID>> List<ID> mapToIdList(@Nonnull Collection<T> entities){
        if (entities.isEmpty()){
            return new ArrayList<>(0);
        }

        return entities.stream()
                .map(BaseEntity::getId)
                .collect(Collectors.toList());
    }

    @Nonnull
    public static List<Long> mapBeansByGetter(@Nonnull Collection<?> beans){
        return mapBeansByGetter(beans, Long.class);
    }

    @Nonnull
    public static <ID> List<ID> mapBeansByGetter(@Nonnull Collection<?> beans, Class<ID> clazz){
        return mapBeansByGetter(beans, "getId", true);
    }

    /**
     * ~ 通过反射讲普通类型集合转为ID集合（需要对象有getId成员） ~
     * @param beans 普通类型集合
     * @return java.util.List<T>
     * <AUTHOR>
     * @date 2022/11/23 9:41
     */
    @Nonnull
    @SuppressWarnings({"unchecked", "null"})
    public static<ID> List<ID> mapBeansByGetter(
            @Nonnull Collection<?> beans,
            @Nonnull String gettersName,
            boolean sameTypeFlag
    ){
        if (beans.isEmpty()){
            return new ArrayList<>(0);
        }
        Preconditions.checkState(StringUtils.isNotBlank(gettersName), "Getter's name can't be blank.");

        List<ID> ids = Lists.newArrayListWithExpectedSize(beans.size());
        Iterator<?> iterator = beans.iterator();
        Object bean;
        Method sameGetter = null, getter;

        while(iterator.hasNext()){
            bean = iterator.next();
            Preconditions.checkNotNull(bean, "Null value in collection.");
            try {

                if (sameGetter == null){
                    getter = bean.getClass().getMethod(gettersName);
                    sameGetter = sameTypeFlag ? getter : null;
                    ids.add((ID) (getter.invoke(bean)));
                }else{
                    ids.add((ID) (sameGetter.invoke(bean)));
                }
            } catch (ClassCastException | NoSuchMethodException | InvocationTargetException | IllegalAccessException e) {
                throw new IllegalArgumentException(e);
            }
        }

        return ids;
    }

    @Nonnull
    @Deprecated // 已废弃，方法名不规范，应使用 "Map<ID, T> mapToIdMap(@Nonnull Collection<T> entities)"
    public static<ID, T extends BaseEntity<ID>> Map<ID, T> transformEntitiesToIdMap(@Nonnull Collection<T> entities){
        if (entities.isEmpty()){
            return new HashMap<>(0);
        }

        return entities.stream()
                .collect(Collectors.toMap(BaseEntity::getId, entity -> entity));
    }

    @Nonnull
    public static<ID, T extends BaseEntity<ID>> Map<ID, T> mapCollectionToIdMap(@Nonnull Collection<T> entities){
        if (entities.isEmpty()){
            return new HashMap<>(0);
        }

        return entities.stream()
                .collect(Collectors.toMap(BaseEntity::getId, entity -> entity));
    }

    @Nonnull
    public static<FROM, TO> TO map(@Nonnull FROM object, @Nonnull Function<FROM, TO> action){
        return action.apply(object);
    }

    @Nonnull
    public static<FROM, TO> List<TO> mapCollectionToList(
            @Nonnull Collection<FROM> objects,
            @Nonnull Function<FROM, TO> action
    ){
        if (objects.isEmpty()){
            return new ArrayList<>(0);
        }

        return objects.stream()
                .map(action)
                .collect(Collectors.toList());
    }

    @Nonnull
    public static<FROM, TO> Set<TO> mapCollectionToSet(
            @Nonnull Collection<FROM> objects,
            @Nonnull Function<FROM, TO> action
    ){
        if (objects.isEmpty()){
            return new HashSet<>(0);
        }

        return objects.stream()
                .map(action)
                .collect(Collectors.toSet());
    }

    @Nonnull
    public static<K, V, T> Map<K, V> mapCollectionToMap(
            @Nonnull Collection<T> objects,
            @Nonnull Function<T, K> kAction,
            @Nonnull Function<T, V> vAction
    ){
        if (objects.isEmpty()){
            return new HashMap<>(0);
        }

        return objects.stream()
                .collect(Collectors.toMap(kAction, vAction));
    }

    @Nonnull
    public static<K, V, T> Map<K, V> mapCollectionToMap(
            @Nonnull Collection<T> objects,
            @Nonnull Function<T, K> kAction,
            @Nonnull Function<T, V> vAction,
            @Nonnull BinaryOperator<V> mergeAction
    ){
        if (objects.isEmpty()){
            return new HashMap<>(0);
        }

        return objects.stream()
                .collect(Collectors.toMap(kAction, vAction, mergeAction));
    }

    @Nonnull
    public static<T> List<T> filterCollectionToList(@Nonnull Collection<T> objects, @Nonnull Predicate<T> filter){
        if (objects.isEmpty()){
            return new ArrayList<>(0);
        }

        return objects.stream()
                .filter(filter)
                .collect(Collectors.toList());
    }

    @Nonnull
    public static<T> Set<T> filterCollectionToSet(@Nonnull Collection<T> objects, @Nonnull Predicate<T> filter){
        if (objects.isEmpty()){
            return new HashSet<>(0);
        }

        return objects.stream()
                .filter(filter)
                .collect(Collectors.toSet());
    }

    @Nonnull
    public static<FROM, TO> List<TO> filterAndMapCollectionToList(
            @Nonnull Collection<FROM> objects,
            @Nonnull Predicate<FROM> filter,
            @Nonnull Function<FROM, TO> mapAction
    ){
        if (objects.isEmpty()){
            return new ArrayList<>(0);
        }

        return objects.stream()
                .filter(filter)
                .map(mapAction)
                .collect(Collectors.toList());
    }

    @Nonnull
    public static<FROM, TO> Set<TO> filterAndMapCollectionToSet(
            @Nonnull Collection<FROM> objects,
            @Nonnull Predicate<FROM> filter,
            @Nonnull Function<FROM, TO> mapAction
    ){
        if (objects.isEmpty()){
            return new HashSet<>(0);
        }

        return objects.stream()
                .filter(filter)
                .map(mapAction)
                .collect(Collectors.toSet());
    }

    @Nonnull
    public static<T, K> Map<K, List<T>> groupingByCollection(
            @Nonnull Collection<T> objects,
            @Nonnull Function<T, K> classifier
    ){
        if (objects.isEmpty()){
            return new HashMap<>();
        }

        return objects.stream()
                .collect(Collectors.groupingBy(classifier));
    }

    @Nonnull
    public static List<Tree<Long>> mapCollectionToHuToolTreeDirectly(
            @Nonnull List<? extends TreeEntity<Long>> objects
    ){
        return BaseEntityUtils.mapCollectionToList(objects, obj -> {
            Tree<Long> tree = new Tree<>();

            tree.setId(obj.getId());
            tree.setName(obj.getName());
            tree.setParentId(obj.getParentId());

            return tree;
        });
    }

    @Nonnull
    public static List<Tree<Long>> mapCollectionToHuToolTree(
            @Nonnull List<? extends TreeEntity<Long>> objects
    ){
        return mapCollectionToHuToolTree(objects, 0L);
    }

    @Nonnull
    public static<T> List<Tree<T>> mapCollectionToHuToolTree(
            @Nonnull List<? extends TreeEntity<T>> objects, T parentId
    ){
        if (objects.isEmpty()){
            return ImmutableList.of();
        }

        return TreeUtil.build(mapCollectionToList(objects, TreeEntity::toTreeNode), parentId);
    }

    /**
     * 获取两个实体的不同字段
     * @param o1 实体1
     * @param o2 实体2
     * @return 以逗号分隔的字段名称字符串
     */
    @Nonnull
    public String getDifferentFieldNames(Object o1, Object o2) {
        return getDifferentFieldNames(o1, o2, StrPool.COMMA);
    }

    /**
     * 获取两个实体的不同字段
     * @param o1 实体1
     * @param o2 实体2
     * @param delimiter 分隔符
     * @return 以指定分隔符分隔的字段名称字符串
     */
    @Nonnull
    public String getDifferentFieldNames(Object o1, Object o2, String delimiter) {
        Map<String, Object> sourceMap = ObjectUtils.defaultIfNull(BeanUtils.beanToMap(o1), ImmutableMap.of());
        Map<String, Object> targetMap = ObjectUtils.defaultIfNull(BeanUtils.beanToMap(o2), ImmutableMap.of());
        Map<String, MapDifference.ValueDifference<Object>> diff = Maps.difference(sourceMap, targetMap).entriesDiffering();

        if (diff.isEmpty()) {
            return "";
        }

        return String.join(delimiter, diff.keySet());
    }
}
