package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.entity.domain.ProjectTaskProgress;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface ProjectTaskProgressMapper extends BaseMapper<ProjectTaskProgress> {

    List<ProjectTaskProgress> findList(@Param("filter") Map<String, Object> filter);

    List<ProjectTaskProgress> getAllProgress(@Param("filter") Map<String, Object> filter);

    List<ProjectTaskProgress> findByTaskId(Long taskId);

    boolean existsById(Long id);
}
