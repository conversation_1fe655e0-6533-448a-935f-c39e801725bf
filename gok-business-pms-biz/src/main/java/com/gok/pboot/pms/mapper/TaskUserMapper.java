package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.entity.domain.TaskUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 任务-人员多对多关联 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-19
 */
@Deprecated
@Mapper
public interface TaskUserMapper extends BaseMapper<TaskUser> {


    /**
     * 逻辑删除
     *
     * @param id 唯一标识
     * @return
     */
    int deleteByLogic(@Param("id") Long id);

    /**
     * 批量插入
     *
     * @param poList  实体集合
     */
    void batchSave(@Param("poList") List<TaskUser> poList);



    /**
     * 批量修改
     *
     * @param list 实体集合
     */
    void batchUpdate(@Param("list") List<TaskUser> list);

    /**
    * 批量逻辑删除
    *
    * @param list id集合
    */
    void batchDel(@Param("list") List<Long> list);

    /**
    * 批量修改不为空字段
    *
    * @param list id集合
    */
    void updateBatch(@Param("list") List<Long> list);

    void deleteBatchUserIds(@Param("list")List<Long> deleteIds,@Param("taskId")Long taskId);

    /**
     * ~ 根据项目ID列表获取该项目下的所有人员 ~
     * @param projectIds 项目ID列表
     * @return java.util.List<java.lang.Long>
     * <AUTHOR>
     * @date 2022/10/26 9:36
     */
    List<Long> findAllUserIdByProjectIds(@Param("projectIds") Collection<Long> projectIds);

    List<TaskUser> findByTaskId(@Param("taskId") Long taskId);
}
