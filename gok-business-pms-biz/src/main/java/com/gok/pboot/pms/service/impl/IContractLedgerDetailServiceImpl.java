package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.base.admin.dto.IdsDTO;
import com.gok.base.admin.feign.RemotePaymentService;
import com.gok.base.admin.vo.ProjectPaymentClaimVO;
import com.gok.components.common.util.R;
import com.gok.pboot.pms.Util.DecimalFormatUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.entity.domain.ContractLedger;
import com.gok.pboot.pms.entity.domain.ContractLedgerDetail;
import com.gok.pboot.pms.entity.vo.ContractPaymentVo;
import com.gok.pboot.pms.entity.vo.OaFileInfoVo;
import com.gok.pboot.pms.enumeration.ContractPaymentStatusEnum;
import com.gok.pboot.pms.enumeration.InvoiceTaxRateEnum;
import com.gok.pboot.pms.mapper.ContractLedgerDetailMapper;
import com.gok.pboot.pms.service.IContractLedgerDetailService;
import com.gok.pboot.pms.service.IContractLedgerService;
import com.gok.pboot.pms.service.IPmsDocImageFileService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
public class IContractLedgerDetailServiceImpl extends ServiceImpl<ContractLedgerDetailMapper, ContractLedgerDetail>
        implements IContractLedgerDetailService {

    private final IContractLedgerService contractLedgerService;
    private final IPmsDocImageFileService pmsDocImageFileService;
    private final RemotePaymentService remotePaymentService;

    @Override
    public Map<Long, List<ContractPaymentVo>> getContractPaymentInfoVoList(List<Long> ids) {
        IdsDTO idsDTO = new IdsDTO();
        idsDTO.setIds(ids);
        R<List<ProjectPaymentClaimVO>> claimR = remotePaymentService.getClaim(idsDTO);
        List<ProjectPaymentClaimVO> projectPaymentClaimVOS = claimR.getData();
        Map<Long, List<ProjectPaymentClaimVO>> projectPaymentClaimVOMap
                = projectPaymentClaimVOS.stream().filter(s -> s.getContractPaymentId() != null)
                .collect(Collectors.groupingBy(ProjectPaymentClaimVO::getContractPaymentId));

        Map<Long, List<ContractPaymentVo>> contractLedgerDetailsMap = new HashMap<>();

        List<ContractLedger> contractLedgerList = contractLedgerService.listByIds(ids);
        Map<Integer, List<ContractLedgerDetail>> contractLedgerDetailByMainIdsMap
                = this.getContractLedgerDetailByMainIds(ids);
        if (CollUtil.isNotEmpty(contractLedgerList)) {
            for (ContractLedger contractLedger : contractLedgerList) {
                List<ContractLedgerDetail> contractLedgerDetails =
                        contractLedgerDetailByMainIdsMap.get(Integer.valueOf(contractLedger.getId().toString()));
                List<ContractPaymentVo> contractPaymentVoList = BeanUtil.copyToList(contractLedgerDetails, ContractPaymentVo.class);
                if (CollUtil.isNotEmpty(contractPaymentVoList)) {
                    for (ContractPaymentVo c : contractPaymentVoList) {
                        //款项状态 采购合同 其他
                        if (Optional.ofNullable(contractLedger.getHtxl()).isPresent()
                                && (contractLedger.getHtxl() == 1 || contractLedger.getHtxl() == 2)) {
                            c.setSjsfkjehs(null);
                            c.setSjsfkrq(StringUtils.EMPTY);
                            if (CollUtil.isNotEmpty(projectPaymentClaimVOMap)) {
                                List<ProjectPaymentClaimVO> projectPaymentClaimVOS1 = projectPaymentClaimVOMap.get(c.getId());
                                if (CollUtil.isNotEmpty(projectPaymentClaimVOS1)) {
                                    BigDecimal sjsfkjehs = new BigDecimal(0);
                                    projectPaymentClaimVOS1 = projectPaymentClaimVOS1.stream()
                                            .sorted(Comparator.comparing(ProjectPaymentClaimVO::getPaymentDate, Comparator.nullsLast(String::compareTo)).reversed())
                                            .collect(Collectors.toList());
                                    c.setSjsfkrq(projectPaymentClaimVOS1.get(0).getPaymentDate());
                                    for (ProjectPaymentClaimVO projectPaymentClaimVO : projectPaymentClaimVOS1) {
                                        sjsfkjehs =
                                                sjsfkjehs.add(new BigDecimal(StringUtils.isNotBlank(projectPaymentClaimVO.getPaymentAmount()) ?
                                                        projectPaymentClaimVO.getPaymentAmount() : "0"));
                                    }
                                    c.setSjsfkjehs(sjsfkjehs);
                                }

                            }
                            if (c.getSjsfkjehs() != null && c.getSjsfkjehs().compareTo(new BigDecimal(0)) > 0) {
                                c.setSkzt(ContractPaymentStatusEnum.RETURN.getValue());
                            } else {
                                c.setSkzt(ContractPaymentStatusEnum.NOT_RETURN.getValue());
                            }
                        } else {
                            if (c.getSjsfkjehs() != null && c.getSjsfkjehs().compareTo(new BigDecimal(0)) > 0) {
                                c.setSkzt(ContractPaymentStatusEnum.RECEIVED.getValue());
                            } else {
                                c.setSkzt(ContractPaymentStatusEnum.NOT_RECEIVED.getValue());
                            }
                        }
                        c.setSkztTxt(EnumUtils.getNameByValue(ContractPaymentStatusEnum.class, c.getSkzt()));
                        String sl = EnumUtils.getNameByValue(InvoiceTaxRateEnum.class, c.getSl());
                        c.setSl(StringUtils.isBlank(sl) ? new Integer(0) : Integer.parseInt(sl));
                        c.setSlTxt(DecimalFormatUtil.setThousandthAndTwoDecimal(new BigDecimal(c.getSl()), DecimalFormatUtil.ZERO) + "%");
                        c.setSkjeTxt(DecimalFormatUtil.setThousandthAndTwoDecimal(c.getSkje(), DecimalFormatUtil.ZERO));
                        c.setSfkjebhsTxt(DecimalFormatUtil.setThousandthAndTwoDecimal(c.getSfkjebhs(), DecimalFormatUtil.ZERO));
                        c.setSjsfkjehsTxt(DecimalFormatUtil.setThousandthAndTwoDecimal(c.getSjsfkjehs(), DecimalFormatUtil.ZERO));
                        //【款项金额】-【实际款项金额】；
                        c.setDsfkjeTxt(DecimalFormatUtil.setThousandthAndTwoDecimal((c.getSkje() == null ? BigDecimal.ZERO : c.getSkje())
                                .subtract((c.getSjsfkjehs() == null ? BigDecimal.ZERO : c.getSjsfkjehs())), DecimalFormatUtil.ZERO));
                    }
                }

                contractLedgerDetailsMap.put(contractLedger.getId(), contractPaymentVoList);
            }
        }
        return contractLedgerDetailsMap;
    }

    @Override
    public List<ContractPaymentVo> getContractPaymentInfoVoList(Long id) {
        IdsDTO idsDTO = new IdsDTO();
        idsDTO.setIds(Collections.singletonList(id));
        Map<Long, List<ProjectPaymentClaimVO>> projectPaymentClaimVOMap = new HashMap<>();
        try {
            R<List<ProjectPaymentClaimVO>> claimR = remotePaymentService.getClaim(idsDTO);
            List<ProjectPaymentClaimVO> projectPaymentClaimVOS = claimR.getData();
            log.info("款项认领数据" + projectPaymentClaimVOS);
            projectPaymentClaimVOMap
                    = projectPaymentClaimVOS.stream()
                    .filter(s -> s.getContractPaymentId() != null)
                    .collect(Collectors.groupingBy(ProjectPaymentClaimVO::getContractPaymentId));
        } catch (Exception e) {
            log.error("请求款项认领数据异常" + e);
        }
        ContractLedger contractLedger = contractLedgerService.getById(id);
        QueryWrapper<ContractLedgerDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ContractLedgerDetail::getMainid, id);
        List<ContractLedgerDetail> contractLedgerDetails = baseMapper.selectList(queryWrapper);
        List<ContractPaymentVo> contractPaymentVoList = BeanUtil.copyToList(contractLedgerDetails, ContractPaymentVo.class);

        for (ContractPaymentVo c : contractPaymentVoList) {
            if (Optional.ofNullable(c.getKxbl()).isPresent()) {
                c.setKxbl(c.getKxbl().multiply(new BigDecimal(100)));
            }

            if (Optional.ofNullable(c.getDdfj()).isPresent()) {
                OaFileInfoVo oaFileInfoVo = new OaFileInfoVo();
                oaFileInfoVo.setRequestId(String.valueOf(contractLedger.getHthqlc()));
                oaFileInfoVo.setFileId(c.getDdfj());
                oaFileInfoVo.setImageFileId(c.getDdfjImagefileid());
                c.setDdfjList(pmsDocImageFileService.getOaFileVoList(c.getDdfj()));
            }
            if (Optional.ofNullable(c.getJsdfj()).isPresent()) {
                OaFileInfoVo oaFileInfoVo = new OaFileInfoVo();
                oaFileInfoVo.setRequestId(String.valueOf(contractLedger.getHthqlc()));
                oaFileInfoVo.setFileId(c.getJsdfj());
                oaFileInfoVo.setImageFileId(c.getJsdfjImagefileid());
                c.setJsdfjList(pmsDocImageFileService.getOaFileVoList(c.getJsdfj()));
            }
            //款项状态 销售合同=1 协议合同=2
            if (Optional.ofNullable(contractLedger.getHtxl()).isPresent()
                    && (contractLedger.getHtxl() == 1 || contractLedger.getHtxl() == 2)) {
                c.setSjsfkjehs(null);
                c.setSjsfkrq(StringUtils.EMPTY);
                if (CollUtil.isNotEmpty(projectPaymentClaimVOMap)) {
                    List<ProjectPaymentClaimVO> projectPaymentClaimVOS1 = projectPaymentClaimVOMap.get(c.getId());
                    if (CollUtil.isNotEmpty(projectPaymentClaimVOS1)) {
                        BigDecimal sjsfkjehs = new BigDecimal(0);
                        projectPaymentClaimVOS1 = projectPaymentClaimVOS1.stream().
                                sorted(Comparator.comparing(ProjectPaymentClaimVO::getPaymentDate).reversed())
                                .collect(Collectors.toList());
                        c.setSjsfkrq(projectPaymentClaimVOS1.get(0).getPaymentDate());
                        for (ProjectPaymentClaimVO projectPaymentClaimVO : projectPaymentClaimVOS1) {
                            sjsfkjehs = sjsfkjehs.add(new BigDecimal(
                                    StringUtils.isNotBlank(projectPaymentClaimVO.getClaimMoney()) ?
                                            projectPaymentClaimVO.getClaimMoney() : "0"));
                        }
                        c.setSjsfkjehs(sjsfkjehs);
                    }
                }
                if (c.getSjsfkjehs() != null && c.getSjsfkjehs().compareTo(new BigDecimal(0)) > 0) {
                    c.setSkzt(ContractPaymentStatusEnum.RETURN.getValue());
                } else {
                    c.setSkzt(ContractPaymentStatusEnum.NOT_RETURN.getValue());
                }
            } else {
                if (c.getSjsfkjehs() != null && c.getSjsfkjehs().compareTo(new BigDecimal(0)) > 0) {
                    c.setSkzt(ContractPaymentStatusEnum.RECEIVED.getValue());
                } else {
                    c.setSkzt(ContractPaymentStatusEnum.NOT_RECEIVED.getValue());
                }
            }
            c.setSkztTxt(EnumUtils.getNameByValue(ContractPaymentStatusEnum.class, c.getSkzt()));
            String sl = EnumUtils.getNameByValue(InvoiceTaxRateEnum.class, c.getSl());
            c.setSl(StringUtils.isBlank(sl) ? new Integer(0) : Integer.parseInt(sl));
            c.setSlTxt(DecimalFormatUtil.setThousandthAndTwoDecimal(new BigDecimal(c.getSl()), DecimalFormatUtil.ZERO) + "%");
            c.setSkjeTxt(DecimalFormatUtil.setThousandthAndTwoDecimal(c.getSkje(), DecimalFormatUtil.ZERO));
            c.setSfkjebhsTxt(DecimalFormatUtil.setThousandthAndTwoDecimal(c.getSfkjebhs(), DecimalFormatUtil.ZERO));
            c.setSjsfkjehsTxt(DecimalFormatUtil.setThousandthAndTwoDecimal(c.getSjsfkjehs(), DecimalFormatUtil.ZERO));
            //【款项金额】-【实际款项金额】；
            c.setDsfkjeTxt(DecimalFormatUtil.setThousandthAndTwoDecimal((c.getSkje() == null ? BigDecimal.ZERO : c.getSkje())
                    .subtract((c.getSjsfkjehs() == null ? BigDecimal.ZERO : c.getSjsfkjehs())), DecimalFormatUtil.ZERO));
        }
        return contractPaymentVoList;
    }

    public Map<Integer, List<ContractLedgerDetail>> getContractLedgerDetailByMainIds(List<Long> ids) {
        QueryWrapper<ContractLedgerDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(ContractLedgerDetail::getMainid, ids);
        List<ContractLedgerDetail> detailList = this.list(queryWrapper);
        return detailList.stream().collect(Collectors.groupingBy(ContractLedgerDetail::getMainid));
    }
}
