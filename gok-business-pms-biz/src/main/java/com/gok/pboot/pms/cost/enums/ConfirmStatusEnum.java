package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 确认状态枚举
 *
 * <AUTHOR>
 * @create 2025/02/18
 **/
@Getter
@AllArgsConstructor
public enum ConfirmStatusEnum implements ValueEnum<Integer> {

    /**
     * 待确认
     */
    AWAIT_CONFIRM(0, "待确认"),

    /**
     * 已确认
     */
    CONFIRM(1, "已确认"),

    /**
     * 部分确认
     */
    PART_CONFIRM(2, "部分确认");

    private final Integer value;

    private final String name;

}
