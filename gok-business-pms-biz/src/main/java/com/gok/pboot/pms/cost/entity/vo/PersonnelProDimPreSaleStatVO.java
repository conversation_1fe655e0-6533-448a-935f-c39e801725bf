package com.gok.pboot.pms.cost.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 人员工单看板-项目维度售前工单统计VO
 *
 * <AUTHOR>
 * @date 2025/05/14
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PersonnelProDimPreSaleStatVO  extends PersonnelPreSaleStatBaseVO{


    /**
     * 人员工号
     */
    @ExcelProperty("员工工号")
    private String workCode;

    /**
     * 人员姓名
     */
    @ExcelProperty("员工姓名")
    private String employeeName;

    /**
     * 项目ID
     */
    @ExcelIgnore
    private Long projectId;

    /**
     * 项目编号
     */
    @ExcelProperty("项目编号")
    private String projectNo;

    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    private String projectName;

    /**
     * 单项目个人评分
     */
    @ExcelProperty("单项目个人评分")
    private BigDecimal singleProjectScore;

}
