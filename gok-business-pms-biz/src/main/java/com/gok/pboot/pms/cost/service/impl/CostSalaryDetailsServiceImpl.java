package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.cost.entity.domain.CostSalaryDetails;
import com.gok.pboot.pms.cost.entity.dto.CostSalaryDTO;
import com.gok.pboot.pms.cost.mapper.CostSalaryDetailsMapper;
import com.gok.pboot.pms.cost.service.ICostSalaryDetailsService;
import com.gok.pboot.pms.enumeration.CostSalaryRelateTypeEnum;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 成本明细表 服务实现类
 *
 * <AUTHOR>
 * @date 2025/05/08
 */
@Service
@RequiredArgsConstructor
public class CostSalaryDetailsServiceImpl extends ServiceImpl<CostSalaryDetailsMapper, CostSalaryDetails> implements ICostSalaryDetailsService {
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveOrUpdate(Collection<CostSalaryDTO> salaryDTOList) {
        if (CollUtil.isEmpty(salaryDTOList)) {
            return;
        }
        List<CostSalaryDetails> existSalaryDetails = baseMapper.getList(salaryDTOList);
        Map<String, Long> existSalaryDetailsIdMap = existSalaryDetails.stream()
                .collect(Collectors.toMap(CostSalaryDetailsServiceImpl::buildRelateKey, CostSalaryDetails::getId));

        List<CostSalaryDetails> saveList = new ArrayList<>();

        List<CostSalaryDetails> updateList = new ArrayList<>();

        for (CostSalaryDTO salaryDTO : salaryDTOList) {
            CostSalaryDetails salaryDetails = buildEntity(salaryDTO);
            String relateKey = buildRelateKey(salaryDetails);
            if (existSalaryDetailsIdMap.containsKey(relateKey)) {
                salaryDetails.setId(existSalaryDetailsIdMap.get(relateKey));
                updateList.add(BaseBuildEntityUtil.buildUpdate(salaryDetails));
            } else {
                saveList.add(BaseBuildEntityUtil.buildInsert(salaryDetails));
            }
        }
        saveBatch(saveList);
        updateBatchById(updateList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeBatchByRelateIds(Collection<Long> relateIds, CostSalaryRelateTypeEnum costSalaryRelateTypeEnum) {
        if (CollUtil.isEmpty(relateIds)) {
            return;
        }
        lambdaUpdate().in(CostSalaryDetails::getRelateId, relateIds)
                .eq(CostSalaryDetails::getRelateType, costSalaryRelateTypeEnum.getValue())
                .remove();
    }

    @NotNull
    private static String buildRelateKey(CostSalaryDetails e) {
        return e.getRelateId() + StrPool.DASHED + e.getRelateType();
    }

    private static CostSalaryDetails buildEntity(CostSalaryDTO costSalaryDTO) {
        return new CostSalaryDetails()
                .setRelateId(costSalaryDTO.getRelateId())
                .setRelateType(costSalaryDTO.getRelateType())
                .setConfigId(costSalaryDTO.getConfigLevelPriceId())
                .setSalary(costSalaryDTO.getSalary())
                .setSocialSecurity(costSalaryDTO.getSocialSecurity())
                .setHousingFund(costSalaryDTO.getHousingFund())
                .setDisabilityFee(costSalaryDTO.getDisabilityFee())
                .setWeekendOvertimePay(costSalaryDTO.getWeekendOvertimePay())
                .setHolidayOvertimePay(costSalaryDTO.getHolidayOvertimePay());
    }

} 