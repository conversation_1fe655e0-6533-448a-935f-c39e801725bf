package com.gok.pboot.pms.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.Util.BigDecimalUtils;
import com.gok.pboot.pms.Util.CommonUtils;
import com.gok.pboot.pms.Util.DailyPaperDateUtils;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.DailyPaperEntry;
import com.gok.pboot.pms.entity.Holiday;
import com.gok.pboot.pms.entity.domain.Roster;
import com.gok.pboot.pms.entity.vo.ProjectTaskFindPageVO;
import com.gok.pboot.pms.entity.vo.ProjectWorkHoursVo;
import com.gok.pboot.pms.entity.vo.WorkHoursDetailVO;
import com.gok.pboot.pms.enumeration.DimensionEnum;
import com.gok.pboot.pms.enumeration.EmployeeTypeEnum;
import com.gok.pboot.pms.enumeration.WorkTypeEnum;
import com.gok.pboot.pms.mapper.*;
import com.gok.pboot.pms.service.IProjectWorkHoursService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <p>
 * 项目工时统计 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-13
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectWorkHoursImpl extends ServiceImpl<ProjectWorkHoursMapper, DailyPaperEntry> implements IProjectWorkHoursService {

    private final RosterMapper rosterMapper;

    private final ProjectTaskeMapper projectTaskeMapper;

    private final CompensatoryLeaveDataMapper compensatoryLeaveDataMapper;

    private final HolidayMapper holidayMapper;

    /**
     * 查询类型 0-人员维度 1-任务维度
     */
    private static final String QUERY_DIMENSION = "dimension";

    @Override
    public ProjectWorkHoursVo countWorkHours(PageRequest pageRequest, Map<String, Object> filter) {
        // 计算总工时
        ProjectWorkHoursVo projectWorkHoursVo = calculateAvgWorkHours2(filter);
        // 根据项目ID和时间分页获取已通过和待审核任务工时信息（根据维度进行分组）
        Page<ProjectTaskFindPageVO> page = new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize());
        Page<ProjectTaskFindPageVO> pageResult = baseMapper.findGroupByDimension(page, filter);
        projectWorkHoursVo.setTaskOrUserPageList(pageResult);
        List<ProjectTaskFindPageVO> records = pageResult.getRecords();
        if (CollUtil.isEmpty(records)) {
            return projectWorkHoursVo;
        }
        // 根据维度计算工时
        calculateByHoursDimension(projectWorkHoursVo, filter);
        // 工时转为人天
        convertHoursToDays(records);
        return projectWorkHoursVo;
    }

    /**
     * 汇总项目分摊工时
     *
     * @param filter 查询参数
     * @return 工时统计结果
     */
    private ProjectWorkHoursVo calculateAvgWorkHours2(Map<String, Object> filter) {
        ProjectWorkHoursVo avgWorkHoursVo = baseMapper.calculateWorkHours(filter);
        if (!Optional.ofNullable(avgWorkHoursVo).isPresent()) {
            avgWorkHoursVo = new ProjectWorkHoursVo();
        }

        BigDecimal avgNormalHours = BigDecimalUtils.bigDecimalNullToZero(avgWorkHoursVo.getAvgNormalHours());
        BigDecimal restOvertimeHours = BigDecimalUtils.bigDecimalNullToZero(avgWorkHoursVo.getRestOvertimeHours());
        BigDecimal holidayOvertimeHours = BigDecimalUtils.bigDecimalNullToZero(avgWorkHoursVo.getHolidayOvertimeHours());
        BigDecimal txHours = BigDecimalUtils.bigDecimalNullToZero(avgWorkHoursVo.getTxHours());
        // 项目分摊工时：正常工时+休息日加班+节假日加班+调休工时
        BigDecimal avgSumHours = avgNormalHours.add(restOvertimeHours).add(holidayOvertimeHours).add(txHours);
        avgWorkHoursVo.setAvgSumHours(avgSumHours);

        // 总统计工时转为人天
        avgWorkHoursVo.setAvgAddedHours(CommonUtils.unitConversion(avgWorkHoursVo.getAvgAddedHours()));
        avgWorkHoursVo.setAvgNormalHours(CommonUtils.unitConversion(avgWorkHoursVo.getAvgNormalHours()));
        avgWorkHoursVo.setWorkOvertimeHours(CommonUtils.unitConversion(avgWorkHoursVo.getWorkOvertimeHours()));
        avgWorkHoursVo.setRestOvertimeHours(CommonUtils.unitConversion(avgWorkHoursVo.getRestOvertimeHours()));
        avgWorkHoursVo.setHolidayOvertimeHours(CommonUtils.unitConversion(avgWorkHoursVo.getHolidayOvertimeHours()));
        avgWorkHoursVo.setDshHours(CommonUtils.unitConversion(avgWorkHoursVo.getDshHours()));
        avgWorkHoursVo.setTxHours(CommonUtils.unitConversion(avgWorkHoursVo.getTxHours()));
        avgWorkHoursVo.setAvgSumHours(CommonUtils.unitConversion(avgWorkHoursVo.getAvgSumHours()));
        return avgWorkHoursVo;
    }

    /**
     * 根据不同维度选择计算工时数据
     *
     * @param projectWorkHoursVo 工时统计结果
     * @param filter             参数列表
     */
    private void calculateByHoursDimension(ProjectWorkHoursVo projectWorkHoursVo, Map<String, Object> filter) {
        // 选择的维度
        Integer dimension = (Integer) filter.get(QUERY_DIMENSION);
        if (DimensionEnum.TASK.getValue().equals(dimension)) {
            // 任务维度统计工时
            calculateHoursByTaskDimension(projectWorkHoursVo, filter);
        } else if (DimensionEnum.USER.getValue().equals(dimension)) {
            // 人员维度统计工时
            calculateHoursByUserDimension(projectWorkHoursVo, filter);
        }
    }

    /**
     * 从人员维度统计工时
     *
     * @param projectWorkHoursVo 工时统计结果
     * @param filter             查询参数
     */
    private void calculateHoursByUserDimension(ProjectWorkHoursVo projectWorkHoursVo, Map<String, Object> filter) {
        List<ProjectTaskFindPageVO> records = projectWorkHoursVo.getTaskOrUserPageList().getRecords();
        // 人员维度
        Set<Long> userIds = records.stream().map(ProjectTaskFindPageVO::getUserId).collect(Collectors.toSet());

        if (!userIds.isEmpty()) {
            Map<Long, Roster> userIdMap = rosterMapper.selectBatchIds(userIds)
                    .stream().collect(Collectors.toMap(Roster::getOaId, o -> {
                        if (o.getEmployeeType() == null) {
                            o.setEmployeeType(-1);
                        }
                        return o;
                    }));
            for (ProjectTaskFindPageVO record : records) {
                Long userId = record.getUserId();
                // 添加项目分摊工时：正常工时+休息日加班+节假日加班+调休工时
                BigDecimal normalHours = BigDecimalUtils.bigDecimalNullToZero(record.getNormalHours());
                BigDecimal restOvertimeHours = BigDecimalUtils.bigDecimalNullToZero(record.getRestOvertimeHours());
                BigDecimal holidayOvertimeHours = BigDecimalUtils.bigDecimalNullToZero(record.getHolidayOvertimeHours());
                BigDecimal txHours = BigDecimalUtils.bigDecimalNullToZero(record.getTxHours());
                record.setTxHours(txHours);
                BigDecimal sumHours = normalHours.add(restOvertimeHours).add(holidayOvertimeHours).add(txHours);
                record.setAvgSumHours(sumHours);
                // 设置员工类型
                Roster user = userIdMap.get(userId);
                Integer employeeType = user == null ? -1 : user.getEmployeeType();
                record.setEmployeeType(employeeType);
                record.setEmployeeTypeTxt(EnumUtils.getNameByValue(EmployeeTypeEnum.class, employeeType));
                if (!StringUtils.hasLength(record.getUserRealName())) {
                    // 只包含调休数据（填充默认数据）
                    record.setUserRealName(user == null ? "异常用户" : user.getAliasName());
                }
            }
        }

    }

    /**
     * 从任务维度统计工时
     *
     * @param projectWorkHoursVo 工时统计结果
     */
    private void calculateHoursByTaskDimension(ProjectWorkHoursVo projectWorkHoursVo, Map<String, Object> filter) {
        List<ProjectTaskFindPageVO> records = projectWorkHoursVo.getTaskOrUserPageList().getRecords();

        // 任务维度
        Set<Long> taskIds = records.stream().map(ProjectTaskFindPageVO::getTaskId).collect(Collectors.toSet());
        if (!taskIds.isEmpty()) {
            // 获取负责人信息
            Map<Long, ProjectTaskFindPageVO> taskLeaderMap = projectTaskeMapper.findTaskLeaders(taskIds)
                    .stream().collect(Collectors.toMap(ProjectTaskFindPageVO::getTaskId, o -> o));
            records.forEach(record -> {
                Long taskId = record.getTaskId();
                // 添加项目分摊工时：正常工时+休息日加班+节假日加班 （任务维度没有调休工时）
                BigDecimal normalHours = BigDecimalUtils.bigDecimalNullToZero(record.getNormalHours());
                BigDecimal restOvertimeHours = BigDecimalUtils.bigDecimalNullToZero(record.getRestOvertimeHours());
                BigDecimal holidayOvertimeHours = BigDecimalUtils.bigDecimalNullToZero(record.getHolidayOvertimeHours());
                BigDecimal sumHours = normalHours.add(restOvertimeHours).add(holidayOvertimeHours);
                record.setAvgSumHours(sumHours);
                // 添加工作类型描述信息
                record.setWorkTypeTxt(EnumUtils.getNameByValue(WorkTypeEnum.class, record.getWorkType()));
                // 添加负责人信息
                ProjectTaskFindPageVO taskLeaders = taskLeaderMap.get(taskId);
                if (taskLeaders != null) {
                    record.setTaskLeaderVOList(taskLeaders.getTaskLeaderVOList());
                }
            });
        }
    }

    /**
     * 所有工时数据由小时->天
     *
     * @param records 工时展示对象
     */
    private static void convertHoursToDays(List<ProjectTaskFindPageVO> records) {
        // 分组统计工时转为人天
        records.forEach(record -> {
            record.setNormalHours(CommonUtils.unitConversion(record.getNormalHours()));
            record.setAddedHours(CommonUtils.unitConversion(record.getAddedHours()));
            record.setWorkOvertimeHours(CommonUtils.unitConversion(record.getWorkOvertimeHours()));
            record.setRestOvertimeHours(CommonUtils.unitConversion(record.getRestOvertimeHours()));
            record.setHolidayOvertimeHours(CommonUtils.unitConversion(record.getHolidayOvertimeHours()));
            record.setTxHours(CommonUtils.unitConversion(record.getTxHours()));
            record.setAvgSumHours(CommonUtils.unitConversion(record.getAvgSumHours()));
            record.setDshHours(CommonUtils.unitConversion(record.getDshHours()));
        });
    }

    @Override
    public List<ProjectTaskFindPageVO> getTaskByTaskNameLike(Long projectId) {
        List<ProjectTaskFindPageVO> list =
                baseMapper.getTaskByTaskNameLike(projectId);
        if (CollUtil.isNotEmpty(list)) {
            list = list.stream()
                    .filter(distinctByKey(p -> p.getTaskId()))
                    .collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public Page<WorkHoursDetailVO> getHoursDetail(PageRequest pageRequest, Map<String, Object> filter) {
        if (filter.get("dimensionId") == null) {
            return new Page<>();
        }
        Page<WorkHoursDetailVO> page = new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize());
        // 根据选择的维度和查询条件获取工时详情
        Page<WorkHoursDetailVO> workDetails = baseMapper.getDetailByDimension(page, filter);
        List<WorkHoursDetailVO> records = workDetails.getRecords();
        if (CollUtil.isEmpty(records)) {
            return workDetails;
        }
        // 获取昨日计划
        List<WorkHoursDetailVO> yesterdayPlanList = baseMapper.getYesterdayPlan2(filter, records);
        Map<String, String> yesterdayPlanMap = yesterdayPlanList.stream().collect(
                Collectors.toMap(r -> "" + r.getUserId() + r.getTaskId() + r.getSubmissionDate().plusDays(1),
                        WorkHoursDetailVO::getYesterdayPlan, (a, b) -> b));

        Set<Long> userIds = records.stream().map(WorkHoursDetailVO::getUserId).collect(Collectors.toSet());
        // 用户id-员工类型映射
        Map<Long, Integer> employeeTypeMap = rosterMapper.selectBatchIds(userIds)
                .stream().collect(Collectors.toMap(Roster::getOaId, Roster::getEmployeeType));

        Set<LocalDate> dates = records.stream().map(WorkHoursDetailVO::getSubmissionDate).collect(Collectors.toSet());
        // 日期-日期类型（工作日/休息日/节假日）映射
        Map<LocalDate, Integer> holidayTypeMap = CollUtil.isEmpty(dates) ? new HashMap<>() :
                holidayMapper.findBatchHolidayType(dates)
                        .stream().collect(Collectors.toMap(Holiday::getDayDate, Holiday::getHolidayType));
        records.forEach(record -> {
            LocalDate date = record.getSubmissionDate();
            record.setHolidayType(holidayTypeMap.get(date));
            record.setWorkTypeTxt(EnumUtils.getNameByValue(WorkTypeEnum.class, record.getWorkType()));
            Integer employeeType = employeeTypeMap.get(record.getUserId());
            record.setEmployeeType(employeeType);
            record.setEmployeeTypeTxt(EnumUtils.getNameByValue(EmployeeTypeEnum.class, employeeType));
            record.setSubmissionDateFormatted(DailyPaperDateUtils.asDateString(date));
            record.setYesterdayPlan(yesterdayPlanMap.get(getYesterdayPlanMapKey(record)));
            // 小时 -> 人天
            record.setNormalHours(CommonUtils.unitConversion(record.getNormalHours()));
            record.setAddedHours(CommonUtils.unitConversion(record.getAddedHours()));
            record.setWorkOvertimeHours(CommonUtils.unitConversion(record.getWorkOvertimeHours()));
            record.setRestOvertimeHours(CommonUtils.unitConversion(record.getRestOvertimeHours()));
            record.setHolidayOvertimeHours(CommonUtils.unitConversion(record.getHolidayOvertimeHours()));
        });
        return workDetails;
    }

    /**
     * @param record 明日计划内容
     * @return 明日计划表的key
     */
    private String getYesterdayPlanMapKey(WorkHoursDetailVO record) {
        return "" + record.getUserId() + record.getTaskId() + record.getSubmissionDate();
    }

    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }

    public ProjectWorkHoursVo calculateAvgWorkHours(Map<String, Object> filter) {
        ProjectWorkHoursVo avgWorkHoursVo = baseMapper.calculateWorkHours(filter);
        if (!Optional.ofNullable(avgWorkHoursVo).isPresent()) {
            return new ProjectWorkHoursVo();
        }
        avgWorkHoursVo.setAvgAddedHours(CommonUtils.unitConversion(avgWorkHoursVo.getAvgAddedHours()));
        avgWorkHoursVo.setAvgNormalHours(CommonUtils.unitConversion(avgWorkHoursVo.getAvgNormalHours()));
        avgWorkHoursVo.setAvgSumHours((avgWorkHoursVo.getAvgNormalHours().add(avgWorkHoursVo.getAvgAddedHours())));
        return avgWorkHoursVo;
    }

    @Override
    public List<ProjectWorkHoursVo> batchCalculateAvgWorkHours(List<Map<String, Object>> filterList) {
        if (CollUtil.isEmpty(filterList)) {
            return new ArrayList<>();
        }
        List<ProjectWorkHoursVo> list = baseMapper.batchCalculateAvgWorkHours(filterList);
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }

        list.forEach(avgWorkHoursVo -> {
            if (Optional.ofNullable(avgWorkHoursVo).isPresent()) {
                avgWorkHoursVo.setAvgAddedHours(BigDecimal.ZERO.equals(avgWorkHoursVo.getAvgAddedHours()) ? BigDecimal.ZERO : CommonUtils.unitConversion(avgWorkHoursVo.getAvgAddedHours()));
                avgWorkHoursVo.setAvgNormalHours(BigDecimal.ZERO.equals(avgWorkHoursVo.getAvgNormalHours()) ? BigDecimal.ZERO : CommonUtils.unitConversion(avgWorkHoursVo.getAvgNormalHours()));
                avgWorkHoursVo.setAvgSumHours((avgWorkHoursVo.getAvgNormalHours().add(avgWorkHoursVo.getAvgAddedHours())));
            }
        });

        return list;
    }

}
