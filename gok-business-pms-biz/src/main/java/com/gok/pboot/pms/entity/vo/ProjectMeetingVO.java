package com.gok.pboot.pms.entity.vo;

import cn.hutool.core.bean.BeanUtil;
import com.gok.pboot.pms.entity.domain.ProjectMeeting;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.Optional;

/**
 * 会议详情 Vo
 *
 * <AUTHOR>
 * @since 2023-07-13
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectMeetingVO {

    /**
     * 会议名称
     */
    private String name;

    /**
     * 召集人id
     */
    private Long convenerId;

    /**
     * 召集人
     */
    private String convener;

    /**
     * 会议日期
     * 格式为：yyyy-MM-dd
     */
    private LocalDate meetingDate;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 记录人id
     */
    private Long recorderId;

    /**
     * 记录人
     */
    private String recorder;

    /**
     * 会议地点
     */
    private String place;

    /**
     * 参会人员
     */
    private String member;

    /**
     * 会议目标
     */
    private String objective;

    /**
     * 会议过程
     */
    private String process;

    /**
     * 会议决议
     */
    private String resolution;

    /**
     * 待办事项
     */
    private String backlog;

    /**
     * 文件id集合
     */
    private String docIds;

    /**
     * 附件名
     */
    private String docNames;

    public static ProjectMeetingVO buildByEntity(ProjectMeeting entity) {
        ProjectMeetingVO vo = new ProjectMeetingVO();
        if (!Optional.ofNullable(entity).isPresent()) {
            return vo;
        }
        BeanUtil.copyProperties(entity, vo);
        return vo;
    }

}
