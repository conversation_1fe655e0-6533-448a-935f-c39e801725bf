package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.Getter;

/**
 * 是否客户承担Enum
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
@Getter
public enum CustomerUndertakesEnum implements ValueEnum<Integer> {

    /**
     * 已验收
     */
    YES(0, "是"),

    /**
     * 未验收
     */
    NO(1, "否");

    private final Integer value;

    private final String name;

    CustomerUndertakesEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
