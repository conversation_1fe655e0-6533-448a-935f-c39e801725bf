package com.gok.pboot.pms.enumeration;

/**
 * 是否
 */
public enum YesOrNoEnum implements ValueEnum<Integer> {
    /**
     * 是
     */
    YES("是", 1),

    /**
     * 否
     */
    NO("否", 0);

    YesOrNoEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    private final String name;
    private final Integer value;

    @Override
    public Integer getValue() {
        return this.value;
    }

    @Override
    public String getName() {
        return this.name;
    }


}
