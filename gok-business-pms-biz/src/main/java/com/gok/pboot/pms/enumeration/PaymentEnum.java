package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 款项名称 Enum
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Getter
@AllArgsConstructor
public enum PaymentEnum implements ValueEnum<String> {

    /**
     * 全部
     */
    ALL("", "全部"),

    /**
     * 预付款
     */
    YFK("预付款", "预付款"),

    /**
     * 到货款
     */
    DHK("到货款", "到货款"),

    /**
     * 设备款
     */
    SBK("设备款", "设备款"),

    /**
     * 服务款
     */
    FWK("服务款", "服务款"),

    /**
     * 进度款
     */
    JDK("进度款", "进度款"),

    /**
     * 初验款
     */
    CYK("初验款", "初验款"),

    /**
     * 终验款
     */
    ZYK("终验款", "终验款"),

    /**
     * 质保金
     */
    ZBJ("质保金", "质保金"),

    /**
     * 全款
     */
    QK("全款", "全款");

    private final String value;

    private final String name;
}
