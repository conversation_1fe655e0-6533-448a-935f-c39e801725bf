package com.gok.pboot.pms.cost.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 交付采购计划表验收条件及状态
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CostDeliverPurchasePlanAcceptanceVO{
    /**
     * 主键ID
     */
    private Long id;

    /**
    * 验收和付款条件
    */
    private String acceptPaymentTerms;

    /**
    * 是否已验收（0=否，1=是）
    */
    private Integer acceptStatus;

    /**
     * 是否已验收（0=否，1=是）
     */
    private String acceptStatusTxt;
}