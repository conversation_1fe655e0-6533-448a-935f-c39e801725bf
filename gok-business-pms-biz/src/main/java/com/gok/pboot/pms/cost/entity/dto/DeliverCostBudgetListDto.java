package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
    * 交付管理-费用报销台账查询参数
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeliverCostBudgetListDto {

    /**
    * 项目id
    */
    private Long projectId;

    /**
     * 成本类型(1=费用报销，2=外采费用)
     */
    private Integer costType;
}