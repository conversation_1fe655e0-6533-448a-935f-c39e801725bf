package com.gok.pboot.pms.entity.dto;

import com.gok.pboot.pms.common.validate.constraint.SelectMonth;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * PMS人才复用表
 *
 * @Auther chenhc
 * @Date 2022-08-24 11:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PersonnelReuseFindPageDTO {

    /**
     * 导入人员姓名
     */
    private String exportName;
    /**
     * 所属项目ID
     */
    private Long projectId;

    /**
     * 选择月份
     */
    @NotBlank(message = "选择月份不能为空")
    @SelectMonth(message = "选择月份格式:【yyyy-MM】")
    private String selectMonth;

    /**
     * 是否为管理员
     */
    private Boolean flag;

    /**
     * 账号Id
     */
    private Long userId;

    /**
     * id集合
     */
    private List<Long> ids;

    /**
     * 项目id集合
     */
    private List<Long> projectIds;

    /**
     * 项目名称
     */
    private String projectName;

}
