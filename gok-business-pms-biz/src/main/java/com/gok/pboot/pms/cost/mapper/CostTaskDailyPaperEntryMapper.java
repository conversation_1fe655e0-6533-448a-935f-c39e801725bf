package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.cost.entity.domain.CostTaskDailyPaperEntry;
import com.gok.pboot.pms.cost.entity.dto.CostChangeApprovalStatusDTO;
import com.gok.pboot.pms.cost.entity.vo.TaskDailyPaperDetailVO;
import com.gok.pboot.pms.entity.DailyPaperEntry;
import com.gok.pboot.pms.entity.dto.ChangeApprovalStatusDTO;
import com.gok.pboot.pms.entity.dto.DailyPaperEntryFilingDTO;
import com.gok.pboot.pms.entity.dto.SubordinatesDailyPaperDTO;
import com.gok.pboot.pms.entity.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 工单工时条目数据库操作接口
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Mapper
public interface CostTaskDailyPaperEntryMapper extends BaseMapper<CostTaskDailyPaperEntry> {
    /**
     * ~ 根据日报ID获取日报条目 ~
     *
     * @param dailyPaperId 日报ID
     * @return java.util.List<com.gok.pboot.pms.entity.CostTaskDailyPaperEntry>
     * <AUTHOR>
     * @date 2022/8/24 9:55
     */
    List<CostTaskDailyPaperEntry> findByDailyPaperId(Long dailyPaperId);


    Page<CostTaskDailyPaperEntry> findList(Page<CostTaskDailyPaperEntry> page, @Param("filter") Map<String, Object> filter);

    List<CostTaskDailyPaperEntry> findList(@Param("filter") CostTaskDailyPaperEntry filter);

    /**
     * ~ 批量插入 ~
     *
     * @param poList 实体列表
     * <AUTHOR>
     * @date 2022/8/24 14:16
     */
    void batchSave(@Param("poList") List<CostTaskDailyPaperEntry> poList);


    /**
     * 批量修改
     *
     * @param list 实体集合
     */
    void batchUpdate(@Param("list") List<CostTaskDailyPaperEntry> list);


    void updateApprovalStatusById(@Param("changeApprovalStatusDTO") CostChangeApprovalStatusDTO changeApprovalStatusDTO);

    void batchApproval(@Param("ids") List<Long> ids, @Param("changeApprovalStatusDTO") ChangeApprovalStatusDTO changeApprovalStatusDTO);


    List<DailyPaperEntryExcelVO> queryAllAndWorkCode(@Param("filter") Map<String, Object> filter);


    /**
     * 查询日报对应日期的归档信息
     *
     * @param dailyPaperIds 日报条目id
     * @return 归档信息<日报条目id ， 填报日期 ， 归档状态>
     */
    List<DailyPaperEntryFilingDTO> selectFilingByDailyPaperIds(@Param("ids") Collection<Long> dailyPaperIds);


    /**
     * 按下属查找
     *
     * @param page 页
     * @param dto  DTO
     * @return {@link Page }<{@link DailyReviewProjectAuditPageVO }>
     */
    @InterceptorIgnore(tenantLine = "true")
    Page<DailyReviewProjectAuditPageVO> findBySubordinate(Page page, @Param("filter") SubordinatesDailyPaperDTO dto);

    /**
     * @create by yzs at 2023/5/11
     * @description:统计下级 工时
     * @param: dto
     * @return: com.gok.pboot.pms.entity.vo.SubordinatePaperEntryStaticVO
     */
    List<SubordinatePaperEntryStaticVO> findBySubordinateStatic(@Param("filter") SubordinatesDailyPaperDTO dto);

    /**
     * @create by yzs at 2023/5/12
     * @description:个人面板查询日报详情
     * @param: pageRequest 分页参数
     * @param: dto 搜索条件
     * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.gok.pboot.pms.entity.CostTaskDailyPaperEntry>
     */
    @InterceptorIgnore(tenantLine = "true")
    Page<DailPanelPaperPageVO> findByPanel(Page page, @Param("filter") SubordinatesDailyPaperDTO dto);

    /**
     * 统计个人面板-日报详细-总计
     *
     * @param dto 查询条件 startTime，endTime，projectName
     * @return {@link PanelProjectSituationAnalysisVO}
     */
    PanelProjectSituationAnalysisVO panelSituationAnalysis(@Param("filter") SubordinatesDailyPaperDTO dto);


    /**
     * 获取待审核日报
     *
     * @param taskIds 任务id列表
     * @param page    分页对象
     * @return {@link List}<{@link DailyPaperEntry}>
     */
    Page<CostTaskDailyPaperEntry> findDSHPageByTaskIds(Page<CostTaskDailyPaperEntry> page, @Param("taskIds") List<Long> taskIds);


    /**
     * 获取无效日报
     *
     * @param taskIds 任务id列表
     * @param page    分页对象
     * @return {@link List}<{@link DailyPaperEntry}>
     */
    Page<CostTaskDailyPaperEntry> findInvalidPageByTaskIds(Page<CostTaskDailyPaperEntry> page, @Param("taskIds") List<Long> taskIds);


    /**
     * 根据工单ID查询日报详情
     *
     * @param page           分页参数
     * @param taskId         工单ID
     * @param approvalStatus 审批状态
     * @return 日报详情列表
     */
    Page<TaskDailyPaperDetailVO> findDailyDetailsByTaskId(@Param("page") Page<TaskDailyPaperDetailVO> page,
                                                          @Param("taskId") Long taskId,
                                                          @Param("approvalStatus") Integer approvalStatus);

} 