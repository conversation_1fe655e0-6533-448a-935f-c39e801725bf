package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
    * 合同台账相关风险信息vo
    * <AUTHOR>
*/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContractRiskInfoVO {

    /**
    * 合同id
    */
    private Long contractId;

    /**
    * 风险类型
    */
    private String riskType;

    /**
     * 风险类型名称
     */
    private String riskTypeName;

    /**
    * 风险内容
    */
    private String riskContent;

    /**
     * 逾期日期
     */
    private String overdueDays;

    /**
    * 提示时间
    */
    private String promptTime;

    /**
    * 风险标识（0 黄色，1红色，2绿色）
    */
    private String riskMark;

}