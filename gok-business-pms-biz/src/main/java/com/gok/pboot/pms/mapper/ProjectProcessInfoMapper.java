package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.entity.domain.ProjectProcessInfo;
import com.gok.pboot.pms.entity.vo.ProjectProcessInfoFindPageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 项目流程动态信息表
 *
 * <AUTHOR>
 * @since 2023-07-17
 **/
@Mapper
public interface ProjectProcessInfoMapper extends BaseMapper<ProjectProcessInfo> {

    /**
     * 分页查询项目动态请求
     *
     * @param page   分页请求实体
     * @param filter 请求参数
     * @return
     */
    Page<ProjectProcessInfo> findListPage(Page<ProjectProcessInfoFindPageVO> page,
                                          @Param("filter") Map<String, Object> filter);

}
