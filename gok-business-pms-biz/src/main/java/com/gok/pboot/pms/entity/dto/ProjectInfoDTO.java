package com.gok.pboot.pms.entity.dto;

import com.gok.pboot.pms.entity.domain.ProjectInfo;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 项目信息Dto
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
@Data
public class ProjectInfoDTO  {

    /**
     * id
     */
    private Long id;

    /**
     * 项目编号
     */
    private String itemNo;

    /**
     * 项目名称
     */
    private String itemName;



    /**
     * 项目所在地
     */
    private String projectLocation;


    /**
     * 业务归属一级部门id
     */
    private Long firstLevelDepartmentId;

    /**
     * 业务归属一级部门
     */
    private String firstLevelDepartment;

    /**
     * 项目交付部门id
     */
    private Long proDeliveryDepartmentId;

    /**
     * 项目交付部门
     */
    private String proDeliveryDepartment;

    /**
     * 是否内部项目
     */
    private Long isNotInternalProject;


    /**
     * 资源通道
     */
    private String resourceChannel;


    /**
     * 是否需要招投标,0:是 1:否
     */
    private Integer sfzjqht;

    /**
     * 预估毛利率
     */
    private String ygmll;

    /**
     * 项目整包预算
     */
    private BigDecimal proPackageBudget;

    /**
     * 预计签单金额
     */
    private BigDecimal expectedOrderAmount;


    /**
     * 签单金额评判依据
     */
    private String qdjeppyj;

    /**
     * 业务方向
     */
    private String businessDirection;

    /**
     * 成熟度
     */
    private Long maturity;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 技术领域
     */
    private String technologyField;

    /**
     * 项目背景及建设内容（项目目标及建设范围）
     */
    private String proConstructionScope;

    /**
     * 我司建设内容
     */
    private String wsjsnr;

    public static ProjectInfoDTO saveOrUpdate(ProjectInfo request){
        ProjectInfoDTO result = new ProjectInfoDTO();
        result.setId(request.getId());
        result.setItemNo(request.getItemNo());
        result.setItemName(request.getItemName());
        result.setProjectLocation(result.getProjectLocation());
        result.setFirstLevelDepartmentId(result.getFirstLevelDepartmentId());
        result.setFirstLevelDepartment(result.getFirstLevelDepartment());
        result.setProDeliveryDepartmentId(result.getProDeliveryDepartmentId());
        result.setProDeliveryDepartment(result.getProDeliveryDepartment());
        result.setIsNotInternalProject(result.getIsNotInternalProject());
        result.setResourceChannel(request.getResourceChannel());
        result.setSfzjqht(request.getSfzjqht());
        result.setYgmll(request.getYgmll());
        result.setProPackageBudget(request.getProPackageBudget());
        result.setExpectedOrderAmount(request.getExpectedOrderAmount());
        result.setQdjeppyj(request.getQdjeppyj());
        result.setBusinessDirection(request.getBusinessDirection());
        result.setMaturity(request.getMaturity());
        result.setBusinessType(request.getBusinessType());
        result.setTechnologyField(request.getTechnologyField());
        result.setProConstructionScope(request.getProConstructionScope());
        result.setWsjsnr(request.getWsjsnr());
        return result;
    }

}
