package com.gok.pboot.pms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.upms.dto.DeptCacheDto;
import com.gok.bcp.upms.feign.RemoteDeptService;
import com.gok.bcp.upms.feign.RemoteOutService;
import com.gok.bcp.upms.vo.SysDeptVo;
import com.gok.bcp.upms.vo.SysUserVo;
import com.gok.components.common.util.R;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.BcpLoggerUtils;
import com.gok.pboot.pms.Util.CollectionUtils;
import com.gok.pboot.pms.Util.SysDeptUtils;
import com.gok.pboot.pms.entity.CustomerBusinessPerson;
import com.gok.pboot.pms.entity.SysDept;
import com.gok.pboot.pms.mapper.CustomerBusinessPersonMapper;
import com.gok.pboot.pms.service.ICustomerBusinessPersonService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <p>
 * 客户经营单元表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerBusinessPersonServiceImpl extends ServiceImpl<CustomerBusinessPersonMapper, CustomerBusinessPerson> implements ICustomerBusinessPersonService {


    private final BcpLoggerUtils bcpLoggerUtils;

    private final RemoteDeptService remoteDeptService;

    private final CustomerBusinessPersonMapper customerBusinessPersonMapper;

    private final RemoteOutService remoteOutService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDel(List<Long> list) {
        baseMapper.batchDel(list);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchCreate(List<CustomerBusinessPerson> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            baseMapper.deletedByBusinessId(list.get(0).getBusinessId());
            for (CustomerBusinessPerson temp : list) {
                //查询负责人姓名及部门id
                Long userId = temp.getManagerId();
                R<SysUserVo> userInfo = remoteOutService.getUserInfoById(userId);
                if (userInfo.getData() != null) {
                    SysUserVo user = userInfo.getData();
                    temp.setManagerName(user.getName());
                    List<SysDeptVo> deptVoList = userInfo.getData().getDeptList();
                    if (CollectionUtils.isNotEmpty(deptVoList)) {
                        //获取最后一级部门数据
                        SysDeptVo sysDeptVo = deptVoList.get(deptVoList.size() - 1);
                        temp.setManagerDeptId(sysDeptVo.getDeptId());
                        //获取部门名称
                        List<DeptCacheDto> deptList = remoteDeptService.getAllDeptList(false);
                        Map<Long, SysDept> deptIdMap = SysDeptUtils.getDeptIdMap(deptList);
                        temp.setManagerDeptName(sysDeptVo.getDeptId() == null ?
                                StrUtil.EMPTY : SysDeptUtils.collectFullName(deptIdMap, sysDeptVo.getDeptId()).replace("-", "/"));
                    }
                }
                BaseBuildEntityUtil.buildInsert(temp);
            }
            saveBatch(list);
        }
    }


    @Override
    public List<CustomerBusinessPerson> findList(CustomerBusinessPerson qo) {
        if (qo == null) {
            qo = new CustomerBusinessPerson();
        }
        return this.list(getQueryWrapper(qo));
    }

    public LambdaQueryWrapper<CustomerBusinessPerson> getQueryWrapper(CustomerBusinessPerson qo) {
        return Wrappers.<CustomerBusinessPerson>lambdaQuery()
                .eq(ObjectUtils.isNotEmpty(qo.getBusinessId()), CustomerBusinessPerson::getBusinessId, qo.getBusinessId())
                .eq(ObjectUtils.isNotEmpty(qo.getManagerId()), CustomerBusinessPerson::getManagerId, qo.getManagerId())
                .eq(ObjectUtils.isNotEmpty(qo.getManagerRole()), CustomerBusinessPerson::getManagerRole, qo.getManagerRole());
    }

    /**
     * 判断当前用户是否是业务角色
     *
     * @param businessId 项目id
     * @param userId     用户id
     * @return {@link List}<{@link Integer}>
     */
    @Override
    public List<Integer> manageRoleList(Long businessId, Long userId) {
        CustomerBusinessPerson qo = new CustomerBusinessPerson();
        qo.setBusinessId(businessId);
        List<CustomerBusinessPerson> personList = findList(qo);
        List<Integer> roleTypes = personList.stream().filter(person -> person.getManagerId().equals(userId)).map(CustomerBusinessPerson::getManagerRole).collect(Collectors.toList());
        return roleTypes;
    }

    @Override
    public List<Integer> manageRoleList(Long businessId, List<Long> userIds) {
        CustomerBusinessPerson qo = new CustomerBusinessPerson();
        qo.setBusinessId(businessId);
        List<CustomerBusinessPerson> personList = findList(qo);
        if (CollUtil.isEmpty(userIds)) {
            return new ArrayList<>();
        }
        //查找当前用户列表有的角色
        List<CustomerBusinessPerson> foundPerson = personList.stream()
                .filter(person -> userIds.contains(person.getManagerId()))
                .collect(Collectors.toList());

        return foundPerson.stream().map(CustomerBusinessPerson::getManagerRole).collect(Collectors.toList());
    }


    @Override
    public boolean isManageRole(Long businessId, List<Long> userIds, Integer role) {
        CustomerBusinessPerson qo = new CustomerBusinessPerson();
        qo.setBusinessId(businessId);
        List<CustomerBusinessPerson> personList = findList(qo);
        List<Long> managerIds = personList.stream()
                .filter(m -> role.equals(m.getManagerRole()))
                .map(CustomerBusinessPerson::getManagerId).distinct()
                .collect(Collectors.toList());
        return CollUtil.isNotEmpty(managerIds) && userIds.stream().anyMatch(managerIds::contains);
    }

}
