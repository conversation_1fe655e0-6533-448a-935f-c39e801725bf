package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 合同附件
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ContractFileVo {

    /**
     * 文件名称
     */
    private String name;

    /**
     * 下载地址
     */
    private String downloadUrl;
}
