package com.gok.pboot.pms.entity.dto;

import com.gok.pboot.pms.common.base.PageRequest;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 交付人员分页参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DelivererFindPageDTO extends PageRequest {
    /**
     * 账号ID
     */
    private Long userId;
    /**
     * 导入人姓名
     */
    private String exportName;
    /**
     * 所属项目
     */
    private Long projectId;
    /**
     * 月份
     */
    private String month;
    /**
     * 是否为管理员
     */
    private Boolean flag;
    /**
     * 所属项目
     */
    private List<Long> projectIds;

    /**
     * id集合
     */
    private List<Long> ids;
}
