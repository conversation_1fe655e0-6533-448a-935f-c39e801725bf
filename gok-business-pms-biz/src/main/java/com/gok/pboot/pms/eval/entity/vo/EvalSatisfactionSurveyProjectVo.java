package com.gok.pboot.pms.eval.entity.vo;

import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.eval.entity.domain.EvalCustomerSatisfactionSurvey;
import com.gok.pboot.pms.eval.enums.EvalSatisfactionSurveyStatusEnum;
import com.gok.pboot.pms.eval.enums.EvalSendStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * EVAL 满意度调查项目 vo
 *
 * <AUTHOR>
 * @date 2025/05/14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class EvalSatisfactionSurveyProjectVo {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 客户经理ID
     */
    private Long salesmanUserId;

    /**
     * 客户经理名称
     */
    private String projectSalesperson;

    /**
     * 干系人ID
     */
    private Long stakeholderId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 联系方式
     */
    private String contactPhone;

    public static EvalCustomerSatisfactionSurvey voToEntitySave(EvalSatisfactionSurveyProjectVo vo, LocalDate surveyDate) {
        return BaseBuildEntityUtil.buildSave(new EvalCustomerSatisfactionSurvey())
                .setProjectId(vo.getProjectId())
                .setStakeholderId(vo.getStakeholderId())
                .setCustomerName(vo.getCustomerName())
                .setContactPhone(vo.getContactPhone())
                .setReleaseDate(surveyDate)
                .setEvalStatus(EvalSatisfactionSurveyStatusEnum.UNEVALUATED.getValue())
                .setSendStatus(EvalSendStatusEnum.SENT.getValue());
    }
}
