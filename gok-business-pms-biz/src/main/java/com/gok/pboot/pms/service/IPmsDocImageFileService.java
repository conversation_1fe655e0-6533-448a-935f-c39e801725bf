package com.gok.pboot.pms.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.entity.domain.PmsDocImageFile;
import com.gok.pboot.pms.entity.vo.OaFileInfoVo;
import com.gok.pboot.pms.entity.vo.OaFileVo;

import java.util.List;

/**
 * OA文件映射 服务类接口
 *
 * <AUTHOR>
 * @date 2023/11/21
 */
public interface IPmsDocImageFileService extends IService<PmsDocImageFile> {

    /**
     * OA文件映射转换
     * @param file OA文件映射
     * @param resourcesData 流程文件
     * @return
     */
     List<OaFileInfoVo>  getOaOaFileInfoList(OaFileInfoVo file,List<OaFileVo> resourcesData);

    /**
     * 按文档 ID 获取
     *
     * @param docIds 文档 ID
     * @return {@link List }<{@link PmsDocImageFile }>
     */
    List<PmsDocImageFile>  getByDocIds(List<Long> docIds);

    /**
     * 获取OA文件列表
     * @param docId
     * @return
     */
    List<OaFileInfoVo> getOaFileVoList(String docId);

}
