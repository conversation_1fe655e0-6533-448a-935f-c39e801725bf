package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;

/**
 * 当日任务状态
 *
 * <AUTHOR>
 * @date 2024/01/26
 */
@AllArgsConstructor
public enum TodayTaskStateEnum implements ValueEnum<Integer> {

    NORMAL(0, "正常"),
    DELAY(1, "延期"),
    FINISHED(2, "按时结束"),
    DELAY_FINISHED(3, "超期结束")
    ;

    private final Integer value;

    private final String name;

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }

}
