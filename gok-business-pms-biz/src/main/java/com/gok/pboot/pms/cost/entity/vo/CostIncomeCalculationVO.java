package com.gok.pboot.pms.cost.entity.vo;

import cn.hutool.core.bean.BeanUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.cost.entity.domain.CostIncomeCalculation;
import com.gok.pboot.pms.cost.enums.ConfirmStatusEnum;
import com.gok.pboot.pms.cost.enums.SettlementStatusEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 收入测算汇总实体类
 *
 * <AUTHOR>
 * @create 2025/02/18
 **/
@Data
public class CostIncomeCalculationVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 归属月份
     */
    private LocalDate belongMonth;

    /**
     * 结算人天
     */
    private BigDecimal settlementHours;

    /**
     * 结算单价
     */
    private BigDecimal settlementUnitPrice;

    /**
     * 客户承担费用
     */
    private BigDecimal customerBearingAmount;

    /**
     * 测算含税金额
     */
    private BigDecimal estimatedInclusiveAmountTax;

    /**
     * 确认状态（0=待确认，1=已确认，2=部分确认）
     * {@link ConfirmStatusEnum}
     */
    private Integer confirmStatus;

    private String confirmStatusTxt;

    /**
     * 确认日期
     */
    private LocalDate confirmDate;

    /**
     * 结算状态（0=待结算，1=已结算）
     * {@link SettlementStatusEnum}
     */
    private Integer settlementStatus;

    private String settlementStatusTxt;

    /**
     * 结算明细数据
     */
    private List<CostIncomeSettlementDetailVO> settlementList;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目经理ID
     */
    private Long managerUserId;

    /**
     * 项目经理人员姓名
     */
    private String managerUserName;

    public static CostIncomeCalculationVO of(CostIncomeCalculation entity,
                                             Map<Long, List<CostIncomeSettlementDetailVO>> settlementDetailMap) {
        if (null == entity) {
            return null;
        }
        CostIncomeCalculationVO vo = BeanUtil.copyProperties(entity, CostIncomeCalculationVO.class);
        vo.setConfirmStatusTxt(EnumUtils.getNameByValue(ConfirmStatusEnum.class, entity.getConfirmStatus()));
        vo.setSettlementStatusTxt(EnumUtils.getNameByValue(SettlementStatusEnum.class, entity.getSettlementStatus()));
        vo.setSettlementList(settlementDetailMap.get(entity.getId()));
        return vo;
    }

}
