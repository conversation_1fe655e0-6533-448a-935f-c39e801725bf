package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.dto.TaskStatQueryDTO;
import com.gok.pboot.pms.cost.entity.vo.*;

import javax.servlet.http.HttpServletResponse;

/**
 * 多维度工单数据统计服务
 *
 * <AUTHOR>
 * @date 2025/05/12
 */
public interface CostTaskStatService {

    /**
     * 项目工单看板-多维度售后交付工单统计
     *
     * @param pageRequest 分页参数
     * @param query       查询条件
     * @return 根据维度返回不同的统计结果
     */
    Page<? extends ProjectDeliverStatBaseVO> findProjectDeliverTaskStat(PageRequest pageRequest, TaskStatQueryDTO query);

    /**
     * 项目工单看板-多维度售后交付工单统计导出
     *
     * @param query    查询条件
     * @param response response
     */
    void exportProjectDeliverTaskStat(TaskStatQueryDTO query, HttpServletResponse response);

    /**
     * 项目工单看板-多维度售前支撑工单统计
     *
     * @param pageRequest 页面请求
     * @param query       查询
     * @return {@link Page }<{@link ? } {@link extends } {@link ProjectProDimPreSaleStatVO }>
     */
    Page<? extends ProjectPreSaleStatBaseVO> findProjectPreSaleTaskStat(PageRequest pageRequest, TaskStatQueryDTO query);

    /**
     * 项目工单看板-多维度售前支撑工单统计导出
     *
     * @param query    查询
     * @param response 响应
     */
    void exportProjectPreSaleTaskStat(TaskStatQueryDTO query, HttpServletResponse response);


    /*以下为人员工单看板相关接口*/

    /**
     * 人员工单看板-多维度售后交付工单统计
     *
     * @param pageRequest 页面请求
     * @param query       查询
     * @return {@link Page }<{@link ? } {@link extends } {@link PersonnelDeliverStatBaseVO }>
     */
    Page<? extends PersonnelDeliverStatBaseVO> findPersonnelDeliverTaskStat(PageRequest pageRequest, TaskStatQueryDTO query);

    /**
     * 人员工单看板-多维度售后交付工单统计导出
     *
     * @param query    查询
     * @param response 响应
     */
    void exportPersonnelDeliverTaskStat(TaskStatQueryDTO query, HttpServletResponse response);

    /**
     * 人员工单看板-多维度售前支撑工单统计
     *
     * @param pageRequest 页面请求
     * @param query       查询
     * @return {@link Page }<{@link ? } {@link extends } {@link PersonnelPreSaleStatBaseVO }>
     */
    Page<? extends PersonnelPreSaleStatBaseVO> findPersonnelPreSaleTaskStat(PageRequest pageRequest, TaskStatQueryDTO query);

    /**
     * 人员工单看板-多维度售前支撑工单统计导
     * @param query    查询
     * @param response 响应
     */
    void exportPersonnelPreSaleTaskStat(TaskStatQueryDTO query, HttpServletResponse response);
}
