package com.gok.pboot.pms.cost.entity.domain;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.gok.pboot.common.secret.AESEncryptor;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.common.base.BaseConstants;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.cost.entity.vo.CostExpensesShareDetailsVO;
import com.gok.pboot.pms.cost.entity.vo.CostPersonnelInformationVO;
import com.gok.pboot.pms.cost.enums.ConfirmStatusEnum;
import com.gok.pboot.pms.cost.enums.QuotationTypeEnum;
import com.gok.pboot.pms.cost.enums.SettlementStatusEnum;
import com.gok.pboot.pms.entity.PayAttendanceData;
import com.gok.pboot.pms.entity.vo.AllocationFindPageVO;
import com.gok.pboot.pms.entity.vo.PersonnelDeliveryHourPageVO;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 收入测算汇总实体类
 *
 * <AUTHOR>
 * @create 2025/02/18
 **/
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("cost_income_calculation_detail")
public class CostIncomeCalculationDetail extends BeanEntity<Long> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 收入测算汇总ID
     */
    private Long calculationId;

    /**
     * 人员信息ID
     */
    private Long memberId;

    /**
     * 姓名
     */
    private String memberName;

    /**
     * 工号
     */
    private String workCode;

    /**
     * 人员属性（0=国科人员，1=第三方）
     */
    private Integer personnelAttribute;

    /**
     * 岗位ID
     */
    private Long jobId;

    /**
     * 岗位名称
     */
    private String jobName;

    /**
     * 归属月份
     */
    private LocalDate belongMonth;

    /**
     * 结算人天
     */
    private BigDecimal settlementHours;

    /**
     * 结算单价
     */
    private BigDecimal settlementUnitPrice;

    /**
     * 报价税率
     */
    private Integer quotedRateId;

    /**
     * 客户承担费用
     */
    private BigDecimal customerBearingAmount;

    /**
     * 测算含税金额
     */
    private BigDecimal estimatedInclusiveAmountTax;

    /**
     * 确认状态（0=待确认，1=已确认，2=部分确认）
     * {@link com.gok.pboot.pms.cost.enums.ConfirmStatusEnum}
     */
    private Integer confirmStatus;

    /**
     * 确认日期
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDate confirmDate;

    /**
     * 结算状态（0=待结算，1=已结算）
     * {@link com.gok.pboot.pms.cost.enums.SettlementStatusEnum}
     */
    private Integer settlementStatus;

    /**
     * 组装实体类
     *
     * @param entity                      实体类 null则新增
     * @param personnelInformation        人员信息
     * @param allocationVO                项目分摊工时信息
     * @param costExpensesShareDetailsVOS 项目分摊明细数据
     * @param payAttendanceData           考勤数据
     * @return
     */
    public static CostIncomeCalculationDetail assemble(CostIncomeCalculationDetail entity,
                                                       CostPersonnelInformationVO personnelInformation,
                                                       AllocationFindPageVO allocationVO,
                                                       List<CostExpensesShareDetailsVO> costExpensesShareDetailsVOS,
                                                       PayAttendanceData payAttendanceData,
                                                       PersonnelDeliveryHourPageVO personnelDeliveryHourData
    ) {
        boolean isSave = null == entity;
        entity = isSave ? new CostIncomeCalculationDetail() : entity;
        entity.setProjectId(personnelInformation.getProjectId());
        entity.setMemberId(personnelInformation.getId());
        entity.setMemberName(personnelInformation.getName());
        entity.setWorkCode(personnelInformation.getWorkCode());
        entity.setPersonnelAttribute(personnelInformation.getPersonnelAttribute());
        entity.setJobId(personnelInformation.getJobId());
        entity.setJobName(personnelInformation.getJobName());
        entity.setBelongMonth(LocalDate.parse(personnelInformation.getBelongMonth()));
        entity.setQuotedRateId(StrUtil.isNotBlank(personnelInformation.getQuotedRateId()) ? Integer.valueOf(personnelInformation.getQuotedRateId()) : null);

        BigDecimal customerBearingAmount = CollUtil.isEmpty(costExpensesShareDetailsVOS)
                ? BigDecimal.ZERO
                : costExpensesShareDetailsVOS.stream()
                .map(CostExpensesShareDetailsVO::getReimburseMoney)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        entity.setCustomerBearingAmount(customerBearingAmount);

        BigDecimal projectShareHours =
                Optional.ofNullable(allocationVO).orElse(new AllocationFindPageVO()).getProjectShareHours();
        entity.setSettlementHours(projectShareHours);

        BigDecimal settlementUnitPrice;
        payAttendanceData = Optional.ofNullable(payAttendanceData).orElse(new PayAttendanceData());
        personnelDeliveryHourData = Optional.ofNullable(personnelDeliveryHourData).orElse(new PersonnelDeliveryHourPageVO());
        QuotationTypeEnum quotationTypeEnum =
                EnumUtils.getEnumByValue(QuotationTypeEnum.class, personnelInformation.getQuotationType());
        if (null != quotationTypeEnum) {
            switch (quotationTypeEnum) {
                case RT:
                    settlementUnitPrice = null != personnelInformation.getQuotationIncludeTax() && null != projectShareHours
                            ? personnelInformation.getQuotationIncludeTax().multiply(projectShareHours).setScale(2, RoundingMode.HALF_UP)
                            : BigDecimal.ZERO;
                    break;
                case RY:
                    BigDecimal cwDueAttendance = personnelDeliveryHourData.getCwDueAttendance();
                    settlementUnitPrice = null != personnelInformation.getQuotationIncludeTax() && null != cwDueAttendance && null != projectShareHours
                            ? personnelInformation.getQuotationIncludeTax()
                            .divide(cwDueAttendance, 4, RoundingMode.HALF_UP)
                            .multiply(projectShareHours).setScale(2, RoundingMode.HALF_UP)
                            : BigDecimal.ZERO;
                    break;
                case GDFL:
                    String decryptedSalary = AESEncryptor.justDecrypt(payAttendanceData.getSalary());
                    BigDecimal salary = StrUtil.isNotBlank(decryptedSalary)
                            ? new BigDecimal(decryptedSalary)
                            : BigDecimal.ZERO;
                    BigDecimal flatRate = StrUtil.isNotBlank(personnelInformation.getFlatRate())
                            ? new BigDecimal(personnelInformation.getFlatRate()).add(BigDecimal.ONE)
                            : BigDecimal.ONE;
                    settlementUnitPrice = salary.multiply(flatRate).setScale(2, RoundingMode.HALF_UP);
                    break;
                default:
                    settlementUnitPrice = BigDecimal.ZERO;
                    break;
            }
        } else {
            settlementUnitPrice = BigDecimal.ZERO;
        }
        entity.setSettlementUnitPrice(settlementUnitPrice);
        entity.setEstimatedInclusiveAmountTax(settlementUnitPrice.add(customerBearingAmount).setScale(2, RoundingMode.HALF_UP));

        entity.setConfirmStatus(ConfirmStatusEnum.AWAIT_CONFIRM.getValue());
        entity.setConfirmDate(null);
        entity.setSettlementStatus(SettlementStatusEnum.AWAIT_SETTLEMENT.getValue());
        if (isSave) {
            entity.setId(IdWorker.getId());
            entity.setCreator("定时任务");
            entity.setCtime(new Timestamp(System.currentTimeMillis()));
            entity.setDelFlag(BaseConstants.NO);
        }
        entity.setModifier("定时任务");
        entity.setMtime(new Timestamp(System.currentTimeMillis()));
        return entity;
    }

}
