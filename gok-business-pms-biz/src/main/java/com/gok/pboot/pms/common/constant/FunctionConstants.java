package com.gok.pboot.pms.common.constant;

import lombok.experimental.UtilityClass;

/**
 * 日志功能模块枚举
 *
 * <AUTHOR>
 * @version 2.3.1
 */
@UtilityClass
public class FunctionConstants {


    public static final String	HOUR_SATURATION	= "工时饱和度";
    public static final String	WORK_HOUR_STATISTICS_TABLE	= "工时统计表";
    public static final String	DELIVERY_PERSONNEL_TABLE	= "交付人员表";
    public static final String	TALENT_REUSE_TABLE	= "人才复用表";
    public static final String	SUMMARY_OF_PROJECT_WORKING_HOURS	= "项目工时汇总";
    public static final String	PROJECT_TIME_ALLOCATION_TABLE	= "项目工时分摊表";
    public static final String	PROJECT_DETAILS_PROJECT_RISKS	= "项目详情-项目风险";
    public static final String	DAILY_REPORT_SUBMISSION_CHECKLIST	= "日报提交一览表";
    public static final String	PROJECT_DETAILS_PROJECT_WEEKLY_REPORT	= "项目详情-项目周报";
    public static final String	PROJECT_DETAILS_MEETING_MINUTES	= "项目详情-会议纪要";
    public static final String	PROJECT_DETAILS_PROJECT_STAKEHOLDERS	= "项目详情-项目干系人";
    public static final String	PROJECT_DETAILS_PROJECT_TASKS	= "项目详情-项目任务";
    public static final String	CUSTOMER_COMMUNICATION_RECORDS	= "客户沟通记录";
    public static final String	BUSINESS_OPPORTUNITY_PROGRESS_RECORD	= "商机进展记录";
    public static final String	BUSINESS_OPPORTUNITY_LEDGER	= "商机台账";
    public static final String	PROJECT_WEEKLY_REPORT	= "项目周报";
    public static final String	REUSE_WORKING_HOURS_REVIEW	="复用工时审核";
    public static final String	REVIEW_OF_DAILY_WORK_HOURS_REPORT	= "工时日报审核";
    public static final String	DELIVERY_PERSONNEL_WORKING_HOURS_IMPORT	= "交付人员工时导入";
    public static final String	TALENT_REUSE_INTRODUCTION	= "人才复用导入";
    public static final String	REPORTING_OF_WORKING_HOURS	= "工时填报";
    public static final String	CONTRACT_LEDGER	= "合同台账";
    public static final String	PROJECT_PAYMENT_TRACKING_FORM	= "项目回款跟踪表";
    public static final String	CUSTOMER_LEDGER	= "客户台账";
    public static final String	CUSTOMER_BUSINESS	= "客户经营单元";
    public static final String	CUSTOMER_UNIT	= "所属客户";
    public static final String	CUSTOMER_BUSINESS_UNIT	= "客户经营单元/所属客户";
}

