package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 出差天数枚举
 *
 * <AUTHOR>
 * @date 2025/01/09
 */
@AllArgsConstructor
@Getter
public enum AwayDayEnum implements ValueEnum<Integer> {

    /**
     * 不限制
     */
    UNLIMITED(0, "不限制"),

    /**
     * 小于30天
     */
    LESS_THAN_30(1, "小于30天"),

    /**
     * 大于等于30天
     */
    GREATER_THAN_OR_EQUAL_TO_30(2, "大于等于30天");


    private final Integer value;

    private final String name;

}
