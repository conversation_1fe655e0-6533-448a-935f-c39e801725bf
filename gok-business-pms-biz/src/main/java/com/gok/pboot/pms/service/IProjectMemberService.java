package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.entity.domain.ProjectMember;

import java.util.List;

/**
 * 项目组成员表（Oa项目项目台账-明细6）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-17 11:11:29
 */
public interface IProjectMemberService extends IService<ProjectMember> {

    /**
     * 项目id查询
     * @param id
     */
    List<ProjectMember> getByProjectId(Long id);
}

