package com.gok.pboot.pms.enumeration;

import lombok.Getter;

/**
 * 归属主体/主体名称枚举
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Getter
public enum AttributableSubjectEnum implements ValueEnum<String> {

    /**
     * 福建国科信息科技有限公司
     */
    FJ_GOK("0", "福建国科信息科技有限公司"),

    /**
     * 成都国科云漫信息科技有限公司
     */
    CD_GOK_YM("1", "成都国科云漫信息科技有限公司"),

    /**
     * 泉州市国科信息科技有限公司
     */
    QZ_GOK("2", "泉州市国科信息科技有限公司"),

    /**
     * 福建国科信息科技有限公司福州分公司
     */
    FJ_FZ_GOK("3", "福建国科信息科技有限公司福州分公司"),

    /**
     * 厦门集风堂科技有限公司
     */
    XM_JFT("4", "厦门集风堂科技有限公司"),

    /**
     * 厦门国科在线信息科技有限公司
     */
    XM_GOK("5", "厦门国科在线信息科技有限公司"),

    /**
     * 厦门硕翔计算机技术有限公司
     */
    XM_SX("6", "厦门硕翔计算机技术有限公司"),

    /**
     * 湖北国科云漫信息科技有限公司
     */
    HB_GOK("7", "湖北国科云漫信息科技有限公司"),

    /**
     * 福州市国科信息科技有限公司
     */
    FZ_GOK("8", "福州市国科信息科技有限公司"),

    /**
     * 成都国科尚科信息科技有限公司
     */
    CD_GOK("9", "成都国科尚科信息科技有限公司"),

    /**
     * 四川恒芯源教育科技有限公司
     */
    SC_HXY("10", "四川恒芯源教育科技有限公司"),

    /**
     * 福建赋氧产教融合科技有限公司
     */
    FJ_FY_CJRH("11", "福建赋氧产教融合科技有限公司"),

    /**
     * 福建国科（工会）
     */
    FJ_GOK_GH("12", "福建国科（工会）"),

    /**
     * 宜宾国科信息科技有限公司
     */
    YB_GOK("13", "宜宾国科信息科技有限公司"),

    /**
     * 漳州国科云谷信息科技有限公司
     */
    ZZ_GOK("14", "漳州国科云谷信息科技有限公司"),

    /**
     * 四川国科愿景融合科技有限公司
     */
    SC_GOK("15", "四川国科愿景融合科技有限公司"),

    /**
     * 作废（勿选）
     */
    EXIT("16", "作废（勿选）"),

    /**
     * 福建国科信息科技有限公司陕西分公司
     */
    FJ_GOK_SX("17", "福建国科信息科技有限公司陕西分公司"),

    /**
     * 北京国科云漫信息科技有限公司
     */
    BJ_GOK("18", "北京国科云漫信息科技有限公司");

    private final String value;

    private final String name;

    AttributableSubjectEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }
}
