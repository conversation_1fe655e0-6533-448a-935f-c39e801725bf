package com.gok.pboot.pms.entity.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 多层级pms字典项
 *
 * <AUTHOR>
 * @date 2023/11/30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PmsDictItemMultiLevel {

    /**
     * ID
     */
    private Long id;

    /**
     * 字典类型
     */
    private String dictType;

    /**
     * key
     */
    private String value;

    /**
     * value
     */
    private String label;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 子集列表
     */
    private List<PmsDictItemMultiLevel> children;
}
