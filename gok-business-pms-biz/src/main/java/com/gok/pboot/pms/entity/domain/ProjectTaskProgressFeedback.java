/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.entity.dto.ProjectTaskProgressFeedbackAddDTO;
import com.google.common.base.Strings;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 任务进展回复内容
 *
 * <AUTHOR> generator
 * @date 2023-08-18 10:07:30
 */
@Data
@TableName("project_task_progress_feedback")
@EqualsAndHashCode(callSuper = true)
public class ProjectTaskProgressFeedback extends BeanEntity<Long> {

    /**
     * 进展ID
     */
    private Long progressId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户头像链接
     */
    private String userAvatarUrl;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 回复内容
     */
    private String content;

    public static ProjectTaskProgressFeedback of(
            ProjectTaskProgressFeedbackAddDTO dto,
            Long userId,
            String userAvatarUrl,
            String userName
    ){
        ProjectTaskProgressFeedback result = new ProjectTaskProgressFeedback();

        result.setProgressId(dto.getProgressId());
        result.setUserId(userId);
        result.setUserAvatarUrl(Strings.nullToEmpty(userAvatarUrl));
        result.setUserName(Strings.nullToEmpty(userName));
        result.setContent(dto.getContent());

        return result;
    }
}
