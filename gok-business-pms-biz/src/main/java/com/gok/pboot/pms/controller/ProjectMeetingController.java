package com.gok.pboot.pms.controller;

import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.module.excel.api.annotation.RequestExcel;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.module.excel.api.enums.ExcelDateEnum;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.base.PropertyFilters;
import com.gok.pboot.pms.entity.dto.ProjectMeetingDTO;
import com.gok.pboot.pms.entity.dto.ProjectMeetingImportExcelDTO;
import com.gok.pboot.pms.entity.vo.ProjectMeetingExportExcelVO;
import com.gok.pboot.pms.entity.vo.ProjectMeetingFindPageVO;
import com.gok.pboot.pms.entity.vo.ProjectMeetingVO;
import com.gok.pboot.pms.handler.ProjectMeetingImportListener;
import com.gok.pboot.pms.service.IProjectMeetingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.websocket.server.PathParam;
import java.util.List;

/**
 * <AUTHOR>
 * @description 项目会议纪要 前端控制器
 * @menu 项目会议纪要
 * @since 2023-07-13
 **/
@Slf4j
@RestController
@RequestMapping("/projectMeeting")
@RequiredArgsConstructor
@Api(tags = "项目会议纪要表")
public class ProjectMeetingController {

    private final IProjectMeetingService service;

    /**
     * 分页查询 项目会议纪要
     *
     * @param pageRequest 分页请求实体
     * @param request     {@link HttpServletRequest}
     * @return {@link ApiResult}<{@link Page}<{@link ProjectMeetingFindPageVO}>>
     */
    @GetMapping("/findPage")
    public ApiResult<Page<ProjectMeetingFindPageVO>> findPage(PageRequest pageRequest, HttpServletRequest request) {
        return ApiResult.success(service.findPageList(pageRequest, PropertyFilters.get(request)));
    }

    /**
     * 根据id查询会议详情
     *
     * @param id 会议纪要主键id
     * @return {@link ApiResult}{@link ProjectMeetingVO}
     */
    @GetMapping("/{id}")
    public ApiResult<ProjectMeetingVO> findById(@PathVariable("id") Long id) {
        return ApiResult.success(service.getById(id));
    }

    /**
     * 新增 项目会议纪要
     *
     * @param request 新增请求实体
     * @return {@link ApiResult}
     */
    @PostMapping
    public ApiResult<Long> save(@Valid @RequestBody ProjectMeetingDTO request) {
        return ApiResult.success(service.save(request));
    }

    /**
     * 编辑 项目会议纪要
     *
     * @param request 编辑请求实体
     * @return {@link ApiResult}
     */
    @PutMapping
    public ApiResult<Long> update(@Valid @RequestBody ProjectMeetingDTO request) {
        return ApiResult.success(service.update(request));
    }

    /**
     * 批量逻辑删除
     *
     * @param list 要删除的项目风险id集合[]
     * @return {@link ApiResult}
     */
    @DeleteMapping("/batchDel")
    public ApiResult<List<Long>> batchDel(@RequestBody List<Long> list) {
        return ApiResult.success(service.batchDel(list));
    }

    /**
     * 导入项目会议纪要
     *
     * @param projectId     项目id
     * @param excelDtoList  解析excel文件得到的数据集合
     * @param bindingResult 参数校验失败信息
     * @return {@link ApiResult}
     */
    @Validated
    @PostMapping("/import")
    public ApiResult importProjectMeeting(@NotNull(message = "项目id不能为空") @PathParam("projectId") Long projectId,
                                          @RequestExcel(readListener = ProjectMeetingImportListener.class, ignoreEmptyRow = true)
                                          List<ProjectMeetingImportExcelDTO> excelDtoList,
                                          BindingResult bindingResult) {
        return service.importByExcel(projectId, excelDtoList, bindingResult);
    }

    /**
     * 导出项目会议纪要
     *
     * @param pageRequest 分页请求
     * @param request     查询参数
     * @return {@link List}{@link ProjectMeetingExportExcelVO}
     */
    @GetMapping("/export")
    @ApiOperation(value = "项目会议纪要导出", notes = "项目会议纪要导出")
    @ResponseExcel(name = "项目会议纪要",nameWithDate = ExcelDateEnum.DATE, dateFormat = "yyyy-MM-dd", writeHandler = LongestMatchColumnWidthStyleStrategy.class)
    public List<ProjectMeetingExportExcelVO> exportExcel(PageRequest pageRequest, HttpServletRequest request) {
        return service.export(pageRequest, PropertyFilters.get(request));
    }

}
