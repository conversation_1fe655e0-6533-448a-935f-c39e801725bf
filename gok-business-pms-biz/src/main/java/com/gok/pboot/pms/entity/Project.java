package com.gok.pboot.pms.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 项目
 *
 * <AUTHOR>
 * @since 2022-08-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(Project.ALIAS)
public class Project extends BeanEntity<Long> {

    public static final String ALIAS = "mhour_project";
    /**
    * 项目名称
    */
    private String projectName;
    /**
    * 项目编号
    */
    private String code;
    /**
    * 项目状态（0=商机，1=商机终止，2=在建，3=挂起，4=初验，5=终验，6=结项，7=异常终止）
    */
    private Integer projectStatus;
    /**
    * 项目经理人员ID
    */
    private Long managerUserId;
    /**
    * 项目经理姓名
    */
    private String managerUserName;
    /**
    * 项目销售人员ID
    */
    private Long salesmanUserId;
    /**
    * 项目销售姓名
    */
    private String salesmanUserName;
    /**
     * 项目售前人员ID
     */
    private Long preSalesmanUserId;
    /**
     *项目售前姓名
     */
    private String preSalesmanUserName;
    /**
    * 业务归属（一级）部门ID
    */
    private Long deptId;


}
