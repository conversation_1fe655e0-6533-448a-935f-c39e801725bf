package com.gok.pboot.pms.cost.controller;


import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.cost.entity.dto.CostConfigSubsidyCustomDTO;
import com.gok.pboot.pms.cost.entity.vo.CostConfigSubsidyCustomVO;
import com.gok.pboot.pms.cost.service.ICostConfigSubsidyCustomService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 自定义补贴配置 控制器
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@RestController
@Validated
@AllArgsConstructor
@RequestMapping("/costConfigSubsidyCustom")
public class CostConfigSubsidyCustomController {

    private ICostConfigSubsidyCustomService service;

    /**
     * 获取自定义补贴配置列表
     *
     * @return {@link ApiResult }<{@link List }<{@link CostConfigSubsidyCustomVO }>>
     */
    @GetMapping("/findList")
    public ApiResult<List<CostConfigSubsidyCustomVO>> getCostConfigSubsidyCustomList() {
        return  ApiResult.success(service.getCostConfigSubsidyCustomList(), "获取成功");
    }

    /**
     * 编辑自定义补贴配置
     *
     * @param dtoList DTO 列表
     * @return {@link ApiResult }<{@link String }>
     */
    @PostMapping("/edit")
    public ApiResult<String> editCostConfigSubsidyCustomList(@Valid @RequestBody List<CostConfigSubsidyCustomDTO> dtoList) {
        service.editCostConfigSubsidyCustomList(dtoList);
        return ApiResult.success("编辑成功");
    }

    /**
     * 根据版本ID获取自定义补贴配置列表
     *
     * @param versionId 版本 ID
     * @return {@link ApiResult }<{@link List }<{@link CostConfigSubsidyCustomVO }>>
     */
    @GetMapping("/findByVersionId/{versionId}")
    public ApiResult<List<CostConfigSubsidyCustomVO>> getCostConfigSubsidyCustomListByVersionId(@PathVariable Long versionId) {
        return ApiResult.success(service.getCostConfigSubsidyCustomListByVersionId(versionId), "获取成功");
    }

}
