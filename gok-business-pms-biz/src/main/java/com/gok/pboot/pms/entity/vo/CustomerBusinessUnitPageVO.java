package com.gok.pboot.pms.entity.vo;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
* <p>
* 客户经营单元所属客户(分页VO)
* </p>
*
* <AUTHOR>
* @since 2024-10-12
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CustomerBusinessUnitPageVO {

    /**
     * id
     */
    private Long id;

    /**
     * 客户经营单元id
     */
    private Long businessId;

    /**
     * 经营单元名称
     */
    private String businessName;


    /**
     * 所属客户经理姓名
     */
    private String unitManager;


    /**
     * 所属客户经理id
     */
    private Long unitManagerId;

    /**
     * 所属客户名称
     */
    private String unitName;

    /**
     * 跟进商机
     */
    private Integer businessCount;

    /**
     * 在建项目
     */
    private Integer doingProjectCount;

    /**
     *结项项目
     */
    private Integer doneProjectCount;

}
