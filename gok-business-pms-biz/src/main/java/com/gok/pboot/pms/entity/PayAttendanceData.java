package com.gok.pboot.pms.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.sql.Date;

/**
 * 导入工资条考勤数据同步
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Data
@TableName(PayAttendanceData.ALIAS)
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PayAttendanceData {

    public static final String ALIAS = "mhour_pay_attendance_data";
    /**
     * 同步主键
     */
    private Long id;

    /**
     * OA表主键
     */
    private Long oaId;

    /**
     * 工资单时间
     */
    private Date payrollTime;

    /**
     * 应出勤天数
     */
    private BigDecimal cwDueAttendance;

    /**
     * 实际出勤天
     */
    private BigDecimal cwActualAttendance;

    /**
     * 工号
     */
    private String workCode;

    /**
     * 公司应发工资
     */
    private String salary;

}
