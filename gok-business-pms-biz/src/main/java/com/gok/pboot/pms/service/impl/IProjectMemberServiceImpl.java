package com.gok.pboot.pms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.entity.domain.ProjectMember;
import com.gok.pboot.pms.mapper.ProjectMemberMapper;
import com.gok.pboot.pms.service.IProjectMemberService;
import org.springframework.stereotype.Service;

import java.util.List;


@Service("projectMemberService")
public class IProjectMemberServiceImpl extends ServiceImpl<ProjectMemberMapper, ProjectMember> implements IProjectMemberService {


    @Override
    public List<ProjectMember> getByProjectId(Long id) {
        QueryWrapper<ProjectMember> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProjectMember::getProjectId, id);
        return baseMapper.selectList(queryWrapper);
    }
}