package com.gok.pboot.pms.cost.excel;

import cn.hutool.core.util.StrUtil;
import com.gok.bcp.admin.vo.DictKvVo;
import com.gok.components.common.util.SpringContextHolder;
import com.gok.pboot.pms.service.IDictService;

/**
 * <AUTHOR>
 */
public class PmsDictSelect implements ExcelDynamicSelect {

    @Override
    public String[] getSource(String code) {
        if (StrUtil.isBlank(code)) {
            return new String[0];
        }
        IDictService dictService = SpringContextHolder.getBean(IDictService.class);
        return dictService.getDictKvList(code).getData().stream()
                .map(DictKvVo::getName).toArray(String[]::new);
    }

}