package com.gok.pboot.pms.entity.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 异常日报列表查询
 *
 * <AUTHOR>
 * @version 1.3.4
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class AbnormalDailyPaperFindPageVO {

    /**
     * 总数
     */
    private Integer totalNum;

    /**
     * 待审核数
     */
    private Integer waitingReviewNum;

    /**
     * 提交异常数
     */
    private Integer wrongSubmitNum;

    /**
     * 分页数据
     */
    private Page<AbnormalDailyPaperVO> pageData;

    public static AbnormalDailyPaperFindPageVO of(
            Integer totalNum,
            Integer waitingReviewNum,
            Integer wrongSubmitNum,
            Page<AbnormalDailyPaperVO> pageData
    ) {
        AbnormalDailyPaperFindPageVO result = new AbnormalDailyPaperFindPageVO();

        result.setTotalNum(totalNum);
        result.setWaitingReviewNum(waitingReviewNum);
        result.setWrongSubmitNum(wrongSubmitNum);
        result.setPageData(pageData);

        return result;
    }
}
