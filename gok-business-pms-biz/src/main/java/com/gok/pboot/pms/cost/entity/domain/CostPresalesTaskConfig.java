package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 售前报工工单配置表
 *
 * <AUTHOR>
 * @date 2025/04/09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("cost_presales_task_config")
public class CostPresalesTaskConfig extends BeanEntity<Long> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 工单名称
     */
    private String taskName;

    /**
     * 工单类别
     */
    private Integer taskCategory;

    /**
     * 工单级别（1一级，2二级，3三级）
     */
    private Integer taskLevel;

    /**
     * 工单描述
     */
    private String taskDesc;

    /**
     * 拆解类型（0=标准工单，1=总成工单）
     */
    private Integer disassemblyType;

    /**
     * 工单负责人类型(0=项目角色，1=指定人)
     */
    private Integer managerType;

    /**
     * 工单负责人角色(0=售前经理、1=项目经理、2=客户经理)
     */
    private Integer managerRole;

    /**
     * 工单负责人id
     */
    private Long managerId;

    /**
     * 工单负责人姓名
     */
    private String managerName;
} 