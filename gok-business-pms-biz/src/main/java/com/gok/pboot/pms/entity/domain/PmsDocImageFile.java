package com.gok.pboot.pms.entity.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * OA文件映射
 *
 * <AUTHOR>
 * @date 2024/2/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("pms_docimagefile")
public class PmsDocImageFile extends Model<PmsDocImageFile> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 文件id
     */
    private Long docId;
    /**
     * imagefileid
     */
    private Long imageFileId;

    /**
     * 文件名
     */
    private String imageFileName;
    /**
     * '文件上传人id'
     */
    private Long operateUserId;

    /**
     * 流程id
     */
    private Long requestId;


}
