package com.gok.pboot.pms.cost.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 人员维度交付任务统计 vo
 *
 * <AUTHOR>
 * @date 2025/05/13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ProjectPerDimDeliverStatVO extends ProjectDeliverStatBaseVO {

    /**
     * 项目编号
     */
    @ExcelProperty("项目编号")
    private String projectNo;

    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    private String projectName;

    /**
     * 人员id
     */
    @ExcelIgnore
    private Long userId;

    /**
     * 员工工号
     */
    @ExcelProperty("员工工号")
    private String workCode;

    /**
     * 员工姓名
     */
    @ExcelProperty("员工姓名")
    private String employeeName;

    /**
     * 单项目个人评分
     */
    @ExcelProperty("单项目个人评分")
    private BigDecimal singleProjectScore;

    /**
     * 个人评分排名
     */
    @ExcelProperty("个人评分排名")
    private Integer personalRank;

    /**
     * 分配工单产值
     */
    @ExcelProperty("分配工单产值")
    private BigDecimal income;

    /**
     * 结算工单产值
     */
    @ExcelProperty("结算工单产值")
    private BigDecimal settleIncome;

    /**
     * 待结算工单产值
     */
    @ExcelProperty("待结算工单产值")
    private BigDecimal waitSettleIncome;

    /**
     * 预算人工成本
     */
    @ExcelProperty("预算人工成本")
    private BigDecimal budgetCost;

    /**
     * 实际已发生人工成本
     */
    @ExcelProperty("实际已发生人工成本")
    private BigDecimal actualLaborCost;

    /**
     * 剩余人工成本
     */
    @ExcelProperty("剩余人工成本")
    private BigDecimal remainingLaborCosts;

    /**
     * 预算工时
     */
    @ExcelProperty("预算工时")
    private BigDecimal estimatedHours;

    /**
     * 实际已发生工时
     */
    @ExcelProperty("实际已发生工时")
    private BigDecimal actualHours;

    /**
     * 已下发工单个数
     */
    @ExcelProperty("已下发工单个数")
    private Integer issuedTaskCount;

    /**
     * 已完成工单个数
     */
    @ExcelProperty("已完成工单个数")
    private Integer completedTaskCount;

    /**
     * 待完成工单个数
     */
    @ExcelProperty("待完成工单个数")
    private Integer waitCompleteTaskCount;

    /**
     * 待评价工单个数
     */
    @ExcelProperty("待评价工单个数")
    private Integer waitEvaluateTaskCount;


}
