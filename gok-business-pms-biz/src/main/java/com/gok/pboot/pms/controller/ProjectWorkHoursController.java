package com.gok.pboot.pms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.base.PropertyFilters;
import com.gok.pboot.pms.entity.vo.ProjectTaskFindPageVO;
import com.gok.pboot.pms.entity.vo.ProjectWorkHoursVo;
import com.gok.pboot.pms.entity.vo.WorkHoursDetailVO;
import com.gok.pboot.pms.service.IProjectWorkHoursService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @description 项目详情-工时统计 前端控制器
 * @menu 项目详情-工时统计
 * @since 2023-07-13
 **/
@Slf4j
@RestController
@RequestMapping("/projectWorkHour")
@RequiredArgsConstructor
@Api(tags = "项目详情-工时统计")
public class ProjectWorkHoursController {

    private final IProjectWorkHoursService service;

    /**
     * 分页查询 项目工时统计
     *
     * @param pageRequest 分页请求实体
     * @param request     {@link HttpServletRequest}
     * @return {@link ApiResult}{@link ProjectWorkHoursVo}
     */
    @GetMapping("/countWorkHours")
    public ApiResult<ProjectWorkHoursVo> countWorkHours(PageRequest pageRequest, HttpServletRequest request) {
        return ApiResult.success(service.countWorkHours(pageRequest, PropertyFilters.get(request)));
    }

    /**
     * 分页查询 项目工时统计  任务模糊查询
     *
     * @param projectId projectId
     * @return
     */
    @GetMapping("/getTaskByTaskNameLike")
    public ApiResult<List<ProjectTaskFindPageVO>> getTaskByTaskNameLike(@RequestParam("projectId") Long projectId) {
        return ApiResult.success(service.getTaskByTaskNameLike(projectId));
    }

    /**
     * 获取工时详情记录
     *
     * @param pageRequest 分页对象
     * @param request     参数对象
     * @return 分页列表
     */
    @GetMapping("/getHoursDetail")
    public ApiResult<Page<WorkHoursDetailVO>> getHoursDetail(PageRequest pageRequest, HttpServletRequest request) {
        return ApiResult.success(service.getHoursDetail(pageRequest, PropertyFilters.get(request)));
    }
}
