package com.gok.pboot.pms.enumeration;

/**
 * 人员类别枚举
 */
public enum EmployeeTypeEnum implements ValueEnum<Integer> {

    PERMANENT(0,"在编人员"),

    REGULAR_DELIVER(1,"正式交付人员"),

    INTERN_DELIVER(2, "实习交付人员"),

    BTWAR(3, "退休返聘人员"),     // back to work after retirement

    NOT_FULL_TIME(4, "非全日制人员"),

    INTERNAL_INTERN(5, "内部实习生"),

    PART_TIME(6, "兼职");

    private final Integer value;
    /**
     * 名称
     */
    private final String name;

    EmployeeTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }
}
