package com.gok.pboot.pms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.BaseController;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.dto.PrivilegeAddAndUpdateDTO;
import com.gok.pboot.pms.entity.dto.PrivilegeFindPageDTO;
import com.gok.pboot.pms.entity.vo.AdminConfigFindPageVo;
import com.gok.pboot.pms.service.IPrivilegeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description 管理员配置 前端控制器
 * @menu 管理员配置
 * @since 2022-08-29
 */
@Slf4j
@RestController
@RequestMapping("privilege")
public class PrivilegeController extends BaseController {

    private IPrivilegeService service;

    @Autowired
    public PrivilegeController(IPrivilegeService service) {
        this.service = service;
    }

    /**
     * 添加管理员配置
     *
     * @param privilegeAddAndUpdateDTO
     * @return {@link ApiResult}
     */
    @PostMapping("/add")
    public ApiResult<String> add(@Valid @RequestBody PrivilegeAddAndUpdateDTO privilegeAddAndUpdateDTO) {
        return service.save(privilegeAddAndUpdateDTO);
    }

    /**
     * 编辑 管理员配置
     *
     * @param privilegeAddAndUpdateDTO
     * @return {@link ApiResult}
     */
    @PostMapping("/update")
    public ApiResult<String> update(@Valid @RequestBody PrivilegeAddAndUpdateDTO privilegeAddAndUpdateDTO) {
        return service.update(privilegeAddAndUpdateDTO);
    }

    /**
     * 批量逻辑删除
     *
     * @param list 要删除的项目id集合[]
     * @return {@link ApiResult}
     */
    @PostMapping("/batchDel")
    public ApiResult<Void> batchDel(@RequestBody List<Long> list) {
        service.batchDel(list);

        return ApiResult.success(null);
    }

    /**
     * 分页查询管理员配置
     *
     * @param pageRequest
     * @param privilegeFindPageDTO
     * @return {@link ApiResult<Page<AdminConfigFindPageVo>>}
     * @customParam name 人员姓名
     * @customParam projectCode 项目编号
     * @customParam projectName 项目名称
     */
    @GetMapping("/findPage")
    @PreAuthorize("@pms.hasPermission('STAFFING_AUDITOR_CONFIG')")
    public ApiResult<Page<AdminConfigFindPageVo>> AdminConfigFindPageVo(PageRequest pageRequest, PrivilegeFindPageDTO privilegeFindPageDTO) {
        return success(service.findPage2(pageRequest, privilegeFindPageDTO));
    }

}
