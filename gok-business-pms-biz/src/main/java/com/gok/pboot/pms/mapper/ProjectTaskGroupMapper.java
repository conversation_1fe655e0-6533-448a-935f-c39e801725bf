package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.entity.domain.ProjectTaskGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ProjectTaskGroupMapper extends BaseMapper<ProjectTaskGroup> {

    /**
     * 获取当前项目的分组数
     * @param projectId 项目id
     * @return {@link Integer}
     */
    Integer getCountByProjectId(@Param("projectId") Long projectId);

    /**
     * 删除分组
     * @param id 分组id
     */
    void delete(@Param("id") Long id);

    /**
     * 获取当前项目分组集合
     * @param projectId 项目id
     * @return 分组集合
     */
    List<ProjectTaskGroup> getListByProjectId(@Param("projectId") Long projectId);
}
