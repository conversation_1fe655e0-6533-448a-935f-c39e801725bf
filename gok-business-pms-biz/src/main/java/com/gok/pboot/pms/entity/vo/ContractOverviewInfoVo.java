package com.gok.pboot.pms.entity.vo;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 合同概览信息vo
 *
 * <AUTHOR>
 * @date 2024/2/22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ContractOverviewInfoVo {

    /**
     * 合同id
     */
    private Long id;

    /**
     * 合同名称
     */
    private String htmc;

    /**
     * 合同金额(含税)
     */
    private BigDecimal htje;

    /**
     * 合同起始日期
     */
    private String htqsrq;

    /**
     * 合同截止日期
     */
    private String htjzrq;

    /**
     * 项目经理id
     */
    private Long xmjl;

    /**
     * 项目经理
     */
    private String xmjlName;

    /**
     * 实际合同签订日期
     */
    private String sjhtqdrq;

    /**
     * 项目立项日期
     */
    private String xmlxrq;

    /**
     * 项目产值进度
     */
    private String xmczjd;

    /**
     * 已开票金额（元）
     */
    private BigDecimal ykpje;

    /**
     * 已开票比例
     */
    private String ykpbl;

    /**
     * 待开票金额（元）
     */
    private BigDecimal dkpje;

    /**
     * 已回款金额
     * <p>
     * (原已收款金额)
     */
    private BigDecimal yskje;

    /**
     * 已回款比例
     * <p>
     * （原已收款比例）
     */
    private String yskbl;

    /**
     * 待回款金额
     * <p>
     * （原待收款金额）
     */
    private BigDecimal dskje;

}
