package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.cost.entity.domain.CostManagePersonnelLevelDetail;
import com.gok.pboot.pms.cost.entity.vo.CostManagePersonnelLevelDetailVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 成本管理人员级别测算明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Mapper
public interface CostManagePersonnelLevelDetailMapper extends BaseMapper<CostManagePersonnelLevelDetail> {

    /**
     * 批量保存
     *
     * @param saveEntries 保存对象集合
     */
    void batchSave(@Param("saveEntries") List<CostManagePersonnelLevelDetail> saveEntries);

    /**
     * 根据预估结果id集合查询
     *
     * @param estimateResultIdList 预估结果id集合
     * @return
     */
    List<CostManagePersonnelLevelDetailVO> findByEstimateResultIdList(@Param("estimateResultIdList") List<Long> estimateResultIdList);

}
