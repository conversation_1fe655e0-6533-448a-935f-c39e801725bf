package com.gok.pboot.pms.entity.bo;

import lombok.*;

import java.math.BigDecimal;

/**
 * 复用交付工时审核-查看日报- 统计
 * <AUTHOR>
 * @version 1.3.2
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class DailyReviewReuseAndDeliveryViewTotalBO {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 审核人数
     */
    private Integer approvalNum;

    /**
     * 汇总工时（人天）
     */
    private BigDecimal aggregatedDays;
}
