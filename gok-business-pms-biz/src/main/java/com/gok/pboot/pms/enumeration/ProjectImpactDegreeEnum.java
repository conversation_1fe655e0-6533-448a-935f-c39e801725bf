package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;

/**
 * 项目影响度
 *
 * <AUTHOR>
 * @version 1.1.0
 */
@AllArgsConstructor
public enum ProjectImpactDegreeEnum implements ValueEnum<Integer> {

    HIGH(0, "高"),
    MEDIUM(1, "中"),
    LOW(2, "低");

    private final Integer value;

    private final String name;

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }

}
