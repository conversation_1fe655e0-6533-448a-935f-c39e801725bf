package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.entity.domain.ProjectTaskProgressFeedback;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 项目任务回复
 *
 * <AUTHOR>
 * @version 1.1.0
 */
@Mapper
public interface ProjectTaskProgressFeedbackMapper extends BaseMapper<ProjectTaskProgressFeedback> {
    
    List<ProjectTaskProgressFeedback> findByProgressIds(@Param("progressIds") Collection<Long> progressIds);

    boolean existsByProgressId(Long progressId);

    int countByProgressId(Long progressId);
}
