package com.gok.pboot.pms.handler;

import com.gok.pboot.pms.cost.entity.domain.CostTaskDailyPaper;
import com.gok.pboot.pms.entity.DailyPaper;
import com.gok.pboot.pms.enumeration.DailyPaperAbnormalEnum;
import org.springframework.data.util.Pair;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 日报分析器
 *
 * <AUTHOR>
 * @version 1.3.4
 */
public interface DailyPaperAnalyzer {

    /**
     * 获取日报异常信息
     * @param dailyPaper 日报对象
     * @param leaveHour 请休假时长
     * @return 异常类型, 异常信息文本
     */
    List<Pair<DailyPaperAbnormalEnum, String>> getDailyPaperAbnormalInfo(
            DailyPaper dailyPaper, BigDecimal leaveHour
    );

    /**
     * 获取日报异常信息
     * @param submissionDate 日报提交日期
     * @param approvalStatus 日报审核状态
     * @param workDay 日报工作日状态
     * @param dailyHourCount 日常工时
     * @param leaveHour 请休假时长
     * @return 异常类型, 异常信息文本
     */
    List<Pair<DailyPaperAbnormalEnum, String>> getAbnormalInfo(
            LocalDate submissionDate,
            Integer approvalStatus,
            Integer workDay,
            BigDecimal dailyHourCount,
            BigDecimal leaveHour
    );

    /**
     * 获取日报异常信息
     * @param dailyPaper 日报对象
     * @param leaveHour 请休假时长
     * @return 异常类型, 异常信息文本
     */
    List<Pair<DailyPaperAbnormalEnum, String>> getDailyPaperAbnormalInfo(
            CostTaskDailyPaper dailyPaper, BigDecimal leaveHour
    );

}
