package com.gok.pboot.pms.entity.vo;

import com.gok.bcp.upms.vo.SysMenuVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * 项目任务甘特图vo
 *
 * <AUTHOR>
 * @date 2024/02/02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectTaskeGanttVo {

    /**
     * 任务ID
     */
    private Long id;

    /**
     * 所属项目ID
     */
    private Long projectId;

    /**
     * 任务名称
     */
    private String title;

    /**
     * 任务类型（0=售前支撑，1=售后交付，null=内部项目不区分）
     * @link com.gok.pboot.pms.enumeration.ProjectTaskKindEnum
     */
    private Integer kind;

    /**
     * 任务类型
     */
    private String kindTxt;

    /**
     * 任务状态（0=正常，1=结束）
     * @link com.gok.pboot.pms.enumeration.ProjectTaskStateEnum
     */
    private Integer state;

    /**
     * 任务类型
     */
    private String stateTxt;

    /**
     * 总工时
     */
    private String totalHour;
    /**
     * 正常工时
     */
    private String normalHour;
    /**
     * 加班工时
     */
    private String addedHour;

    /**
     * 是否长期任务
     */
    private Integer permanentFlag;

    /**
     * 是否长期任务txt
     */
    private String permanentFlagTxt;

    /**
     * 计划开始日期
     */
    private LocalDate expectStartDate;

    /**
     * 计划结束日期
     */
    private LocalDate expectEndDate;

    /**
     * 实际开始日期
     */
    private LocalDate startDate;

    /**
     * 实际结束日期
     */
    private LocalDate endDate;

    /**
     * 任务负责人姓名（逗号分隔）
     */
    private String leaderNames;

    /**
     * 任务参与人姓名（逗号分隔）
     */
    private String memberNames;

    /**
     * 任务负责人列表
     */
    private List<ProjectTaskeUserVo> leaders;

    /**
     * 任务参与人列表
     */
    private List<ProjectTaskeUserVo> members;

    /**
     * 按钮权限列表
     */
    private List<SysMenuVo> buttonAuthorities;

    /**
     * 甘特图跨度
     */
    private Long duration;

    /**
     * 甘特图开始日期
     */
    private LocalDate beginDate;

    /**
     * 甘特图结束日期
     */
    private LocalDate finalDate;

    /**
     * 甘特图是否分裂（否：null，是：split）
     */
    private String render;

    /**
     * 正常工时进度（无加班时为0）
     */
    private String progress;

    /**
     * 甘特图父任务id
     */
    private Long parentId;

}
