package com.gok.pboot.pms.entity.domain;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.entity.dto.ProjectSystemProcessInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 项目系统过程动态信息表
 *
 * <AUTHOR>
 * @since 2023-07-14
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(ProjectRisk.ALIAS)
public class ProjectSystemProcessInfo extends BeanEntity<Long> {

    public static final String ALIAS = "project_system_process_info";

    /**
     * 会议纪要id/周报id
     */
    private Long requestId;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 申请人id
     */
    private Long applicatId;

    /**
     * 申请人
     */
    private String applicat;

    /**
     * 流程类型
     */
    private String processType;

    /**
     * 流程状态
     */
    private String status;

    /**
     * 流程名
     */
    private String name;

    public static ProjectSystemProcessInfo buildSave(ProjectSystemProcessInfoDTO request) {
        ProjectSystemProcessInfo entity = new ProjectSystemProcessInfo();
        BeanUtil.copyProperties(request, entity);
        BaseBuildEntityUtil.buildInsert(entity);
        return entity;
    }

}
