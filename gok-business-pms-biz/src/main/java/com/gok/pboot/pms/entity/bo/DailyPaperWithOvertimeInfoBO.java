package com.gok.pboot.pms.entity.bo;

import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.entity.DailyPaper;
import com.gok.pboot.pms.entity.OvertimeLeaveData;
import com.gok.pboot.pms.enumeration.LeaveStatusEnum;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * - 带加班信息的日报 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/11/14 9:44
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DailyPaperWithOvertimeInfoBO {

    private Long id;

    /**
     * 员工ID
     */
    private Long userId;

    /**
     * 员工类型（0=正式，1=实习）
     */
    private Integer userStatus;

    /**
     * 员工部门ID
     */
    private Long userDeptId;

    /**
     * 提交日期
     */
    private LocalDate submissionDate;

    /**
     * 是否工作日（0=否，1=是）
     */
    private Integer workday;

    /**
     * 填报状态（0=正常，1=滞后）
     */
    private Integer fillingState;

    /**
     * 审核状态（0=未提交，1=已退回，2=待审核，3=不通过，4=已通过）
     */
    private Integer approvalStatus;

    /**
     * 项目数量
     */
    private Integer projectCount;

    /**
     * 任务数量
     */
    private Integer taskCount;

    /**
     * 日常工时数量
     */
    private BigDecimal dailyHourCount;

    /**
     * 加班工时数量
     */
    private BigDecimal addedHourCount;

    /**
     * 请假信息对象列表
     */
    private List<OvertimeLeaveData> overtimeLeaveData;

    /**
     * 总请假时长（扣除销假）
     */
    private BigDecimal totalOvertimeHourData;

    public DailyPaperWithOvertimeInfoBO(DailyPaper dailyPaper){
        this.id = dailyPaper.getId();
        this.userId = dailyPaper.getUserId();
        this.userStatus = dailyPaper.getUserStatus();
        this.userDeptId = dailyPaper.getUserDeptId();
        this.submissionDate = dailyPaper.getSubmissionDate();
        this.workday = dailyPaper.getWorkday();
        this.fillingState = dailyPaper.getFillingState();
        this.approvalStatus = dailyPaper.getApprovalStatus();
        this.projectCount = dailyPaper.getProjectCount();
        this.taskCount = dailyPaper.getTaskCount();
        this.dailyHourCount = dailyPaper.getDailyHourCount();
        this.addedHourCount = dailyPaper.getAddedHourCount();
        this.overtimeLeaveData = Lists.newArrayListWithCapacity(0);
        this.totalOvertimeHourData = BigDecimal.ZERO;
    }

    public DailyPaperWithOvertimeInfoBO(DailyPaper dailyPaper, List<OvertimeLeaveData> overtimeLeaveData){
        this(dailyPaper);
        this.overtimeLeaveData = overtimeLeaveData;
        for (OvertimeLeaveData ol : overtimeLeaveData) {
            if (EnumUtils.valueEquals(ol.getType(), LeaveStatusEnum.XJ)) {
                this.totalOvertimeHourData = this.totalOvertimeHourData.subtract(ol.getHourData());
            } else {
                this.totalOvertimeHourData = this.totalOvertimeHourData.add(ol.getHourData());
            }
        }
    }
}
