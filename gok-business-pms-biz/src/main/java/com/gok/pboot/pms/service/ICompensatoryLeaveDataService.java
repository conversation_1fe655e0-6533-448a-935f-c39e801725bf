package com.gok.pboot.pms.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.entity.CompensatoryLeaveData;
import com.gok.pboot.pms.entity.dto.PanelRequestDTO;
import com.gok.pboot.pms.entity.vo.CompensatoryLeaveDataDetailVO;
import com.gok.pboot.pms.entity.vo.CompensatoryLeaveDataVO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;


/**
 * 加班、请假、销假数据同步
 *
 * <AUTHOR>
 * @date 2024-02-26 14:58:58
 */
public interface ICompensatoryLeaveDataService extends IService<CompensatoryLeaveData> {

    /**
     * 根据时间范围和用户ID查询项目调休数据
     * @param startTime 开始时间
     * @param endTime 结束日期
     * @param userId 用户ID
     * @return 项目调休数据列表
     */
    List<CompensatoryLeaveDataVO> findByDateTimeRangeAndUserId(LocalDate startTime,
                                                               LocalDate endTime,
                                                               Long userId);

    /**
     * 根据条件查询项目对应的耗时
     * @return
     */
    Map<Long, BigDecimal> getCompensatoryLeaveDataMap(PanelRequestDTO filter);

    /**
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param userId 用户id
     * @return 项目调休数据
     */
    List<CompensatoryLeaveData> getCompensatoryLeaveDataListByUserId(LocalDate startTime,
                                                                     LocalDate endTime,
                                                                     Long userId,String type);

    /**
     * 根据时间范围和用户ID查询项目调休数据
     * @param startTime 开始时间
     * @param endTime 结束日期
     * @param userIds 用户ID集合
     * @return 项目调休数据列表
     */
    List<CompensatoryLeaveDataDetailVO> findByDateTimeRangeAndUserIds(LocalDate startTime,
                                                                      LocalDate endTime,
                                                                     List<Long> userIds);
}

