package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 导出项目回款跟踪模板表
 *
 * <AUTHOR>
 * @since 2023-10-13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectPaymentExcelVO {

    /**
     * 收款公司value
     * {@link com.gok.pboot.pms.enumeration.AttributableSubjectEnum}
     */
    @ColumnWidth(40)
    @ExcelProperty("收款公司")
    private String paymentCompanyTxt;

    /**
     * 客户名称
     */
    @ColumnWidth(25)
    @ExcelProperty("客户名称")
    private String customerName;

    /**
     * 企业名称
     */
    @ColumnWidth(25)
    @ExcelProperty("企业名称")
    private String enterpriseName;

    /**
     * 收款日期
     */
    @ColumnWidth(20)
    @ExcelProperty("收款日期")
    private String paymentDate;

    /**
     * 收款金额
     */
    @ColumnWidth(20)
    @ExcelProperty("收款金额")
    private String paymentAmount;

    /**
     * 收款平台value
     * {@link com.gok.pboot.pms.enumeration.AttributableSubjectEnum}
     */
    @ColumnWidth(20)
    @ExcelProperty("收款平台")
    private String paymentPlatformTxt;

    /**
     * 银行账户
     */
    @ColumnWidth(20)
    @ExcelProperty("银行账户")
    private String bankAccount;

    /**
     * 备注
     */
    @ColumnWidth(60)
    @ExcelProperty("备注")
    private String paymentNote;

}
