package com.gok.pboot.pms.common.base;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2019-11-12 11:18
 * @desc 租户基础公用类
 **/

@Data
@EqualsAndHashCode(callSuper = false)
public class TenantBeanEntity<T> extends BeanEntity<T> {

    @JsonSerialize(using = ToStringSerializer.class)
    protected T tenantId;
}
