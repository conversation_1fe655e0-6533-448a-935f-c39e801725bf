package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.entity.domain.PmsContractProcessInfo;
import com.gok.pboot.pms.entity.vo.ContractProcessInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 合同台账相关流程信息Mapper
 * <AUTHOR>
 */
@Mapper
public interface PmsContractProcessInfoMapper extends BaseMapper<PmsContractProcessInfo> {

    /**
     * 根据合同ID和项目ID查询流程数据
     * @param contractId
     * @param projectId
     * @return
     */
    List<ContractProcessInfoVO> selByContractIdAndProjectId(@Param("contractId") Long contractId,@Param("projectId") Long projectId);
}