package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.domain.ProjectProcessInfo;
import com.gok.pboot.pms.entity.vo.ProjectProcessInfoFindPageVO;
import com.gok.pboot.pms.mapper.ProjectProcessInfoMapper;
import com.gok.pboot.pms.service.IProjectProcessInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 项目动态表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-14
 **/
@Slf4j
@Service
public class ProjectProcessInfoServiceImpl extends ServiceImpl<ProjectProcessInfoMapper, ProjectProcessInfo> implements IProjectProcessInfoService {

    @Override
    public Page<ProjectProcessInfoFindPageVO> findPage(PageRequest pageRequest, Map<String, Object> filter) {
        Page<ProjectProcessInfo> page =
                baseMapper.findListPage(new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize()), filter);
        if (CollUtil.isEmpty(page.getRecords())) {
            return new Page();
        }
        Page<ProjectProcessInfoFindPageVO> pageResult = BeanUtil.copyProperties(page, Page.class);
        List<ProjectProcessInfoFindPageVO> voList =
                BeanUtil.copyToList(page.getRecords(), ProjectProcessInfoFindPageVO.class);
        return pageResult.setRecords(voList);
    }

}
