package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 客户沟通记录-导出excel
 *
 * <AUTHOR>
 * @date 2023/11/21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class CCRExportExcelVO {

    /**
     * 主键ID
     */
    @ExcelIgnore
    private Long id;

    /**
     * 客户名称
     */
    @ExcelProperty({"客户名称"})
    @ColumnWidth(40)
    private String customerName;

    /**
     * 客户分级
     */
    @ExcelProperty({"客户分级"})
    @ColumnWidth(20)
    private String customerGrade;

    /**
     * 交流方式
     */
    @ExcelProperty({"交流方式"})
    @ColumnWidth(20)
    private String communicationMethod;

    /**
     * 交流时间
     */
    @ExcelProperty({"交流时间"})
    @ColumnWidth(20)
    private String communicationTime;

    /**
     * 交流主题及目标
     */
    @ExcelProperty({"交流主题及目标"})
    @ColumnWidth(40)
    private String themeAndObjectives;

    /**
     * 交流内容情况
     */
    @ExcelProperty({"交流内容情况"})
    @ColumnWidth(40)
    private String contentSituation;

    /**
     * 客户反馈及要求
     */
    @ExcelProperty({"客户的反馈及要求"})
    @ColumnWidth(40)
    private String feedbackAndRequirements;

    /**
     * 取得的关键进展
     */
    @ExcelProperty({"取得的关键进展"})
    @ColumnWidth(40)
    private String keyProgressMade;

    /**
     * 下一步计划
     */
    @ExcelProperty({"下一步计划"})
    @ColumnWidth(40)
    private String nextStepForwardPlan;

    /**
     * 客户经理
     */
    @ExcelProperty({"客户经理"})
    @ColumnWidth(20)
    private String accountManager;

    /**
     * 对方参与人
     */
    @ExcelProperty({"对方参与人"})
    @ColumnWidth(20)
    private String otherParticipants;

    /**
     * 我方参与人
     */
    @ExcelProperty({"我方参与人"})
    @ColumnWidth(20)
    private String ourParticipants;

    /**
     * 提交人
     */
    @ExcelProperty({"提交人"})
    @ColumnWidth(20)
    private String submitter;

    /**
     * 提交时间
     */
    @ExcelProperty({"提交时间"})
    @ColumnWidth(20)
    private LocalDateTime submissionTime;

}
