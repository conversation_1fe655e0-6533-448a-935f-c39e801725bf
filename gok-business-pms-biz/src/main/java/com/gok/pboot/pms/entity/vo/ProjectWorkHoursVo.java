package com.gok.pboot.pms.entity.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 项目详情-工时统计 Vo类
 *
 * <AUTHOR>
 * @since 2023-07-13
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectWorkHoursVo {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目正常工时（天）
     */
    private BigDecimal avgNormalHours;

    /**
     * 项目加班工时（天）
     */
    private BigDecimal avgAddedHours;

    /**
     * 工作日加班工时（天）
     */
    private BigDecimal workOvertimeHours;

    /**
     * 休息日加班工时（天）
     */
    private BigDecimal restOvertimeHours;

    /**
     * 节假日加班工时（天）
     */
    private BigDecimal holidayOvertimeHours;

    /**
     * 调休工时（天）
     */
    private BigDecimal txHours;

    /**
     * 待审核工时（天）
     */
    private BigDecimal dshHours;

    /**
     * 项目分摊工时（天）
     */
    private BigDecimal avgSumHours;

    /**
     * 任务或人员维度分页查询结果
     */
    private Page<ProjectTaskFindPageVO> taskOrUserPageList;

    /**
     * 开始时间
     */
    private LocalDate startDate;

    /**
     * 结束时间
     */
    private LocalDate endDate;

}
