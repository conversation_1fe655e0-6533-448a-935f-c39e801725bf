

package com.gok.pboot.pms.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/07/13
 */
@Data
@ApiModel(value = "前端用户展示对象")
public class UserJobDeptVo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@ApiModelProperty(value = "主键")
	private Long userId;

	/**
	 * 用户名
	 */
	@ApiModelProperty(value = "用户名")
	private String username;


	/**
	 * 部门ID
	 */
	@ApiModelProperty(value = "部门ID")
	private Long deptId;

   /**
	 * 部门名称
	 */
	@ApiModelProperty(value = "部门名称")
	private String deptName;

	/**
	 * 岗位id
	 */
	@ApiModelProperty(value = "岗位id")
	private Long jobId;

	/**
	 * 岗位名称
	 */
	@ApiModelProperty(value = "岗位名称")
	private String jobName;



}
