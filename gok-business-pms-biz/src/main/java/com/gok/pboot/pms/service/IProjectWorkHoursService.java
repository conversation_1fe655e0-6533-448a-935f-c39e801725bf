package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.vo.ProjectTaskFindPageVO;
import com.gok.pboot.pms.entity.vo.ProjectWorkHoursVo;
import com.gok.pboot.pms.entity.vo.WorkHoursDetailVO;

import java.util.List;
import java.util.Map;

/**
 * 项目工时统计
 *
 * <AUTHOR>
 * @since 2023-07-13
 **/
public interface IProjectWorkHoursService {

    /**
     * 工时统计查询
     *
     * @param pageRequest 分页请求
     * @param filter      查询参数
     * @return
     */
    ProjectWorkHoursVo countWorkHours(PageRequest pageRequest, Map<String, Object> filter);

    /**
     * 查询项目工时数据
     *
     * @param filter
     * @return
     */
    ProjectWorkHoursVo calculateAvgWorkHours(Map<String, Object> filter);

    /**
     * 查询项目工时数据
     *
     * @param filterList 查询条件集合
     * @return key-projectId,value-周报工时数据
     */
    List<ProjectWorkHoursVo> batchCalculateAvgWorkHours(List<Map<String, Object>> filterList);

    /**
     * 任务模糊查询
     * @param taskName
     * @return
     */
    List<ProjectTaskFindPageVO> getTaskByTaskNameLike( Long projectId);

    /**
     * 获取工时详情记录
     *
     * @param pageRequest 分页对象
     * @param filter     参数对象
     * @return 分页列表
     */
    Page<WorkHoursDetailVO> getHoursDetail(PageRequest pageRequest, Map<String, Object> filter);
}
