package com.gok.pboot.pms.cost.entity.dto;

import com.gok.pboot.pms.cost.enums.CostTaskStatDimensionEnum;
import lombok.Data;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

/**
 * 多维度工单统计查询DTO
 *
 * <AUTHOR>
 * @date 2025/05/13
 */
@Data
public class TaskStatQueryDTO  {


    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;


    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 员工姓名
     */
    private String employeeName;

    /**
     * 员工工号
     */
    private String workCode;

    /**
     * 工单编号
     */
    private String taskNo;

    /**
     * 任务类型
     */
    private Integer taskType;


    /**
     * 工单状态
     */
    private List<Integer> taskStatuses;

    /**
     * 工单名称
     */
    private String taskName;

    /**
     * 查询维度
     * @see CostTaskStatDimensionEnum
     */
    private CostTaskStatDimensionEnum dimensionEnum;


    /**
     * 项目 ID
     */
    private Collection<Long> projectIds;

    /**
     * 用户 ID
     */
    private Collection<Long> userIds;

    /**
     * 项目权限
     */
    private Collection<Long> projectAuth;

    /**
     * 用户权限
     */
    private Collection<Long> userAuth;

    /**
     * 全部权限
     */
    private Boolean isAll;
} 