package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
    * 人力外包-费用分摊
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cost_expenses_share")
public class CostExpensesShare extends BeanEntity<Long> {

    private static final long serialVersionUID = 1L;

    /**
    * 项目id
    */
    private Long projectId;

    /**
    * 项目名称
    */
    private String projectName;

    /**
    * 收款人姓名
    */
    private String recipientUserName;

    /**
    * 工号
    */
    private String workCode;

    /**
    * 所属部门id
    */
    private Long departmentId;

    /**
    * 所属部门
    */
    private String departmentName;

    /**
    * 科目名称id
    */
    private Long accountId;

    /**
    * 科目名称
    */
    private String accountName;

    /**
    * 成本科目类别id
    */
    private Long accountCategoryId;

    /**
    * 成本科目类别名称
    */
    private String accountCategoryName;

    /**
    * 报销金额
    */
    private BigDecimal reimburseMoney;

    /**
    * 关联流程ID
    */
    private Long requestId;

    /**
    * 报账编号
    */
    private String requestNumber;

    /**
    * 申请人id
    */
    private Long applicantId;

    /**
    * 申请人姓名
    */
    private String applicantName;

    /**
    * 申请时间
    */
    private String applicantTime;

    /**
     * 归属月份
     */
    private String belongingMonth;

    /**
    * 归档时间
    */
    private String filingTime;

    /**
    * 是否客户承担
    */
    private Integer customerUndertakes;

}