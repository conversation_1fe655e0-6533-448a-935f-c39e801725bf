package com.gok.pboot.pms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.module.excel.api.annotation.RequestExcel;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.BaseController;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.dto.AttendanceHourImportDTO;
import com.gok.pboot.pms.entity.dto.PersonnelDeliveryHourImportDTO;
import com.gok.pboot.pms.entity.dto.PersonnelReuseUpdateDTO;
import com.gok.pboot.pms.entity.vo.PersonnelDeliveryHourPageVO;
import com.gok.pboot.pms.enumeration.ApiResultEnum;
import com.gok.pboot.pms.handler.PersonnelDeliveryHourImportEventListener;
import com.gok.pboot.pms.service.IPersonnelDeliveryHourService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;

/**
 * 交付人员工时
 *
 * <AUTHOR>
 * @create 2023/2/9
 * @menu 交付人员工时模块
 */
@Slf4j
@RestController
@RequestMapping("/personnelDeliveryHour")
@RequiredArgsConstructor
public class PersonnelDeliveryHourController extends BaseController {

    private final IPersonnelDeliveryHourService personnelDeliveryHourService;

    /**
     * 考勤工时导入消息提醒
     *
     * @return {@link ApiResult}<{@link String}>
     */
    @Inner(value = false)
    @GetMapping("/messageAlert")
    public ApiResult<String> messageAlert() {
        return personnelDeliveryHourService.messageAlert() ? ApiResult.success("消息提醒成功") : ApiResult.failure("暂无项目需消息提醒");
    }

    /**
     * 考勤工时导入
     *
     * @param projectId     项目ID
     * @param excelList     数据列表
     * @param bindingResult 参数校验
     * @param file          文件
     * @return {@link ApiResult}
     */
    @PostMapping("/attendanceHourImport")
    public ApiResult attendanceHourImport(@RequestParam("projectId") Long projectId,
                                          @RequestExcel(readListener = PersonnelDeliveryHourImportEventListener.class, ignoreEmptyRow = true)
                                          List<AttendanceHourImportDTO> excelList,
                                          BindingResult bindingResult, @RequestParam("file") MultipartFile file) {
        ApiResult apiResult;
        try {
            apiResult = personnelDeliveryHourService.attendanceHourImport(projectId, excelList, bindingResult, file);
        } catch (Exception e) {
            log.warn("考勤工时导入解析错误", e);
            return ApiResult.builder().apiResultEnum(ApiResultEnum.IMPORT_VAILD_FAIL).result("导入解析错误,请联系管理员！").build();
        }
        return apiResult;
    }

    /**
     * 导入交付人员工时
     *
     * @param excelVOList   列表
     * @param bindingResult 错误信息列表
     * @return R
     */
    @PostMapping("/import")
    public ApiResult importUser(@RequestExcel(readListener = PersonnelDeliveryHourImportEventListener.class, ignoreEmptyRow = true)
                                List<PersonnelDeliveryHourImportDTO> excelVOList, BindingResult bindingResult,
                                @RequestParam("file") MultipartFile file) {
        ApiResult apiResult;
        try {
            apiResult = personnelDeliveryHourService.importUser(excelVOList, bindingResult, file);
        } catch (Exception e) {
            log.warn("人才复用导入解析错误", e);
            return ApiResult.builder().apiResultEnum(ApiResultEnum.IMPORT_VAILD_FAIL).result("导入解析错误,请联系管理员！").build();
        }
        return apiResult;
    }

    /**
     * 交付人员工时分页查询
     *
     * @param pageRequest 分页
     * @param projectId   项目ID
     * @param projectName 项目名称
     * @param time        时间范围
     * @param status      审核状态
     * @param name        人员姓名
     * @return {@link ApiResult<Page<PersonnelDeliveryHourPageVO>>}
     */
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_DELIVERY_PERSONNEL')")
    @GetMapping("/findPage")
    public ApiResult<Page<PersonnelDeliveryHourPageVO>> pagePersonnelDeliveryHour(
            PageRequest pageRequest, 
            @RequestParam(value = "projectId", required = false) Long projectId,
            @RequestParam("projectName") String projectName, 
            @RequestParam("time") LocalDate time,
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "name", required = false) String name) {
        return ApiResult.success(personnelDeliveryHourService.pagePersonnelDeliveryHour(projectId, pageRequest, projectName, time, status, name));
    }

    /**
     * 编辑
     *
     * @param personnelReuseUpdateDTO 实体
     * @return {@link ApiResult}
     */
    @PostMapping("/update")
    public ApiResult<String> update(@RequestBody @Valid PersonnelReuseUpdateDTO personnelReuseUpdateDTO) {
        return personnelDeliveryHourService.update(personnelReuseUpdateDTO);
    }

    /**
     * 批量逻辑删除
     *
     * @param list 要删除的id集合[]
     * @return {@link ApiResult}
     */
    @PostMapping("/batchDel")
    public ApiResult<String> batchDel(@RequestBody List<Long> list) {
        return personnelDeliveryHourService.batchDel(list);
    }
}
