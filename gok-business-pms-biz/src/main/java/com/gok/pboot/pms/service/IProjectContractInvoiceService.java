package com.gok.pboot.pms.service;



import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.entity.domain.ProjectContractInvoice;
import com.gok.pboot.pms.entity.vo.ContractInvoiceVo;

import java.util.List;
import java.util.Map;


/**
 * 发票记录（数仓同步）
 *
 * <AUTHOR>
 * @date 2024-02-26 14:58:58
 */
public interface IProjectContractInvoiceService extends IService<ProjectContractInvoice> {

    /**
     *  根据合同id查询合同发票记录
     * @param id 合同id
     * @return 发票记录
     */
    List<ContractInvoiceVo> getContractInvoiceVoList(Long id);

    /**
     * 通过合同ids批量查询发票
     * @param ids llhtmxskbh集合
     * @return
     */
    Map<Long,List<ContractInvoiceVo>> getContractInvoiceVoListMap(List<Long> ids);


}

