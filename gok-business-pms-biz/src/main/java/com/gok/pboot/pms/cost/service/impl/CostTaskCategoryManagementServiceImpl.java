package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.cost.entity.domain.CostTaskCategoryManagement;
import com.gok.pboot.pms.cost.entity.dto.CostTaskCategoryManagementDTO;
import com.gok.pboot.pms.cost.entity.vo.CostTaskCategoryManagementVO;
import com.gok.pboot.pms.cost.entity.vo.CostTaskCategorySortVO;
import com.gok.pboot.pms.cost.mapper.CostTaskCategoryManagementMapper;
import com.gok.pboot.pms.cost.service.ICostTaskCategoryManagementService;
import com.gok.pboot.pms.enumeration.ProjectTaskKindEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.gok.pboot.pms.cost.entity.domain.CostTaskCategoryManagement.buildEntity;


/**
 * 类别管理表 服务实现类
 *
 * <AUTHOR>
 * @date 2024/03/26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CostTaskCategoryManagementServiceImpl extends ServiceImpl<CostTaskCategoryManagementMapper, CostTaskCategoryManagement> implements ICostTaskCategoryManagementService {

    /**
     * 任务类别列表
     *
     * @return {@link List }<{@link CostTaskCategoryManagementVO }>
     */
    @Override
    public List<CostTaskCategoryManagementVO> taskCategoryList() {
        // 查询未删除的售前支撑工单类别
        List<CostTaskCategoryManagement> entityList = this.lambdaQuery()
                .eq(CostTaskCategoryManagement::getTaskType, ProjectTaskKindEnum.PRE_SALES_SUPPORT.getValue())
                .orderByAsc(CostTaskCategoryManagement::getSort)
                .list();

        // 转换为VO
        return entityList.stream().map(CostTaskCategoryManagementVO::buildVo)
                .collect(Collectors.toList());
    }

    /**
     * 添加类别
     *
     * @param dto DTO
     */
    @Override
    public void addTaskCategory(CostTaskCategoryManagementDTO dto) {
        // 获取最大的工单类别值
        Integer maxTaskCategory = baseMapper.getMaxTaskCategory();
        // 获取最大的排序值
        Integer maxSort = baseMapper.getMaxSort();

        // 构建实体
        CostTaskCategoryManagement taskCategoryManagement = buildEntity(new CostTaskCategoryManagement(), dto);

        taskCategoryManagement
                // 设置工单类别值为最大值+1
                .setTaskCategory(maxTaskCategory + 1)
                // 设置排序值为最大值+1
                .setSort(maxSort + 1);

        save(taskCategoryManagement);
    }

    /**
     * 编辑类别
     *
     * @param dto DTO
     */
    @Override
    public void editTaskCategory(CostTaskCategoryManagementDTO dto) {
        Long categoryId = dto.getId();
        if (categoryId == null) {
            throw new RuntimeException("类别id不能为空");
        }

        // 获取工单类别
        CostTaskCategoryManagement taskCategoryManagement = getCostCategoryManagement(categoryId);

        // 设置值
        buildEntity(taskCategoryManagement, dto);

        updateById(taskCategoryManagement);
    }

    @NotNull
    private CostTaskCategoryManagement getCostCategoryManagement(Long categoryId) {
        CostTaskCategoryManagement taskCategoryManagement = getById(categoryId);
        if (taskCategoryManagement == null) {
            throw new RuntimeException("类别不存在");
        }
        return taskCategoryManagement;
    }

    /**
     * 删除类别
     *
     * @param id 身份证
     */
    @Override
    public void removeTaskCategory(Long id) {

        CostTaskCategoryManagement taskCategoryManagement = getCostCategoryManagement(id);

        BaseBuildEntityUtil.buildUpdate(taskCategoryManagement);
        removeById(id);
    }

    @Override
    public Map<Integer, CostTaskCategoryManagement> getCostTaskCategoryMap() {
        List<CostTaskCategoryManagement> allCategoryList = baseMapper.getAllCategoryList();
        if (CollUtil.isEmpty(allCategoryList)) {
            return Collections.emptyMap();
        }
        return allCategoryList.stream()
                .collect(Collectors.toMap(CostTaskCategoryManagement::getTaskCategory,
                        entity -> entity,
                        (a, b) -> a,
                        LinkedHashMap::new));
    }

    /**
     * 获取任务类别 dict
     *
     * @return {@link List }<{@link CostTaskCategorySortVO }>
     */
    @Override
    public List<CostTaskCategorySortVO> getTaskCategoryDict() {
        return this.lambdaQuery()
                .eq(CostTaskCategoryManagement::getTaskType, ProjectTaskKindEnum.PRE_SALES_SUPPORT.getValue())
                .orderByAsc(CostTaskCategoryManagement::getSort)
                .list().stream()
                .map(entity -> new CostTaskCategorySortVO(
                        entity.getTaskCategory(),
                        entity.getTaskCategoryName(),
                        entity.getSort(),
                        entity.getCanSelfCreate()
                )).collect(Collectors.toList());
    }

    /**
     * 更新工单类别排序
     *
     * @param taskCategoryIds 工单类别ID列表，按照新的排序顺序排列
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTaskCategorySort(List<Long> taskCategoryIds) {
        if (CollUtil.isEmpty(taskCategoryIds)) {
            return;
        }

        // 获取所有需要更新的售前支撑类别
        List<CostTaskCategoryManagement> taskCategoryList = this.lambdaQuery()
                .eq(CostTaskCategoryManagement::getTaskType, ProjectTaskKindEnum.PRE_SALES_SUPPORT.getValue())
                .in(CostTaskCategoryManagement::getId, taskCategoryIds)
                .list();

        if (CollUtil.isEmpty(taskCategoryList)) {
            return;
        }

        // 创建ID到实体的映射，方便后续更新
        Map<Long, CostTaskCategoryManagement> taskCategoryMap = taskCategoryList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(CostTaskCategoryManagement::getId, taskCategory -> taskCategory));

        // 按照传入的顺序更新sort值
        for (int i = 0; i < taskCategoryIds.size(); i++) {
            Long taskCategoryId = taskCategoryIds.get(i);
            CostTaskCategoryManagement taskCategory = taskCategoryMap.get(taskCategoryId);
            if (taskCategory != null) {
                taskCategory.setSort(i + 1);
                BaseBuildEntityUtil.buildUpdate(taskCategory);
            }
        }

        // 批量更新
        updateBatchById(taskCategoryList);
    }

}