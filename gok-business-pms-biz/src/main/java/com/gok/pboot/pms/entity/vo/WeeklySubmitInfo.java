package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.entity.domain.ProjectInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 项目周报是否提交相关信息vo
 *
 * <AUTHOR>
 * @date 2023/10/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WeeklySubmitInfo {

    /**
     * 项目id
     */
    Long projectId;

    /**
     * 项目名称
     */
    String projectName;

    /**
     * 项目经理
     */
    String managerUserName;

    /**
     * 汇报周期
     */
    String reportStartEnd;

    /**
     * 周报提交时间
     */
    String ctime;

    /**
     * 项目周报提交状态：未提交0、已提交1、滞后提交2
     * {@link com.gok.pboot.pms.enumeration.WeeklySubmitStateEnum}
     */
    Integer submitState;

    /**
     * 项目周报提交状态文本：未提交0、已提交1、滞后提交2
     * {@link com.gok.pboot.pms.enumeration.WeeklySubmitStateEnum}
     */
    String submitStateText;


    public static WeeklySubmitInfo of(ProjectInfo request) {
        WeeklySubmitInfo result = new WeeklySubmitInfo();
        result.setProjectId(request.getId());
        result.setProjectName(request.getItemName());
        result.setManagerUserName(request.getManagerUserName());
        return result;
    }

    public static WeeklySubmitInfo from(ProjectWeeklyVO request) {
        WeeklySubmitInfo result = new WeeklySubmitInfo();
        result.setProjectId(request.getProjectId());
        result.setProjectName(request.getProjectName());
        result.setReportStartEnd(request.getReportStart().toString()+"~"+request.getReportEnd());
        result.setCtime(request.getCtime());
        return result;
    }
}
