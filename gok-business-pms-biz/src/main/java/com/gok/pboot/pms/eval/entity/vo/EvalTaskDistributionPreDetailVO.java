package com.gok.pboot.pms.eval.entity.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 项目人员售前工单评价分布VO
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class EvalTaskDistributionPreDetailVO extends EvalTaskDistributionDetailVO{

    /**
     * 待审核工时
     */
    private BigDecimal unAuditedHours;

    /**
     * 已审核总工时
     */
    private BigDecimal auditedHours;

    /**
     * 已审核人工成本
     */
    private String actualLaborCost;
}