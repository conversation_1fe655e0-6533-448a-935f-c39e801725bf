package com.gok.pboot.pms.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.entity.domain.ProjectStakeholderCustomer;
import com.gok.pboot.pms.entity.dto.ProjectStakeholderCustomerDTO;
import com.gok.pboot.pms.entity.vo.ProjectStakeholderCustomerVO;

import java.util.List;

/**
 * 项目干系人-客户
 *
 * <AUTHOR>
 * @date 2023-07-11 17:05:26
 */
public interface IProjectStakeholderCustomerService extends IService<ProjectStakeholderCustomer> {

    /**
     * 查询项目客户列表
     *
     * @param projectId 项目id
     * @return {@link List}<{@link ProjectStakeholderCustomerVO}>
     */
    List<ProjectStakeholderCustomerVO> getCustomerByProjectId(Long projectId);

    /**
     * 保存or修改项目客户
     *
     * @param dto dto
     * @return {@link ApiResult}
     */
    Boolean saveOrUpdateCustomer( ProjectStakeholderCustomerDTO dto);

    /**
     * 删除项目客户
     *
     * @param id 客户id
     * @return {@link R}
     */
    Boolean deleteById(Long id);
}

