package com.gok.pboot.pms.entity.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 项目工时统计-任务分页查询结果
 *
 * <AUTHOR>
 * @since 2023-07-13
 **/
@Data
public class ProjectTaskFindPageVO {

    /**
     * 日报条目id
     */
    private Long id;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 提交人姓名
     */
    private String userRealName;

    /**
     * 提交人Id
     */
    private Long userId;

    /**
     * 工时类型（0=售前支撑，1=售后交付）',
     * {@link com.gok.pboot.pms.enumeration.WorkTypeEnum}
     */
    private Integer workType;

    /**
     * 工时类型值
     */
    private String workTypeTxt;

    /**
     * 员工类型id（0=在编人员，1=正式交付人员，2=实习交付人员，3=退休返聘人员，4=非全日制人员，5=内部实习生，6=兼职）
     */
    private Integer employeeType;

    /**
     * 员工类型值
     */
    private String employeeTypeTxt;

    /**
     * 正常工时
     */
    private BigDecimal normalHours;

    /**
     * 加班工时
     */
    private BigDecimal addedHours;

    /**
     * 工作日加班工时
     */
    private BigDecimal workOvertimeHours;

    /**
     * 休息日加班工时
     */
    private BigDecimal restOvertimeHours;

    /**
     * 节假日加班工时
     */
    private BigDecimal holidayOvertimeHours;

    /**
     * 待审核工时
     */
    private BigDecimal dshHours;

    /**
     * 调休工时
     */
    private BigDecimal txHours;

    /**
     * 项目分摊工时
     */
    private BigDecimal avgSumHours;


    /**
     * 审核状态（0=未提交，1=已退回，2=待审核，3=不通过，4=已通过）
     */
    private Integer approvalStatus;

    /**
     * 负责人
     */
    private List<TaskLeaderVO> taskLeaderVOList;

}
