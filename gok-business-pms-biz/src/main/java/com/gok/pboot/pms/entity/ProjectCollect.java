package com.gok.pboot.pms.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.enumeration.ProjectCollectTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 任务
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(ProjectCollect.ALIAS)
public class ProjectCollect extends BeanEntity<Long> {

    public static final String ALIAS = "mhour_project_collect";
    private static final long serialVersionUID = 1L;
    /**
    * 所属项目ID
    */
    @ApiModelProperty(value = "所属项目ID")
    private Long projectId;
    /**
    * 收藏类型
     * {@link ProjectCollectTypeEnum}
    */
    private Integer collectType;


}
