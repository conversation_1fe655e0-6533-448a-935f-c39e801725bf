package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.entity.CustomerBusinessUnit;
import com.gok.pboot.pms.entity.dto.CustomerBusinessSearchDTO;
import com.gok.pboot.pms.entity.dto.CustomerBusinessUnitPageDTO;
import com.gok.pboot.pms.entity.vo.CustomerBusinessListVO;
import com.gok.pboot.pms.entity.vo.CustomerBusinessUnitPageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 客户经营单元-客户组成(关联客户) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Mapper
public interface CustomerBusinessUnitMapper extends BaseMapper<CustomerBusinessUnit> {
    /**
     * 批量逻辑删除
     *
     * @param list id集合
     */
    void batchDel(@Param("list") List<Long> list);


    /**
     * 根据businessId删除
     * @param businessId
     */
    void deletedByBusinessId(@Param("businessId") Long businessId);


    /**
     * 简单查询
     * @param businessId
     * @return
     */
    List<CustomerBusinessUnitPageVO> selectSimplyList(@Param("businessId") Long businessId);

    /**
     * 分页查询
     * @param page
     * @param filter
     * @return
     */
    Page<CustomerBusinessUnitPageVO> findListPage(Page<CustomerBusinessUnitPageVO> page, @Param("filter") CustomerBusinessUnitPageDTO filter);


    int findByName(CustomerBusinessUnit customerBusinessUnit);

    List<CustomerBusinessListVO> findNameList(@Param("filter") CustomerBusinessSearchDTO customerBusinessSearchDTO);

    /**
     * 过滤businessId
     *
     * @param filter 参数
     * @return 项目ID列表
     */
    List<Long> findId(@Param("filter") Map<String, Object> filter);
}
