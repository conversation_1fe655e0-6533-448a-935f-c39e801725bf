package com.gok.pboot.pms.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @desc 下级日报工时统计 基础VO
 * @createTime 2023/5/11 11:09
 */
@Data
public class SubordinatePaperEntryStaticVO {

    private Long userId;

    private LocalDate submissionDate;
    /**
     * 正常工时（人天）
     */
    @ApiModelProperty(value = "正常工时（人天）")
    private BigDecimal normalHours;
    /**
     * 加班工时（人天）
     */
    @ApiModelProperty(value = "加班工时（人天）")
    private BigDecimal addedHours;

    /**
     * '工作日加班工时'
     */
    @ApiModelProperty(value = "工作日加班工时（人天）")
    private BigDecimal workOvertimeHours;

    /**
     * '休息日加班工时'
     */
    @ApiModelProperty(value = "休息日加班工时（人天）")
    private BigDecimal restOvertimeHours;
    /**
     * '节假日加班工时'
     */
    @ApiModelProperty(value = "节假日加班工时（人天）")
    private BigDecimal holidayOvertimeHours;

    /**
     * 项目分摊工时（人天）
     */
    @ApiModelProperty("项目分摊工时（人天）")
    private BigDecimal projectShareHours;
}
