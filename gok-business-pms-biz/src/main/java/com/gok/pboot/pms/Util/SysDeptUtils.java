package com.gok.pboot.pms.Util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.gok.bcp.upms.dto.DeptCacheDto;
import com.gok.bcp.upms.vo.SysDeptOutVO;
import com.gok.bcp.upms.vo.SysDeptVo;
import com.gok.bcp.upms.vo.SysUserVo;
import com.gok.pboot.pms.common.base.BaseConstants;
import com.gok.pboot.pms.entity.SysDept;
import com.gok.pboot.pms.enumeration.EmployeeDeptEnum;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Queues;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.ArrayUtils;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * - 处理部门相关逻辑工具类 -
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@UtilityClass
public class SysDeptUtils {

    /**
     * 将中台返回的部门对象集合转为SysDept集合
     *
     * @param bcpDeptVOList 中台部门VO集合
     * @return SysDept集合
     */
    @Nonnull
    public static Collection<SysDept> mapBCPDeptVOToSysDept(@Nullable Collection<SysDeptOutVO> bcpDeptVOList) {
        if (CollectionUtils.isEmpty(bcpDeptVOList)) {
            return new ArrayList<>();
        }

        return bcpDeptVOList.stream()
                .map(SysDeptUtils::mapToSysDept)
                .collect(Collectors.toList());
    }

    /**
     * 将中台部门对象转为SysDept
     *
     * @param outVO 中台返回的部门VO
     * @return SysDept
     */
    @Nonnull
    public static SysDept mapToSysDept(@Nullable SysDeptOutVO outVO) {
        if (outVO == null) {
            throw new NullPointerException();
        }

        SysDept result = new SysDept();

        result.setDeptId(outVO.getDeptId());
        result.setName(Strings.nullToEmpty(outVO.getName()));
        result.setSortOrder(outVO.getSortOrder());
        result.setCreateTime(outVO.getCreateTime());
        result.setUpdateTime(outVO.getUpdateTime());
        result.setParentId(outVO.getParentId());
        result.setDelFlag(BaseConstants.NO_STR);

        return result;
    }

    /**
     * 将中台返回的部门对象集合转为SysDept集合
     *
     * @param deptDtoList 中台部门dto集合
     * @return SysDept集合
     */
    @Nonnull
    public static Collection<SysDept> mapBCPDeptCacheDtoToSysDept(@Nullable Collection<DeptCacheDto> deptDtoList) {
        if (CollectionUtils.isEmpty(deptDtoList)) {
            return new ArrayList<>();
        }

        return deptDtoList.stream()
                .map(SysDeptUtils::mapToSysDept)
                .collect(Collectors.toList());
    }

    /**
     * 将中台部门对象转为SysDept
     *
     * @param dept 中台返回的部门VO
     * @return SysDept
     */
    public static SysDept mapToSysDept(@Nullable DeptCacheDto dept) {
        if (dept == null) {
            throw new NullPointerException();
        }

        SysDept result = new SysDept();
        boolean deprecatedFlag = "9".equals(dept.getStatus());

        result.setDeptId(dept.getDeptId());
        result.setName(Strings.nullToEmpty(dept.getName()));
        result.setSortOrder(deprecatedFlag ? Integer.MAX_VALUE : dept.getSortOrder());
        result.setParentId(dept.getParentId());
        result.setDelFlag(BaseConstants.NO_STR);

        return result;
    }

    /**
     * 从部门列表收集指定部门名称
     *
     * @param deptList 部门列表
     * @param deptId   部门ID
     * @return eg. “A-B-C”
     */
    @Nonnull
    public static String collectFullName(
            @Nonnull Collection<SysDept> deptList,
            @Nonnull Long deptId
    ) {
        if (deptList.isEmpty()) {
            return "";
        }

        return collectFullName(
                BaseEntityUtils.mapCollectionToMap(deptList, SysDept::getDeptId, d -> d),
                deptId
        );
    }

    /**
     * 从部门ID Map中根据ID获取部门全称
     *
     * @param deptIdMap 部门ID Map
     * @param deptId    部门ID
     * @return 部门全称
     */
    @Nonnull
    public static String collectFullName(
            @Nonnull Map<Long, SysDept> deptIdMap,
            @Nonnull Long deptId
    ) {
        if (deptIdMap.isEmpty()) {
            return "";
        }

        Long deptIdNow = deptId;
        SysDept dept = deptIdMap.get(deptIdNow);
        Deque<String> nameStack = Queues.newArrayDeque();
        StringJoiner joiner = new StringJoiner("-");

        while (dept != null && StringUtils.isNotBlank(dept.getName())) {
            nameStack.push(dept.getName());
            deptIdNow = dept.getParentId();
            dept = deptIdMap.get(deptIdNow);
            if (isTopDeptParentId(deptIdNow)) {
                break;
            }
        }
        nameStack.forEach(joiner::add);

        return joiner.toString();
    }

    /**
     * 根据部门ID收集部门名称
     *
     * @param deptList 部门ID列表
     * @param deptId   部门ID
     * @return String[] length=3
     */
    public static String[] collectNames(
            @Nonnull List<SysDept> deptList,
            @Nonnull Long deptId
    ) {
        if (deptList.isEmpty()) {
            return new String[]{"", "", ""};
        }

        return collectNames(BaseEntityUtils.mapCollectionToMap(
                deptList,
                SysDept::getDeptId,
                d -> d
        ), deptId);
    }

    /**
     * 根据部门ID收集部门名称
     *
     * @param deptIdMap 部门ID Map
     * @param deptId    部门ID
     * @return String[] length=3
     */
    public static String[] collectNames(
            @Nonnull Map<Long, SysDept> deptIdMap,
            @Nonnull Long deptId
    ) {
        String[] result = {"", "", ""};

        if (deptIdMap.isEmpty()) {
            return result;
        }
        int pos = 0;
        Long deptIdNow = deptId;
        SysDept dept = deptIdMap.get(deptIdNow);

        while (dept != null && pos <= 2) {
            result[pos] = dept.getName();
            deptIdNow = dept.getParentId();
            if (isTopDeptParentId(deptIdNow)) {
                break;
            }
            dept = deptIdMap.get(deptIdNow);
            ++pos;
        }
        ArrayUtils.reverse(result);

        String[] finalResult = {"", "", ""};
        int level = 0;
        for (String r : result) {
            if (StrUtil.isNotBlank(r)) {
                finalResult[level++] = r;
            }
        }

        return finalResult;
    }

    @Nullable
    public static SysDept findTopDept(
            @Nonnull Collection<SysDept> depts,
            @Nonnull Long childDeptId
    ) {
        if (depts.isEmpty()) {
            return null;
        }

        return findTopDept(
                BaseEntityUtils.mapCollectionToMap(depts, SysDept::getDeptId, d -> d),
                childDeptId
        );
    }

    /**
     * 从给定的Map中获取指定部门的一级部门
     *
     * @param deptIdMap   部门Map
     * @param childDeptId 子部门ID
     * @return 一级部门
     */
    @Nullable
    public static SysDept findTopDept(
            @Nonnull Map<Long, SysDept> deptIdMap,
            @Nonnull Long childDeptId
    ) {
        Long deptIdNow = childDeptId;
        SysDept dept;

        if (deptIdMap.isEmpty()) {
            return null;
        }
        dept = deptIdMap.get(deptIdNow);
        deptIdNow = dept.getParentId();
        while (!isTopDeptParentId(deptIdNow)) {
            dept = deptIdMap.get(deptIdNow);
            deptIdNow = dept.getParentId();
        }

        return dept;
    }

    @Nullable
    public static Long findTopDeptId(
            @Nonnull Map<Long, SysDept> deptIdMap,
            @Nonnull Long childDeptId
    ) {
        SysDept topDept = findTopDept(deptIdMap, childDeptId);

        return topDept == null ? null : topDept.getDeptId();
    }

    @Nullable
    public static Long findTopDeptId(
            @Nonnull Collection<SysDept> depts,
            @Nonnull Long childDeptId
    ) {
        SysDept topDept = findTopDept(depts, childDeptId);

        return topDept == null ? null : topDept.getDeptId();
    }

    @Nonnull
    public static List<SysDept> findAllTopDept(@Nonnull Collection<SysDept> depts) {
        if (depts.isEmpty()) {
            return ImmutableList.of();
        }

        return depts.stream()
                .filter(dept -> isTopDeptParentId(dept.getParentId()))
                .collect(Collectors.toList());
    }

    @Nonnull
    public static List<SysDept> findAllParentDept(
            @Nonnull Map<Long, SysDept> deptIdMap,
            @Nonnull Long childDeptId
    ) {
        List<SysDept> result = new ArrayList<>(deptIdMap.size());

        collectAllParentDept(deptIdMap, result, childDeptId);

        return result;
    }

    @Nonnull
    private static List<Long> findAllParentDeptId(
            @Nonnull Map<Long, SysDept> deptIdMap,
            @Nonnull Long childDeptId
    ) {
        List<Long> result = new ArrayList<>(deptIdMap.size());

        collectAllParentDeptId(deptIdMap, result, childDeptId);

        return result;
    }

    public static void collectAllParentDept(
            @Nonnull Map<Long, SysDept> deptIdMap,
            @Nonnull List<SysDept> resultList,
            @Nonnull Long childDeptId
    ) {
        Long deptIdNow = childDeptId;
        SysDept dept;

        if (deptIdMap.isEmpty()) {
            return;
        }
        resultList.add(deptIdMap.get(childDeptId));
        while (!isTopDeptParentId(deptIdNow)) {
            dept = deptIdMap.get(deptIdNow);
            deptIdNow = dept.getParentId();
            resultList.add(dept);
        }
    }

    public static void collectAllParentDeptId(
            @Nonnull Map<Long, SysDept> deptIdMap,
            @Nonnull List<Long> resultList,
            @Nonnull Long childDeptId
    ) {
        Long deptIdNow = childDeptId;
        SysDept dept;

        if (deptIdMap.isEmpty()) {
            return;
        }
        resultList.add(childDeptId);
        while (!isTopDeptParentId(deptIdNow)) {
            dept = deptIdMap.get(deptIdNow);
            deptIdNow = dept.getParentId();
            resultList.add(deptIdNow);
        }
    }

    @Nonnull
    public static List<Tree<Long>> createTree(@Nonnull Collection<SysDept> depts) {
        List<TreeNode<Long>> nodes;

        if (depts.isEmpty()) {
            return ImmutableList.of();
        }
        nodes = depts.stream()
                .sorted(Comparator.comparingInt(SysDept::getSortOrder))
                .map(dept -> {
                    TreeNode<Long> treeNode = new TreeNode<>();
                    treeNode.setId(dept.getDeptId());
                    treeNode.setParentId(dept.getParentId());
                    treeNode.setName(dept.getName());
                    treeNode.setWeight(dept.getSortOrder());

                    Map<String, Object> extra = new HashMap<>(8);
                    extra.put("createTime", dept.getCreateTime());
                    treeNode.setExtra(extra);

                    return treeNode;
                })
                .collect(Collectors.toList());

        return TreeUtil.build(nodes, 0L);
    }

    @Nonnull
    public static Collection<SysDept> findAllChildrenDept(Collection<SysDept> depts, @NotNull Long parentId) {
        if (depts.isEmpty()) {
            return ImmutableList.of();
        }
        if (isTopDeptParentId(parentId)) {
            return depts;
        }

        return depts.stream()
                .filter(dept -> parentId.equals(dept.getParentId()))
                .collect(Collectors.toList());
    }

    public static boolean isTopDeptParentId(Long id) {
        return id != null && (id == 0 || id == -1);
    }

    /**
     * 根据用户获取对应的部门信息
     *
     * @param userVo
     * @return
     */
    public static SysDeptVo getUserDept(SysUserVo userVo) {
        if (null == userVo) {
            return null;
        }
        return getUserDept(userVo.getDeptList());
    }

    /**
     * 获取用户所在部门
     * @param deptList 用户部门列表
     * @return 用户所在部门
     */
    public static SysDeptVo getUserDept(List<SysDeptVo> deptList) {
        if (CollUtil.isEmpty(deptList)) {
            return null;
        }
        return ListUtil.sort(deptList, (o1, o2) -> o2.getLevel() - o1.getLevel()).get(0);
    }

    /**
     * 判断是否为可用部门ID
     * @param deptId 部门ID
     * @return bool
     */
    public static boolean isAvailableDeptId(Long deptId) {
        return !isTopDeptParentId(deptId) && !EnumUtils.valueEquals(deptId, EmployeeDeptEnum.OUTSIDE);
    }

    /**
     * 获取部门id-部门对象实体映射
     *
     * @param deptList 部门列表
     * @return map
     */
    public static Map<Long, SysDept> getDeptIdMap(List<DeptCacheDto> deptList) {
        Map<Long, SysDept> deptIdMap;
        if (CollectionUtils.isEmpty(deptList)) {
            deptIdMap = ImmutableMap.of();
        } else {
            deptIdMap = SysDeptUtils.mapBCPDeptCacheDtoToSysDept(deptList)
                    .stream()
                    .collect(Collectors.toMap(SysDept::getDeptId, dept -> dept));
        }
        return deptIdMap;
    }
}
