package com.gok.pboot.pms.entity.vo;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 合同基本信息头部数据vo
 *
 * <AUTHOR>
 * @date 2024/2/22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ContractBaseHeadVo {

    /**
     * 合同id
     */
    private Long id;

    /**
     * 合同名称
     */
    private String htmc;

    /**
     * 合同编号
     */
    private String htbh;

    /**
     * 合同状态
     */
    private Integer htzt;

    /**
     * 项目名称
     */
    private Long xmmc;

    /**
     * 项目名称
     */
    private String xmmcName;

    /**
     * 项目编号
     */
    private String xmbh;

    /**
     * 合同所属公司
     */
    private Integer htssgs;

    /**
     * 对方名称id
     */
    private String khmc;
    /**
     * 对方名称
     */
    private String khmcName;

    /**
     * 合同类别
     */
    private Integer htlb;

    /**
     * 合同细类
     */
    private Integer htxl;

    /**
     * 项目销售人员（客户经理）id
     */
    private Long xmxsry;

    /**
     * 项目销售人员（客户经理）
     */
    private String xmxsryName;

    /**
     * 合同金额（含税）
     */
    private BigDecimal htje;

    /**
     * 累计收款金额
     */
    private BigDecimal ljskje;

    /**
     * 变更类型
     */
    private Integer bglx;

    /**
     * 实际合同签订日期
     */
    private String sjhtqdrq;

    /**
     * 已收款比例
     */
    private BigDecimal yskbl;

}
