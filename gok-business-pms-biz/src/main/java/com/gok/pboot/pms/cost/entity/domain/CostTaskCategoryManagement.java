package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.cost.entity.dto.CostTaskCategoryManagementDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 类别管理表
 *
 * <AUTHOR>
 * @date 2024/03/26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("cost_task_category_management")
@Accessors(chain = true)
public class CostTaskCategoryManagement extends BeanEntity<Long> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工单类别
     */
    private Integer taskCategory;

    /**
     * 工单类别名称
     */
    private String taskCategoryName;

    /**
     * 工单类型(0=售前支撑, 1=售后交付)
     */
    private Integer taskType;

    /**
     * 是否可自创建(0=否, 1=是)
     */
    private Integer canSelfCreate;

    /**
     * 排序
     */
    private Integer sort;

    public static CostTaskCategoryManagement buildEntity(CostTaskCategoryManagement taskCategoryManagement, CostTaskCategoryManagementDTO dto) {
        return BaseBuildEntityUtil.buildSave(taskCategoryManagement)
                .setTaskCategoryName(dto.getTaskCategoryName())
                .setTaskType(dto.getTaskType())
                .setCanSelfCreate(dto.getCanSelfCreate());
    }
} 