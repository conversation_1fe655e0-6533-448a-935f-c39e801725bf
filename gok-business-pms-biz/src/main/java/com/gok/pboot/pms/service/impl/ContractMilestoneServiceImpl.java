package com.gok.pboot.pms.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.admin.vo.DictKvVo;
import com.gok.pboot.pms.Util.DecimalFormatUtil;
import com.gok.pboot.pms.entity.domain.ContractMilestone;
import com.gok.pboot.pms.entity.vo.ContractBaseHeadVo;
import com.gok.pboot.pms.entity.vo.ContractMilestoneVO;
import com.gok.pboot.pms.mapper.ContractLedgerMapper;
import com.gok.pboot.pms.mapper.ContractMilestoneMapper;
import com.gok.pboot.pms.service.IContractMilestoneService;
import com.gok.pboot.pms.service.IDictService;
import com.gok.pboot.pms.service.IPmsDocImageFileService;
import com.google.common.collect.ImmutableList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025/03/20
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class ContractMilestoneServiceImpl extends ServiceImpl<ContractMilestoneMapper, ContractMilestone> implements IContractMilestoneService {

    private final IDictService idictService;
    private final ContractLedgerMapper contractLedgerMapper;
    private final IPmsDocImageFileService pmsDocImageFileService;

    @Override
    public List<ContractMilestoneVO> getContractPaymentById(Long contractId) {
        List<ContractMilestoneVO> contractMilestoneVOList = baseMapper.findByContractIds(Arrays.asList(contractId));
        ContractBaseHeadVo headVo = contractLedgerMapper.selHeadInfoById(contractId);
        if (CollUtil.isEmpty(contractMilestoneVOList) || null == headVo) {
            return ImmutableList.of();
        }
        BigDecimal contractAmount = Optional.ofNullable(headVo.getHtje()).orElse(BigDecimal.ZERO);

        Map<Integer, String> taxRateMap = idictService.getDictKvList("税率").getData().stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName)
                );
        for (ContractMilestoneVO vo : contractMilestoneVOList) {
            if (contractAmount.compareTo(BigDecimal.ZERO) != 0 && null != vo.getPlannedAmountIncludeTax()) {
                vo.setMilestoneRatio(vo.getPlannedAmountIncludeTax().divide(contractAmount, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
            }
            vo.setPlannedTaxRateTxt(null != vo.getPlannedTaxRate() ? taxRateMap.get(vo.getPlannedTaxRate()) : null);
            vo.setEstimatedTaxRateTxt(null != vo.getEstimatedTaxRate() ? taxRateMap.get(vo.getEstimatedTaxRate()) : null);
            vo.setPlannedAmountTxt(DecimalFormatUtil.setThousandthAndTwoDecimal(vo.getPlannedAmount(), DecimalFormatUtil.ZERO));
            vo.setEstimatedAmountTxt(DecimalFormatUtil.setThousandthAndTwoDecimal(vo.getEstimatedAmount(), DecimalFormatUtil.ZERO));
            vo.setAmountDifferenceTxt(DecimalFormatUtil.setThousandthAndTwoDecimal(vo.getAmountDifference(), DecimalFormatUtil.ZERO));
            vo.setActualPaymentAmountTxt(DecimalFormatUtil.setThousandthAndTwoDecimal(vo.getActualPaymentAmount(), DecimalFormatUtil.ZERO));
            vo.setPlannedAmountIncludeTaxTxt(DecimalFormatUtil.setThousandthAndTwoDecimal(vo.getPlannedAmountIncludeTax(), DecimalFormatUtil.ZERO));
            vo.setEstimatedAmountIncludeTaxTxt(DecimalFormatUtil.setThousandthAndTwoDecimal(vo.getEstimatedAmountIncludeTax(), DecimalFormatUtil.ZERO));
            vo.setSettlementAttachmentList(pmsDocImageFileService.getOaFileVoList(vo.getSettlementAttachment()));
            vo.setMilestoneEvidenceList(pmsDocImageFileService.getOaFileVoList(vo.getMilestoneEvidence()));
        }
        return contractMilestoneVOList;
    }

}
