package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.entity.domain.ProjectTask;
import com.gok.pboot.pms.enumeration.TaskMilestone;
import com.gok.pboot.pms.enumeration.TaskWorkingState;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 项目任务VO
 *
 * <AUTHOR>
 * @date 2023/8/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectTaskVO {

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 层级
     */
    private Integer treeLevel;

    /**
     * 父级到子级的全路径
     */
    private String treePathIds;

    /**
     * 任务标题
     */
    private String title;

    /**
     * 负责人ID
     */
    private Long managerUserId;

    /**
     * 负责人姓名
     */
    private String managerUserName;

    /**
     * 状态（0=未开始，1=进行中，2=已完成）
     *
     * @see com.gok.pboot.pms.enumeration.TaskWorkingState
     */
    private Integer state;

    /**
     * 状态文本描述
     *
     * @see com.gok.pboot.pms.enumeration.TaskWorkingState
     */
    private String stateText;

    /**
     * 计划开始时间
     */
    private LocalDateTime expectedStartTime;

    /**
     * 计划结束时间
     */
    private LocalDateTime expectedEndTime;

    /**
     * 父级的计划开始时间
     */
    private LocalDateTime parentExpectedStartTime;

    /**
     * 父级的计划结束时间
     */
    private LocalDateTime parentExpectedEndTime;

    /**
     * 计划开始时间与计划结束时间的时差
     */
    private Long expectedTimeET;

    /**
     * 实际开始时间
     */
    private LocalDateTime actualStartTime;

    /**
     * 实际结束时间
     */
    private LocalDateTime actualEndTime;

    /**
     * 是否里程碑
     *
     * @see com.gok.pboot.pms.enumeration.TaskMilestone
     */
    private Integer milestone;

    /**
     * 里程碑文本描述
     *
     * @see com.gok.pboot.pms.enumeration.TaskMilestone
     */
    private String milestoneText;

    /**
     * 项目进度（0=0%，...，10=100%）
     */
    private Integer progress;

    /**
     * 是否挂靠工时
     */
    private Integer isDailyPaperEntry;

    private List<ProjectTaskVO> children;

    public static ProjectTaskVO of(
            ProjectTask po,
            Map<Long, Integer> projectIdAndProgressMap,
            List<Long> dailyPaperEntryTaskIds
    ) {
        ProjectTaskVO result = new ProjectTaskVO();

        Long taskId = po.getId();
        Integer state = po.getState();
        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalDateTime expectedStartTime = po.getExpectedStartTime();
        LocalDateTime expectedEndTime = po.getExpectedEndTime();
        LocalDateTime actualStartTime = po.getActualStartTime();
        LocalDateTime actualEndTime = po.getActualEndTime();
        // 补全字段
        result.setStateText(EnumUtils.getNameByValue(TaskWorkingState.class, state));
        // 将原来状态写入StateText后，重新写入state值为甘特图对应值
        if (actualEndTime != null) {
            //已完成项目
            if (actualEndTime.isBefore(expectedEndTime) || actualEndTime.isEqual(expectedEndTime)) {
                // 按时完成的
                result.setState(TaskWorkingState.FINISHED_IN_TIME.getValue());
            } else {
                // 超期完成的
                result.setState(TaskWorkingState.FINISHED_OUT_TIME.getValue());
            }
        } else {
            // 未完成任务
            if (expectedEndTime.isBefore(currentDateTime)) {
                // 未完成，已超期
                result.setState(TaskWorkingState.UNFINISHED_OUT_TIME.getValue());
            } else {
                // 未完成，未超期
                result.setState(TaskWorkingState.UNFINISHED_IN_TIME.getValue());
            }
        }

        result.setMilestone(po.getMilestone() ? 1 : 0);
        result.setMilestoneText(EnumUtils.getNameByValue(TaskMilestone.class, po.getMilestone()));
        Integer progress = projectIdAndProgressMap.get(taskId);
        if (progress != null) {
            result.setProgress(progress *10);
        }else {
            result.setProgress(0);
        }

        // 判断是否挂靠工时
        if (dailyPaperEntryTaskIds.contains(taskId)) {
            result.setIsDailyPaperEntry(1);
        }else {
            result.setIsDailyPaperEntry(0);
        }

        // 其他字段
        result.setTaskId(taskId);
        result.setProjectId(po.getProjectId());
        result.setParentId(po.getParentId());
        result.setTreeLevel(po.getTreeLevel());
        result.setTreePathIds(po.getTreePathIds());
        result.setTitle(po.getTitle());
        result.setManagerUserId(po.getManagerUserId());
        result.setManagerUserName(po.getManagerUserName());
        result.setExpectedStartTime(expectedStartTime);
        result.setExpectedEndTime(expectedEndTime);
        if (actualStartTime != null) {
            result.setActualStartTime(actualStartTime);
        }
        if (actualEndTime != null) {
            result.setActualEndTime(actualEndTime);
        }

        Duration between = Duration.between(expectedStartTime, expectedEndTime);
        result.setExpectedTimeET(between.toDays());

        return result;
    }
}
