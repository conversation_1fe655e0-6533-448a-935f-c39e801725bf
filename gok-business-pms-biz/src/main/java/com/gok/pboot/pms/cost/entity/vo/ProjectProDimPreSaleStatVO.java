package com.gok.pboot.pms.cost.entity.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目工单看板-项目维度售前工单统计VO
 *
 * <AUTHOR>
 * @date 2025/05/14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectProDimPreSaleStatVO extends ProjectPreSaleStatBaseVO {

    /**
     * 项目编号
     */
    @ExcelProperty("项目编号")
    private String projectNo;

    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    private String projectName;

} 