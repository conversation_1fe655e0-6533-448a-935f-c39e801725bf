package com.gok.pboot.pms.cost.controller;


import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.cost.entity.dto.CostExpensesShareListDto;
import com.gok.pboot.pms.cost.entity.vo.CostExpensesShareListVO;
import com.gok.pboot.pms.cost.service.ICostExpensesShareService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * 交付管理费用分摊管理
 *
 * <AUTHOR>
 * @menu 交付管理费用分摊管理
 * @since 2025-02-18
 */
@RestController
@RequestMapping("/costExpensesShare")
@AllArgsConstructor
public class CostExpensesShareController {

    private final ICostExpensesShareService service;

    /**
     * 费用分摊列表
     * @param dto
     * @return {@link ApiResult}
     */
    @GetMapping("/findExpensesShare")
    public ApiResult<CostExpensesShareListVO> findExpensesShare(CostExpensesShareListDto dto) {
        return ApiResult.success(service.findExpensesShare(dto), "获取成功");
    }

    /**
     * 导出费用分摊列表
     * @param dto
     * @return {@link ApiResult}
     */
    @PostMapping("/export")
    public void exportExpensesShare(HttpServletResponse response, @RequestBody CostExpensesShareListDto dto) {
        service.exportExpensesShare(response,dto);
    }
}
