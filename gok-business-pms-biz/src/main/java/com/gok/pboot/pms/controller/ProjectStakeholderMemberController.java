package com.gok.pboot.pms.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.base.PropertyFilters;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.entity.dto.ProjectStakeholderMemberBatchDTO;
import com.gok.pboot.pms.entity.dto.ProjectStakeholderMemberDTO;
import com.gok.pboot.pms.entity.vo.ProjectInfoMemberVO;
import com.gok.pboot.pms.entity.vo.ProjectStakeholderMemberVO;
import com.gok.pboot.pms.entity.vo.UserJobDeptVo;
import com.gok.pboot.pms.service.IProjectStakeholderMemberService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @description 项目 前端控制器
 * @menu 项目干系人-成员
 * @since 2023-07-11 17:05:26
 */
@Validated
@RestController
@RequestMapping("/projectStakeholderMember")
@Api("项目成员")
public class ProjectStakeholderMemberController {
    @Resource
    private IProjectStakeholderMemberService projectStakeholderMemberService;

    /**
     * 查询项目成员列表
     *
     * @param projectId 项目id
     * @return {@link ApiResult}<{@link List}<{@link ProjectStakeholderMemberVO}>>
     */
    @ApiOperation(value = "查询项目成员列表", notes = "查询项目成员列表")
    @GetMapping("/list/{projectId}")
    public ApiResult<List<ProjectStakeholderMemberVO>> list(@PathVariable("projectId") Long projectId) {
        return projectStakeholderMemberService.getMemberByProjectId(projectId, null);
    }

    /**
     * 查询项目成员列表
     *
     * @param jsonObject JSON 对象
     * @return {@link ApiResult }<{@link List }<{@link ProjectStakeholderMemberVO }>>
     */
    @PostMapping("/list")
    public ApiResult<List<ProjectStakeholderMemberVO>> list(@RequestBody JSONObject jsonObject) {
        return ApiResult.success(projectStakeholderMemberService.getList(jsonObject));
    }

    /**
     * 分页查询项目成员列表
     *
     * @param pageRequest 分页请求对象
     * @param request     请求体
     * @return {@link ApiResult}<{@link Page}<{@link ProjectStakeholderMemberVO}>>
     * @customParam pageNumber 页码
     * @customParam pageSize 页长
     * @customParam filter_L_projectId 传入的项目id
     * @customParam filter_S_memberName 传入的用户姓名
     * @customParam filter_L_syncOaType 传入的同步oa类型
     */
    @ApiOperation(value = "分页查询项目成员列表", notes = "分页查询项目成员列表")
    @GetMapping("/page")
    public ApiResult<Page<ProjectStakeholderMemberVO>> page(PageRequest pageRequest, HttpServletRequest request) {
        Map<String, Object> filter = PropertyFilters.get(request, true);
        return projectStakeholderMemberService.findPage(pageRequest, filter);
    }

    /**
     * 查询项目铁三角
     *
     * @param projectId 项目id
     * @return {@link ApiResult}<{@link ProjectInfoMemberVO}>>
     */
    @GetMapping("/getIronTriangleByProjectId/{projectId}")
    public ApiResult<ProjectInfoMemberVO> getIronTriangleByProjectId(@PathVariable("projectId") Long projectId) {
        return ApiResult.success(projectStakeholderMemberService.getIronTriangleByProjectId(projectId));
    }

    /**
     * 保存or修改项目成员
     *
     * @param dto dto
     * @return {@link ApiResult}
     */
    @ApiOperation(value = "保存or修改项目成员", notes = "保存or修改项目成员")
    @PostMapping("/saveOrUpdate")
    public ApiResult<Boolean> save(@RequestBody @Validated ProjectStakeholderMemberBatchDTO dto) {
        return ApiResult.success(projectStakeholderMemberService.save(dto));
    }

    /**
     * 删除项目成员
     *
     * @param id 成员id
     * @return {@link ApiResult}
     */
    @ApiOperation(value = "删除项目成员", notes = "删除项目成员")
    @DeleteMapping("/delete/{id}")
    public ApiResult<String> delete(@PathVariable("id") Long id) {
        return projectStakeholderMemberService.delById(id);
    }


    /**
     * 通过userId查询用户部门职责信息
     *
     * @param id 用户ID
     * @return {@link R}<{@link UserJobDeptVo}>>
     */
    @ApiOperation(value = "通过userId查询用户部门职责信息", notes = "通过userId查询用户部门职责信息")
    @GetMapping("/UserJobDept/{id}")
    public ApiResult<UserJobDeptVo> getUserJobDeptByUserId(@PathVariable("id") Long id) {
        return projectStakeholderMemberService.getUserJobDeptByUserId(id);
    }

    /**
     * 修改项目成员角色
     *
     * @param dto 请求数据对象
     * @return {@link ApiResult}<{@link Boolean}>
     */
    @ApiOperation(value = "修改项目成员角色", notes = "修改项目成员角色")
    @PutMapping("/updateRoleType")
    public ApiResult<Boolean> updateRoleType(@RequestBody @Validated ProjectStakeholderMemberDTO dto) {
        return ApiResult.success(projectStakeholderMemberService.updateRoleType(dto));
    }

    /**
     * 修改项目成员备注
     *
     * @param dto 请求对数据象
     * @return {@link ApiResult}<{@link Boolean}>
     */
    @ApiOperation(value = "修改项目成员备注", notes = "修改项目成员备注")
    @PutMapping("/updateRemark")
    public ApiResult<Boolean> updateRemark(@RequestBody @Validated ProjectStakeholderMemberDTO dto) {
        return ApiResult.success(projectStakeholderMemberService.updateRemark(dto));
    }

    /**
     * 同步OA
     *
     * @param projectId 项目id
     * @return {@link ApiResult}<{@link String}>
     */
    @ApiOperation(value = "同步OA", notes = "同步OA")
    @PostMapping("/syncOa")
    public ApiResult<String> syncOa(@RequestParam("projectId") Long projectId) {
        return ApiResult.success(null, projectStakeholderMemberService.syncOa(projectId));
    }
}
