package com.gok.pboot.pms.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.sql.Timestamp;
import java.util.List;

/**
 * 管理员配置分页查询Vo
 *
 * <AUTHOR>
 * @since 2023-08-18
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AdminConfigFindPageVo {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目状态值（0=商机，1=商机终止，2=在建，3=挂起，4=初验，5=终验，6=结项，7=异常终止）
     */
    private String projectStatusName;

    /**
     * 项目销售姓名
     */
    private String salesmanUserName;

    /**
     * 项目销售id
     */
    private Long salesmanUserId;

    /**
     * 项目经理姓名
     */
    private String managerUserName;

    /**
     * 项目经理id
     */
    private Long managerUserId;

    /**
     * 管理人员集合
     */
    @JsonIgnore
    private List<PrivilegeUserInfoVO> userList;

    /**
     * 工时审核员
     */
    private List<PrivilegeUserInfoVO> mhourAuditor;

    /**
     * 项目操作员集合
     */
    private List<PrivilegeUserInfoVO> projectOperator;

    /**
     * 项目售前人员ID
     */
    private Long preSalesmanUserId;

    /**
     * 项目售前姓名
     */
    private String preSalesmanUserName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp ctime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp mtime;

}
