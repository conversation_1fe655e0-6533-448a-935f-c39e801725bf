package com.gok.pboot.pms.cost.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 人力外包-费用分摊
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CostExpensesShareListVO {

    /**
     * 费用分摊合计数据
     */
    private CostExpensesShareSumDataVO costExpensesShareSumDataVO;

    /**
    * 汇总列表
    */
    private List<CostExpensesShareSummaryListVO> summaryList;

    /**
     * 明细列表
     */
    private List<CostExpensesShareDetailsListVO> detailsList;

}