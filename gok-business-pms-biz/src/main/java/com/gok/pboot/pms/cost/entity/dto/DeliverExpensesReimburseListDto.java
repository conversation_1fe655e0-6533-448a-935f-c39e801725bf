package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
    * 交付管理-费用报销台账查询参数
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeliverExpensesReimburseListDto {

    // 页号
    private int pageNumber = 0;

    // 每页大小
    private int pageSize = 10;

    /**
    * 项目id
    */
    private Long projectId;

    /**
     * 收款人姓名
     */
    private String recipientUserName;

    /**
    * 科目名称id
    */
    private Long oaId;

    /**
    * 申请时间开始时间
    */
    private String applicantStartTime;

    /**
     * 申请时间结束时间
     */
    private String applicantEndTime;
}