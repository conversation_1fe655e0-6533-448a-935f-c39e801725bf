package com.gok.pboot.pms.cost.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.dto.TaskStatQueryDTO;
import com.gok.pboot.pms.cost.entity.vo.*;
import com.gok.pboot.pms.cost.service.CostTaskStatService;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * 工单数据看板
 *
 * <AUTHOR>
 * @date 2025/05/12
 */
//@Inner(false)
@RequestMapping("/costTaskStat")
@RestController
@AllArgsConstructor
public class CostTaskStatController {

    private final CostTaskStatService costTaskStatService;

    /**
     * 项目工单看板-多维度售后交付工单统计
     *
     * @param pageRequest 页面请求
     * @param query       查询
     * @return {@link ApiResult }<{@link Page }<{@link ? } {@link extends } {@link ProjectProDimDeliverStatVO }>>
     */
    @PostMapping("/project/findDeliverTaskStat")
    public ApiResult<Page<? extends ProjectDeliverStatBaseVO>> findProjectDeliverTaskStat(PageRequest pageRequest, @RequestBody TaskStatQueryDTO query) {
        return ApiResult.success(costTaskStatService.findProjectDeliverTaskStat(pageRequest, query));
    }

    /**
     * 项目工单看板-多维度售后交付工单统计导出
     *
     * @param query 查询
     */
    @PostMapping("/project/exportDeliverTaskStat")
    public void exportProjectDeliverTaskStat(@RequestBody TaskStatQueryDTO query, HttpServletResponse response) {
        costTaskStatService.exportProjectDeliverTaskStat(query, response);
    }

    /**
     * 项目工单看板-多维度售前支撑工单统计
     *
     * @param pageRequest 页面请求
     * @param query       查询
     * @return {@link ApiResult }<{@link Page }<{@link ? } {@link extends } {@link ProjectProDimDeliverStatVO }>>
     */
    @PostMapping("/project/findPreSaleTaskStat")
    public ApiResult<Page<? extends ProjectPreSaleStatBaseVO>> findProjectPreSaleTaskStat(PageRequest pageRequest, @RequestBody TaskStatQueryDTO query) {
        return ApiResult.success(costTaskStatService.findProjectPreSaleTaskStat(pageRequest, query));
    }

    /**
     * 项目工单看板-多维度售前支撑工单统计导出
     *
     * @param query 查询
     */
    @PostMapping("/project/exportPreSaleTaskStat")
    public void exportProjectPreSaleTaskStat(@RequestBody TaskStatQueryDTO query, HttpServletResponse response) {
        costTaskStatService.exportProjectPreSaleTaskStat(query, response);
    }




    /**
     * 人员工单看板-多维度售后交付工单统计
     *
     * @param pageRequest 页面请求
     * @param query       查询
     * @return {@link ApiResult }<{@link Page }<{@link ? } {@link extends } {@link ProjectProDimDeliverStatVO }>>
     */
    @PreAuthorize("@pms.hasPermission('PERSON_WORK_ORDER_DASHBOARD')")
    @PostMapping("/personnel/findPersonnelDeliverTaskStat")
    public ApiResult<Page<? extends PersonnelDeliverStatBaseVO>> findPersonnelDeliverTaskStat(PageRequest pageRequest, @RequestBody TaskStatQueryDTO query) {
        return ApiResult.success(costTaskStatService.findPersonnelDeliverTaskStat(pageRequest, query));
    }

    /**
     * 人员工单看板-多维度售后交付工单统计导出
     *
     * @param query 查询
     */
    @PreAuthorize("@pms.hasPermission('PERSON_WORK_ORDER_DASHBOARD')")
    @PostMapping("/personnel/exportPersonnelDeliverTaskStat")
    public void exportPersonnelDeliverTaskStat(@RequestBody TaskStatQueryDTO query, HttpServletResponse response) {
        costTaskStatService.exportPersonnelDeliverTaskStat(query, response);
    }

    /**
     * 人员工单看板-多维度售前支撑工单统计
     *
     * @param pageRequest 页面请求
     * @param query       查询
     * @return {@link ApiResult }<{@link Page }<{@link ? } {@link extends } {@link ProjectProDimDeliverStatVO }>>
     */
    @PreAuthorize("@pms.hasPermission('PERSON_WORK_ORDER_DASHBOARD')")
    @PostMapping("/personnel/findPersonnelPreSaleTaskStat")
    public ApiResult<Page<? extends PersonnelPreSaleStatBaseVO>> findPersonnelPreSaleTaskStat(PageRequest pageRequest, @RequestBody TaskStatQueryDTO query) {
        return ApiResult.success(costTaskStatService.findPersonnelPreSaleTaskStat(pageRequest, query));
    }

    /**
     * 人员工单看板-多维度售前支撑工单统计导出
     *
     * @param query 查询
     */
    @PreAuthorize("@pms.hasPermission('PERSON_WORK_ORDER_DASHBOARD')")
    @PostMapping("/personnel/exportPersonnelPreSaleTaskStat")
    public void exportPersonnelPreSaleTaskStat(@RequestBody TaskStatQueryDTO query, HttpServletResponse response) {
        costTaskStatService.exportPersonnelPreSaleTaskStat(query, response);
    }


}
