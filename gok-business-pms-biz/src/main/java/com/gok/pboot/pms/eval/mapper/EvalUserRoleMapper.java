package com.gok.pboot.pms.eval.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.eval.entity.domain.EvalUserRole;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 项目评价用户角色Mapper
 *
 * <AUTHOR>
 * @date 2025/05/12
 */
@Mapper
public interface EvalUserRoleMapper extends BaseMapper<EvalUserRole> {

    /**
     * 查询所有用户角色
     *
     * @return 用户角色列表
     */
    List<EvalUserRole> findAll();

} 