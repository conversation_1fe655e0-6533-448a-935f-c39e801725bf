package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;

/**
 * 角色类型
 *
 * <AUTHOR>
 * @date 2024/01/19
 */
@AllArgsConstructor
public enum RoleTypeEnum implements ValueEnum<Integer> {

    /**
     * 项目销售经理
     */
    PROJECT_SALES_MANAGER(0, "客户经理",1),
    PROJECT_PRE_SALES_MANAGER(1, "售前经理",2),
    PROJECT_MANAGER(2, "项目经理",3),
    PROJECT_OPERATIONS_ASSISTANT(3, "项目操作助理",5),
    PROJECT_MEMBER(4, "项目成员",6),
    BUSINESS_MEMBER(5, "业务经理",4)
    ;

    private final Integer value;

    private final String name;

    private final Integer sortOrder;

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }

    public static Integer getSortOrderByValue(Integer value) {
        for (RoleTypeEnum roleTypeEnum : RoleTypeEnum.values()) {
            if (roleTypeEnum.value.equals(value)) {
                return roleTypeEnum.sortOrder;
            }
        }
        return null;
    }

}
