package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 成本配置差旅补贴 DTO
 *
 * <AUTHOR>
 * @date 2025/01/08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CostConfigTravelSubsidyDTO {

    /**
     * 人员数量（0=单人，1=多人）
     */
    @NotNull(message = "人员数量不能为空")
    private Integer personNum;

    /**
     * 出差天数（0=不限制，1=小于30天，2=大于等于30天）
     */
    @NotNull(message = "出差天数不能为空")
    private Integer awayDay;

    /**
     * 是否自行解决住宿（0=否，1=是）
     */
    @NotNull(message = "是否自行解决住宿不能为空")
    private Integer stayOwn;

    /**
     * 补贴标准（元/天）
     */
    @DecimalMin(value = "0.00", message = "补贴标准金额必须大于或等于0")
    @DecimalMax(value = "9999.00", message = "补贴标准金额必须小于或等于9999")
    @NotNull(message = "补贴标准金额不能为空")
    private BigDecimal subsidyPrice;

}
