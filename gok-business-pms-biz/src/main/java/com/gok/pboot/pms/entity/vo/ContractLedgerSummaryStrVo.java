package com.gok.pboot.pms.entity.vo;


import lombok.*;
import lombok.experimental.Accessors;

/**
 * 合同台账列表汇总vo
 *
 * <AUTHOR>
 * @date 2024/2/22
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ContractLedgerSummaryStrVo {

    /**
     * 合同总金额
     */
    private String salesContractAmount;

    /**
     * 待验收金额
     */
    private String beAcceptedAmount;

    /**
     * 待回款金额
     */
    private String accumulatedAmount;

    /**
     * 履约中合同数
     */
    private Integer contractsNumber;

    /**
     * 质保金占用总额
     */
    private String warrantyAmount;

}
