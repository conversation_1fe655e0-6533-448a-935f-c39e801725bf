package com.gok.pboot.pms.cost.controller;

import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.cost.entity.dto.CostConfigAccountDTO;
import com.gok.pboot.pms.cost.entity.vo.CostConfigAccountVO;
import com.gok.pboot.pms.cost.service.ICostConfigAccountService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 成本科目配置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @menu 成本科目配置
 * @since 2025-01-07
 */
@RestController
@Validated
@RequiredArgsConstructor
@RequestMapping("/costConfigAccount")
public class CostConfigAccountController {

    private final ICostConfigAccountService costConfigAccountService;

    /**
     * 从OA同步最新成本科目配置数据
     *
     * @return {@link ApiResult}<{@link String}>
     */
    @PostMapping("/insertCostSubjectConfigInfo")
    public ApiResult<String> insertCostSubjectConfigInfo() {
        costConfigAccountService.insertCostSubjectConfigInfo();
        return ApiResult.success("OA数据同步完成");
    }

    /**
     * 获取成本科目配置信息
     *
     * @return {@link ApiResult }<{@link List }<{@link CostConfigAccountVO }>>
     */
    @GetMapping("/getCostSubjectConfigInfo")
    public ApiResult<List<CostConfigAccountVO>> getCostSubjectConfigInfo() {
        return ApiResult.success(costConfigAccountService.getCostSubjectConfigInfo(), "获取成功");
    }

    /**
     * 根据版本id获取成本科目配置信息
     *
     * @param versionId 版本 ID
     * @return {@link ApiResult}<{@link List}<{@link CostConfigAccountVO}>>
     */
    @GetMapping("/findByVersionId/{versionId}")
    public ApiResult<List<CostConfigAccountVO>> getCostSubjectConfigByVersionId(@PathVariable("versionId") Long versionId) {
        return ApiResult.success(costConfigAccountService.getCostSubjectConfigByVersionId(versionId), "获取成功");
    }

    /**
     * 更新成本科目配置信息
     *
     * @param dtoList DTO 列表
     * @return {@link ApiResult}<{@link String}>
     */
    @PutMapping("/updateCostSubjectConfigInfo")
    public ApiResult<String> updateCostSubjectConfigInfo(@RequestBody @Valid List<CostConfigAccountDTO> dtoList) {
        costConfigAccountService.updateCostSubjectConfigInfo(dtoList);
        return ApiResult.successMsg("操作成功");
    }

}
