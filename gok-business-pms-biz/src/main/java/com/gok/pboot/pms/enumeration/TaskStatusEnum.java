package com.gok.pboot.pms.enumeration;

/**
 * 任务状态枚举
 **/
public enum TaskStatusEnum implements ValueEnum<Integer> {
    /**
     * 正常
     */
    ZC(0, "正常"),
    /**
     * 关闭
     */
    GB(1, "关闭"),
    ;

    //值
    private Integer  value;
    //名称
    private String name;

    TaskStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 获取值
     *
     * @return Integer
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取名称
     *
     * @return String
     */
    @Override
    public String getName() {
        return name;
    }

    public static String getNameByVal(Integer value) {
        for (TaskStatusEnum statusEnum : TaskStatusEnum.values()) {
            if (statusEnum.value.equals(value)) {
                return statusEnum.name;
            }
        }
        return "";
    }
    public static Integer getValByName(String name) {
        for (TaskStatusEnum statusEnum : TaskStatusEnum.values()) {
            if (statusEnum.getName().equals(name)) {
                return statusEnum.getValue();
            }
        }
        return null;
    }
}
