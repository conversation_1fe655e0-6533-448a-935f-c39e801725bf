package com.gok.pboot.pms.cost.entity.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 成本确认 DTO
 *
 * <AUTHOR>
 * @date 2025/01/08
 */
@Data
public class CostConfirmDTO {

    /**
     * 成本管理版本 ID
     */
    @NotNull(message = "成本管理版本 ID不能为空")
    private Long costManageVersionId;

    /**
     * 操作
     */
    @NotNull(message = "操作不能为空")
    private Boolean operate;

    /**
     * 拒绝原因
     */
    private String refuseReason;
}
