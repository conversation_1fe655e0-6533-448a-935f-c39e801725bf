package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.entity.CompensatoryLeaveData;
import com.gok.pboot.pms.entity.dto.CompensatoryLeaveDTO;
import com.gok.pboot.pms.entity.dto.PanelRequestDTO;
import com.gok.pboot.pms.entity.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 加班、请假、销假数据同步 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-12
 */
@Mapper
public interface CompensatoryLeaveDataMapper extends BaseMapper<CompensatoryLeaveData> {

    List<CompensatoryLeaveDataVO> findByDateTimeRangeAndUserId(
            @Param("startTime") LocalDate startTime,
            @Param("endTime") LocalDate endTime,
            @Param("userId") Long userId,
            @Param("type") String type
    );

    List<CompensatoryLeaveDataVO> findByDateTimeRangeAndUserIds(@Param("startTime") LocalDate startTime,
                                                                @Param("endTime") LocalDate endTime,
                                                                @Param("userIds") List<Long> userIds,
                                                                @Param("type") String type);



    List<CompensatoryLeaveSumVO> sumLeaveByDateTimeRangeAndUserIds(@Param("startTime") LocalDate startTime,
                                                                   @Param("endTime") LocalDate endTime,
                                                                   @Param("userIds") List<Long> userIds,
                                                                   @Param("projectIds") List<Long> projectIds,
                                                                   @Param("type") String type);

    List<CompensatoryLeaveProjectSumVO> projectLeaveByDateTimeRangeAndUserIds(@Param("startTime") LocalDate startTime,
                                                                              @Param("endTime") LocalDate endTime,
                                                                              @Param("userIds") List<Long> userIds,
                                                                              @Param("projectIds") List<Long> projectIds,
                                                                              @Param("type") String type);

    List<CompensatoryLeaveDeptSumVO> sumLeaveDeptByDateTimeRangeAndUserIds(@Param("startTime") LocalDate startTime,
                                                                           @Param("endTime") LocalDate endTime,
                                                                           @Param("userIds") List<Long> userIds,
                                                                           @Param("projectIds") List<Long> projectIds,
                                                                           @Param("type") String type);

    /**
     * 根据参数获取调休数据
     *
     * @param filter 查询参数
     * @return 调休数据集
     */
    List<CompensatoryLeaveDataVO> findTxByParams(@Param("filter") Map<String, Object> filter);

    /**
     * 根据参数获取调休数据统计数据
     *
     * @param filter 查询参数
     * @return 调休数据集
     */
    BigDecimal getCompensatoryHoursSum(@Param("filter") CompensatoryLeaveDTO filter);

    /**
     * 按时间范围和项目名称查询该用户的所有项目工时数据
     *
     * @param filter 查询条件
     * @param page   分页参数
     * @return {@link Page < PanelProjectSituationVO >}
     */
    Page<PanelProjectSituationVO> selectPanelProjectPage(@Param("filter") PanelRequestDTO filter, Page<Object> page);
}

