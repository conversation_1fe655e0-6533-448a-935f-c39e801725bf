package com.gok.pboot.pms.common.constant;

import lombok.experimental.UtilityClass;

/**
 * PMS相关常量
 *
 * <AUTHOR>
 * @version 1.3.2
 */
@UtilityClass
public class PmsConstants {

    public static final String RDK_DAILY_PERSONNEL_FIND_DEPT_USER_TREE = "dailyPersonnelFindDeptUserTree:";
    /**
     * 接入中台日志相关
     */
    public static final String PMS_LOG_LEVEL_NAME = "PMS日志级别";
    public static final String PMS_LOG_NAME = "PMS日志名称";
    public static final String PMS_FEATURES_PAGE = "PMS功能页";

    /**
     * 中台超管
     */
    public static final String BCP_ADMIN_NAME = "超级管理员";
    /**
     * 中台超管ID
     */
    public static final Long BCP_ADMIN_ID = 10000L;


    public static final Long CLIENT_ID = 1698521681078931458L;
}
