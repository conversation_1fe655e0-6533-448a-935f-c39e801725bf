package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.cost.entity.domain.CostIncomeCalculationDetail;
import com.gok.pboot.pms.cost.entity.dto.CostIncomeCalculationDTO;
import com.gok.pboot.pms.cost.entity.vo.CostIncomeCalculationDetailVO;

import java.util.List;

/**
 * 收入测算服务接口
 *
 * <AUTHOR>
 * @create 2025/02/18
 **/
public interface ICostIncomeCalculationDetailService extends IService<CostIncomeCalculationDetail> {

    /**
     * 查询收入测算明细列表
     *
     * @param projectId 项目ID
     * @param request   查询请求
     * @return
     */
    List<CostIncomeCalculationDetailVO> findDetailList(Long projectId, CostIncomeCalculationDTO request);

    /**
     * 更新或新增收入测算数据
     */
    void saveOrUpdateCostIncomeCalculation(List<Long> projectIds);

    /**
     * 批量重新生成收入测算数据
     *
     * @return 收入测算ID
     */
    List<Long> batchRegenerate(CostIncomeCalculationDTO request);

    /**
     * 批量确认收入测算数据
     *
     * @param request
     * @return
     */
    List<Long> batchConfirm(CostIncomeCalculationDTO request);

    /**
     * 批量取消确认
     *
     * @param request
     * @return
     */
    List<Long> batchCancelConfirm(CostIncomeCalculationDTO request);

    /**
     * 批量结算明细
     *
     * @param request
     * @return
     */
    List<Long> batchSettlement(CostIncomeCalculationDTO request);

}
