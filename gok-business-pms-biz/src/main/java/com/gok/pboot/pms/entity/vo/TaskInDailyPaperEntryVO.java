package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.common.join.TaskInDailyPaperEntry;
import com.gok.pboot.pms.entity.domain.Task;
import lombok.*;

/**
 * - 日报条目中的任务对象VO -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/26 9:16
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
public class TaskInDailyPaperEntryVO {
    /**
     * ID
     */
    private Long id;
    /**
     * 名称
     */
    private String taskName;
    /**
     * 旧任务标记
     */
    private Integer oldTaskFlag;

    public TaskInDailyPaperEntryVO(Task task) {
        this.id = task.getId();
        this.taskName = task.getTaskName();
    }

    public TaskInDailyPaperEntryVO(TaskInDailyPaperEntry taskInEntry){
        this.id = taskInEntry.getId();
        this.taskName = taskInEntry.getTaskName();
    }
}
