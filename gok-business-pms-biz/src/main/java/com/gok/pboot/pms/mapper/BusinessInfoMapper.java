package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.data.datascope.BusinessDataScope;
import com.gok.pboot.pms.entity.domain.BusinessContact;
import com.gok.pboot.pms.entity.domain.BusinessInfo;
import com.gok.pboot.pms.entity.dto.BusinessInfoDTO;
import com.gok.pboot.pms.entity.dto.BusinessProgressDTO;
import com.gok.pboot.pms.entity.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 商机信息表（OA同步）(BusinessInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-11-21 14:18:07
 */
@Mapper
public interface BusinessInfoMapper extends BaseMapper<BusinessInfo> {

    /**
     * 查询ID列表
     *
     * @param filter 参数
     * @return 项目ID列表
     */
    List<Long> findIds(@Param("filter") Map<String, Object> filter);

    /**
     * 分页获取商机信息
     *
     * @param page 分页参数
     * @param dto  请求参数
     * @return Page<BusinessInfo>
     */
    @Deprecated
    Page<BusinessInfo> findBusinessInfoPage(Page page, @Param("filter") BusinessInfoDTO dto);

    /**
     * 分页获取商机台账vo
     *
     * @param page   分页参数
     * @param filter 过滤条件
     * @return Page<BusinessInfoVO>
     */
    Page<BusinessInfoVO> findBusinessInfoVOPage(Page page, @Param("filter") Map<String, Object> filter);

    /**
     * 获取商机台账vo
     *
     * @param filter 请求参数
     * @return 数据列表
     */
    List<BusinessInfoVO> findBusinessInfoVO(@Param("filter") Map<String, Object> filter);

    /**
     * 根据商机id获取单个商机vo
     *
     * @param id 商机ID
     * @return BusinessInfoVO
     */
    BusinessInfoVO findOne(@Param("id") Long id);

    /**
     * 根据项目id获取单个商机vo
     *
     * @param projectId 项目ID
     * @return BusinessInfoVO
     */
    BusinessInfoVO findOneByProjectId(@Param("projectId") Long projectId);

    /**
     * 获取商机对应联系人列表
     *
     * @param businessId 商机ID
     * @return List<BusinessContact>
     */
    List<BusinessContact> findContact(@Param("businessId") Long businessId);

    /**
     * 获取商机对应联系人数量
     *
     * @param businessIds 商机ID列表
     * @return List<BusinessContact>
     */
    List<BusinessOfContactCountVo> getContactCount(@Param("businessIds") List<Long> businessIds);

    /**
     * 动态获取所有的项目所在城市列表
     *
     * @return 城市列表
     */
    List<String> findAllProjectLocation();

    /**
     * 根据商机ID获取对应的商机进展列表（根据提交时间倒序）
     *
     * @param id 商机ID
     * @return List<BusinessProgressVO>
     */
    List<BusinessProgressVO> findProgressGroupByBusinessId(Long id);

    /**
     * 根据商机ID列表获取对应最新的商机进展列表
     *
     * @param id 商机ID
     * @return List<BusinessProgressVO>
     */
    List<BusinessProgressVO> findNewProgressByBusinessIds(@Param("businessIds") List<Long> id);

    /**
     * 获取商机对应的数据变更日志
     *
     * @param id        商机ID
     * @param projectId 项目ID
     * @return List<BusinessDataLogVO>
     */
    List<BusinessDataLogVO> findDataLog(@Param("id") Long id, @Param("projectId") Long projectId);

    /**
     * 分页获取商机进展vo
     *
     * @param page 分页参数
     * @param dto  请求参数
     * @return {@link Page<BusinessProgressVO>}
     */
    @BusinessDataScope(deptOrUser = "user", scopeUserNameList = {"salesman_user_id", "submit_user_id"})
    Page<BusinessProgressVO> findBusinessProgressPage(Page page, @Param("filter") BusinessProgressDTO dto);

    /**
     * 获取商机进展vo
     *
     * @param dto 请求参数
     * @return 数据列表
     */
    @BusinessDataScope(deptOrUser = "user", scopeUserNameList = {"salesman_user_id", "submit_user_id"})
    List<BusinessProgressVO> findBusinessProgress(@Param("filter") BusinessProgressDTO dto);

    /**
     * 根据id查询商机进展
     *
     * @param id 商机进展ID
     * @return {@link BusinessProgressDetailsVO}
     */
    BusinessProgressDetailsVO findOneProgress(@Param("id") Long id);

    /**
     * 商机流程文件接口（获取文件对应的下载路径信息）
     *
     * @param requestId 请求id
     * @return {@link BusinessProgressVO}
     */
    BusinessProgressVO findBusinessProgressByRequestId(@Param("requestId") Long requestId);

    /**
     * 获取用户可用的商机进展记录 对应的商机ID列表
     *
     * @param filter 参数
     * @return 项目ID列表
     */
    List<Long> findBusinessIdsByProgress(@Param("filter") Map<String, Object> filter);

    /**
     * 查询ID列表
     *
     * @param filter 参数
     * @return 商机ID列表
     */
    List<Long> findId(@Param("filter") Map<String, Object> filter);


    /**
     * 根据所属客户id查找商机
     * @param unitId
     * @param businessStatus
     * @return
     */
    Integer countByUnitId(@Param("unitId") Long unitId, @Param("businessStatus") Integer businessStatus);
}
