package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.entity.domain.SalesReceipt;
import com.gok.pboot.pms.entity.dto.SalesReceiptDTO;
import com.gok.pboot.pms.entity.vo.SalesReceiptVO;

import java.util.List;

/**
 * 销售收款计划Service
 *
 * <AUTHOR>
 * @description 针对表【sales_receipt(销售收款计划)】的数据库操作Service
 * @createDate 2023-09-27 16:08:24
 */
public interface ISalesReceiptService extends IService<SalesReceipt> {

    /**
     * 分页条件查询
     *
     * @param dto 查询条件
     * @return page
     */
    Page<SalesReceiptVO> findPage(SalesReceiptDTO dto);

    /**
     * 导出Excel
     *
     * @param dto dto
     * @return {@link List}
     */
    List<SalesReceiptVO> export(SalesReceiptDTO dto);

    Boolean pushMessage();
}
