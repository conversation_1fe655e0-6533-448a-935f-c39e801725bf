package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.user.PigxUser;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.CommonUtils;
import com.gok.pboot.pms.Util.NewOldComparer;
import com.gok.pboot.pms.Util.PageAdapter;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.OperatingRecord;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.domain.Roster;
import com.gok.pboot.pms.entity.domain.Task;
import com.gok.pboot.pms.entity.domain.TaskUser;
import com.gok.pboot.pms.entity.dto.TaskAddDTO;
import com.gok.pboot.pms.entity.dto.TaskAddUserDTO;
import com.gok.pboot.pms.entity.dto.TaskUpdateDTO;
import com.gok.pboot.pms.entity.dto.UserPmsDTO;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.enumeration.ApprovalStatusEnum;
import com.gok.pboot.pms.enumeration.OperationTypeEnum;
import com.gok.pboot.pms.enumeration.TaskStatusEnum;
import com.gok.pboot.pms.enumeration.TaskTypeEnum;
import com.gok.pboot.pms.mapper.*;
import com.gok.pboot.pms.service.IProjectTaskService;
import com.gok.pboot.pms.service.ITaskService;
import com.gok.pboot.pms.service.fegin.CenterUserService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-19
 */
@Service
@Slf4j
@Transactional(readOnly = true, rollbackFor = Exception.class)
@AllArgsConstructor
public class TaskServiceImpl implements ITaskService {

    private final TaskMapper mapper;
    private final ProjectMapper projectMapper;
    private final TaskUserMapper taskUserMapper;
    private final OperatingRecordMapper operatingRecordMapper;
    private final RosterMapper rosterMapper;

    private final ProjectInfoMapper projectInfoMapper;

    private final CenterUserService centerUserService;

    private final IProjectTaskService projectTaskService;


    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public ApiResult deleteByLogic(Long id) {
        //①子任务下存在工时情况下，不可删除，且触发toast提示
        TaskInfoVO taskInfoVo = mapper.getTaskInfo(id, null);
        if(taskInfoVo.getActualHour().compareTo(BigDecimal.ZERO) == 1){
            return ApiResult.failure("删除失败，子任务下存在工时！");
        }
        mapper.deleteByLogic(id);
        return ApiResult.success("删除成功");
    }


    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public Page<TaskInfoVO> findProjectIdPage(Long id, PageRequest pageRequest, Map<String, Object> filter) {
        Page page = new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize());
        //获取任务分页
        List<TaskInfoVO> taskInfoVoPage = mapper.selectListVo(new PageAdapter(page), id, filter);
        Long aLong = mapper.selectTaskCount(id, filter);
        //如果任务数为0，手动添加一条Task
        if (aLong == 0){
            if (mapper.selectTaskCountMr(id) == 0){

                Task task = new Task();
                task.setTaskName(TaskTypeEnum.MRRW.getName());
                task.setTaskType(TaskTypeEnum.MRRW.getValue());
                task.setTaskStatus(TaskStatusEnum.ZC.getValue());
                task.setProjectId(id);
                BaseBuildEntityUtil.buildInsert(task);
                task.setCreator("admin");
                task.setCreatorId(1559424497052119042L);

                //插入
                mapper.insert(task);

                //重新查询一次
                taskInfoVoPage = mapper.selectListVo(new PageAdapter(new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize())), id, filter);
                page.setTotal(aLong+1);

            }
        }
        page.setTotal(aLong);
        for (TaskInfoVO taskInfoVO : taskInfoVoPage) {
            taskInfoVO.setNormalHour(CommonUtils.unitConversion(taskInfoVO.getNormalHour()));
            taskInfoVO.setAddedHour(CommonUtils.unitConversion(taskInfoVO.getAddedHour()));
            taskInfoVO.setActualHour(CommonUtils.unitConversion(taskInfoVO.getActualHour()));
        }
        page.setRecords(taskInfoVoPage);
        return page;
    }

    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public ApiResult<String> addTaskUser(TaskAddUserDTO entity) {
        if (!TaskOpen(entity.getId())){
            return ApiResult.failure("添加人员失败,请先开启任务状态！");
        }
        List<Long> taskUserIds = entity.getTaskUserIds();
        //校验
        if (taskUserIds.size() == 0){
            return ApiResult.failure("添加人员失败,人员信息不能为空！");
        }
        //去掉重复的人员id
        List<TaskUser> taskUsersOld = taskUserMapper.selectList(new QueryWrapper<TaskUser>().eq("task_id", entity.getId())
                .eq("del_flag", "0"));
        List<Long> collect = taskUsersOld.stream().map(TaskUser::getUserId).collect(Collectors.toList());
        taskUserIds.removeAll(collect);
        if (taskUserIds.size() == 0){
            return ApiResult.failure("添加人员已存在！");
        }
        UserPmsDTO userPmsDTO = new UserPmsDTO();
        userPmsDTO.setUserIds(taskUserIds);
        Map<Long, Roster> userIdMap = rosterMapper.findUserIdMap(taskUserIds);
        Set<Long> resultSet;

        if (userIdMap.isEmpty()) {
            return ApiResult.failure("添加人员失败,全部人员id未知");
        }
        resultSet = userIdMap.keySet();
        //人员获取缺失
        if (resultSet.size() < taskUserIds.size()){
            taskUserIds.removeAll(resultSet);
            String msg = "";
            for (int i = 0; i < taskUserIds.size(); i++) {
                msg = msg + taskUserIds.get(i)+ ",";
            }
            msg = msg.substring(0,msg.length()-1);
            return ApiResult.failure("添加人员失败,未知人员id："+ msg );
        }

        //添加人员-任务关系
        final String[] operationInfo = {""};
        Long taskId = entity.getId();
        ArrayList<TaskUser> taskUsers = new ArrayList<>();
        userIdMap.values().forEach(x -> {
            TaskUser taskUser = new TaskUser();
            String aliasName = x.getAliasName();

            taskUser.setTaskId(taskId);
            taskUser.setUserId(x.getId());
            taskUser.setUserName(aliasName);
            operationInfo[0] = operationInfo[0] + aliasName + ",";
            BaseBuildEntityUtil.buildInsert(taskUser);
            taskUsers.add(taskUser);
        });
        taskUserMapper.batchSave(taskUsers);

        //添加操作记录
        insertOperatingRecord(OperationTypeEnum.TJ.getValue(),
                operationInfo[0].substring(0,operationInfo[0].length() - 1),
                taskId
        );

        return ApiResult.success("添加人员成功！");
    }

    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public ApiResult<String> update(TaskUpdateDTO entity) {
        //通过id获取数据库原始数据 进行对比
        TaskInfoVO taskInfo = mapper.getTaskInfo(entity.getId(), ApprovalStatusEnum.YTG.getValue());
        // 任务重名校验
        String taskName = entity.getTaskName();
        boolean duplicateNameInvalid = mapper.findByProjectId(taskInfo.getProjectId())
                .stream()
                .anyMatch(t -> !taskInfo.getId().equals(t.getId()) && StringUtils.equals(t.getTaskName(), taskName));
        if (duplicateNameInvalid){
            return ApiResult.failure("编辑失败，同项目下已有相同名称的任务！");
        }
        //状态变更判断
        if (!taskInfo.getTaskStatus().equals(entity.getTaskStatus())){
            //增加一条 状态变更记录
            insertOperatingRecord(OperationTypeEnum.ZTBG.getValue(),
                    TaskStatusEnum.getNameByVal(entity.getTaskStatus()),
                    entity.getId());
        }

        //新旧人员集合
        List<TaskUserInfoVO> taskUserInfoVoList = taskInfo.getTaskUserInfoVoList();
        List<Long> oldUserIds = taskUserInfoVoList.stream().map(x -> x.getUserId()).collect(Collectors.toList());
        List<Long> newUserIds = entity.getTaskUserIds();

        //传入新旧人员id集合、自动添加操作记录、删除和添加人员-任务关系
        autoUpdateOperatingRecord(newUserIds,oldUserIds,entity.getId());

        //更新任务
        Task task = new Task();
        BeanUtils.copyProperties(taskInfo,task);
        task.setTaskName(entity.getTaskName());
        task.setTaskStatus(entity.getTaskStatus());
        BaseBuildEntityUtil.buildUpdate(task);
        mapper.updateById(task);

        return ApiResult.success("更新成功！");
    }

    @Override
    public Page<OperatingRecordPageVO> findOperatingRecordPage(Long id, PageRequest pageRequest) {
        Page<OperatingRecordPageVO> page= operatingRecordMapper.findOperatingRecordPage(new Page<OperatingRecordPageVO>(pageRequest.getPageNumber(), pageRequest.getPageSize()), id);
        return page;
    }

    @Override
    @Deprecated
    public List<TaskInDailyPaperEntryVO> findAvailableTasksForCurrentUserByProjectId(Long projectId) {
        return mapper.findByUserIdAndProjectIdForEntry(SecurityUtils.getUser().getId(), projectId)
                .stream()
                .map(TaskInDailyPaperEntryVO::new)
                .collect(Collectors.toList());
    }

    @Override
    public List<Task> findByprojectId(Long id) {
        Map<String, Object> filter = new HashMap<>();
        filter.put("projectId", id);
        List<ProjectTaskVO> projectTaskVOList = projectTaskService.findList(filter);
        List<Task> taskList = new ArrayList<>();
        for (ProjectTaskVO projectTaskVO : projectTaskVOList) {
            Task task = BeanUtil.copyProperties(projectTaskVO, Task.class);
            task.setId(projectTaskVO.getTaskId());
            task.setTaskName(projectTaskVO.getTitle());
            task.setTaskStatus(projectTaskVO.getState());
            task.setProjectId(projectTaskVO.getProjectId());
            taskList.add(task);
        }
        taskList.addAll(mapper.findByProjectId(id));
        return taskList;
    }

    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public ApiResult<String> newAdd(TaskAddDTO entity) {

//        List<SysUserVO> sysUserVoList = new ArrayList<>();
        Map<Long, String> userIdNameMap = new HashMap<>();

        //项目id校验
        Long projectId = entity.getProjectId();
        ProjectInfo project = projectInfoMapper.selectOne(new QueryWrapper<ProjectInfo>().eq("id", projectId));
        if (null == project){
            return ApiResult.failure("新增失败，所属项目ID不存在！");
        }

        // 任务重名校验
        String taskName = entity.getTaskName();
        boolean duplicateNameInvalid = mapper.findByProjectId(projectId)
                .stream()
                .anyMatch(t -> StringUtils.equals(t.getTaskName(), taskName));
        if (duplicateNameInvalid){
            return ApiResult.failure("新增失败，同项目下已有相同名称的任务！");
        }

        //人员列表校验（人员是否存在）
        List<Long> taskUserIds = entity.getTaskUserIds();
        if (!taskUserIds.isEmpty()){
//            // 远程调用 此处仅用到用户信息的用户id
//            UserPmsDTO userPmsDTO = new UserPmsDTO();
//            userPmsDTO.setUserIds(taskUserIds);
//            sysUserVoList = centerUserService.getUserListByMultiParameterPms(userPmsDTO).unpack().orElse(new ArrayList<>())
//                    .stream().map(r -> {
//                        SysUserVO sysUserVO = SysUserVO.from(r);
//                        return sysUserVO;
//                    }).distinct().collect(Collectors.toList());
//
//            List<Long> resultList = sysUserVoList.stream().map(x -> x.getUserId()).collect(Collectors.toList());

            // 获取有效用户id-name map集合
            userIdNameMap = rosterMapper.selectBatchIds(taskUserIds)
                    .stream().collect(Collectors.toMap(Roster::getId, Roster::getAliasName));
            if (userIdNameMap.isEmpty()){
                return ApiResult.failure("新增失败,全部人员id未知");
            }
            //人员获取缺失
            if (userIdNameMap.size() < taskUserIds.size()){
                taskUserIds.removeAll(userIdNameMap.keySet());
                StringBuilder msg = new StringBuilder();
                for (Long taskUserId : taskUserIds) {
                    msg.append(taskUserId).append(",");
                }
                return ApiResult.failure("新增失败,未知人员id："+ msg.substring(0, msg.length() - 1));
            }

        }

        Task task = new Task();
        BeanUtils.copyProperties(entity,task);
        task.setTaskType(TaskTypeEnum.SDTJ.getValue());

        BaseBuildEntityUtil.buildInsert(task);
        mapper.insert(task);

        //人员-任务关系
        if (!userIdNameMap.isEmpty()){
            Long taskId = task.getId();
            ArrayList<TaskUser> taskUsers = new ArrayList<>();

            for (Map.Entry<Long, String> entry : userIdNameMap.entrySet()) {
                TaskUser taskUser = new TaskUser();
                taskUser.setTaskId(taskId);
                taskUser.setUserId(entry.getKey());
                taskUser.setUserName(entry.getValue());
                BaseBuildEntityUtil.buildInsert(taskUser);
                taskUsers.add(taskUser);
            }

            taskUserMapper.batchSave(taskUsers);
        }

        return ApiResult.success("新增成功！");
    }

    /**
     * 任务是否关闭 true：开启
     */
    private Boolean TaskOpen(Long id){
        Task one = mapper.selectOne(new QueryWrapper<Task>().eq("id", id));
        return TaskStatusEnum.ZC.getValue().equals(one.getTaskStatus());
    }

    /**
     * 添加操作记录对象包装
     * @param operationType 操作类型
     * @param operationInfo 操作信息（操作了哪些用户）
     * @param taskId 操作任务id
     */
    private void insertOperatingRecord(Integer operationType,String operationInfo,Long taskId) {
        OperatingRecord operatingRecord = new OperatingRecord();
        operatingRecord.setOperationType(operationType);
        operatingRecord.setOperationInfo(StringUtils.isBlank(operationInfo) ? "无" : operationInfo);
        operatingRecord.setTaskId(taskId);
        PigxUser user = SecurityUtils.getUser();
        operatingRecord.setOperator(user.getName());
        BaseBuildEntityUtil.buildInsert(operatingRecord);
        operatingRecordMapper.insert(operatingRecord);
    }

    /**
     * 自动编辑操作记录对象包装
     * @param newIds 新人员信息
     * @param oldIds 旧人员信息
     * @param taskId 操作任务id
     */
    private void autoUpdateOperatingRecord(List<Long> newIds, List<Long> oldIds,Long taskId) {
        NewOldComparer newOldComparer = new NewOldComparer();
        newOldComparer.NewOldComparer(newIds,oldIds);
        List<Long> insertIds = newOldComparer.insertIds();
        List<Long> deleteIds = newOldComparer.deleteIds();
        ArrayList<Long> taskUserIds = new ArrayList<>();
        taskUserIds.addAll(insertIds);
        taskUserIds.addAll(deleteIds);
        if (taskUserIds.isEmpty()){
            return;
        }
        // 远程调用 此处仅用到用户信息的用户id、name
//        UserPmsDTO userPmsDTO = new UserPmsDTO();
//        userPmsDTO.setUserIds(taskUserIds);
//        Map<Long, String> sysUserVoMap = centerUserService.getUserListByMultiParameterPms(userPmsDTO)
//                .unpack().orElse(new ArrayList<>()).stream().collect(Collectors.toMap(SysUserOutVO::getUserId, SysUserOutVO::getName, (a, b) -> a));
        Map<Long, String> sysUserVoMap = rosterMapper.selectBatchIds(taskUserIds)
                .stream().collect(Collectors.toMap(Roster::getId, Roster::getAliasName, (a, b) -> a));
        final String[] operationInfo = {"",""};
        if (!insertIds.isEmpty()){
            //增加人员关系
            ArrayList<TaskUser> taskUsers = new ArrayList<>();
            insertIds.forEach(x -> {
                String s = sysUserVoMap.get(x);
                operationInfo[0] = operationInfo[0] +s+",";
                TaskUser taskUser = new TaskUser();
                taskUser.setTaskId(taskId);
                taskUser.setUserId(x);
                taskUser.setUserName(s);
                BaseBuildEntityUtil.buildInsert(taskUser);
                taskUsers.add(taskUser);
            });
            taskUserMapper.batchSave(taskUsers);
            insertOperatingRecord(OperationTypeEnum.TJ.getValue(),
                    operationInfo[0].substring(0,operationInfo[0].length()-1),taskId);
        }
        if (!deleteIds.isEmpty()){
            //删除任务-人员关系
            taskUserMapper.deleteBatchUserIds(deleteIds,taskId);
            deleteIds.forEach(x -> {
                operationInfo[1] = operationInfo[1] +sysUserVoMap.get(x)+",";
            });
            insertOperatingRecord(OperationTypeEnum.YC.getValue(),
                    operationInfo[1].substring(0,operationInfo[1].length()-1),taskId);
        }

    }



}
