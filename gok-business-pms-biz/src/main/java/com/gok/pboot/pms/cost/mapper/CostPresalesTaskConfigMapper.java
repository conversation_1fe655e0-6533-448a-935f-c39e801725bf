package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.cost.entity.domain.CostPresalesTaskConfig;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 售前报工工单配置表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025/01/15
 */
@Mapper
public interface CostPresalesTaskConfigMapper extends BaseMapper<CostPresalesTaskConfig> {


    /**
     * 获取不存在交付任务项目列表
     *
     * @return {@link List }<{@link ProjectInfo }>
     */
    List<ProjectInfo> getNotExistDeliverTaskProjectList();
}