package com.gok.pboot.pms.entity.vo;

import lombok.Data;

/**
 * 研发风险表VO
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-17 11:11:28
 */
@Data
public class ProjectResearchRiskVO{

    /**
     * id
     */
    private Long id;

	/**
	 * 项目ID
	 */
	private Long projectId;
	/**
	 * 风险类型
	 */
	private Integer riskType;
    /**
     * 风险类型
     */
    private String riskTypeName;
	/**
	 * 风险描述
	 */
	private String riskDescription;
	/**
	 * 风险原因
	 */
	private String cause;
	/**
	 * 发生概率
	 */
	private String probability;
	/**
	 * 影响程度
	 */
	private Integer impactLevel;
    /**
     * 影响程度
     */
    private String impactLevelName;
	/**
	 * 应对措施
	 */
	private String mitigationStrategy;

}
