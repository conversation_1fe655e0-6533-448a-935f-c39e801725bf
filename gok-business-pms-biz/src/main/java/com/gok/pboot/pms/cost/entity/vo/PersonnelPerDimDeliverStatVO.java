package com.gok.pboot.pms.cost.entity.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 人员工单看板-人员维度售后工单统计VO
 *
 * <AUTHOR>
 * @date 2025/05/15
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PersonnelPerDimDeliverStatVO extends PersonnelDeliverStatBaseVO{
    /**
     * 人员工号
     */
    @ExcelProperty("员工工号")
    private String workCode;

    /**
     * 人员姓名
     */
    @ExcelProperty("员工姓名")
    private String employeeName;

    /**
     * 分配工单产值
     */
    @ExcelProperty("分配工单产值")
    private BigDecimal income;

    /**
     * 结算工单产值
     */
    @ExcelProperty("结算工单产值")
    private BigDecimal settleIncome;

    /**
     * 待结算工单产值
     */
    @ExcelProperty("待结算工单产值")
    private BigDecimal waitSettleIncome;

    /**
     * 预算人工成本
     */
    @ExcelProperty("预算人工成本")
    private BigDecimal budgetCost;

    /**
     * 实际已发生人工成本
     */
    @ExcelProperty("实际已发生人工成本")
    private BigDecimal actualLaborCost;

    /**
     * 剩余人工成本
     */
    @ExcelProperty("剩余人工成本")
    private BigDecimal remainingLaborCosts;

    /**
     * 预算工时
     */
    @ExcelProperty("预算工时")
    private BigDecimal estimatedHours;

    /**
     * 实际已发生工时
     */
    @ExcelProperty("实际已发生工时")
    private BigDecimal actualHours;

    /**
     * 已下发工单个数
     */
    @ExcelProperty("已下发工单个数")
    private Integer issuedTaskCount;

    /**
     * 已完成工单个数
     */
    @ExcelProperty("已完成工单个数")
    private Integer completedTaskCount;

    /**
     * 待完成工单个数
     */
    @ExcelProperty("待完成工单个数")
    private Integer waitCompleteTaskCount;

    /**
     * 待评价工单个数
     */
    @ExcelProperty("待评价工单个数")
    private Integer waitEvaluateTaskCount;
}
