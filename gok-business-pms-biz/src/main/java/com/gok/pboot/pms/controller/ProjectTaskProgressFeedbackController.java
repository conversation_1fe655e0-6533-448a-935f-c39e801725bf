package com.gok.pboot.pms.controller;

import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.entity.dto.ProjectTaskProgressFeedbackAddDTO;
import com.gok.pboot.pms.service.IProjectTaskProgressFeedbackService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 项目任务进展回复
 *
 * <AUTHOR>
 * @version 1.1.0
 */
@RestController
@RequestMapping("/projectTaskProgressFeedback")
@AllArgsConstructor
public class ProjectTaskProgressFeedbackController {

    private IProjectTaskProgressFeedbackService service;

    /**
     * 新增任务进展回复
     * @param dto 请求参数对象
     * @return void
     */
    @PostMapping("/add")
    public ApiResult<Void> add(@Valid @RequestBody ProjectTaskProgressFeedbackAddDTO dto){
        service.add(dto);

        return ApiResult.success(null);
    }

    /**
     * 删除任务进展回复
     * @param id ID
     * @return void
     */
    @DeleteMapping("/{id}")
    public ApiResult<Void> deleteById(@PathVariable("id") Long id){
        service.deleteById(id);

        return ApiResult.success(null);
    }
}
