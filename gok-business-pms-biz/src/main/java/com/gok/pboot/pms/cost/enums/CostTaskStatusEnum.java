package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工单状态枚举
 *
 * <AUTHOR>
 * @date 2025/01/15
 */
@AllArgsConstructor
@Getter
public enum CostTaskStatusEnum implements ValueEnum<Integer> {

    /**
     * 待拆解
     */
    DCJ(0, "待拆解"),

    /**
     * 未完成
     */
    WWC(1, "未完成"),

    /**
     * 待二级负责人审核
     */
    DEJFZRSH(2, "待二级负责人审核"),

    /**
     * 二级负责人已通过
     */
    EJFZRYTG(3, "二级负责人已通过"),

    /**
     * 待一级负责人审核
     */
    DYJFZRSH(4, "待一级负责人审核"),

    /**
     * 一级负责人已通过
     */
    YJFZRYTG(5, "一级负责人已通过"),


    /**
     * 待项目经理审核
     */
    DXMJLSH(6, "待项目经理审核"),

    /**
     * 已完成
     */
    YWC(7, "已完成"),

    /**
     * 已退回
     */
    BTH(8, "被退回"),

    /**
     * 正常(售前支撑)
     */
    ZC(9, "正常"),

    /**
     * 结束(售前支撑)
     */
    JS(10, "结束"),



// 仅用作查询，不入库
    /**
     * 工时待审核(售前工单)
     */
    GSDSH(50, "工时待审核"),
    ;


    private final Integer value;

    private final String name;

    /**
     * 通过枚举获取下一个节点枚举值
     *
     * @param statusEnum status 枚举
     * @return {@link Integer }
     */
    public static Integer getNextValByEnum(CostTaskStatusEnum statusEnum) {
        if (statusEnum == null) {
            throw new ServiceException("工单状态不能为空~");
        }
        switch (statusEnum) {
            case DEJFZRSH:
                return EJFZRYTG.getValue();
            case DYJFZRSH:
                return YJFZRYTG.getValue();
            case DXMJLSH:
                return YWC.getValue();
            default:
                return statusEnum.getValue();
        }
    }

    /**
     * 获取工单状态值
     *
     * @param taskLevel 工单级别
     * @return {@link Integer }
     */
    public static Integer getValByTaskLevel(Integer taskLevel) {
        switch (taskLevel){
            case 1:
                return DXMJLSH.getValue();
            case 2:
                return DYJFZRSH.getValue();
            case 3:
                return DEJFZRSH.getValue();
            default:
                return WWC.getValue();
        }
    }

    /**
     * 是否已提交
     *
     * @param status 地位
     * @return boolean
     */
    public static boolean isSubmitted(Integer status) {
        return !EnumUtils.valueEquals(status, CostTaskStatusEnum.DCJ)
                && !EnumUtils.valueEquals(status, CostTaskStatusEnum.WWC);
    }
}
