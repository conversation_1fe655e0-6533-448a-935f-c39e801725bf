package com.gok.pboot.pms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.Util.CollectionUtils;
import com.gok.pboot.pms.Util.DbApiUtil;
import com.gok.pboot.pms.Util.PageUtils;
import com.gok.pboot.pms.Util.RSAUtils;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.domain.ProjectData;
import com.gok.pboot.pms.entity.domain.ProjectEstimatedCost;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.enumeration.ContentTypeEnum;
import com.gok.pboot.pms.mapper.ProjectDataMapper;
import com.gok.pboot.pms.mapper.ProjectEstimatedCostMapper;
import com.gok.pboot.pms.service.ProjectDetailsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@Transactional(readOnly = true, rollbackFor = Exception.class)
@RequiredArgsConstructor
public class ProjectDetailsServiceImpl implements ProjectDetailsService {

    private final ProjectDataMapper projectDataMapper;
    private final ProjectEstimatedCostMapper projectEstimatedCostMapper;

    private final DbApiUtil dbApiUtil;

    //费用项类别filedID
    private final String fyxlbFiledID = "10959";

    //费用项filedID
    private final String fyxFiledID = "10390";

    // 公司fieldId
    private final String companyFieldId = "304390";
    

    private final DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
    private final DecimalFormat decimalFormat2 = new DecimalFormat("0.00");


    @Value("${oa.url.httpUrl}")
    private String oaUrl;

    @Value("${oa.url.applytoken}")
    private String applytoken;

    @Value("${oa.url.getRequestResources}")
    private String getRequestResources;

    @Value("${oa.resourcesAppId}")
    private String resourcesAppId;

    @Value("${oa.secret}")
    private String secret;

    @Value("${oa.spk}")
    private String spk;

    private static final String PARAM_PROJECT_ID = "projectId";
    private static final String PARAM_SEARCH_START_TIME = "searchstarttime";
    private static final String PARAM_SEARCH_END_TIME = "searchendtime";

    @Override
    public ApiResult<AdditionalIncomeVo> additionalIncome(PageRequest pageRequest, Map<String, Object> stringObjectMap) {

        // 通过DBApiUtil 工具类获取到追加预算总收入
        List<AdditionalIncomePageVo> additionalIncomePageVoList = dbApiUtil
                .additionalIncome((String) stringObjectMap.get(PARAM_PROJECT_ID),
                        (String) stringObjectMap.get(PARAM_SEARCH_START_TIME),
                        (String) stringObjectMap.get(PARAM_SEARCH_END_TIME));

        // 判空
        if (!Optional.ofNullable(additionalIncomePageVoList).isPresent() || additionalIncomePageVoList.isEmpty()) {
            return ApiResult.success(new AdditionalIncomeVo());
        }

        // 计算销售金额合计（含税）、销售金额合计（不含税）
        AtomicReference<BigDecimal> totalSalesAmount = new AtomicReference<>(BigDecimal.valueOf(0.00).setScale(2, RoundingMode.DOWN));
        AtomicReference<BigDecimal> totalSalesAmountBhs = new AtomicReference<>(BigDecimal.valueOf(0.00).setScale(2, RoundingMode.DOWN));
        additionalIncomePageVoList.forEach(r -> {
            totalSalesAmount.updateAndGet(v -> v.add(new BigDecimal(Optional.ofNullable(r.getSalesAmount()).orElse("0.00"))));
            totalSalesAmountBhs.updateAndGet(v -> v.add(new BigDecimal(Optional.ofNullable(r.getSalesAmountBhs()).orElse("0.00"))));
            r.setTaxRate(r.getTaxRate());
            r.setScale(2, RoundingMode.DOWN, decimalFormat2);
        });

        // 组装追加预算总收入
        AdditionalIncomeVo additionalIncomeVo = new AdditionalIncomeVo();
        additionalIncomeVo.setTotalSalesAmount(totalSalesAmount.get().toString());
        additionalIncomeVo.setTotalSalesAmountBhs(totalSalesAmountBhs.get().toString());
        Page<AdditionalIncomePageVo> page = PageUtils.page(additionalIncomePageVoList, pageRequest);
        additionalIncomeVo.setAdditionalIncomePageVoPage(page);
        additionalIncomeVo.setScale(2, RoundingMode.DOWN, decimalFormat2);

        return ApiResult.success(additionalIncomeVo);
    }

    @Override
    public ApiResult<AdditionalIncomeVo> existingIncome(PageRequest pageRequest, Map<String, Object> stringObjectMap) {
        List<AdditionalIncomePageVo> list;
        // 通过DBApiUtil 工具类获取到已有预算总收入
        List<AdditionalIncomePageVo> additionalIncomePageVoList = dbApiUtil
                .existingIncome((String) stringObjectMap.get(PARAM_PROJECT_ID),
                        (String) stringObjectMap.get(PARAM_SEARCH_START_TIME),
                        (String) stringObjectMap.get(PARAM_SEARCH_END_TIME));

        // 判空
        if (!Optional.ofNullable(additionalIncomePageVoList).isPresent() || additionalIncomePageVoList.isEmpty()) {
            return ApiResult.success(new AdditionalIncomeVo());
        }

        List<AdditionalIncomePageVo> BTable = additionalIncomePageVoList.stream().filter(a -> NumberUtils.INTEGER_ZERO.equals(a.getType())).collect(Collectors.toList());
        List<AdditionalIncomePageVo> grossProfit = additionalIncomePageVoList.stream().filter(a -> NumberUtils.INTEGER_ONE.equals(a.getType())).collect(Collectors.toList());

        list = CollectionUtils.isNotEmpty(BTable) ? BTable : grossProfit;
        // 判空
        if (!Optional.ofNullable(list).isPresent() || list.isEmpty()) {
            return ApiResult.success(new AdditionalIncomeVo());
        }
        // 计算销售金额合计（含税）、销售金额合计（不含税）
        AtomicReference<BigDecimal> totalSalesAmount = new AtomicReference<>(BigDecimal.valueOf(0.00).setScale(2, RoundingMode.DOWN));
        AtomicReference<BigDecimal> totalSalesAmountBhs = new AtomicReference<>(BigDecimal.valueOf(0.00).setScale(2, RoundingMode.DOWN));
        list.forEach(r -> {
            totalSalesAmount.updateAndGet(v -> v.add(new BigDecimal(Optional.ofNullable(r.getSalesAmount()).orElse("0.00"))));
            totalSalesAmountBhs.updateAndGet(v -> v.add(new BigDecimal(Optional.ofNullable(r.getSalesAmountBhs()).orElse("0.00"))));
            r.setTaxRate(r.getTaxRate());
            r.setScale(2, RoundingMode.DOWN, decimalFormat2);
        });

        // 组装追加预算总收入
        AdditionalIncomeVo additionalIncomeVo = new AdditionalIncomeVo();
        additionalIncomeVo.setTotalSalesAmount(totalSalesAmount.get().toString());
        additionalIncomeVo.setTotalSalesAmountBhs(totalSalesAmountBhs.get().toString());
        Page<AdditionalIncomePageVo> page = PageUtils.page(list, pageRequest);
        additionalIncomeVo.setAdditionalIncomePageVoPage(page);
        additionalIncomeVo.setScale(2, RoundingMode.DOWN, decimalFormat2);

        return ApiResult.success(additionalIncomeVo);
    }

    @Override
    public ApiResult<AdditionalCostsVo> additionalCosts(PageRequest pageRequest, Map<String, Object> stringObjectMap) {

        //获取费用项-字典值
        List<ProjectDictVo> fyxDictVOS = dbApiUtil.projectDict(fyxlbFiledID);
        //key:选项值 value:选项名称
        Map<Integer, String> fyxMap = fyxDictVOS.stream().collect(Collectors.toMap(ProjectDictVo::getSelectvalue, ProjectDictVo::getSelectname, (a, b) -> a));

        //获取科目名称-字典值
        List<SubjectDetailsVO> subjectDetails = dbApiUtil.subjectDetails();
        //key:选项值 value:选项名称
        Map<Integer, String> kmmcMap = subjectDetails.stream().collect(Collectors.toMap(SubjectDetailsVO::getId, SubjectDetailsVO::getKmmc, (a, b) -> a));

        // 通过DBApiUtil 工具类获取到已有预算总收入
        List<AdditionalCostsPageVo> additionalCostsPageVoList = dbApiUtil
                .additionalCosts((String) stringObjectMap.get(PARAM_PROJECT_ID),
                        (String) stringObjectMap.get(PARAM_SEARCH_START_TIME),
                        (String) stringObjectMap.get(PARAM_SEARCH_END_TIME));

        // 判空
        if (!Optional.ofNullable(additionalCostsPageVoList).isPresent() || additionalCostsPageVoList.isEmpty()) {
            return ApiResult.success(new AdditionalCostsVo());
        }

        // 计算销售金额合计（含税）、销售金额合计（不含税）
        AtomicReference<BigDecimal> totalBudgetAmount = new AtomicReference<>(BigDecimal.valueOf(0.00).setScale(2, RoundingMode.DOWN));
        AtomicReference<BigDecimal> totalBudgetAmountBhs = new AtomicReference<>(BigDecimal.valueOf(0.00).setScale(2, RoundingMode.DOWN));
        additionalCostsPageVoList.forEach(r -> {
            totalBudgetAmount.updateAndGet(v -> v.add(new BigDecimal(Optional.ofNullable(r.getBudgetAmount()).orElse("0.00"))));
            totalBudgetAmountBhs.updateAndGet(v -> v.add(new BigDecimal(Optional.ofNullable(r.getBudgetAmountBhs()).orElse("0.00"))));
            r.setTaxRate(r.getTaxRate());
            r.setScale(2, RoundingMode.DOWN, decimalFormat2);
            r.setKmmc(kmmcMap.getOrDefault(Integer.valueOf(Optional.ofNullable(r.getKmmc()).orElse("-1")), ""));
            r.setCbfyx(fyxMap.getOrDefault(Integer.valueOf(Optional.ofNullable(r.getCbfyx()).orElse("-1")), ""));
        });

        // 组装追加预算总收入
        AdditionalCostsVo additionalCostsVo = new AdditionalCostsVo();
        additionalCostsVo.setTotalBudgetAmount(totalBudgetAmount.get().toString());
        additionalCostsVo.setTotalBudgetAmountBhs(totalBudgetAmountBhs.get().toString());
        Page<AdditionalCostsPageVo> page = PageUtils.page(additionalCostsPageVoList, pageRequest);
        additionalCostsVo.setAdditionalCostsPageVoPage(page);
        additionalCostsVo.setScale(2, RoundingMode.DOWN, decimalFormat2);

        return ApiResult.success(additionalCostsVo);
    }

    @Override
    public ApiResult<AdditionalCostsVo> existingBudget(PageRequest pageRequest, Map<String, Object> stringObjectMap) {
        List<AdditionalCostsPageVo> list;
        //获取费用项-字典值
        List<ProjectDictVo> fyxDictVOS = dbApiUtil.projectDict(fyxlbFiledID);
        //key:选项值 value:选项名称
        Map<Integer, String> fyxMap = fyxDictVOS.stream().collect(Collectors.toMap(ProjectDictVo::getSelectvalue, ProjectDictVo::getSelectname, (a, b) -> a));

        //获取科目名称-字典值
        List<SubjectDetailsVO> subjectDetails = dbApiUtil.subjectDetails();
        //key:选项值 value:选项名称
        Map<Integer, String> kmmcMap = subjectDetails.stream().collect(Collectors.toMap(SubjectDetailsVO::getId, SubjectDetailsVO::getKmmc, (a, b) -> a));

        // 通过DBApiUtil 工具类获取到已有预算总收入
        List<AdditionalCostsPageVo> additionalCostsPageVoList = dbApiUtil
                .existingBudget((String) stringObjectMap.get(PARAM_PROJECT_ID),
                        (String) stringObjectMap.get(PARAM_SEARCH_START_TIME),
                        (String) stringObjectMap.get(PARAM_SEARCH_END_TIME));

        // 判空
        if (!Optional.ofNullable(additionalCostsPageVoList).isPresent() || additionalCostsPageVoList.isEmpty()) {
            return ApiResult.success(new AdditionalCostsVo());
        }

        List<AdditionalCostsPageVo> BTable = additionalCostsPageVoList.stream().filter(a -> NumberUtils.INTEGER_ZERO.equals(a.getType())).collect(Collectors.toList());
        List<AdditionalCostsPageVo> grossProfit = additionalCostsPageVoList.stream().filter(a -> NumberUtils.INTEGER_ONE.equals(a.getType())).collect(Collectors.toList());

        list = CollectionUtils.isNotEmpty(BTable) ? BTable : grossProfit;

        // 判空
        if (!Optional.ofNullable(list).isPresent() || list.isEmpty()) {
            return ApiResult.success(new AdditionalCostsVo());
        }
        // 计算销售金额合计（含税）、销售金额合计（不含税）
        AtomicReference<BigDecimal> totalBudgetAmount = new AtomicReference<>(BigDecimal.valueOf(0.00).setScale(2, RoundingMode.DOWN));
        AtomicReference<BigDecimal> totalBudgetAmountBhs = new AtomicReference<>(BigDecimal.valueOf(0.00).setScale(2, RoundingMode.DOWN));
        list.forEach(r -> {
            totalBudgetAmount.updateAndGet(v -> v.add(new BigDecimal(Optional.ofNullable(r.getBudgetAmount()).orElse("0.00"))));
            totalBudgetAmountBhs.updateAndGet(v -> v.add(new BigDecimal(Optional.ofNullable(r.getBudgetAmountBhs()).orElse("0.00"))));
            r.setTaxRate(r.getTaxRate());
            r.setScale(2, RoundingMode.DOWN, decimalFormat2);
            r.setKmmc(kmmcMap.getOrDefault(Integer.valueOf(Optional.ofNullable(r.getKmmc()).orElse("-1")), ""));
            r.setCbfyx(fyxMap.getOrDefault(Integer.valueOf(Optional.ofNullable(r.getCbfyx()).orElse("-1")), ""));
        });

        // 组装追加预算总收入
        AdditionalCostsVo additionalCostsVo = new AdditionalCostsVo();
        additionalCostsVo.setTotalBudgetAmount(totalBudgetAmount.get().toString());
        additionalCostsVo.setTotalBudgetAmountBhs(totalBudgetAmountBhs.get().toString());
        Page<AdditionalCostsPageVo> page = PageUtils.page(list, pageRequest);
        additionalCostsVo.setAdditionalCostsPageVoPage(page);
        additionalCostsVo.setScale(2, RoundingMode.DOWN, decimalFormat2);

        return ApiResult.success(additionalCostsVo);
    }

    /**
     * 项目管理-项目费用报销明细-dbApi调用
     *
     * @param stringObjectMap
     * @return
     */
    @Override
    public ApiResult<Page<ReimburseDetailsVO>> reimburseDetails(PageRequest pageRequest, Map<String, Object> stringObjectMap) {
        //获取费用项类别-字典值
        List<ProjectDictVo> fyxlbDictVOS = dbApiUtil.projectDict(fyxlbFiledID);
        //key:选项值 value:选项名称
        Map<Integer, String> fyxlbMap = fyxlbDictVOS.stream().collect(Collectors.toMap(ProjectDictVo::getSelectvalue, ProjectDictVo::getSelectname, (a, b) -> a));

        //获取费用项-字典值
        List<ProjectDictVo> fyxDictVOS = dbApiUtil.projectDict(fyxFiledID);
        //key:选项值 value:选项名称
        Map<Integer, String> fyxMap = fyxDictVOS.stream().collect(Collectors.toMap(ProjectDictVo::getSelectvalue, ProjectDictVo::getSelectname, (a, b) -> a));

        //获取科目名称-字典值
        List<SubjectDetailsVO> subjectDetails = dbApiUtil.subjectDetails();
        //key:选项值 value:选项名称
        Map<Integer, String> kmmcMap = subjectDetails.stream().collect(Collectors.toMap(SubjectDetailsVO::getId, SubjectDetailsVO::getKmmc, (a, b) -> a));

        //获取项目付款明细
        List<ReimburseDetailsVO> reimburseDetailsVOList = dbApiUtil.reimburseDetails((String) stringObjectMap.getOrDefault(PARAM_PROJECT_ID, "")
                , (String) stringObjectMap.get(PARAM_SEARCH_START_TIME),
                (String) stringObjectMap.get(PARAM_SEARCH_END_TIME), (String) stringObjectMap.get("fyxlb"));

        // 判空
        if (!Optional.ofNullable(reimburseDetailsVOList).isPresent() || reimburseDetailsVOList.isEmpty()) {
            return ApiResult.success(new Page<>());
        }

        //数据组装
        reimburseDetailsVOList.forEach(e -> {
            e.setFyxlbValue(fyxlbMap.getOrDefault(e.getFyxlb(), ""));
            e.setFyxValue(fyxMap.getOrDefault(e.getFyx(), ""));
            e.setKmmcValue(kmmcMap.getOrDefault(e.getKmmc(), ""));
            e.setScale(2, RoundingMode.DOWN, decimalFormat);
        });
        Page<ReimburseDetailsVO> page = PageUtils.page(reimburseDetailsVOList, pageRequest);
        return ApiResult.success(page);
    }

    /**
     * 项目管理-销售合同-dbApi调用
     *
     * @param pageRequest     页面请求
     * @param stringObjectMap 字符串对象映射
     * @return {@link ApiResult}<{@link Page}<{@link ProjectContractPageVo}>>
     */
    @Override
    public ApiResult<ProjectContractVo> projectContract(PageRequest pageRequest, Map<String, Object> stringObjectMap) {
        // 获取公司名称-字典值
        List<ProjectDictVo> companyDictVOS = dbApiUtil.projectDict(companyFieldId);
        //key:选项值 value:选项名称
        Map<Integer, String> companyMap = companyDictVOS.stream().collect(Collectors.toMap(ProjectDictVo::getSelectvalue, ProjectDictVo::getSelectname, (a, b) -> a));
        // 通过DBApiUtil 工具类获取到项目合同
        List<ProjectContractPageVo> projectContractPageVoList = dbApiUtil
                .projectContract((String) stringObjectMap.getOrDefault(PARAM_PROJECT_ID, ""),
                        (String) stringObjectMap.get("contractType"));

        // 判空
        if (!Optional.ofNullable(projectContractPageVoList).isPresent() || projectContractPageVoList.isEmpty()) {
            return ApiResult.success(new ProjectContractVo());
        }

        if (CollectionUtils.isNotEmpty(projectContractPageVoList)) {
            projectContractPageVoList = projectContractPageVoList.stream().map(e -> {
                //申请人ID
                String creatorid = e.getCreatorid();
                //流程ID
                String requestid = e.getRequestid();
                //获取oa的token
                String getToken = HttpRequest.post(applytoken)
                        .header("appid", resourcesAppId)
                        .header("secret", secret)
                        .execute().body();
                String token = JSONUtil.parse(getToken).getByPath("token").toString();

                //申请人ID加密
                String encrypt = RSAUtils.encrypt(creatorid, spk);

                String body = HttpRequest.get(getRequestResources + requestid)
                        .header("appid", resourcesAppId)
                        .header("token", token)
                        .header("userid", encrypt).execute().body();
                JSONArray jsonArray = JSONUtil.parseArray(JSONUtil.parse(body).getByPath("data"));
                //封装合同文件
                List<ContractFileVo> contractFileVos = JSONUtil.toList(jsonArray, ContractFileVo.class);
                if (CollectionUtils.isNotEmpty(contractFileVos)) {
                    //过滤掉DownloadUrl为null的数据
                    List<ContractFileVo> collect = contractFileVos.stream().filter(n -> Optional.ofNullable(n.getDownloadUrl()).isPresent())
                            .map(file -> {
                                file.setDownloadUrl(oaUrl + file.getDownloadUrl());
                                return file;
                            }).collect(Collectors.toList());
                    e.setContractFileVoList(collect);
                }
                return e;
            }).collect(Collectors.toList());
        }
        Page<ProjectContractPageVo> page = null != pageRequest
                ? PageUtils.page(projectContractPageVoList, pageRequest)
                : new Page<>();

        // 组装项目合同，计算项目金额（含税）、项目合同金额（不含税）
        ProjectContractVo projectContractVo = new ProjectContractVo();
        AtomicReference<BigDecimal> contractPriceSum = new AtomicReference<>(BigDecimal.valueOf(0.00).setScale(2, RoundingMode.DOWN));
        AtomicReference<BigDecimal> contractPriceBhsSum = new AtomicReference<>(BigDecimal.valueOf(0.00).setScale(2, RoundingMode.DOWN));
        projectContractPageVoList.forEach(r -> {
            contractPriceSum.updateAndGet(v -> v.add(new BigDecimal(Optional.ofNullable(r.getContractPrice()).orElse("0.00"))));
            contractPriceBhsSum.updateAndGet(v -> v.add(new BigDecimal(Optional.ofNullable(r.getContractPriceBhs()).orElse("0.00"))));
            r.setScale(2, RoundingMode.DOWN, decimalFormat);
            r.setAffiliatedCompany(companyMap.getOrDefault(Integer.valueOf(r.getAffiliatedCompany()), ""));
        });
        projectContractVo.setContractPriceSum(contractPriceSum.get().toString());
        projectContractVo.setContractPriceBhsSum(contractPriceBhsSum.get().toString());
        projectContractVo.setProjectContractPageVoPage(page);
        projectContractVo.setScale(2, RoundingMode.DOWN, decimalFormat);

        return ApiResult.success(projectContractVo);
    }

    @Override
    public ApiResult<FinancialDataVo> financialData(Map<String, Object> stringObjectMap) {
        // 通过DBUtil 工具类获取到财务数据
        String projectIdStr = stringObjectMap.getOrDefault(PARAM_PROJECT_ID, "").toString();
        FinancialDataVo financialDataVo = dbApiUtil
                .financialData(projectIdStr,
                        (String) stringObjectMap.get(PARAM_SEARCH_START_TIME),
                        (String) stringObjectMap.get(PARAM_SEARCH_END_TIME));

        // 判空
        if (!Optional.ofNullable(financialDataVo).isPresent()) {
            return ApiResult.success(new FinancialDataVo());
        }

        // 设置商机阶段人工投入、售前费用投入字段
        Object projectIdObj = stringObjectMap.getOrDefault(PARAM_PROJECT_ID, "");
        if (Optional.ofNullable(projectIdObj).isPresent()) {
            ProjectData projectData = projectDataMapper.selectById(Long.valueOf(String.valueOf(projectIdObj)));
            if (Optional.ofNullable(projectData).isPresent()) {
                financialDataVo.setPreSalesLaborInput(projectData.getPreSalesLaborInput() == null ? "0.00" : projectData.getPreSalesLaborInput().toString());
                financialDataVo.setSqfytr(projectData.getSqfytr() == null ? "0.00" : projectData.getSqfytr().toString());
            } else {
                financialDataVo.setPreSalesLaborInput("0.00");
                financialDataVo.setSqfytr("0.00");
            }
        }

        // 获取内部项目数据
        ProjectData projectData = projectDataMapper.selectById(Long.valueOf(projectIdStr));
        if (null != projectData) {
            financialDataVo.setInternalTotalBudgetCostIncludeTax(StrUtil.toStringOrNull(projectData.getInternalTotalBudgetCostIncludeTax()));
            financialDataVo.setInternalEstimatedTotalManDays(projectData.getInternalEstimatedTotalManDays());
            financialDataVo.setInternalLaborBudget(StrUtil.toStringOrNull(projectData.getInternalLaborBudget()));
            financialDataVo.setInternalOutsourcingBudgetTax(StrUtil.toStringOrNull(projectData.getInternalOutsourcingBudgetTax()));
            financialDataVo.setInternalBudgetChangesNum(projectData.getInternalBudgetChangesNum());
        }

        financialDataVo.setScale(2, RoundingMode.DOWN, decimalFormat);

        return ApiResult.success(financialDataVo);
    }

    @Override
    public ApiResult<Page<ManDayDetailsPageVo>> manDayDetails(PageRequest pageRequest, Map<String, Object> stringObjectMap) {
        // 通过DBUtil 工具类获取到人天明细
        List<ManDayDetailsPageVo> manDayDetailsPageVoList = dbApiUtil
                .manDayDetails((Long) stringObjectMap.getOrDefault(PARAM_PROJECT_ID, ""),
                        (String) stringObjectMap.get("workType"), (String) stringObjectMap.get(PARAM_SEARCH_START_TIME),
                        (String) stringObjectMap.get(PARAM_SEARCH_END_TIME), (String) stringObjectMap.get("userStatus"));
        // 判空
        if (!Optional.ofNullable(manDayDetailsPageVoList).isPresent() || manDayDetailsPageVoList.isEmpty()) {
            return ApiResult.success(new Page<>());
        }

        Page<ManDayDetailsPageVo> page = PageUtils.page(manDayDetailsPageVoList, pageRequest);
        return ApiResult.success(page);
    }

    @Override
    public ApiResult<Page<TotalCostDetailsPageVo>> totalCostDetails(PageRequest pageRequest, Map<String, Object> stringObjectMap) {
        // 通过DBUtil 工具类获取到直接人工（项目分摊）明细
        List<TotalCostDetailsPageVo> totalCostDetailsPageVoList = dbApiUtil
                .totalCostDetails((String) stringObjectMap.getOrDefault("projectName", ""),
                        (String) stringObjectMap.get(PARAM_SEARCH_START_TIME), (String) stringObjectMap.get(PARAM_SEARCH_END_TIME));

        // 判空
        if (!Optional.ofNullable(totalCostDetailsPageVoList).isPresent() || totalCostDetailsPageVoList.isEmpty()) {
            return ApiResult.success(new Page<>());
        }

        Page<TotalCostDetailsPageVo> page = PageUtils.page(totalCostDetailsPageVoList, pageRequest);
        return ApiResult.success(page);
    }

    @Override
    public ApiResult<Page<ProcureDetailsPageVo>> procureDetails(PageRequest pageRequest, Map<String, Object> stringObjectMap) {
        //获取费用项类别-字典值
        List<ProjectDictVo> fyxlbDictVOS = dbApiUtil.projectDict(fyxlbFiledID);
        //key:选项值 value:选项名称
        Map<Integer, String> fyxlbMap = fyxlbDictVOS.stream().collect(Collectors.toMap(ProjectDictVo::getSelectvalue, ProjectDictVo::getSelectname, (a, b) -> a));

        //获取科目名称-字典值
        List<SubjectDetailsVO> subjectDetails = dbApiUtil.subjectDetails();
        //key:选项值 value:选项名称
        Map<Integer, String> kmmcMap = subjectDetails.stream().collect(Collectors.toMap(SubjectDetailsVO::getId, SubjectDetailsVO::getKmmc, (a, b) -> a));

        // 通过DBUtil 工具类获取到项目采购付款明细
        List<ProcureDetailsPageVo> procureDetailsPageVoList = dbApiUtil
                .procureDetails((String) stringObjectMap.getOrDefault(PARAM_PROJECT_ID, ""),
                        (String) stringObjectMap.get(PARAM_SEARCH_START_TIME),
                        (String) stringObjectMap.get(PARAM_SEARCH_END_TIME));

        // 判空
        if (!Optional.ofNullable(procureDetailsPageVoList).isPresent() || procureDetailsPageVoList.isEmpty()) {
            return ApiResult.success(new Page<>());
        }

        procureDetailsPageVoList.forEach(p -> {
            p.setFyxlbValue(fyxlbMap.getOrDefault(p.getFyxlb(), ""));
            p.setKmmc(kmmcMap.getOrDefault(Integer.valueOf(p.getKmmc()), ""));
            p.setScale(2, RoundingMode.DOWN, decimalFormat);
        });

        Page<ProcureDetailsPageVo> page = PageUtils.page(procureDetailsPageVoList, pageRequest);
        return ApiResult.success(page);
    }

    @Override
    public ApiResult<Page<ContractDetailsPageVo>> contractDetails(PageRequest pageRequest, Map<String, Object> stringObjectMap) {
        // 通过DBUtil 工具类获取到合同明细
        List<ContractDetailsPageVo> contractDetailsPageVoList = dbApiUtil
                .contractDetails((String) stringObjectMap.getOrDefault(PARAM_PROJECT_ID, ""),
                        (String) stringObjectMap.get("contractType"), (String) stringObjectMap.get(PARAM_SEARCH_START_TIME),
                        (String) stringObjectMap.get(PARAM_SEARCH_END_TIME));

        // 判空
        if (!Optional.ofNullable(contractDetailsPageVoList).isPresent() || contractDetailsPageVoList.isEmpty()) {
            return ApiResult.success(new Page<>());
        }

        contractDetailsPageVoList.forEach(r -> r.setScale(2, RoundingMode.DOWN, decimalFormat));

        Page<ContractDetailsPageVo> page = PageUtils.page(contractDetailsPageVoList, pageRequest);
        return ApiResult.success(page);
    }

    @Override
    public void install(String fileName, String fileUrl, HttpServletResponse response) {
        getInputStreamFromUrl(fileName, fileUrl, response);
    }

    @Override
    public ApiResult<AdditionalCostsVo> internalBudget(PageRequest pageRequest, Map<String, Object> filter) {
        Page<ProjectEstimatedCost> pageResult = projectEstimatedCostMapper.findPage(
                new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize()),
                filter);
        String projectId = filter.getOrDefault("projectId", "").toString();

        AdditionalCostsVo result = new AdditionalCostsVo();
        List<ProjectEstimatedCost> records = pageResult.getRecords();
        if (CollUtil.isEmpty(records)) {
            return ApiResult.success(result);
        }

        List<AdditionalCostsPageVo> additionalCostsPageVoList = new ArrayList<>(records.size());
        records.forEach(r -> {
            AdditionalCostsPageVo additionalCostsPageVo = new AdditionalCostsPageVo();
            additionalCostsPageVo.setProjectId(projectId);
            additionalCostsPageVo.setKmdm(r.getSubjectCode());
            additionalCostsPageVo.setKmmc(r.getSubjectName());
            additionalCostsPageVo.setCbfyx(r.getCostExpenseItemsValue());
            additionalCostsPageVo.setBudgetAmount(null != r.getBudgetAmount() ? r.getBudgetAmount().toString() : "0.00");
            additionalCostsPageVo.setTaxRate(r.getTaxRateValue());
            additionalCostsPageVo.setBudgetAmountBhs(null != r.getBudgetAmountNoTax() ? r.getBudgetAmountNoTax().toString() : "0.00");
            additionalCostsPageVo.setCbfysm(r.getCostExplanation());
            additionalCostsPageVo.setGenerationDate(r.getCreateDate());
            additionalCostsPageVoList.add(additionalCostsPageVo);
        });

        result.setAdditionalCostsPageVoPage(PageUtils.page(additionalCostsPageVoList, pageRequest));
        return ApiResult.success(result);
    }

    public static void getInputStreamFromUrl(String fileName, String fileUrl, HttpServletResponse response) {
        String code = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());

        HttpUtil.createGet(fileUrl);
        try {

            InputStream fis = getInputStreamByUrl(fileUrl);

            response.setCharacterEncoding("UTF-8");
            response.setContentType(ContentTypeEnum.getTypeByCode(code));
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

            ServletOutputStream os = response.getOutputStream();
            IOUtils.copy(fis, os);

        } catch (Exception e) {
            e.printStackTrace();
        } finally {

        }

    }


    public static InputStream getInputStreamByUrl(String strUrl) {
        HttpURLConnection conn = null;
        try (final ByteArrayOutputStream output = new ByteArrayOutputStream()) {
            URL url = new URL(strUrl);
            conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(20 * 1000);
//            final ByteArrayOutputStream output = new ByteArrayOutputStream();
            IOUtils.copy(conn.getInputStream(), output);
            return new ByteArrayInputStream(output.toByteArray());
        } catch (Exception e) {

        } finally {
            try {
                if (conn != null) {
                    conn.disconnect();
                }
            } catch (Exception e) {
            }
        }
        return null;
    }
}
