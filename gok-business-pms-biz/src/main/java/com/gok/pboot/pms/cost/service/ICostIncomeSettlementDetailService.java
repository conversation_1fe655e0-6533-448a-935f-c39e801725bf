package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.cost.entity.domain.CostIncomeSettlementDetail;
import com.gok.pboot.pms.cost.entity.dto.CostIncomeSettlementDetailsEditDTO;
import com.gok.pboot.pms.cost.entity.dto.CostIncomeSettlementDetailsImportDTO;
import com.gok.pboot.pms.cost.entity.dto.CostIncomeSettlementListDTO;
import com.gok.pboot.pms.cost.entity.vo.CostIncomeSettlementDetailVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 收入结算明细服务接口
 * <AUTHOR>
 */
public interface ICostIncomeSettlementDetailService extends IService<CostIncomeSettlementDetail> {

    /**
     * 查询收入结算明细列表
     * @param dto
     * @return
     */
    List<CostIncomeSettlementDetailVO> findDetailList(CostIncomeSettlementListDTO dto);

    /**
     * 新增或编辑收入结算明细
     * @param dtoList
     * @return
     */
    List<CostIncomeSettlementDetail> addOrUpdate(List<CostIncomeSettlementDetailsEditDTO> dtoList);

    /**
     * 导出
     * @param response
     * @param dto
     */
    void export(HttpServletResponse response, CostIncomeSettlementListDTO dto);

    /**
     * 导入
     * @param projectId
     * @param importDTOList
     * @return
     */
    ApiResult<String> importExcel(Long projectId, List<CostIncomeSettlementDetailsImportDTO> importDTOList);

    /**
     * 删除收入结算明细
     * @param ids
     * @return
     */
    ApiResult<String> delete(List<Long> ids);
}
