package com.gok.pboot.pms.common;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * Redis初始化
 *
 * <AUTHOR>
 * @date 2023/08/30
 */
@Component
@Slf4j
public class RedisCommandLineRunner implements CommandLineRunner {

    final String DBAPI_TOKEN = "dbapi:analysis:token";

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 删除失效token
     *
     * @param args
     */
    @Override
    public void run(String... args) {
        // 初始化时，删除dbapi的token，防止失效token影响后续业务
        if (redisTemplate.hasKey(DBAPI_TOKEN)) {
            redisTemplate.delete(DBAPI_TOKEN);
        }
    }

}
