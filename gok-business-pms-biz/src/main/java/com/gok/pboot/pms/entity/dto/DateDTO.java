package com.gok.pboot.pms.entity.dto;

import lombok.Getter;
import lombok.ToString;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description 日期传输对象 （yyyy-MM-dd 格式日期 + 星期 + 工作日/休息日/节假日标志）
 * @since 2024/7/17
 */

@ToString
@Getter
public class DateDTO {

    /**
     * 日期时间
     */
    private LocalDate localDate;

    /**
     * 周一到周日
     */
    private Integer dayOfWeek;

    /**
     * 0-休息日 1-节假日 2-工作日
     */
    private Integer holidayType;

    private void setLocalDate(LocalDate localDate) {
        this.localDate = localDate;
    }

    private void setDayOfWeek(Integer dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }

    private void setHolidayType(Integer holidayType) {
        this.holidayType = holidayType;
    }

    private DateDTO() {
    }

    /**
     * 日期建造器
     */
    public static class DateBuilder {
        private LocalDate localDate;
        private Integer dayOfWeek;
        private Integer holidayType;

        /**
         * 设置日期（包括星期）
         *
         * @param localDate 日期参数
         * @return builder
         */
        public DateBuilder withLocalDate(LocalDate localDate) {
            this.localDate = localDate;
            // 自动设置dayOfWeek
            this.dayOfWeek = localDate.getDayOfWeek().getValue();
            return this;
        }

        /**
         * 设置日期类型
         *
         * @param holidayType 0-休息日 1-节假日 2-工作日
         * @return builder
         */
        public DateBuilder withHolidayType(Integer holidayType) {
            this.holidayType = holidayType;
            return this;
        }

        /**
         * 构造DateDTO实例
         *
         * @return DateDTO对象实例
         */
        public DateDTO build() {
            DateDTO dateDTO = new DateDTO();
            dateDTO.setLocalDate(localDate);
            dateDTO.setDayOfWeek(dayOfWeek);
            dateDTO.setHolidayType(holidayType);
            return dateDTO;
        }
    }

    /**
     * 创建日期建造起
     *
     * @return dto builder
     */
    public static DateBuilder builder() {
        return new DateBuilder();
    }

    public static void main(String[] args) {
        DateDTO dateDTO = DateDTO.builder()
                .withLocalDate(LocalDate.of(2024, 5, 1))
                .withHolidayType(1).build();
        System.out.println(dateDTO);
    }
}
