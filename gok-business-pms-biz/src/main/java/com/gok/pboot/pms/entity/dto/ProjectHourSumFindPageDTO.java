package com.gok.pboot.pms.entity.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * PMS项目工时汇总
 *
 * @Auther chenhc
 * @Date 2022-08-24 11:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ProjectHourSumFindPageDTO {

    /**
     * 所属项目ID
     */
    private Long projectId;

    /**
     * 日期开始时间
     */
    @NotNull(message = "日期开始时间不能为空")
    private LocalDate startTime;
    /**
     * 日期结束时间
     */
    @NotNull(message = "日期结束时间不能为空")
    private LocalDate endTime;

    /**
     * 项目状态
     */
    private Integer projectStatus;

    /**
     * 内外部项目(1:内部项目，2：外部项目.-1:全部)
     */
    private Integer isNotInternalProject;

    /**
     * 工时统计类型 （1：按天统计，2：按小时统计）
     */
    private Integer workloadType;

    /**
     * 人员状态
     */
    private Integer personnelStatus;

    /**
     * id集合
     */
    private List<Long> projectIds;

    /**
     * 内部项目类型
     */
    private List<Integer> projectTypeList;

    /**
     * 项目状态
     */
    private List<Integer> projectStatusList;

}
