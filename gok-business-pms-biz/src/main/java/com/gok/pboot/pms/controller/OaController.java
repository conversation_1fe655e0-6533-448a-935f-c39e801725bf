package com.gok.pboot.pms.controller;


import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.BaseController;
import com.gok.pboot.pms.entity.vo.OaFileDownloadVo;
import com.gok.pboot.pms.enumeration.OaFileDownLoadTypeEnum;
import com.gok.pboot.pms.service.IOaService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;


/**
 * <AUTHOR>
 * @menu OA
 * @since 2022-08-19
 */
@Slf4j
@RestController
@RequestMapping("/oa")
@AllArgsConstructor
public class OaController extends BaseController {

   private final IOaService oaService;

    /**
     * Oa附件下载
     *
     * @param id       id
     * @param fileType 文件类型
     * @param fileId   文件Id
     * @return {@link ApiResult }<{@link OaFileDownloadVo }>
     */
    @GetMapping("/fileDownload")
    public ApiResult<OaFileDownloadVo> oaFileDownload(@RequestParam("id") Long id,
                                                      @RequestParam("fileType") OaFileDownLoadTypeEnum fileType,
                                                      @RequestParam("fileId") String fileId) {
        return success(oaService.oaFileDownload(id,fileType,fileId));
    }

    /**
     * 根据OA链接下载文件
     *
     * @param response  response
     * @param fileUrl   oa文件地址
     * @param fileName  OA文件名
     * @param ifPreview 是否预览
     */
    @Inner(value = false)
    @GetMapping("/getFileByUrl")
    public void getFileByUrl(HttpServletResponse response,@RequestParam("fileUrl") String fileUrl,
                             @RequestParam("fileName")String fileName,
                             @RequestParam(name = "ifPreview",defaultValue = "false") boolean ifPreview)  {
        oaService.getFileByUrl(response,fileUrl,fileName,ifPreview);
    }

    /**
     * 获取 OA 工作流状态
     *
     * @param requestId 请求 ID
     * @return {@link ApiResult }<{@link Integer }>
     */
    @GetMapping("/getOaWorkFlowStatus/{requestId}")
    public ApiResult<Integer> getOaWorkFlowStatus(@PathVariable("requestId") Long requestId) {
        return success(oaService.getOaWorkFlowStatus(requestId));
    }

    /**
     * 根据项目id查询 OA合同台账是否存在归档的合同
     *
     * @param projectId 项目 ID
     * @return {@link ApiResult }<{@link Boolean }>
     */
    @GetMapping("/existContractByOaHttz/{projectId}")
    public ApiResult<Boolean> existContractByOaHttz(@PathVariable("projectId") Long projectId) {
        return success(oaService.existContractByOaHttz(projectId));
    }



}
