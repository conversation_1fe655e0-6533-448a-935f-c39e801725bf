package com.gok.pboot.pms.cost.entity.vo;

import lombok.Data;

/**
 * 成本管理配置项版本VO类
 *
 * <AUTHOR>
 * @create 2025/01/09
 **/
@Data
public class CostManageConfigVersionVO {

    /**
     * 成本管理配置项版本类型
     * {@link com.gok.pboot.pms.cost.enums.CostConfigVersionTypeEnum}
     */
    private Integer costConfigVersionType;

    /**
     * 成本管理配置项版本类型文本
     */
    private String costConfigVersionTypeTxt;

    /**
     * 当前版本ID
     */
    private Long currentVersionId;

    /**
     * 当前版本名称
     */
    private String currentVersionName;

    /**
     * 最新版本ID
     */
    private Long latestVersionId;

    /**
     * 最新版本名称
     */
    private String latestVersionName;

}
