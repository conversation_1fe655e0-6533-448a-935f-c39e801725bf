package com.gok.pboot.pms.cost.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.dto.CostConfigLevelPriceDTO;
import com.gok.pboot.pms.cost.entity.vo.CostConfigLevelPriceVO;
import com.gok.pboot.pms.cost.service.ICostConfigLevelPriceService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 人员级别单价配置 控制器
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Validated
@RestController
@AllArgsConstructor
@RequestMapping("/costConfigLevelPrice")
public class CostConfigLevelPriceController {

    private final ICostConfigLevelPriceService service;

    /**
     * 获取人员级别单价配置列表
     *
     * @return {@link ApiResult }<{@link List }<{@link CostConfigLevelPriceVO }>>
     */
    @GetMapping("/findList")
    public ApiResult<List<CostConfigLevelPriceVO>> getCostConfigLevelPriceList() {
        return ApiResult.success(service.getCostConfigLevelPriceList(),  "获取成功");
    }

    /**
     * 编辑人员级别单价配置列表
     *
     * @param dtoList DTO 列表
     * @return {@link ApiResult }<{@link String }>
     */
    @PostMapping("/edit")
    public ApiResult<String> editCostConfigLevelPriceList(@RequestBody @Valid List<CostConfigLevelPriceDTO> dtoList) {
        service.editCostConfigLevelPriceList(dtoList);
        return ApiResult.success("编辑成功");
    }

    /**
     * 根据版本ID获取人员级别单价配置列表
     *
     * @param versionId   版本 ID
     * @param pageRequest 页面请求
     * @return {@link ApiResult }<{@link Page }<{@link CostConfigLevelPriceVO }>>
     */
    @PostMapping("/findPage/{versionId}")
    public ApiResult<Page<CostConfigLevelPriceVO>> getLevelPriceListByVersionIdPage(@PathVariable Long versionId, @RequestBody PageRequest pageRequest) {
        return ApiResult.success(service.getLevelPriceListByVersionIdPage(versionId, pageRequest), "获取成功");
    }
}
