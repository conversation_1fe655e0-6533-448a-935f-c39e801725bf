package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.message.dto.BcpMessageBatchDTO;
import com.gok.bcp.message.dto.BcpMessageTargetBatchDTO;
import com.gok.bcp.message.dto.BcpMessageTargetDTO;
import com.gok.bcp.message.entity.enums.ChannelEnum;
import com.gok.bcp.message.entity.enums.MsgTypeEnum;
import com.gok.bcp.message.entity.enums.SourceEnum;
import com.gok.bcp.message.entity.enums.TargetTypeEnum;
import com.gok.bcp.message.entity.model.BcpMessageContentModel;
import com.gok.bcp.message.entity.model.WeComBatchModel;
import com.gok.bcp.message.entity.model.WeComModel;
import com.gok.bcp.message.feign.RemoteSendMsgService;
import com.gok.components.common.constant.SecurityConstants;
import com.gok.components.common.user.PigxUser;
import com.gok.pboot.common.core.util.DateUtils;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.common.base.BaseConstants;
import com.gok.pboot.pms.cost.config.MergeCellStrategyHandler;
import com.gok.pboot.pms.cost.entity.domain.CostIncomeCalculation;
import com.gok.pboot.pms.cost.entity.domain.CostIncomeCalculationDetail;
import com.gok.pboot.pms.cost.entity.dto.CostIncomeCalculationDTO;
import com.gok.pboot.pms.cost.entity.dto.CostIncomeSettlementListDTO;
import com.gok.pboot.pms.cost.entity.vo.*;
import com.gok.pboot.pms.cost.enums.ConfirmStatusEnum;
import com.gok.pboot.pms.cost.enums.SettlementStatusEnum;
import com.gok.pboot.pms.cost.mapper.CostIncomeCalculationDetailMapper;
import com.gok.pboot.pms.cost.mapper.CostIncomeCalculationMapper;
import com.gok.pboot.pms.cost.mapper.CostIncomeSettlementDetailMapper;
import com.gok.pboot.pms.cost.service.ICostIncomeCalculationService;
import com.gok.pboot.pms.entity.vo.AllocationFindPageVO;
import com.google.common.base.Charsets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.gok.pboot.pms.cost.controller.CostIncomeCalculationController.DETAIL_FLAG;

/**
 * <AUTHOR>
 * @create 2025/02/26
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class CostIncomeCalculationServiceImpl
        extends ServiceImpl<CostIncomeCalculationMapper, CostIncomeCalculation> implements ICostIncomeCalculationService {

    private final CostIncomeCalculationDetailMapper detailMapper;
    private final CostIncomeSettlementDetailMapper settlementDetailMapper;

    private final RemoteSendMsgService remoteSendMsgService;
    @Lazy
    @Autowired
    private CostIncomeCalculationDetailServiceImpl detailsService;

    private static final String CONTENT_TYPE_SHEET = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    private static final String PARAM_CONTENT_DISPOSITION = "Content-disposition";
    private static final String CONTENT_DISPOSITION = "attachment;filename*=utf-8''";
    private static final String XLSX_SUFFIX = ".xlsx";

    /**
     * 应用ID
     */
    @Value("${pushMessage.clientId}")
    private Long clientId;
    @Value("${pushMessage.redirectPrefixUrl}")
    private String redirectPrefix;
    @Value("${pushMessage.costIncomeCalculationUrlRedirect}")
    private String costIncomeCalculationUrlRedirect;

    @Override
    public List<CostIncomeCalculationVO> findList(Long projectId, CostIncomeCalculationDTO request) {
        request.setProjectIds(Arrays.asList(projectId));

        // 获取对应测算明细数据
        CostIncomeCalculationDTO detailQuery = CostIncomeCalculationDTO.builder()
                .projectIds(Arrays.asList(projectId))
                .query(request.getQuery())
                .calculationIds(request.getIds())
                .build();
        List<Long> calculationIds =
                Optional.ofNullable(detailsService.findDetailList(projectId, detailQuery)).orElse(new ArrayList<>(0))
                        .stream()
                        .map(CostIncomeCalculationDetailVO::getCalculationId)
                        .collect(Collectors.toList());
        if (CollUtil.isEmpty(calculationIds)) {
            return ListUtil.empty();
        }
        request.setIds(calculationIds);

        // 根据测算明细获取对应汇总数据
        List<CostIncomeCalculation> entities = baseMapper.findList(request);
        if (CollUtil.isEmpty(entities)) {
            return ListUtil.empty();
        }

        List<Long> costIncomeCalculationIds = entities.stream().map(CostIncomeCalculation::getId).collect(Collectors.toList());
        Map<Long, List<CostIncomeSettlementDetailVO>> settlementDetailMap =
                Optional.ofNullable(settlementDetailMapper.selListForCalculation(CostIncomeSettlementListDTO.builder().costIncomeCalculationIds(costIncomeCalculationIds).build()))
                        .orElse(new ArrayList<>(0)).stream()
                        .collect(Collectors.groupingBy(CostIncomeSettlementDetailVO::getCostIncomeCalculationId));

        return entities.stream().map(e -> CostIncomeCalculationVO.of(e, settlementDetailMap)).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public List<CostIncomeCalculation> batchSaveOrUpdate(List<CostIncomeCalculationDetail> detailList,
                                                         List<AllocationFindPageVO> allocationList,
                                                         List<CostExpensesShareDetailsVO> expensesShareDetailsList) {
        if (CollUtil.isEmpty(detailList)) {
            return ListUtil.empty();
        }

        // key-项目ID value-明细集合
        Map<Long, List<CostIncomeCalculationDetail>> projectDetailMap =
                detailList.stream().collect(Collectors.groupingBy(CostIncomeCalculationDetail::getProjectId));
        // 项目分摊工时集合 key-项目ID value-工时明细集合
        Map<Long, List<AllocationFindPageVO>> projectShareHoursMap =
                Optional.ofNullable(allocationList).orElse(ListUtil.empty()).stream()
                        .collect(Collectors.groupingBy(AllocationFindPageVO::getId));
        Map<Long, List<CostExpensesShareDetailsVO>> expensesShareMap =
                Optional.ofNullable(expensesShareDetailsList).orElse(ListUtil.empty()).stream()
                        .collect(Collectors.groupingBy(CostExpensesShareDetailsVO::getProjectId));

        // 获取已存在的汇总数据
        List<CostIncomeCalculation> existCalculationList =
                baseMapper.findList(CostIncomeCalculationDTO.builder().projectIds(detailList.stream().map(e -> e.getProjectId()).collect(Collectors.toList())).build());
        Map<String, CostIncomeCalculation> existCalculationMap = Optional.ofNullable(existCalculationList).orElse(ListUtil.empty()).stream()
                .collect(Collectors.toMap(vo -> StrUtil.format("{}-{}", vo.getProjectId(), vo.getBelongMonth()), Function.identity()));

        List<CostIncomeCalculation> addList = new ArrayList<>();
        List<CostIncomeCalculation> updateList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD);
        projectDetailMap.forEach((projectId, details) -> {
            // key-归属月份 value-明细集合
            Map<String, List<CostIncomeCalculationDetail>> belongMonthDetailMap = details.stream()
                    .collect(Collectors.groupingBy(e -> e.getBelongMonth().toString()));
            Map<String, List<AllocationFindPageVO>> belongMonthProjectShareHoursMap =
                    projectShareHoursMap.getOrDefault(projectId, ListUtil.empty()).stream()
                            .collect(Collectors.groupingBy(AllocationFindPageVO::getDate));
            Map<String, List<CostExpensesShareDetailsVO>> belongMonthExpensesShareMap =
                    expensesShareMap.getOrDefault(projectId, ListUtil.empty()).stream()
                            .collect(Collectors.groupingBy(e -> StrUtil.format("{}-01", e.getBelongingMonth())));
            belongMonthDetailMap.forEach((belongMonth, detail) -> {
                BigDecimal projectShareHours = belongMonthProjectShareHoursMap.getOrDefault(belongMonth, ListUtil.empty()).stream()
                        .map(e -> e.getProjectShareHours())
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal settlementUnitPrice = detail.stream().map(CostIncomeCalculationDetail::getSettlementUnitPrice)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal estimatedInclusiveAmountTax = detail.stream().map(CostIncomeCalculationDetail::getEstimatedInclusiveAmountTax)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal customerBearingAmount = belongMonthExpensesShareMap.getOrDefault(belongMonth, ListUtil.empty()).stream()
                        .map(CostExpensesShareDetailsVO::getReimburseMoney)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                CostIncomeCalculation costIncomeCalculation = existCalculationMap.get(StrUtil.format("{}-{}", projectId, belongMonth));
                if (null == costIncomeCalculation) {
                    costIncomeCalculation = CostIncomeCalculation.builder()
                            .projectId(projectId)
                            .belongMonth(LocalDate.parse(belongMonth, formatter))
                            .settlementHours(projectShareHours)
                            .settlementUnitPrice(settlementUnitPrice)
                            .customerBearingAmount(customerBearingAmount)
                            .estimatedInclusiveAmountTax(estimatedInclusiveAmountTax)
                            .confirmStatus(ConfirmStatusEnum.AWAIT_CONFIRM.getValue())
                            .settlementStatus(SettlementStatusEnum.AWAIT_SETTLEMENT.getValue())
                            .build();
                    try {
                        BaseBuildEntityUtil.buildInsert(costIncomeCalculation);
                    } catch (Exception e) {
                        costIncomeCalculation.setId(IdWorker.getId());
                        costIncomeCalculation.setCtime(new Timestamp(System.currentTimeMillis()));
                        costIncomeCalculation.setDelFlag(BaseConstants.NO);
                    }
                    addList.add(costIncomeCalculation);
                } else {
                    try {
                        costIncomeCalculation.setSettlementHours(projectShareHours);
                        costIncomeCalculation.setSettlementUnitPrice(settlementUnitPrice);
                        costIncomeCalculation.setCustomerBearingAmount(customerBearingAmount);
                        costIncomeCalculation.setEstimatedInclusiveAmountTax(estimatedInclusiveAmountTax);
                        BaseBuildEntityUtil.buildUpdate(costIncomeCalculation);
                    } catch (Exception e) {
                        costIncomeCalculation.setMtime(new Timestamp(System.currentTimeMillis()));
                        costIncomeCalculation.setDelFlag(BaseConstants.NO);
                    }
                    updateList.add(costIncomeCalculation);
                }
            });
        });

        List<CostIncomeCalculation> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(addList)) {
            this.saveBatch(addList);
            result.addAll(addList);
        }
        if (CollUtil.isNotEmpty(updateList)) {
            this.updateBatchById(updateList);
            result.addAll(updateList);
        }
        return result;
    }

    @Override
    @Transactional
    public List<Long> batchConfirm(CostIncomeCalculationDTO request) {
        List<CostIncomeCalculation> unConfirmCalculationList = CollUtil.emptyIfNull(baseMapper.selectBatchIds(request.getIds())).stream()
                .filter(e -> !ConfirmStatusEnum.CONFIRM.getValue().equals(e.getConfirmStatus())
                        && SettlementStatusEnum.AWAIT_SETTLEMENT.getValue().equals(e.getSettlementStatus()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(unConfirmCalculationList)) {
            return ListUtil.empty();
        }
        unConfirmCalculationList.forEach(e -> {
            e.setConfirmStatus(ConfirmStatusEnum.CONFIRM.getValue());
            e.setConfirmDate(LocalDate.now());
            BaseBuildEntityUtil.buildUpdate(e);
        });
        this.updateBatchById(unConfirmCalculationList);
        PigxUser user = SecurityUtils.getUser();
        List<Long> unConfirmIds = unConfirmCalculationList.stream().map(CostIncomeCalculation::getId).collect(Collectors.toList());
        detailMapper.batchConfirmByCalculation(unConfirmIds, ConfirmStatusEnum.CONFIRM.getValue(), LocalDate.now().toString(), user.getId(), user.getName());

        return unConfirmIds;
    }

    @Override
    @Transactional
    public List<Long> batchCancelConfirm(CostIncomeCalculationDTO request) {
        List<CostIncomeCalculation> confirmCalculationList = CollUtil.emptyIfNull(baseMapper.selectBatchIds(request.getIds())).stream()
                .filter(e -> ConfirmStatusEnum.CONFIRM.getValue().equals(e.getConfirmStatus())
                        && SettlementStatusEnum.AWAIT_SETTLEMENT.getValue().equals(e.getSettlementStatus()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(confirmCalculationList)) {
            return ListUtil.empty();
        }
        confirmCalculationList.forEach(e -> {
            e.setConfirmStatus(ConfirmStatusEnum.AWAIT_CONFIRM.getValue());
            e.setConfirmDate(null);
            BaseBuildEntityUtil.buildUpdate(e);
        });
        this.updateBatchById(confirmCalculationList);

        PigxUser user = SecurityUtils.getUser();
        List<Long> confirmIds = confirmCalculationList.stream().map(CostIncomeCalculation::getId).collect(Collectors.toList());
        detailMapper.batchConfirmByCalculation(confirmIds, ConfirmStatusEnum.AWAIT_CONFIRM.getValue(), null, user.getId(), user.getName());

        return confirmIds;
    }

    @Override
    @Transactional
    public List<Long> batchSettlement(CostIncomeCalculationDTO request) {
        List<CostIncomeCalculation> unSettlementCalculationList = CollUtil.emptyIfNull(baseMapper.selectBatchIds(request.getIds())).stream()
                .filter(e -> ConfirmStatusEnum.CONFIRM.getValue().equals(e.getConfirmStatus())
                        && SettlementStatusEnum.AWAIT_SETTLEMENT.getValue().equals(e.getSettlementStatus()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(unSettlementCalculationList)) {
            return ListUtil.empty();
        }
        // 批量修改汇总结算状态
        unSettlementCalculationList.forEach(e -> {
            e.setSettlementStatus(SettlementStatusEnum.SETTLEMENT.getValue());
            BaseBuildEntityUtil.buildUpdate(e);
        });
        this.updateBatchById(unSettlementCalculationList);

        // 明细批量结算
        List<Long> calculationIds = unSettlementCalculationList.stream().map(CostIncomeCalculation::getId).collect(Collectors.toList());
        List<CostIncomeCalculationDetail> detailList =
                detailMapper.findList(CostIncomeCalculationDTO.builder().calculationIds(calculationIds).build());
        if (CollUtil.isNotEmpty(detailList)) {
            CostIncomeCalculationDTO detailSettlementRequest = CostIncomeCalculationDTO.builder()
                    .ids(detailList.stream().map(CostIncomeCalculationDetail::getId).collect(Collectors.toList()))
                    .source(0)
                    .build();
            detailsService.batchSettlement(detailSettlementRequest);
        }

        return calculationIds;
    }

    @Override
    @Transactional
    public List<Long> batchRegenerate(CostIncomeCalculationDTO request) {
        List<CostIncomeCalculation> unRegenerateCalculationList = CollUtil.emptyIfNull(baseMapper.selectBatchIds(request.getIds())).stream()
                .filter(e -> !ConfirmStatusEnum.CONFIRM.getValue().equals(e.getConfirmStatus())
                        && SettlementStatusEnum.AWAIT_SETTLEMENT.getValue().equals(e.getSettlementStatus()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(unRegenerateCalculationList)) {
            return ListUtil.empty();
        }
        List<Long> calculationIds = unRegenerateCalculationList.stream().map(CostIncomeCalculation::getId).collect(Collectors.toList());
        List<CostIncomeCalculationDetail> detailList =
                detailMapper.findList(CostIncomeCalculationDTO.builder().calculationIds(calculationIds).build());

        CostIncomeCalculationDTO detailRegenerateRequest = CostIncomeCalculationDTO.builder()
                .ids(detailList.stream().map(CostIncomeCalculationDetail::getId).collect(Collectors.toList()))
                .source(0)
                .build();
        detailsService.batchRegenerate(detailRegenerateRequest);

        return calculationIds;
    }

    @Override
    public void export(HttpServletResponse response, CostIncomeCalculationDTO request) {
        try {
            Long projectId = request.getProjectIds().get(0);
            // 封装测算导出数据
            List<CostIncomeCalculationVO> calculationList;
            List<CostIncomeCalculationDetailVO> detailList;
            if (!DETAIL_FLAG.equals(request.getSource())) {
                calculationList = this.findList(projectId, request);
                // 获取汇总对应的明细数据
                List<Long> calculationIds =
                        calculationList.stream().map(CostIncomeCalculationVO::getId).distinct().collect(Collectors.toList());
                detailList = CollUtil.isNotEmpty(calculationIds)
                        ? detailsService.findDetailList(projectId, CostIncomeCalculationDTO.builder().calculationIds(calculationIds).build())
                        : ListUtil.empty();
            } else {
                detailList = detailsService.findDetailList(projectId, request);
                // 获取明细对应汇总数据
                List<Long> calculationIds = detailList.stream().map(CostIncomeCalculationDetailVO::getCalculationId).distinct().collect(Collectors.toList());
                calculationList = CollUtil.isNotEmpty(calculationIds)
                        ? this.findList(projectId, CostIncomeCalculationDTO.builder().ids(calculationIds).build())
                        : ListUtil.empty();
            }
            List<CostIncomeCalculationExportVO> calculationExportList = CostIncomeCalculationExportVO.fromVoList(calculationList);
            List<CostIncomeCalculationDetailExportVO> calculationDetailExportList = CostIncomeCalculationDetailExportVO.fromVoList(detailList);

            // 文件导出
            WriteSheet sheet1 = EasyExcel.writerSheet(0, "测算_汇总")
                    .head(CostIncomeCalculationExportVO.class)
                    .registerWriteHandler(new MergeCellStrategyHandler(true, 1, Arrays.asList(0, 1, 2, 3, 4, 5, 6, 7)))
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .build();
            WriteSheet sheet2 = EasyExcel.writerSheet(1, "测算_明细")
                    .head(CostIncomeCalculationDetailExportVO.class)
                    .registerWriteHandler(new MergeCellStrategyHandler(true, 1, Arrays.asList(0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12)))
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .build();

            // 文件名
            String fileName = URLEncoder.encode("收入测算", StandardCharsets.UTF_8.name()
            ).replaceAll("\\+", "%20");
            // 写入响应体信息
            response.setContentType(CONTENT_TYPE_SHEET);
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.setHeader(PARAM_CONTENT_DISPOSITION, CONTENT_DISPOSITION + fileName + XLSX_SUFFIX);
            // 设置sheet名
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).autoCloseStream(Boolean.FALSE).build();
            // 写入excelWriter
            excelWriter.write(calculationExportList, sheet1);
            excelWriter.write(calculationDetailExportList, sheet2);
            // 关闭excelWriter
            excelWriter.finish();
            response.flushBuffer();
        } catch (Exception e) {
            log.error("导出测算数据失败", e);
        }
    }

    @Override
    public List<Long> sendUnconfirmedMsg() {
        List<CostIncomeCalculationVO> unconfirmedProjectList = baseMapper.findUnconfirmedProject();
        if (CollUtil.isEmpty(unconfirmedProjectList)) {
            log.info("收入测算数据已全部确认，消息推送任务完成！");
        }

        List<WeComModel> weComModelList = new ArrayList<>(unconfirmedProjectList.size());
        BcpMessageBatchDTO bcpMessageDto = new BcpMessageBatchDTO();
        Map<String, BcpMessageContentModel> bcMessageContentMap = new HashMap<>();
        List<BcpMessageTargetBatchDTO> bcpMessageTargetList = new ArrayList<>();
        for (CostIncomeCalculationVO vo : unconfirmedProjectList) {
            // 封装企微消息对象
            WeComModel weComModel = new WeComModel();
            weComModel.setSource(SourceEnum.PROJECT.getValue());
            weComModel.setType(MsgTypeEnum.TEXT_MSG.getValue());
            weComModel.setTitle("收入测算数据确认提醒");
            weComModel.setSenderId(clientId);
            weComModel.setSender(SourceEnum.PROJECT.getName());
            weComModel.setTargetType(TargetTypeEnum.USERS.getValue());

            String redirectUrl = StrUtil.format(costIncomeCalculationUrlRedirect, vo.getProjectId());
            weComModel.setRedirectUrl(redirectPrefix + Base64.encode(redirectUrl, Charsets.UTF_8));

            BcpMessageTargetDTO target = BcpMessageTargetDTO.builder()
                    .targetId(String.valueOf(vo.getManagerUserId()))
                    .targetName(vo.getManagerUserName())
                    .build();
            weComModel.setTargetList(Arrays.asList(target));
            String content = StrUtil.format("【{}】收入测算数据尚未全部确认，请及时进行数据确认~", vo.getProjectName());
            weComModel.setContent(content + "\n<a href=\"" + weComModel.getRedirectUrl() + "\">" + "查看详情</a>");
            weComModelList.add(weComModel);

            // 封装门户消息对象
            String contentId = UUID.randomUUID().toString();
            BcpMessageContentModel bcpMessageContentModel = new BcpMessageContentModel();
            bcpMessageContentModel.setTitle(weComModel.getTitle());
            bcpMessageContentModel.setContent(content);
            bcpMessageContentModel.setRedirectUrl(weComModel.getRedirectUrl());
            bcpMessageContentModel.setType(MsgTypeEnum.TEXT_MSG.getValue());
            bcMessageContentMap.put(contentId, bcpMessageContentModel);

            BcpMessageTargetBatchDTO bcpMessageTargetBatchDTO = new BcpMessageTargetBatchDTO();
            bcpMessageTargetBatchDTO.setContentId(contentId);
            bcpMessageTargetBatchDTO.setTargetId(target.getTargetId());
            bcpMessageTargetBatchDTO.setTargetName(target.getTargetName());
            bcpMessageTargetList.add(bcpMessageTargetBatchDTO);
        }
        // 批量发送企微消息
        WeComBatchModel weComBatchModel = new WeComBatchModel();
        weComBatchModel.setData(weComModelList);
        remoteSendMsgService.sendWeComMsgBatch(SecurityConstants.FROM_IN, weComBatchModel);

        // 发送门户消息
        bcpMessageDto.setSource(SourceEnum.PROJECT.getValue());
        bcpMessageDto.setChannel(ChannelEnum.MAIL.getValue());
        bcpMessageDto.setSenderId(clientId);
        bcpMessageDto.setSender(SourceEnum.PROJECT.getName());
        bcpMessageDto.setTargetType(TargetTypeEnum.USERS.getValue());
        bcpMessageDto.setSendTime(DateUtil.formatTime(new Date()));
        bcpMessageDto.setContentMap(bcMessageContentMap);
        bcpMessageDto.setTargetList(bcpMessageTargetList);
        remoteSendMsgService.sendMsgBatch(SecurityConstants.FROM_IN, bcpMessageDto);

        return unconfirmedProjectList.stream().map(CostIncomeCalculationVO::getProjectId).collect(Collectors.toList());
    }

}
