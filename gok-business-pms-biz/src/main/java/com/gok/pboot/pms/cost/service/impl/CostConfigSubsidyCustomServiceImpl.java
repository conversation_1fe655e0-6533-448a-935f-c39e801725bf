package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.cost.entity.domain.CostConfigSubsidyCustom;
import com.gok.pboot.pms.cost.entity.domain.CostConfigVersion;
import com.gok.pboot.pms.cost.entity.dto.CostConfigSubsidyCustomDTO;
import com.gok.pboot.pms.cost.entity.vo.CostConfigSubsidyCustomVO;
import com.gok.pboot.pms.cost.enums.CalculationMethodEnum;
import com.gok.pboot.pms.cost.enums.CostConfigVersionTypeEnum;
import com.gok.pboot.pms.cost.mapper.CostConfigSubsidyCustomMapper;
import com.gok.pboot.pms.cost.service.ICostConfigSubsidyCustomService;
import com.gok.pboot.pms.cost.service.ICostConfigVersionService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 自定义补贴配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Service
@AllArgsConstructor
public class CostConfigSubsidyCustomServiceImpl extends ServiceImpl<CostConfigSubsidyCustomMapper, CostConfigSubsidyCustom> implements ICostConfigSubsidyCustomService {

    private final ICostConfigVersionService costConfigVersionService;
    /**
     * 获取成本配置补贴自定义列表
     *
     * @return {@link List }<{@link CostConfigSubsidyCustomVO }>
     */
    @Override
    public List<CostConfigSubsidyCustomVO> getCostConfigSubsidyCustomList() {
        // 获取当前最大版本号
        CostConfigVersion crrMaxVersion = costConfigVersionService.getCrrCostConfigVersion(CostConfigVersionTypeEnum.ZDYBT);
        // 获取最大版本号下的差旅住宿标准配置数据
        List<CostConfigSubsidyCustomVO> configTravelStayVoList = Objects.nonNull(crrMaxVersion)
                ? baseMapper.getSubsidyCustomsByVersionId(crrMaxVersion.getId())
                : Collections.emptyList();
        return handleCalculateMethodToList(configTravelStayVoList);
    }

    /**
     * 编辑成本配置补贴自定义列表
     *
     * @param dtoList DTO 列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editCostConfigSubsidyCustomList(List<CostConfigSubsidyCustomDTO> dtoList) {
        if (CollUtil.isEmpty(dtoList)){
            return;
        }
        // 检查补贴名称是否重复
        Set<String> subsidyNameSet = new HashSet<>(0);
        // 获取最新版本名称
        Long versionId = costConfigVersionService.generateVersionName(CostConfigVersionTypeEnum.ZDYBT);
        List<CostConfigSubsidyCustom> configSubsidyCustomList = dtoList.stream()
                .map(item ->
                        {
                            if (!subsidyNameSet.add(item.getSubsidyName())){
                                throw new ServiceException(("补贴名称:" + item.getSubsidyName() + "重复"));
                            }
                            return BaseBuildEntityUtil.buildInsert(new CostConfigSubsidyCustom()
                                    .setVersionId(versionId)
                                    .setSubsidyName(item.getSubsidyName())
                                    .setSubsidyPrice(item.getSubsidyPrice())
                                    .setCalculationMethod(item.getCalculationMethod()));
                        })
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(configSubsidyCustomList)){
            this.saveBatch(configSubsidyCustomList);
        }
    }

    /**
     * 根据版本ID获取自定义补贴配置列表
     *
     * @param versionId 版本 ID
     * @return {@link List }<{@link CostConfigSubsidyCustomVO }>
     */
    @Override
    public List<CostConfigSubsidyCustomVO> getCostConfigSubsidyCustomListByVersionId(Long versionId) {
        List<CostConfigSubsidyCustomVO> subsidyCustomVoList = baseMapper.getSubsidyCustomsByVersionId(versionId);
        if (CollUtil.isEmpty(subsidyCustomVoList)){
            return Collections.emptyList();
        }
        return handleCalculateMethodToList(subsidyCustomVoList);
    }

    /**
     * 获取计算方式
     *
     * @param subsidyCustomVoList 补贴自定义 VO 列表
     * @return {@link List }<{@link CostConfigSubsidyCustomVO }>
     */
    private static List<CostConfigSubsidyCustomVO> handleCalculateMethodToList(List<CostConfigSubsidyCustomVO> subsidyCustomVoList) {
        return subsidyCustomVoList.stream()
                .map(item -> item.setCalculateMethodStr(EnumUtils.getNameByValue(CalculationMethodEnum.class, item.getCalculationMethod())))
                .collect(Collectors.toList());
    }
}
