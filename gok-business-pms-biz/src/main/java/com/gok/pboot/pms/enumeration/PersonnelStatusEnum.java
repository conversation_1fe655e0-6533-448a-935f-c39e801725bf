package com.gok.pboot.pms.enumeration;

/**
 * 人员状态枚举
 **/
public enum PersonnelStatusEnum implements ValueEnum<Integer> {
    /**
     *
     */
    ZS(0, "正式"),
    /**
     * 实习
     */
    SX(1, "实习"),
    /**
     * 项目实习
     */
    XMSX(2, "项目实习"),
    /**
     * 外包
     */
    WB(3,"外包");

    //值
    private final Integer value;
    //名称
    private final String name;

    PersonnelStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 获取值
     *
     * @return Integer
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取名称
     *
     * @return String
     */
    @Override
    public String getName() {
        return name;
    }

    public static String getNameByVal(Integer value) {
        for (PersonnelStatusEnum statusEnum : PersonnelStatusEnum.values()) {
            if (statusEnum.value.equals(value)) {
                return statusEnum.name;
            }
        }
        return "";
    }
    public static Integer getValByName(String name) {
        for (PersonnelStatusEnum statusEnum : PersonnelStatusEnum.values()) {
            if (statusEnum.getName().equals(name)) {
                return statusEnum.getValue();
            }
        }
        return null;
    }

    public static PersonnelStatusEnum getByEmployeeStatusEnum(EmployeeStatusEnum employeeStatusEnum){
        if (employeeStatusEnum == EmployeeStatusEnum.SHIXI) {
            return PersonnelStatusEnum.SX;
        }

        return PersonnelStatusEnum.ZS;
    }
}
