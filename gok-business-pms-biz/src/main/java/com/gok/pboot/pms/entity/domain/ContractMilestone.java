package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 合同里程碑表
 *
 * <AUTHOR>
 * @date 2024/03/20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value = "contract_milestone")
@Accessors(chain = true)
public class ContractMilestone extends BeanEntity<Long> {

    /**
     * 关联合同ID
     */
    private Long contractId;

    /**
     * 里程碑
     */
    private String milestone;

    /**
     * 款项名称ID
     */
    private String paymentNameId;

    /**
     * 款项名称
     */
    private String paymentName;

    /**
     * 预计完成日期
     */
    private LocalDate expectedCompleteDate;

    /**
     * 实际完成日期
     */
    private LocalDate actualCompleteDate;

    /**
     * 预估款项金额_含税
     */
    private BigDecimal estimatedAmountIncludeTax;

    /**
     * 预估款项金额_不含税
     */
    private BigDecimal estimatedAmount;

    /**
     * 预估税率
     */
    private Integer estimatedTaxRate;

    /**
     * 计划款项金额_含税
     */
    private BigDecimal plannedAmountIncludeTax;

    /**
     * 计划款项金额_不含税
     */
    private BigDecimal plannedAmount;

    /**
     * 计划税率
     */
    private Integer plannedTaxRate;

    /**
     * 回款账期
     */
    private String paymentPeriodDate;

    /**
     * 实际回款金额
     */
    private BigDecimal actualPaymentAmount;

    /**
     * 实际回款日期
     */
    private LocalDate actualPaymentDate;

    /**
     * 款项条件
     */
    private String milestoneCondition;

    /**
     * 里程碑达成佐证
     */
    private String milestoneEvidence;

    /**
     * 结算单附件(盖章)
     */
    private String settlementAttachment;

    /**
     * 坏账流程编号
     */
    private String badDebtProcessNumber;

    /**
     * 坏账金额
     */
    private BigDecimal badDebtAmount;

    /**
     * 坏账归档时间
     */
    private LocalDate badDebtFilingTime;

    /**
     * 款项差额
     */
    private BigDecimal amountDifference;

    /**
     * 款项状态
     */
    private String milestoneStatus;

} 