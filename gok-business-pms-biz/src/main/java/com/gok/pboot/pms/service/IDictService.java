package com.gok.pboot.pms.service;

import com.gok.bcp.admin.entity.SysDictItem;
import com.gok.bcp.admin.vo.DictKvVo;
import com.gok.bcp.admin.vo.DictLevelKvVo;
import com.gok.components.common.util.R;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 字典 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-30
 */
public interface IDictService {

    /**
     * 批量查询中台字典
     *
     * @param dictKeyList
     * @return
     */
    R<Map<String, List<DictKvVo>>> getDictKvBatchList(Set<String> dictKeyList);

    /**
     * 获取数据字典kv值(字典名称咨询后端)
     */
    R<List<DictKvVo>> getDictKvList(String dictKey);

    /**
     * 批量获取树结构字典
     *
     * @return
     */
    R<Map<String, List<DictLevelKvVo>>> batchTreeList(Set<String> dictKeyList);

    /**
     * 批量获取层级字典集合
     *
     * @return
     */
    R<Map<String, List<SysDictItem>>> batchLeverList(Set<String> dictKeyList);

}
