package com.gok.pboot.pms.entity.vo;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 合同台账明细表 收款金额汇总数据
 *
 * <AUTHOR>
 * @date 2024/2/22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ContractDetailSumVo {

    /**
     * 合同id
     */
    private Long id;

    /**
     * 收款金额
     */
    private BigDecimal skje;

}
