package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 交付方式枚举
 *
 * <AUTHOR>
 * @date 13/12/2023
 */
@Getter
@AllArgsConstructor
public enum ContractDeliveryFormEnum implements ValueEnum<Integer> {

    /**
     * 人力外包
     */
    HUMAN_RESOURCE_OUTSOURCING(0, "人力外包"),

    /**
     * 项目外包
     */
    PROJECT_OUTSOURCING(1, "项目外包"),
    ;

    private final Integer value;

    private final String name;

}
