package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 项目核心数据
 *
 * <AUTHOR>
 * @date 2023-07-18
 */
@Data
@TableName("project_data")
@EqualsAndHashCode(callSuper = true)
public class ProjectData extends BaseEntity<Long> {

    /**
     * 预算总收入（含税）
     */
    private BigDecimal totalBudgetRevenueIncludeTax;

    /**
     * 预估收入总额(不含税)
     */
    private BigDecimal totalBudgetRevenue;

    /**
     * 预算总成本（含税）
     */
    private BigDecimal totalBudgetCostIncludeTax;

    /**
     * 预估成本总额(不含税)
     */
    private BigDecimal totalBudgetCost;

    /**
     * 实际总成本（含税）
     */
    private BigDecimal actualTotalBudgetCostIncludeTax;

    /**
     * 实际总成本（不含税）
     */
    private BigDecimal actualTotalBudgetCost;

    /**
     * 预估总人天
     */
    private String estimatedTotalManDays;

    /**
     * 销售合同金额（含税）
     */
    private BigDecimal contractPrice;

    /**
     * 管理费用
     */
    private BigDecimal managementCost;

    /**
     * 销售费用
     */
    private BigDecimal salesCost;

    /**
     * 已发生人天
     */
    private BigDecimal manDays;

    /**
     * 售前已发生人天
     */
    private BigDecimal beforeSalesManDays;

    /**
     * 售后已发生人天
     */
    private BigDecimal afterSalesManDays;

    /**
     * 学员人天
     */
    private BigDecimal studentManDay;

    /**
     * 创建时间
     */
    private LocalDateTime ctime;

    /**
     * 售前人工投入
     */
    private BigDecimal preSalesLaborInput;

    /**
     * 售前费用投入
     */
    private BigDecimal sqfytr;

    /**
     * 内部项目预算总成本
     */
    private BigDecimal internalTotalBudgetCostIncludeTax;

    /**
     * 内部项目预估总人天
     */
    private String internalEstimatedTotalManDays;

    /**
     * 内部项目人工预算
     */
    private BigDecimal internalLaborBudget;

    /**
     * 内部项目外采预算(含税)
     */
    private BigDecimal internalOutsourcingBudgetTax;

    /**
     * 内部项目预算变更次数
     */
    private Long internalBudgetChangesNum;

    /**
     * 预估毛利(B表)
     */
    private BigDecimal estimatedGrossProfit;

    /**
     * 预估毛利率(B表)
     */
    private BigDecimal estimatedGrossProfitRate;

    /**
     * 预估收入总额(不含税 毛利测算)
     */
    private BigDecimal secondTotalBudgetRevenue;

    /**
     * 预估成本总额(不含税 毛利测算)
     */
    private BigDecimal secondTotalBudgetCost;

    /**
     * 预估毛利(毛利测算)
     */
    private BigDecimal secondEstimatedGrossProfit;

    /**
     * 预估毛利率(毛利测算)
     */
    private BigDecimal secondEstimatedGrossProfitRate;

    /**
     * 销售合同金额_不含税
     */
    private BigDecimal xshtjeBhs;

    /**
     * 项目回款
     */
    private BigDecimal xmhk;

    /**
     * A表收入总额(不含税)
     */
    private BigDecimal abygsrzeBhs;

    /**
     * A表成本总额(不含税)
     */
    private BigDecimal abygcbzeBhs;

    /**
     * A表项目预计毛利
     */
    private BigDecimal abygmle;

    /**
     * A表项目预计毛利率
     */
    private BigDecimal abygmll;

    /**
     * 主营业务收入
     */
    private BigDecimal zyywsr;

    /**
     * 主营业务成本
     */
    private BigDecimal zyywcb;

    /**
     * C表毛利额
     */
    private BigDecimal cbmle;

    /**
     * C表毛利率
     */
    private BigDecimal cbmll;

    /**
     * 主营业务收入_财务口径
     */
    private BigDecimal zyywsrCwkj;

    /**
     * 主营业务成本_财务口径
     */
    private BigDecimal zyywcbCwkj;

    /**
     * 毛利额
     */
    private BigDecimal mle;

    /**
     * 毛利率
     */
    private BigDecimal mll;

    /**
     * 流程ID
     */
    private Long requestId;

    /**
     * 流程名称
     */
    private String requestName;

}