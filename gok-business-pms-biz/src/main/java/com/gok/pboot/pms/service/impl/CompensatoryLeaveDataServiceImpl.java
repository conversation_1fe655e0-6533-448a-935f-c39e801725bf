package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.entity.CompensatoryLeaveData;
import com.gok.pboot.pms.entity.LeaveDetail;
import com.gok.pboot.pms.entity.dto.PanelRequestDTO;
import com.gok.pboot.pms.entity.vo.CompensatoryLeaveDataDetailVO;
import com.gok.pboot.pms.entity.vo.CompensatoryLeaveDataVO;
import com.gok.pboot.pms.mapper.CompensatoryLeaveDataMapper;
import com.gok.pboot.pms.mapper.HolidayMapper;
import com.gok.pboot.pms.service.ICompensatoryLeaveDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * - 请休假服务实现类 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/24 9:48
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CompensatoryLeaveDataServiceImpl extends ServiceImpl<CompensatoryLeaveDataMapper, CompensatoryLeaveData>
        implements ICompensatoryLeaveDataService {
     private final CompensatoryLeaveDataMapper compensatoryLeaveDataMapper;
     private final HolidayMapper  holidayMapper;

    /**
     * 根据时间范围和用户ID查询
     * @param startTime 开始时间
     * @param endTime 结束日期
     * @param userId 用户ID
     * @return 请休假、加班数据列表
     */
    public List<CompensatoryLeaveDataVO> findByDateTimeRangeAndUserId(LocalDate startTime,
                                                                            LocalDate endTime,
                                                                            Long userId){
        List<CompensatoryLeaveDataVO> overtimeLeaveDataTimeVOList
                = compensatoryLeaveDataMapper.findByDateTimeRangeAndUserId(startTime,endTime,userId,"3");
        return overtimeLeaveDataTimeVOList;

        /*Set<LocalDate>  holidays=holidayMapper.findAll();
        if(CollUtil.isNotEmpty(overtimeLeaveDataTimeVOList)) {
            for (int i = 0; i < overtimeLeaveDataTimeVOList.size(); i++) {
                CompensatoryLeaveDataVO overtimeLeaveDataTime = overtimeLeaveDataTimeVOList.get(i);
                LocalDateTime belongdateTime = overtimeLeaveDataTime.getBelongDateTime();

                LocalDateTime startLeaveTime = LocalDateTime.of(belongdateTime.getYear(), belongdateTime.getMonth(),
                        belongdateTime.getDayOfMonth(), belongdateTime.getHour(), belongdateTime.getMinute());
                BigDecimal totalLeaveHours = overtimeLeaveDataTime.getHourData();

                List<LeaveDetail> leaveDetails = calculateLeaveDetails(startLeaveTime, totalLeaveHours, holidays);
                for (LeaveDetail detail : leaveDetails) {
                    CompensatoryLeaveDataDetailVO compensatoryLeaveData = BeanUtil.toBean(overtimeLeaveDataTime, CompensatoryLeaveDataDetailVO.class);
                    compensatoryLeaveData.setId(IdWorker.getId());
                    compensatoryLeaveData.setBelongdate(detail.getDate());
                    compensatoryLeaveData.setHourData(detail.getHours());
                    compensatoryLeaveData.setMinuteData(detail.getHours().multiply(new BigDecimal(60)).intValue());
                    overtimeLeaveDataList.add(compensatoryLeaveData);
                }
            }
        }
        return overtimeLeaveDataList;*/
    }

    private  boolean isHoliday(LocalDate date, Set<LocalDate> holidays) {
        boolean b = holidays.contains(date) || date.getDayOfWeek() == DayOfWeek.SATURDAY || date.getDayOfWeek() == DayOfWeek.SUNDAY;
        return b;
    }

    private  List<LeaveDetail> calculateLeaveDetails(LocalDateTime startLeaveTime, BigDecimal totalLeaveHours, Set<LocalDate> holidays) {
        List<LeaveDetail> leaveDetails = new ArrayList<>();
        BigDecimal remainingHours = totalLeaveHours;
        LocalDateTime currentTime = startLeaveTime;
        while(isHoliday(currentTime.toLocalDate(), holidays)){
            currentTime = nextWorkTimePoint(currentTime);
        }
        while (remainingHours.compareTo(BigDecimal.ZERO)>0) {
            // 如果剩余时长小于一天的工作时长，那么计算剩余时长
            BigDecimal workHours = hoursInWorkDay(currentTime, remainingHours);
            remainingHours = remainingHours.subtract(workHours);
            leaveDetails.add(new LeaveDetail(currentTime.toLocalDate(), workHours));
            // 跳到第二天的工作时间开始
            LocalDateTime localDateTime = nextWorkDayStart(currentTime, holidays);
            currentTime = localDateTime;
        }
        return leaveDetails;
    }

    private  BigDecimal hoursInWorkDay(LocalDateTime time,BigDecimal remainingHours) {
        LocalTime timeOfDay = time.toLocalTime();
        LocalTime workStartMorning = LocalTime.of(9, 0);
        LocalTime workEndMorning = LocalTime.of(12, 0);
        LocalTime workStartAfternoon = LocalTime.of(14, 0);
        LocalTime workEndAfternoon = LocalTime.of(18, 0);

        if (timeOfDay.compareTo(workStartMorning)<=0) {
            if(remainingHours.compareTo(new BigDecimal(7))>=0){
                return new BigDecimal(7);
            }else{
                return remainingHours;
            }
        } else if(timeOfDay.compareTo(workStartMorning)>0&&timeOfDay.compareTo(workEndMorning)<=0){
            Duration duration = Duration.between(timeOfDay, workEndMorning);
            BigDecimal preciseHours = new BigDecimal(duration.getSeconds()).divide(BigDecimal.valueOf(3600), 2, RoundingMode.HALF_UP);
            if(remainingHours.compareTo(preciseHours)>=0){
                return preciseHours;
            }else{
                return remainingHours;
            }
        }else if(timeOfDay.compareTo(workEndMorning)>0&&timeOfDay.compareTo(workStartAfternoon)<=0){
            if(remainingHours.compareTo(new BigDecimal(4))>=0){
                return new BigDecimal(4);
            }else{
                return remainingHours;
            }
        }else if(timeOfDay.compareTo(workStartAfternoon)>0&&timeOfDay.compareTo(workEndAfternoon)<=0){
            Duration duration = Duration.between(timeOfDay, workEndAfternoon);
            BigDecimal preciseHours = new BigDecimal(duration.getSeconds()).divide(BigDecimal.valueOf(3600), 2, RoundingMode.HALF_UP);
            if(remainingHours.compareTo(preciseHours)>=0){
                return preciseHours;
            }else{
                return remainingHours;
            }
        }else{
            return BigDecimal.ZERO;
        }
    }



    private  LocalDateTime nextWorkTimePoint(LocalDateTime time) {
        LocalDate date = time.toLocalDate();
        date = date.plusDays(1);
        return  LocalDateTime.of(date, LocalTime.of(9, 0));

    }

    private  LocalDateTime nextWorkDayStart(LocalDateTime time, Set<LocalDate> holidays) {
        LocalDate date = time.toLocalDate();
        date = date.plusDays(1);
        time=LocalDateTime.of(date, LocalTime.of(9, 0));
        if (isHoliday(date, holidays)) {
            LocalDateTime localDateTime = nextWorkDayStart(time, holidays);
            time=localDateTime;
        }
        return time;
    }

    public  List<CompensatoryLeaveData> getCompensatoryLeaveDataListByUserId(LocalDate startTime, LocalDate endTime,
                                                                            Long userId,String type){
        List<CompensatoryLeaveData> compensatoryLeaveDataList=new ArrayList<>();
        List<CompensatoryLeaveDataVO> compensatoryLeaveDataVOList
                = baseMapper.findByDateTimeRangeAndUserId(startTime, endTime, userId, type);
        if(CollUtil.isNotEmpty(compensatoryLeaveDataVOList)){
            compensatoryLeaveDataList= BeanUtil.copyToList(compensatoryLeaveDataVOList, CompensatoryLeaveData.class);
        }
        return  compensatoryLeaveDataList;
    }

    @Override
    public Map<Long, BigDecimal> getCompensatoryLeaveDataMap(PanelRequestDTO filter){
        List<CompensatoryLeaveData> compensatoryLeaveDataList
                = this.getCompensatoryLeaveDataListByUserId(filter.getStartTime(), filter.getEndTime(), filter.getUserId(), "3");
        return getXmmcTimeMap(compensatoryLeaveDataList);
    }


    /**
     * 项目对应调休工时
     * @param compensatoryLeaveDataList
     * @return
     */
    private  Map<Long, BigDecimal>  getXmmcTimeMap(List<CompensatoryLeaveData> compensatoryLeaveDataList){
        Map<Long, BigDecimal> xmmcTimeMap = new HashMap<>();

        if(CollUtil.isNotEmpty(compensatoryLeaveDataList)){
            Map<Long, List<CompensatoryLeaveData>> overtimeLeaveMap = compensatoryLeaveDataList.stream()
                    .filter(d -> d.getXmmc()!=null)
                    .collect(Collectors.groupingBy(CompensatoryLeaveData::getXmmc));
            Set<Map.Entry<Long, List<CompensatoryLeaveData>>> entries = overtimeLeaveMap.entrySet();
            entries.forEach(entry -> {
                BigDecimal sumHours=BigDecimal.ZERO;
                List<CompensatoryLeaveData> value = entry.getValue();
                for (CompensatoryLeaveData v :
                        value) {
                    sumHours=sumHours.add(v.getHourData());
                }
                xmmcTimeMap.put(entry.getKey(),sumHours);
            });
        }
        return xmmcTimeMap;
    }

    /**
     * 根据时间范围和用户ID查询
     * @param startTime 开始时间
     * @param endTime 结束日期
     * @param userIds 用户IDs
     * @return 请休假、加班数据列表
     */
    @Override
    public List<CompensatoryLeaveDataDetailVO> findByDateTimeRangeAndUserIds(LocalDate startTime,
                                                                             LocalDate endTime,
                                                                            List<Long> userIds){
        List<CompensatoryLeaveDataVO> overtimeLeaveDataTimeVOList
                = compensatoryLeaveDataMapper.findByDateTimeRangeAndUserIds(startTime,endTime,userIds,"3");
        List<CompensatoryLeaveDataDetailVO>  overtimeLeaveDataList
                =BeanUtil.copyToList(overtimeLeaveDataTimeVOList,CompensatoryLeaveDataDetailVO.class);
        /*  Set<LocalDate>  holidays=holidayMapper.findAll();
      if(CollUtil.isNotEmpty(overtimeLeaveDataTimeVOList)) {
            for (int i = 0; i < overtimeLeaveDataTimeVOList.size(); i++) {
                CompensatoryLeaveDataVO overtimeLeaveDataTime = overtimeLeaveDataTimeVOList.get(i);
                LocalDate belongdateTime = overtimeLeaveDataTime.getBelongDate();

                LocalDateTime startLeaveTime = LocalDateTime.of(belongdateTime.getYear(), belongdateTime.getMonth(),
                        belongdateTime.getDayOfMonth(), belongdateTime.getHour(), belongdateTime.getMinute());
                BigDecimal totalLeaveHours = overtimeLeaveDataTime.getHourData();

                List<LeaveDetail> leaveDetails = calculateLeaveDetails(startLeaveTime, totalLeaveHours, holidays);
                for (LeaveDetail detail : leaveDetails) {
                    CompensatoryLeaveDataDetailVO compensatoryLeaveData = BeanUtil.toBean(overtimeLeaveDataTime, CompensatoryLeaveDataDetailVO.class);
                    compensatoryLeaveData.setId(IdWorker.getId());
                    compensatoryLeaveData.setBelongDate(detail.getDate());
                    compensatoryLeaveData.setHourData(detail.getHours());
                    compensatoryLeaveData.setMinuteData(detail.getHours().multiply(new BigDecimal(60)).intValue());
                    overtimeLeaveDataList.add(compensatoryLeaveData);
                }
            }
        }*/

        return overtimeLeaveDataList;
    }
}

