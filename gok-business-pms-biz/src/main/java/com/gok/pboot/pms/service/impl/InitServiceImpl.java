package com.gok.pboot.pms.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.entity.DailyPaperEntry;
import com.gok.pboot.pms.entity.Holiday;
import com.gok.pboot.pms.mapper.DailyPaperEntryMapper;
import com.gok.pboot.pms.mapper.HolidayMapper;
import com.gok.pboot.pms.service.IInitService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 * 初始化 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-30
 */
@Service
@Slf4j
@AllArgsConstructor
public class InitServiceImpl implements IInitService {

    private final HolidayMapper holidayMapper;

    private final DailyPaperEntryMapper dailyPaperEntryMapper;
    @Override
    public ApiResult<String> overworkTimeAndType() {
        LocalDate startDate = LocalDate.of(2024, 7, 1);
        QueryWrapper<DailyPaperEntry> dailyPaperEntryQueryWrapper = new QueryWrapper<>();
        dailyPaperEntryQueryWrapper.lambda().ge(DailyPaperEntry::getSubmissionDate,startDate);
        List<DailyPaperEntry> dailyPaperEntryList = dailyPaperEntryMapper.selectList(dailyPaperEntryQueryWrapper);
        List<DailyPaperEntry> updateDailyPaperEntryList=new ArrayList<>();
        if(CollUtil.isNotEmpty(dailyPaperEntryList)){
            List<Holiday> holidayList = holidayMapper.findHolidayList();
            LinkedHashMap<LocalDate, Integer> holidayMap
                    = holidayList.stream()
                    .filter(h-> h.getHolidayType()!=null)
                    .collect(Collectors.toMap(Holiday::getDayDate, Holiday::getHolidayType, (v1, v2) -> v1, LinkedHashMap::new));
            dailyPaperEntryList.stream().forEach(d->{
                Integer holidayType = holidayMap.get(d.getSubmissionDate());
                if(d.getAddedHours().compareTo(BigDecimal.ZERO)!=0){
                    if(holidayType!=null&&holidayType==0){
                        d.setHolidayOvertimeHours(BigDecimal.ZERO);
                        d.setWorkOvertimeHours(BigDecimal.ZERO);
                        d.setRestOvertimeHours(d.getAddedHours());
                    }else if(holidayType!=null&&holidayType==1){
                        d.setHolidayOvertimeHours(d.getAddedHours());
                        d.setWorkOvertimeHours(BigDecimal.ZERO);
                        d.setRestOvertimeHours(BigDecimal.ZERO);
                    }else{
                        d.setHolidayOvertimeHours(BigDecimal.ZERO);
                        d.setWorkOvertimeHours(d.getAddedHours());
                        d.setRestOvertimeHours(BigDecimal.ZERO);
                    }
                    updateDailyPaperEntryList.add(d);
                }

            });
            dailyPaperEntryMapper.batchUpdate(updateDailyPaperEntryList);
        }
        return ApiResult.success("加班数据初始化成功");
    }
}
