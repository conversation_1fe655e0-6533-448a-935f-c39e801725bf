package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 用户数据权限vo
 *
 * <AUTHOR>
 * @date 2023/9/5
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SysUserDataScopeVO {

    /**
     * 是否拥有全部权限
     */
    private Boolean isAll;

    /**
     * 自定义权限，可看的部门
     */
    private List<Long> deptIdList;

    /**
     * 自定义权限，可看的人
     */
    private List<Long> userIdList;
    
    /**
     * 自定义权限，可看的人名称
     */
    private List<String> userNameList;

}
