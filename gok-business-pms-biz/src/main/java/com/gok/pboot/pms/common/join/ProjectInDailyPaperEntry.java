package com.gok.pboot.pms.common.join;

import lombok.*;

import java.time.LocalDate;
import java.util.List;

/**
 * - 日报条目中的项目对象 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/25 11:16
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
public class ProjectInDailyPaperEntry {
    /**
     * ID
     */
    private Long id;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 项目状态
     * {@link com.gok.pboot.pms.enumeration.BusinessStatusEnum}
     */
    private Integer projectStatus;
    /**
     * 销售人员名
     */
    private String salesmanUserName;
    /**
     * 项目销售人员ID
     */
    private Long preSalesmanUserId;
    /**
     * 项目销售姓名
     */
    private String preSalesmanUserName;
    /**
     * 项目经理名
     */
    private String managerUserName;
    /**
     * 审核员名列表
     */
    private List<String> auditorNames;

    /**
     * 是否内部项目标识
     */
    private Integer isInsideProject;

    /**
     * 创建时间
     */
    private LocalDate ctime;
    /**
     * 收藏项目实体ID
     */
    private Long collectId;
    /**
     * 收藏人id
     */
    private Long collectUserId;
    /**
     * 收藏时间
     */
    private String collectTime;
}
