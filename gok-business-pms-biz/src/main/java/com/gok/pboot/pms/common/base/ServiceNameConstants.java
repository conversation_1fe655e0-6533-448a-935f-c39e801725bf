/*
 *
 *      Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: lengleng (<EMAIL>)
 *
 */

package com.gok.pboot.pms.common.base;

/**
 * <AUTHOR>
 * @date 2018年06月22日16:41:01 服务名称
 */
public interface ServiceNameConstants {

	/**
	 * 认证中心
	 */
	String AUTH_SERVICE = "ehr-auth";

	/**
	 * UMPS模块
	 */
	String UPMS_SERVICE = "ehr-upms";

	/**
	 * 分布式事务协调服务
	 */
	String TX_MANAGER = "ehr-tx-manager";
	
	/**
	 * 文档服务
	 */
    String DOCUMENT = "ehr-document";

	/**
	 * 平台微服务
	 */
	String PLATFORM = "ehr-platform";

	/**
	 * 短信微服务
	 */
	String SMS = "ehr-sms";

	/**
	 * ehr业务服务
	 */
	String EHR_SERVICE = "ehr-service";

	/**
	 * ehr流程服务
	 */
	String FLOW = "flow";

	/**
	 * PMS业务服务
	 */
	String PMS_SERVICE = "pms-service";

	/**
	 * 业务中台
	 */
	String BCP_SERVICE = "gok-bcp-upms-biz";

}
