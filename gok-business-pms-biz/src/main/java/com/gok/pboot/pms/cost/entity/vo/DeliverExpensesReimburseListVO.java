package com.gok.pboot.pms.cost.entity.vo;

import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
    * 交付管理-费用报销台账
 * <AUTHOR>
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class DeliverExpensesReimburseListVO {
    /**
    * 主键ID
    */
    private Long id;

    /**
    * 项目id
    */
    private Long projectId;

    /**
    * 项目名称
    */
    private String projectName;

    /**
    * 收款人姓名
    */
    private String recipientUserName;

    /**
    * 科目名称id
    */
    private Long accountId;

    /**
    * 科目名称
    */
    private String accountName;

    /**
     * 税率
     */
    private Integer taxRate;

    /**
    * 成本科目类别id
    */
    private Long accountCategoryId;

    /**
    * 成本科目类别名称
    */
    private String accountCategoryName;

    /**
     * 费用项id
     */
    private Long accountItemId;

    /**
     * 费用项名称
     */
    private String accountItemName;

    /**
    * 报销金额
    */
    private BigDecimal reimburseMoney;

    /**
    * 报销说明
    */
    private String expensesDesc;

    /**
    * 关联流程ID
    */
    private Long requestId;

    /**
    * 关联流程名称
    */
    private String requestName;

    /**
    * 申请人id
    */
    private Long applicantId;

    /**
    * 申请人姓名
    */
    private String applicantName;

    /**
    * 申请时间
    */
    private String applicantTime;

    /**
    * 归档时间
    */
    private String filingTime;

}