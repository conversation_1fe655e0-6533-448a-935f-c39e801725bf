package com.gok.pboot.pms.cost.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ExcelIgnoreUnannotated
public class ExitPersonInfoVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 姓名
     */
    @ExcelProperty("姓名")
    private String name;

    /**
     * 工号
     */
    @ExcelProperty("工号")
    private String workCode;

    /**
     * 人员属性(0: 国科人员,1: 第三方)
     */
    private Integer personnelAttribute;

    /**
     * 人员属性名称
     */
    @ExcelProperty("人员属性")
    private String personnelAttributeName;

    /**
     * 所属部门ID
     */
    private Long deptId;

    /**
     * 所属部门名称
     */
    @ExcelProperty("人员归属部门")
    private String deptName;

    /**
     * 岗位ID
     */
    private Long jobId;

    /**
     * 岗位名称
     */
    @ExcelProperty("岗位")
    private String jobName;

    /**
     * 职级ID
     */
    private Long positionId;

    /**
     * 职级名称
     */
    @ExcelProperty("职级")
    private String positionName;

    /**
     * 入场时间
     */
    @ExcelProperty("入场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String entryTime;

    /**
     * 离场时间
     */
    @ExcelProperty("离场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String leaveTime;

    /**
     * 状态(0: 在场,1: 已离场)
     */
    private Integer status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 驻场时长(天)
     */
    @ExcelProperty("驻场时长(天)")
    private BigDecimal durationDays;

    /**
     * 驻场地点
     */
    @ExcelProperty("驻场地点")
    private String domicile;

    /**
     * 报价方式(0: 人天,1: 人月,2: 固定费率)
     */
    private Integer quotationType;

    /**
     * 报价方式名称
     */
    @ExcelProperty("报价方式")
    private String quotationTypeName;

    /**
     * 含税报价
     */
    @ExcelProperty("含税报价")
    private BigDecimal quotationIncludeTax;

    /**
     * 报价税率ID
     */
    private String quotedRateId;

    /**
     * 报价税率
     */
    @ExcelProperty("报价税率")
    private String quotedRate;

    /**
     * 不含税报价
     */
    @ExcelProperty("不含税报价")
    private BigDecimal quotationExcludeTax;

    /**
     * 固定费率
     */
    @ExcelProperty("固定费率")
    private String flatRate;

    /**
     * 外采含税单价
     */
    @ExcelProperty("外采含税单价")
    private BigDecimal foreignPurchaseUnitPriceIncludeTax;

    /**
     * 外采税率ID
     */
    private String foreignPurchaseTaxRateId;

    /**
     * 外采税率
     */
    @ExcelProperty("外采税率")
    private String foreignPurchaseTaxRate;

    /**
     * 外采不含税单价
     */
    @ExcelProperty("外采不含税单价")
    private BigDecimal foreignPurchaseUnitPriceExcludeTax;

    /**
     * 修改时间
     */
    @ExcelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String mtime;

    /**
     * 创建人
     */
    @ExcelProperty("创建人")
    private String creator;

}