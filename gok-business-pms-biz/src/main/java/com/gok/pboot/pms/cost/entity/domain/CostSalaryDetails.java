package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.enumeration.CostSalaryRelateTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 成本明细表
 *
 * <AUTHOR>
 * @date 2025/05/08
 */
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cost_salary_details")
public class CostSalaryDetails extends BeanEntity<Long> {

    /**
     * 关联类型
     * @see CostSalaryRelateTypeEnum#getValue()
     */
    private Integer relateType;

    /**
     * 关联id
     */
    private Long relateId;

    /**
     * 配置id
     */
    private Long configId;

    /**
     * 工资
     */
    private BigDecimal salary;

    /**
     * 社保
     */
    private BigDecimal socialSecurity;

    /**
     * 公积金
     */
    private BigDecimal housingFund;

    /**
     * 残保金
     */
    private BigDecimal disabilityFee;

    /**
     * 休息日加班费
     */
    private BigDecimal weekendOvertimePay;

    /**
     * 节假日加班费
     */
    private BigDecimal holidayOvertimePay;
} 