package com.gok.pboot.pms.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCache;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.util.StringUtils;

import javax.annotation.Nonnull;
import java.time.Duration;

/**
 * 缓存配置
 *
 * <AUTHOR>
 * @version 1.3.2
 */
@Configuration
public class RedisConfig {

    /**
     * 配置自定义的RedisCacheManager（支持过期时间设置）
     * @param redisTemplate RedisTemplate
     * @return RedisCacheManager
     */
    @Bean
    public SelfRedisCacheManager selfCacheManager(RedisTemplate<String, Object> redisTemplate) {
        RedisConnectionFactory connectionFactory = redisTemplate.getConnectionFactory();
        RedisCacheWriter redisCacheWriter;
        RedisCacheConfiguration redisCacheConfiguration;

        if (connectionFactory == null) {
            throw new NullPointerException("RedisConnectionFactory = null");
        }
        redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(connectionFactory);
        redisCacheConfiguration = RedisCacheConfiguration.defaultCacheConfig()
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(
                        redisTemplate.getValueSerializer()
                ));

        return new SelfRedisCacheManager(redisCacheWriter, redisCacheConfiguration);
    }

    private static class SelfRedisCacheManager extends RedisCacheManager {

        public SelfRedisCacheManager(RedisCacheWriter cacheWriter, RedisCacheConfiguration defaultCacheConfiguration) {
            super(cacheWriter, defaultCacheConfiguration);
        }

        @Nonnull
        @Override
        protected RedisCache createRedisCache(@Nonnull String name, RedisCacheConfiguration cacheConfig) {
            String[] cells = StringUtils.delimitedListToStringArray(name, "=");

            name = cells[0];
            if (cells.length > 1) {
                long ttl = Long.parseLong(cells[1]);
                cacheConfig = cacheConfig.entryTtl(Duration.ofSeconds(ttl));
            }

            return super.createRedisCache(name, cacheConfig);
        }
    }
}
