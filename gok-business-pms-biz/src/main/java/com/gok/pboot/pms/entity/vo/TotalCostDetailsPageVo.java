package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 项目管理-查询直接人工（项目分摊）明细
 *
 * <AUTHOR>
 * @date 2023/08/23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class TotalCostDetailsPageVo {

    /**
     * 项目名称
     */
    @ExcelIgnore
    private String projectName;

    /**
     * 人员归属部门
     */
    @ExcelProperty({"人员归属部门"})
    @ColumnWidth(20)
    private String empDepartment;

    /**
     * 成本产生日期
     */
    @ExcelProperty({"成本产生日期"})
    @ColumnWidth(50)
    private LocalDate enteringTime;

    /**
     * 直接人工成本金额
     */
    @ExcelProperty({"直接人工成本金额"})
    @ColumnWidth(20)
    private BigDecimal totalCost;

}
