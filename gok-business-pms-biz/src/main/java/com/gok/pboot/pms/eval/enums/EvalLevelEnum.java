package com.gok.pboot.pms.eval.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工单评价状态枚举
 *
 * <AUTHOR>
 * @date 2025/05/08
 */
@AllArgsConstructor
@Getter
public enum EvalLevelEnum implements ValueEnum<Integer> {

    /**
     * 差
     */
    WANTING(0, "差"),

    /**
     * 合格
     */
    PASS(1, "合格"),

    /**
     * 良好
     */
    FINE(2, "良好"),

    /**
     * 优秀
     */
    EXCELLENT(3, "优秀");


    private final Integer value;

    private final String name;

}
