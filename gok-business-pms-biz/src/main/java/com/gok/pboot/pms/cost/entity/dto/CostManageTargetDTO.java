package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CostManageTargetDTO {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 项目需求
     */
    @NotBlank(message = "项目需求不能为空")
    private String projectRequirements;

    /**
     * 交付要求
     */
    private String deliveryRequirements;

    /**
     * 交付物
     */
    private String deliveryItems;

    /**
     * 交付期限
     */
    private String deliveryDeadline;

    /**
     * 交付地点
     */
    private String deliveryPlace;

    /**
     * 质保期(月)
     */
    private String warrantyPeriod;

    /**
     * 保密要求
     */
    private String secrecyRequirements;

    /**
     * 其他要求
     */
    private String otherRequirements;

    /**
     * 详细说明文档(附件)，多个逗号隔开
     */
    private String detailFiles;

}