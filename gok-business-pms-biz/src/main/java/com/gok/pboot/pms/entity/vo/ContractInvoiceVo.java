package com.gok.pboot.pms.entity.vo;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 合同发票vo
 *
 * <AUTHOR>
 * @date 2024/2/22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ContractInvoiceVo {

    /**
     * ID id
     */
    private Long id;
    /**
     * 发票合同收款明细id
     */
    private Long llhtmxskbh;
    /**
     * oa流程id
     */
    private Long requestid;
    /**
     * 发票代码
     */
    private String fpdm;
    /**
     * 发票号码
     */
    private String fphm;
    /**
     * 发票类型(开票类型)
     */
    private Integer kplx;
    /**
     * 发票类型(开票类型)txt
     */
    private String kplxTxt;
    /**
     * 发票状态
     */
    private Integer fpzt;
    /**
     * 发票状态Txt
     */
    private String fpztTxt;
    /**
     * 发票日期
     */
    private String fprq;
    /**
     * 发票金额（含税）
     */
    private BigDecimal fpje;

    /**
     * 发票金额（含税）
     */
    private String fpjeTxt;
    /**
     * 税率
     */
    private Integer kpsl;
    /**
     * 税率
     */
    private String kpslTxt;

    /**
     * 发票金额（不含税）
     */
    private BigDecimal fpjebhs;
    /**
     * 发票金额（不含税）
     */
    private String fpjebhsTxt;

    /**
     * 税费
     */
    private BigDecimal fpsf;
    /**
     * 税费
     */
    private String fpsfTxt;
    /**
     * 发票附件
     */
    private String fpsmj;

    /**
     * 发票附件名字
     */
    private List<OaFileInfoVo> fpsmjName;

    /**
     * 流程相关人id
     */
    private Long nodeoperator;

    /**
     * 合同id
     */
    private Long mainId;
}
