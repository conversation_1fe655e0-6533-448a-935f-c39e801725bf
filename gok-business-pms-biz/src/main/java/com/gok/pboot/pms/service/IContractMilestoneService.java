package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.entity.domain.ContractMilestone;
import com.gok.pboot.pms.entity.vo.ContractMilestoneVO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/03/20
 **/
public interface IContractMilestoneService extends IService<ContractMilestone> {

    /**
     * 根据合同id查询合同款项记录
     *
     * @param contractId 合同ID
     * @return 款项记录
     */
    List<ContractMilestoneVO> getContractPaymentById(Long contractId);

}
