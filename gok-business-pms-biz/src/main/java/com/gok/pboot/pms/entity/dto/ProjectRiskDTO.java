package com.gok.pboot.pms.entity.dto;

import com.gok.pboot.pms.enumeration.RiskLevelEnum;
import com.gok.pboot.pms.enumeration.RiskStatusEnum;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 项目风险 新增/编辑会议 Dto类
 *
 * <AUTHOR>
 * @since 2023-07-12
 **/
@Data
public class ProjectRiskDTO {

    /**
     * 项目风险主键id
     */
    private Long id;

    /**
     * 项目id
     */
    @NotNull(message = "项目id不能为空")
    private Long projectId;

    /**
     * 风险描述
     */
    @NotBlank(message = "风险描述不能为空")
    @Length(max = 100)
    private String description;

    /**
     * 发生概率 (%)
     * 四位小数
     */
    @NotNull(message = "发生概率不能为空")
    @Digits(integer = 3, fraction = 4, message = "发生概率格式错误")
    @DecimalMin(value = "0.0000", message = "发生概率最小值不能低于0")
    @DecimalMax(value = "100.0000", message = "发生概率最大值不能高于100")
    private BigDecimal probability;

    /**
     * 影响程度 极大、大、中、小 字典id
     * {@link com.gok.pboot.pms.enumeration.InfluenceDegreeEnum}
     */
    @NotNull(message = "影响程度不能为空")
    @Min(0)
    @Max(3)
    private Integer influenceDegree;

    /**
     * 风险等级字典id
     * {@link RiskLevelEnum}
     */
    @NotNull(message = "风险等级不能为空")
    @Min(0)
    @Max(2)
    private Integer level;

    /**
     * 风险应对计划
     */
    @NotBlank(message = "风险应对计划不能为空")
    @Length(max = 500)
    private String responsePlan;

    /**
     * 责任人id
     */
    @NotNull(message = "责任人id不能为空")
    private Long chargeUserId;

    /**
     * 责任人
     */
    @NotBlank(message = "责任人不能为空")
    @Length(max = 50)
    private String chargeUser;

    /**
     * 状态 开放0、关闭1
     * {@link RiskStatusEnum}
     */
    @NotNull(message = "状态不能为空")
    @Max(1)
    @Min(0)
    private Integer status;

}
