package com.gok.pboot.pms.entity.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * PMS日报提交一览表
 *
 * @Auther chenhc
 * @Date 2022-08-24 11:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DailyFindPageDTO {

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 部门id
     */
    private List<Long> deptIds;

    /**
     * 人员状态
     */
    private Integer personnelStatus;

    /**
     * 人员状态名称
     */
    private String personnelStatusName;

    /**
     * 日期开始时间
     */
    private LocalDate startTime;

    /**
     * 日期结束时间
     */
    private LocalDate endTime;

    /**
     * 用户id集合
     */
    private List<Long> userIds;

    /**
     * 异常类型（分页查询时需要）
     * DailyPaperAbnormalEnum
     */
    private Integer abnormalType;

}
