package com.gok.pboot.pms.eval.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 项目评价用户角色枚举
 *
 * <AUTHOR>
 * @create 2025/05/12
 **/
@Getter
@AllArgsConstructor
public enum EvalUserRoleEnum implements ValueEnum<Integer> {

    /**
     * PMO
     */
    PMO(0, "PMO"),
    CUSTOMER_MARKET_LEADER(1, "客户市场中心领导"),
    SHARED_OPERATION_LEADER(2, "共享运维中心领导"),
    ;

    private final Integer value;
    private final String name;
} 