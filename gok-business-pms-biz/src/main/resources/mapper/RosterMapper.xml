<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.RosterMapper">
    <sql id="baseColumnList">
        a.id,
        a.oa_id,
        a.work_code,
        a.dept_id,
        a.alias_name,
        a.id_card_no,
        a.employee_type,
        a.employee_status,
        a.job,
        a.start_date,
        a.end_date,
        a.leader_id
    </sql>

    <select id="findUserIdMap" resultType="com.gok.pboot.pms.entity.domain.Roster">
        SELECT
        <include refid="baseColumnList"/>
        FROM mhour_roster a
        <if test="userIds != null">
            WHERE oa_id IN
            <foreach collection="userIds" item="uId" open="(" separator="," close=")">
                #{uId}
            </foreach>
        </if>
    </select>

    <select id="findIds" resultType="java.lang.Long">
        SELECT id
        FROM mhour_roster
        <where>
            <if test="filter.aliasName != null">
                <bind name="aliasNameLike" value="'%' + filter.aliasName + '%'"/>
                alias_name LIKE #{aliasNameLike}
            </if>
            <if test="filter.deptIds != null">
                AND dept_id IN
                <foreach collection="filter.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findLowerByUserId" resultType="java.lang.Long">
        select id
        from mhour_roster
        where leader_id = #{userId}
    </select>

    <select id="findIdByDeptIds" resultType="java.lang.Long">
        SELECT id
        FROM mhour_roster
        WHERE dept_id IN
        <foreach collection="deptIds" item="dId" open="(" separator="," close=")">
            #{dId}
        </foreach>
    </select>

    <select id="findAliasNameById" resultType="java.lang.String">
        SELECT alias_name
        FROM mhour_roster
        WHERE id = #{userId}
    </select>

    <select id="findSelectionByAliasNameLike" resultType="com.gok.pboot.pms.entity.bo.RosterSelectionBO">
        SELECT
        id,
        alias_name,
        employee_status,
        dept_id
        FROM mhour_roster
        WHERE
        <bind name="aliasNameLike" value="'%' + aliasName + '%'"/>
        alias_name LIKE #{aliasNameLike}
    </select>

    <select id="findIdAndAliasNameMapByIds" resultType="java.lang.String">
        SELECT
        alias_name
        FROM mhour_roster
        WHERE id IN
        <foreach collection="ids" item="uId" open="(" separator="," close=")">
            #{uId}
        </foreach>
    </select>

    <select id="normalUserIds" resultType="java.lang.Long">
        SELECT id
        FROM `mhour_roster`
        where employee_status in (0, 1, 2, 3, 4)
    </select>
    <select id="findByName" resultType="com.gok.pboot.pms.entity.domain.Roster">
        select
        id,
        alias_name,
        employee_status,
        dept_id
        from mhour_roster
        <where>
            <if test="userNames != null and userNames.size() > 0">
                alias_name in
                <foreach collection="userNames" item="username" open="(" close=")" separator=",">
                    #{username}
                </foreach>
            </if>
        </where>

    </select>

</mapper>
