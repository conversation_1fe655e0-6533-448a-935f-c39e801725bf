<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostConfigAccountMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.cost.entity.domain.CostConfigAccount">
        <id column="id" property="id"/>
        <result column="version_id" property="versionId"/>
        <result column="oa_id" property="oaId"/>
        <result column="account_category_id" property="accountCategoryId"/>
        <result column="account_category_name" property="accountCategoryName"/>
        <result column="account_name" property="accountName"/>
        <result column="account_code" property="accountCode"/>
        <result column="account_type" property="accountType"/>
        <result column="creator" property="creator"/>
        <result column="creator_id" property="creatorId"/>
        <result column="modifier" property="modifier"/>
        <result column="modifier_id" property="modifierId"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        cca.id,
        cca.version_id,
        cca.oa_id,
        cca.account_category_id,
        cca.account_category_name,
        cca.account_name,
        cca.account_code,
        cca.account_type,
        cca.creator,
        cca.creator_id,
        cca.modifier,
        cca.modifier_id,
        cca.ctime,
        cca.mtime,
        cca.del_flag
    </sql>
    <select id="getCurrentVersionCostSubjectConfigInfoList"
            resultType="com.gok.pboot.pms.cost.entity.domain.CostConfigAccount">
        select
        <include refid="BaseColumnList"/>
        from cost_config_account as cca
        join cost_config_version as ccv
        on cca.version_id = ccv.id
        where ccv.version_type = 3
        <if test="versionId != null">
            and cca.version_id = #{versionId}
        </if>
        <if test="versionId == null">
            and ccv.version_status = 0
        </if>
        order by cca.account_category_id, cca.account_type
    </select>
    <select id="getVersionCount" resultType="java.lang.Integer">
        select count(distinct version_id)
        from cost_config_account
    </select>

    <select id="getCostSubjectConfigByVersionId"
            resultType="com.gok.pboot.pms.cost.entity.vo.CostConfigAccountVO">
        SELECT cca.id,
               cca.oa_id,
               cca.version_id,
               ccv.version_name,
               cca.account_category_id,
               cca.account_category_name,
               cca.account_name,
               cca.account_code,
               cca.account_type,
               cca.account_definition_desc,
               cca.for_estimate_flag
        FROM cost_config_account AS cca
        LEFT JOIN
             cost_config_version AS ccv ON cca.version_id = ccv.id AND ccv.del_flag = 0
        WHERE cca.del_flag = 0
          AND cca.version_id = #{versionId}
        ORDER BY cca.account_category_id, cca.account_name
    </select>

</mapper>
