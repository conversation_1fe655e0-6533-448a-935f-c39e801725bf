<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.eval.mapper.EvalProjectDetailMapper">

    <resultMap id="baseResultMap" type="com.gok.pboot.pms.eval.entity.domain.EvalProjectDetail">
        <id column="id" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="overview_id" property="overviewId"/>
        <result column="deliver_type" property="deliverType"/>
        <result column="index_type" property="indexType"/>
        <result column="assessment_project" property="assessmentProject"/>
        <result column="weight" property="weight"/>
        <result column="score" property="score"/>
        <result column="creator" property="creator"/>
        <result column="creator_id" property="creatorId"/>
        <result column="modifier" property="modifier"/>
        <result column="modifier_id" property="modifierId"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

</mapper>