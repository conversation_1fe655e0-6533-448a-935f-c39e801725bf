<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.OvertimeLeaveDataMapper">


    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.OvertimeLeaveData">
        <id column="id" property="id"/>
        <result column="oaId" property="oaId"/>
        <result column="minuteData" property="minuteData"/>
        <result column="hourData" property="hourData"/>
        <result column="belongdate" property="belongdate"/>
        <result column="xmmc" property="xmmc"/>
        <result column="type" property="type"/>
    </resultMap>

    <!-- 日报一览表查询映射结果 -->
    <resultMap id="QCResultMap" type="com.gok.pboot.pms.entity.vo.OvertimeLeaveDataVO">
        <result column="userId" property="userId"/>
        <collection property="overtimeLeaveDatas"
                    ofType="com.gok.pboot.pms.entity.OvertimeLeaveData">
            <id column="id" property="id"/>
            <result column="hourData" property="hourData"/>
            <result column="belongdate" property="belongdate"/>
            <result column="type" property="type"/>
        </collection>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id AS 'id',
        a.oa_id AS 'oaId',
        a.minute_data AS 'minuteData',
        a.hour_data AS 'hourData',
        a.belongdate AS 'belongdate',
        a.xmmc AS 'xmmc',
        a.type AS 'type'
    </sql>


    <select id="findList" resultType="com.gok.pboot.pms.entity.OvertimeLeaveData">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_overtime_leave_data a
        ORDER BY a.id desc
    </select>

    <select id="getOvertimeLeaveDataList" resultMap="QCResultMap">
        SELECT t1.userId,
        t2.id,
        t2.hourData,
        t2.belongdate,
        t2.type
        FROM (
        SELECT
        ol.oa_id AS 'userId'
        FROM mhour_overtime_leave_data ol
        <where>
            ol.type NOT IN (99,100)
            <if test="dailyFindPageDTO.startTime != null">
                AND DATE_FORMAT(ol.belongdate, '%Y-%m-%d') &gt;= DATE_FORMAT(#{dailyFindPageDTO.startTime}, '%Y-%m-%d')
            </if>
            <if test="dailyFindPageDTO.endTime != null">
                AND DATE_FORMAT(ol.belongdate, '%Y-%m-%d') &lt;= DATE_FORMAT(#{dailyFindPageDTO.endTime}, '%Y-%m-%d')
            </if>
            <if test="dailyFindPageDTO.userIds != null and dailyFindPageDTO.userIds.size() > 0">
                AND ol.oa_id IN
                <foreach collection="dailyFindPageDTO.userIds" separator="," item="item" open="(" close=")">#{item}
                </foreach>
            </if>
        </where>
        GROUP BY ol.oa_id
        ) t1 LEFT JOIN
        (
        SELECT
        ol.id,
        ol.oa_id AS 'userId',
        ol.hour_data AS 'hourData',
        ol.belongdate,
        ol.type
        FROM mhour_overtime_leave_data ol
        <where>
            ol.type NOT IN (99,100)
            <if test="dailyFindPageDTO.startTime != null">
                AND DATE_FORMAT(ol.belongdate, '%Y-%m-%d') &gt;= DATE_FORMAT(#{dailyFindPageDTO.startTime}, '%Y-%m-%d')
            </if>
            <if test="dailyFindPageDTO.endTime != null">
                AND DATE_FORMAT(ol.belongdate, '%Y-%m-%d') &lt;= DATE_FORMAT(#{dailyFindPageDTO.endTime}, '%Y-%m-%d')
            </if>
            <if test="dailyFindPageDTO.userIds != null and dailyFindPageDTO.userIds.size() > 0">
                AND ol.oa_id IN
                <foreach collection="dailyFindPageDTO.userIds" separator="," item="item" open="(" close=")">#{item}
                </foreach>
            </if>
        </where>
        ) t2 ON t2.userId = t1.userId
        ORDER BY t2.belongdate,t2.userId
    </select>


    <select id="getOvertimeDataListByUserId" resultType="com.gok.pboot.pms.entity.OvertimeLeaveData">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_overtime_leave_data a
        WHERE
        a.type IN
        <foreach collection="types" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
        AND a.belongdate BETWEEN #{startDate} AND #{endDate}
        AND a.oa_id = #{userId}
    </select>

    <select id="getLeaveDataListByUserIds" resultType="com.gok.pboot.pms.entity.OvertimeLeaveData">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_overtime_leave_data a
        WHERE
        a.type NOT IN ('99')
        AND a.belongdate not IN (SELECT day_date FROM mhour_holiday)
        AND a.belongdate BETWEEN #{startDate} AND #{endDate}
        <if test="userIds != null and userIds.size() > 0">
            AND a.oa_id IN
            <foreach collection="userIds" item="uId" open="(" separator="," close=")">
                #{uId}
            </foreach>
        </if>
    </select>

    <select id="getOvertimeDataListByUserIds" resultType="com.gok.pboot.pms.entity.OvertimeLeaveData">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_overtime_leave_data a
        WHERE
        a.type = '99'
        AND a.belongdate BETWEEN #{startDate} AND #{endDate}
        AND a.oa_id IN
        <foreach collection="userIds" item="uId" open="(" separator="," close=")">
            #{uId}
        </foreach>
    </select>

    <select id="getLeaveDataListByUserId" resultType="com.gok.pboot.pms.entity.OvertimeLeaveData">
        SELECT
            <include refid="Base_Column_List"/>
        FROM mhour_overtime_leave_data a
        WHERE
            a.type != '99'
        AND
            a.belongdate = #{queryDate}
        AND
            a.oa_id = #{userId}
    </select>

    <select id="findByDateAndUserId" resultType="com.gok.pboot.pms.entity.OvertimeLeaveData">
        SELECT
            <include refid="Base_Column_List"/>
        FROM mhour_overtime_leave_data a
        WHERE
            a.belongdate = #{queryDate}
        AND
            a.oa_id = #{userId}
    </select>

    <select id="findByDateRangeAndUserId" resultType="com.gok.pboot.pms.entity.OvertimeLeaveData">
        SELECT
            <include refid="Base_Column_List"/>
        FROM mhour_overtime_leave_data a
        WHERE
            a.oa_id = #{userId}
        AND
            a.belongdate BETWEEN #{startDate} AND #{endDate}
    </select>
    <select id="getTxHoursByParams" resultType="com.gok.pboot.pms.entity.OvertimeLeaveData">
        select
        <include refid="Base_Column_List"/>
        from mhour_overtime_leave_data a
        where a.type = ${@<EMAIL>()}
        and a.xmmc = #{projectId}
    </select>

</mapper>
