<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.gok.pboot.pms.mapper.ProjectTaskeMapper">

    <sql id="listResult">
        t.id,
        t.project_id,
        t.title,
        t.kind,
        t.state,
        t.permanent_flag,
        t.expect_start_date,
        t.expect_end_date,
        t.start_date,
        t.end_date,
        t.leader_names,
        t.member_names,
        t.creator,
        t.creator_id,
        t.modifier,
        t.modifier_id,
        t.ctime,
        t.mtime
    </sql>

    <select id="findListItem" resultType="com.gok.pboot.pms.entity.domain.ProjectTaske">
        select
            t.id,
            t.project_id,
            t.title,
            t.kind,
            t.state,
            t.permanent_flag,
            t.expect_start_date,
            t.expect_end_date,
            t.start_date,
            t.end_date,
            t.leader_names,
            t.member_names,
            t.creator,
            t.creator_id,
            t.modifier,
            t.modifier_id,
            t.ctime,
            t.mtime,
            p.manager_user_id,
            p.salesman_user_id,
            p.pre_sale_user_id,
            tu.user_id
        from project_taske t
        left join project_info p on t.project_id = p.id
        left join project_taske_user tu on t.id = tu.task_id
        where 1 = 1
        and t.del_flag = 0
        and tu.del_flag = 0
        and (p.manager_user_id in
            <foreach collection="ids" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
            or p.salesman_user_id in
            <foreach collection="ids" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
            or p.pre_sale_user_id in
            <foreach collection="ids" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
            or #{filter.isLeader} = 1
            or #{filter.isAll} = 1
            or #{filter.isOperationsAssistant} = 1
            or (
                    tu.user_id  in
                    <foreach collection="ids" item="id" separator="," open="(" close=")">
                        #{id}
                    </foreach>
            )
        )
        <if test="filter.projectId != null">
            and t.project_id = #{filter.projectId}
        </if>
        <if test="filter.kind != null">
            and t.kind = #{filter.kind}
        </if>
        <if test="filter.title != null and filter.title != ''">
            and t.title like concat('%',#{filter.title},'%')
        </if>
        <if test="filter.leaderName != null and filter.leaderName != ''">
            and t.leader_names like concat('%',#{filter.leaderName},'%')
        </if>
        <if test="filter.state != null">
            <if test="filter.state == 0">
                and ((t.state = ${@<EMAIL>()} and t.permanent_flag = 1)
                or (t.state = ${@<EMAIL>()} and t.expect_end_date &gt;= curdate()))
            </if>
            <if test="filter.state == 1">
                and (t.permanent_flag = 0 and t.state = ${@<EMAIL>()} and t.expect_end_date &lt; curdate())
            </if>
            <if test="filter.state == 2">
                and ((t.state = ${@<EMAIL>()} and t.permanent_flag = 1)
                or (t.state = ${@<EMAIL>()} and t.expect_end_date &gt;= t.end_date))
            </if>
            <if test="filter.state == 3">
                and(t.permanent_flag = 0 and t.state = ${@<EMAIL>()} and t.expect_end_date &lt; t.end_date)
            </if>
        </if>
        group by t.id
        order by t.ctime desc, t.title, t.kind
    </select>

    <select id="findListByAllItem" resultType="com.gok.pboot.pms.entity.domain.ProjectTaske">
        select <include refid="listResult"/>
        from project_taske t
        where 1 = 1
        and t.del_flag = 0
        <if test="filter.projectId != null">
            and t.project_id = #{filter.projectId}
        </if>
        <if test="filter.kind != null">
            and t.kind = #{filter.kind}
        </if>
        <if test="filter.title != null and filter.title != ''">
            and t.title like concat('%',#{filter.title},'%')
        </if>
        <if test="filter.leaderName != null and filter.leaderName != ''">
            and t.leader_names like concat('%',#{filter.leaderName},'%')
        </if>
        <if test="filter.state != null">
            <if test="filter.state == 0">
                and t.state = ${@<EMAIL>()}
                and (t.permanent_flag = 1 or t.expect_end_date &gt;= curdate())
            </if>
            <if test="filter.state == 1">
                and t.state = ${@<EMAIL>()}
                and (t.permanent_flag = 1 or t.expect_end_date &lt; curdate())
            </if>
            <if test="filter.state == 2">
                and t.state = ${@<EMAIL>()}
                and (t.permanent_flag = 1 or t.expect_end_date &gt;= t.end_date)
            </if>
            <if test="filter.state == 3">
                and t.state = ${@<EMAIL>()}
                and (t.permanent_flag = 1 or t.expect_end_date &lt; t.end_date)
            </if>
        </if>
        order by t.ctime desc
    </select>

    <select id="findMyTask" resultType="com.gok.pboot.pms.entity.vo.MyTaskVo">
        select
        t.id,
        t.project_id,
        t.title,
        t.kind,
        t.state,
        t.permanent_flag,
        t.expect_start_date,
        t.expect_end_date,
        t.start_date,
        t.end_date,
        t.leader_names,
        t.member_names,
        t.creator,
        t.creator_id,
        t.modifier,
        t.modifier_id,
        t.ctime,
        t.mtime,
        p.item_name as projectName
        from project_taske t
        inner join project_taske_user tu on tu.task_id = t.id
        left join project_info p on t.project_id = p.id
        where 1 = 1
        and t.del_flag = 0
        and tu.del_flag = 0
        and tu.user_id = #{filter.userId}
        and tu.task_role = ${@<EMAIL>()}
        <if test="filter.projectName != null and filter.projectName != ''">
            and p.item_name like concat('%',#{filter.projectName},'%')
        </if>
        order by t.ctime desc
    </select>

    <select id="findMyTaskCount" resultType="java.lang.Integer">
        select count(t.id)
        from project_taske t
        inner join project_taske_user tu on tu.task_id = t.id
        where 1 = 1
          and t.del_flag = 0
          and tu.del_flag = 0
          and tu.user_id = #{userId}
          and tu.task_role = ${@<EMAIL>()}
    </select>

    <select id="findListByOwnItem" resultType="com.gok.pboot.pms.entity.domain.ProjectTaske">
        select <include refid="listResult"/>
        from project_taske t
        inner join project_taske_user tu on tu.task_id = t.id
        where 1 = 1
        and t.del_flag = 0
        and tu.del_flag = 0
        and tu.user_id = #{filter.userId}
        and tu.task_role = ${@<EMAIL>()}
        <if test="filter.projectId != null">
            and t.project_id = #{filter.projectId}
        </if>
        <if test="filter.kind != null">
            and t.kind = #{filter.kind}
        </if>
        <if test="filter.title != null and filter.title != ''">
            and t.title like concat('%',#{filter.title},'%')
        </if>
        <if test="filter.leaderName != null and filter.leaderName != ''">
            and t.leader_names like concat('%',#{filter.leaderName},'%')
        </if>
        <if test="filter.state != null">
            <if test="filter.state == 0">
                and t.state = ${@<EMAIL>()}
                and (t.permanent_flag = 1 or t.expect_end_date &gt;= curdate())
            </if>
            <if test="filter.state == 1">
                and t.state = ${@<EMAIL>()}
                and (t.permanent_flag = 1 or t.expect_end_date &lt; curdate())
            </if>
            <if test="filter.state == 2">
                and t.state = ${@<EMAIL>()}
                and (t.permanent_flag = 1 or t.expect_end_date &gt;= t.end_date)
            </if>
            <if test="filter.state == 3">
                and t.state = ${@<EMAIL>()}
                and (t.permanent_flag = 1 or t.expect_end_date &lt; t.end_date)
            </if>
        </if>
        order by t.ctime desc
    </select>

    <select id="findListByMyItem" resultType="com.gok.pboot.pms.entity.domain.ProjectTaske">
        select <include refid="listResult"/>
        from project_taske t
        inner join project_taske_user tu on tu.task_id = t.id
        where 1 = 1
        and t.del_flag = 0
        and tu.del_flag = 0
        and tu.user_id = #{filter.userId}
        and tu.task_role = ${@<EMAIL>()}
        <if test="filter.projectId != null">
            and t.project_id = #{filter.projectId}
        </if>
        <if test="filter.kind != null">
            and t.kind = #{filter.kind}
        </if>
        <if test="filter.title != null and filter.title != ''">
            and t.title like concat('%',#{filter.title},'%')
        </if>
        <if test="filter.leaderName != null and filter.leaderName != ''">
            and t.leader_names like concat('%',#{filter.leaderName},'%')
        </if>
        <if test="filter.state != null">
            <if test="filter.state == 0">
                and t.state = ${@<EMAIL>()}
                and (t.permanent_flag = 1 or t.expect_end_date &gt;= curdate())
            </if>
            <if test="filter.state == 1">
                and t.state = ${@<EMAIL>()}
                and (t.permanent_flag = 1 or t.expect_end_date &lt; curdate())
            </if>
            <if test="filter.state == 2">
                and t.state = ${@<EMAIL>()}
                and (t.permanent_flag = 1 or t.expect_end_date &gt;= t.end_date)
            </if>
            <if test="filter.state == 3">
                and t.state = ${@<EMAIL>()}
                and (t.permanent_flag = 1 or t.expect_end_date &lt; t.end_date)
            </if>
        </if>
        order by t.ctime desc
    </select>

    <select id="findProjectIdByTaskId" resultType="java.lang.Long">
        SELECT
            project_id
        FROM project_taske
        WHERE
            del_flag = 0
        AND
            id = #{taskId}
    </select>

    <select id="isExistById" resultType="java.lang.Integer">
        select exists (select id from project_taske where id = #{id})
    </select>

    <update id="logicDeleteById">
        update project_taske
        set del_flag = 1
        where id = #{id}
    </update>

    <update id="finishById">
        update project_taske
        set state = ${@<EMAIL>()},
            end_date = #{endDate}
        where id = #{id}
    </update>

    <update id="restartById">
        update project_taske
        set state = ${@<EMAIL>()},
            end_date = null
        where id = #{id}
    </update>

    <select id="findListByProjectId" resultType="com.gok.pboot.pms.entity.domain.ProjectTaske">
        select <include refid="listResult"/>
        from project_taske t
        where 1 = 1
        and t.del_flag = 0
        and t.project_id = #{projectId}
    </select>

    <select id="findTaskIdByProjectIds" resultType="java.lang.Long">
        SELECT id
        FROM project_taske
        WHERE
            del_flag = 0
        AND project_id IN
        <foreach collection="projectIds" open="(" close=")" separator="," item="projectId">
            #{projectId}
        </foreach>
    </select>

    <select id="findByUserIdAndProjectIdForEntry" resultType="com.gok.pboot.pms.entity.domain.ProjectTaske">
        select <include refid="listResult"/>
        from project_taske t
        inner join project_taske_user tu on tu.task_id = t.id
        where 1 = 1
        and t.del_flag = 0
        and t.state = 0
        and tu.del_flag = 0
        and tu.user_id = #{userId}
        <if test="projectIds != null and projectIds.size() > 0">
            and t.project_id in
            <foreach collection="projectIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and tu.task_role = ${@<EMAIL>()}
        AND (t.expect_start_date &lt;= #{date} OR t.permanent_flag = 1)
    </select>

    <select id="findByTaskIds" resultType="com.gok.pboot.pms.entity.domain.ProjectTaske">
        select <include refid="listResult"/>
        from project_taske t
        where 1 = 1
        and t.del_flag = 0
        <if test="taskIds != null and taskIds.size() > 0">
            and t.id in
            <foreach collection="taskIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="batchUpdateStartDate">
        <foreach collection="list" item="item" separator=";" index="index">
            update project_taske
            set start_date = #{item.startDate}
            where id = #{item.id}
        </foreach>
    </update>

    <select id="findCreatedDefaultByProjectIds" resultType="com.gok.pboot.pms.entity.domain.ProjectTaske">
        select <include refid="listResult"/>
        from project_taske t
        where 1 = 1
        <if test="projectIds != null and projectIds.size() > 0">
            and t.project_id in
            <foreach collection="projectIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <insert id="batchUpdate" parameterType="com.gok.pboot.pms.entity.domain.ProjectTaske">
        <foreach collection="list" item="item" separator=";">
            UPDATE project_taske
            SET
                id = #{item.id},
                project_id = #{item.projectId},
                title = #{item.title},
                kind = #{item.kind},
                state = #{item.state},
                permanent_flag = #{item.permanentFlag},
                expect_start_date = #{item.expectStartDate},
                expect_end_date = #{item.expectEndDate},
                start_date = #{item.startDate},
                end_date = #{item.endDate},
                leader_names = #{item.leaderNames},
                member_names = #{item.memberNames},
                modifier = #{item.modifier},
                modifier_id = #{item.modifierId},
                mtime = #{item.mtime}
            WHERE
            id = #{item.id}
        </foreach>
    </insert>

    <insert id="batchSave">
        insert into project_taske
        (
            id,
            project_id,
            title,
            kind,
            state,
            permanent_flag,
            expect_start_date,
            expect_end_date,
            start_date,
            end_date,
            leader_names,
            member_names,
            creator,
            creator_id,
            modifier,
            modifier_id,
            ctime,
            mtime,
            del_flag
        )
        values
        <foreach collection="list" item="item" separator="," index="index">
            (
                #{item.id},
                #{item.projectId},
                #{item.title},
                #{item.kind},
                #{item.state},
                #{item.permanentFlag},
                #{item.expectStartDate},
                #{item.expectEndDate},
                #{item.startDate},
                #{item.endDate},
                #{item.leaderNames},
                #{item.memberNames},
                #{item.creator},
                #{item.creatorId},
                #{item.modifier},
                #{item.modifierId},
                #{item.ctime},
                #{item.mtime},
                #{item.delFlag}
            )
        </foreach>
    </insert>

    <select id="findUnfinishedByProjectIds" resultType="com.gok.pboot.pms.entity.domain.ProjectTaske">
        select <include refid="listResult"/>
        from project_taske t
        where t.del_flag = 0
        and t.state = 0
        and t.project_id in
        <foreach collection="projectIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="batchFinish">
        update project_taske t
        set t.state = ${@<EMAIL>()},
            t.end_date = ifnull((select pe.submission_date
                                from mhour_daily_paper_entry pe
                                where pe.task_id = t.id order by pe.submission_date desc limit 1), curdate())
        where t.id in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="findUnFinishedTaskIds" resultType="java.lang.Long">
        select distinct t.id
        from project_taske t
        inner join project_taske_user tu on tu.task_id = t.id
        where t.del_flag = 0
        and tu.del_flag = 0
        and t.id in
        <foreach collection="taskIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and t.state = 0
        and tu.user_id = #{userId}
    </select>
    <select id="findTaskLeaders" resultMap="ProjectTaskFindPageVOMap">
        select task_id, user_id, user_name
        from project_taske_user
        <where>
            del_flag = 0 and task_role = 0
            <if test="taskIds != null and taskIds.size() > 0">
                and task_id in
                <foreach collection="taskIds" item="taskId" open="(" close=")" separator=",">
                    #{taskId}
                </foreach>
            </if>
        </where>
        group by task_id, user_id
    </select>

    <resultMap id="ProjectTaskFindPageVOMap" type="com.gok.pboot.pms.entity.vo.ProjectTaskFindPageVO">
        <result property="taskId" column="task_id"/>
        <collection property="taskLeaderVOList" ofType="com.gok.pboot.pms.entity.vo.TaskLeaderVO">
            <result property="userId" column="user_id"/>
            <result property="userRealName" column="user_name"/>
        </collection>
    </resultMap>

</mapper>
