<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostTaskStatMapper">
    <!-- 通用查询条件 -->
    <sql id="commonWhere">
        and t.disassembly_type = 0
        and t.del_flag = 0
        and t.task_type = #{query.taskType}

        <if test="query.taskNo != null and query.taskNo != '' ">
            AND t.task_no LIKE CONCAT('%', #{query.taskNo}, '%')
        </if>
        <if test="query.taskName != null and query.taskName != '' ">
            AND t.task_name LIKE CONCAT('%', #{query.taskName}, '%')
        </if>
        <if test="query.taskStatuses != null and query.taskStatuses.size() > 0">
            AND t.task_status IN
            <foreach collection="query.taskStatuses" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>

        <!-- 时间筛选 -->
        <if test="query.startDate != null and query.endDate != null">
            <if test="query.taskType == 0 ">
                and t.ctime &lt;= concat(#{query.endDate}, ' 23:59:59')
                and (t.completion_time IS NULL or t.completion_time >= concat(#{query.startDate}, ' 00:00:00'))
            </if>
            <if test="query.taskType == 1 ">
                and t.start_date &lt;= #{query.endDate} and t.end_date >= #{query.startDate}
            </if>
        </if>

        <if test="query.projectNo != null and query.projectNo != '' ">
            AND p.item_no LIKE CONCAT('%', #{query.projectNo}, '%')
        </if>

        <if test="query.projectName != null and query.projectName != ''">
            AND p.item_name LIKE CONCAT('%', #{query.projectName}, '%')
        </if>

        <if test="query.projectIds != null and query.projectIds.size() > 0">
            AND p.id IN
            <foreach collection="query.projectIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>


        <!-- 数据权限 -->
        <if test="query.isAll == null or !query.isAll">
            <if test="query.userAuth != null and query.userAuth.size() > 0">
                and u.id in
                <foreach collection="query.userAuth" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.projectAuth != null and query.projectAuth.size() > 0">
                and p.id in
                <foreach collection="query.projectAuth" item="projectId" open="(" separator="," close=")">
                    #{projectId}
                </foreach>
            </if>
        </if>


        <if test="query.userIds != null and query.userIds.size() > 0">
            AND u.id IN
            <foreach collection="query.userIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>

        <if test="query.workCode != null and query.workCode != ''">
            AND u.work_code like CONCAT('%', #{query.workCode}, '%')
        </if>

        <if test="query.employeeName != null and query.employeeName != ''">
            AND u.alias_name like CONCAT('%', #{query.employeeName}, '%')
        </if>
    </sql>

    <!-- 项目工单看板-项目维度统计 -->
    <select id="projectProDimDeliverStatPage"
            resultType="com.gok.pboot.pms.cost.entity.vo.ProjectProDimDeliverStatVO">
        SELECT
        p.id AS projectId,
        p.item_no AS projectNo,
        p.item_name AS projectName,
        p.ctime AS projectCreateTime,
        -- 分配工单产值
        SUM(t.income) AS income,
        -- 结算工单产值
        SUM(IF(t.task_status = ${@<EMAIL>()}, t.income, 0)) AS
        settleIncome,
        -- 待结算工单产值
        SUM(IF(t.task_status != ${@<EMAIL>()}, t.income, 0)) AS
        waitSettleIncome,
        -- 已下发工单个数
        COUNT(t.id) AS issuedTaskCount,
        -- 已完成工单个数
        COUNT(CASE WHEN t.task_status = ${@<EMAIL>()} THEN 1 END)
        AS completedTaskCount,
        -- 待完成工单个数
        COUNT(CASE WHEN t.task_status != ${@<EMAIL>()} THEN 1 END)
        AS waitCompleteTaskCount,
        -- 待评价工单个数
        COUNT(CASE WHEN t.evaluation_status = 0 THEN 1 END) AS waitEvaluateTaskCount
        FROM project_info p
        LEFT JOIN cost_deliver_task t ON p.id = t.project_id
        <where>
            <include refid="commonWhere"/>
        </where>
        GROUP BY p.id
        ORDER BY p.ctime DESC
    </select>

    <!-- 个人评分排名 -->
    <sql id="selectPersonalRank">
        SELECT COUNT(DISTINCT e2.adjusted_score) + 1
        FROM eval_task_calibration e2
        WHERE e2.adjusted_score > e.adjusted_score
    </sql>

    <!-- 项目工单看板-人员维度统计 -->
    <select id="projectPerDimDeliverStatList"
            resultType="com.gok.pboot.pms.cost.entity.vo.ProjectPerDimDeliverStatVO">
        SELECT
        t.project_id,
        p.item_no as project_no,
        p.ctime as projectCreateTime,
        t.project_name,
        t.manager_id as user_id,
        u.work_code,
        u.alias_name as employee_name,
        -- 单项目个人评分
        ifnull(e.adjusted_score,e.current_score) as singleProjectScore,
        -- 个人评分排名
        e.rank AS personalRank,
        -- 分配工单产值
        SUM(t.income) AS income,
        -- 结算工单产值
        SUM(IF(t.task_status = ${@<EMAIL>()}, t.income, 0)) AS
        settleIncome,
        -- 待结算工单产值
        SUM(IF(t.task_status != ${@<EMAIL>()}, t.income, 0)) AS
        waitSettleIncome,
        -- 已下发工单个数
        COUNT(t.id) AS issuedTaskCount,
        -- 已完成工单个数
        COUNT(CASE WHEN t.task_status = ${@<EMAIL>()} THEN 1 END)
        AS completedTaskCount,
        -- 待完成工单个数
        COUNT(CASE WHEN t.task_status != ${@<EMAIL>()} THEN 1 END)
        AS waitCompleteTaskCount,
        -- 待评价工单个数
        COUNT(CASE WHEN t.evaluation_status = 0 THEN 1 END) AS waitEvaluateTaskCount,
        -- 预计工时
        SUM(t.estimated_hours) as estimated_hours
        FROM mhour_roster u
        LEFT JOIN cost_deliver_task t ON t.manager_id = u.id
        LEFT JOIN eval_task_calibration e ON t.project_id = e.project_id
        LEFT JOIN project_info p ON t.project_id = p.id
        <where>
            
            <include refid="commonWhere"/>
        </where>
        GROUP BY p.id,u.id
        ORDER BY p.ctime DESC,personalRank
    </select>

    <!-- 项目工单看板-工单维度统计 -->
    <select id="projectTaskDimDeliverStatList" resultType="com.gok.pboot.pms.cost.entity.vo.ProjectTaskDimDeliverStatVO">
        SELECT
        t.project_id,
        p.item_no as project_no,
        t.project_name,
        p.ctime as projectCreateTime,
        u.work_code,
        t.manager_id as user_id,
        u.alias_name as employee_name,
        t.id as task_id,
        t.task_no,
        t.task_name,
        t.task_status,
        -- 预计工时
        t.estimated_hours,
        -- 实际工时
        (IF(t.normal_hours is null,0, t.normal_hours)+
        IF(t.work_overtime_hours is null,0, t.work_overtime_hours)+
        IF(t.rest_overtime_hours is null,0, t.rest_overtime_hours)+
        IF(t.holiday_overtime_hours is null,0, t.holiday_overtime_hours)) as actual_hours,
        t.budget_cost as budgetLaborCostStr,
        t.actual_labor_cost as actualLaborCostStr,
        -- 工单综合评分
        e.comprehensive_score,
        -- 结算工单产值
        SUM(IF(t.task_status = ${@<EMAIL>()}, t.income, 0)) AS
        settleIncome,
        -- 结算工单产值占比
        t.income / t2.incomeSum AS settlementValueRatio
        FROM mhour_roster u
        LEFT JOIN cost_deliver_task t ON t.manager_id = u.id
        LEFT JOIN eval_task e ON t.id = e.task_id
        LEFT JOIN project_info p ON t.project_id = p.id
        LEFT JOIN (<include refid="getIncomeSum"/>) t2 ON t.project_id = t2.project_id AND t.manager_id = t2.manager_id
        <where>
            <include refid="commonWhere"/>
        </where>
        group by t.id
        order by p.ctime desc,e.comprehensive_score desc ,t.id
    </select>

    <sql id="getIncomeSum">
        SELECT project_id, manager_id, sum(income) AS incomeSum
        FROM cost_deliver_task
        WHERE del_flag = ${@<EMAIL>()}
        <if test="query.userAuth != null and query.userAuth.size() > 0">
            AND manager_id in
            <foreach collection="query.userAuth" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.projectAuth != null and query.projectAuth.size() > 0">
            AND project_id in
            <foreach collection="query.projectAuth" item="projectId" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        GROUP BY project_id, manager_id
    </sql>


    <!-- 项目工单看板-售前工单统计 -->
    <select id="projectProDimPreSaleStatPage" resultType="com.gok.pboot.pms.cost.entity.vo.ProjectProDimPreSaleStatVO">
        SELECT
        t.project_id,
        p.item_no as projectNo,
        p.item_name as projectName,
        p.ctime as projectCreateTime
        FROM project_info p
        left join cost_deliver_task t on t.project_id = p.id
        LEFT JOIN mhour_roster u ON t.manager_id = u.id
        <where>
            <include refid="commonWhere"/>
        </where>
        GROUP BY p.id
        ORDER BY p.ctime DESC
    </select>

    <select id="getCostTasks" resultType="com.gok.pboot.pms.cost.entity.domain.CostDeliverTask">
        SELECT t.*
        from cost_deliver_task t
        left join project_info p on p.id = t.project_id
        left join mhour_roster u on u.id = t.manager_id
        <where>
            <include refid="commonWhere"/>
        </where>
        group by t.id
    </select>

    <select id="projectPerDimPreSaleStatList"
            resultType="com.gok.pboot.pms.cost.entity.vo.ProjectPerDimPreSaleStatVO">
        SELECT
        t.project_id,
        p.item_no as project_no,
        t.project_name,
        p.ctime as projectCreateTime,
        t.manager_id as user_id,
        u.work_code,
        u.alias_name as employee_name,
        -- 单项目个人评分
        ifnull(e.adjusted_score,e.current_score) as singleProjectScore,
        -- 个人评分排名
        e.rank AS personalRank
        FROM mhour_roster u
        LEFT JOIN cost_deliver_task t ON t.manager_id = u.id
        LEFT JOIN eval_task_calibration e ON t.project_id = e.project_id
        LEFT JOIN project_info p ON t.project_id = p.id
        <where>
            
            <include refid="commonWhere"/>
        </where>
        GROUP BY p.id,u.id
        ORDER BY p.ctime DESC,personalRank
    </select>

    <select id="projectTaskDimPreSaleStatList" resultType="com.gok.pboot.pms.cost.entity.vo.ProjectTaskDimPreSaleStatVO">
        SELECT
        t.project_id,
        p.item_no as project_no,
        t.project_name,
        p.ctime as projectCreateTime,
        u.work_code,
        t.manager_id as user_id,
        u.alias_name as employee_name,
        t.id as task_id,
        t.task_no,
        t.task_name,
        -- 工单综合评分
        e.comprehensive_score
        FROM mhour_roster u
        LEFT JOIN cost_deliver_task t ON t.manager_id = u.id
        LEFT JOIN eval_task e ON t.id = e.task_id
        LEFT JOIN project_info p ON t.project_id = p.id
        <where>
            <include refid="commonWhere"/>
        </where>
        group by t.id
        order by p.ctime desc,e.comprehensive_score desc ,t.id
    </select>

    <select id="projectPage" resultType="com.gok.pboot.pms.entity.domain.ProjectInfo">
        select p.id
        from project_info p
        left join cost_deliver_task t ON t.project_id = p.id
        left join mhour_roster u ON t.manager_id = u.id
        <where>
            <include refid="commonWhere"/>
        </where>
        group by p.id
        order by p.ctime desc
    </select>

    <select id="personnelPerDimDeliverStatPage" resultType="com.gok.pboot.pms.cost.entity.vo.PersonnelPerDimDeliverStatVO">
        SELECT
        t.manager_id as user_id,
        u.work_code,
        u.alias_name as employee_name,
        -- 分配工单产值
        SUM(t.income) AS income,
        -- 结算工单产值
        SUM(IF(t.task_status = ${@<EMAIL>()}, t.income, 0)) AS
        settleIncome,
        -- 待结算工单产值
        SUM(IF(t.task_status != ${@<EMAIL>()}, t.income, 0)) AS
        waitSettleIncome,
        -- 已下发工单个数
        COUNT(t.id) AS issuedTaskCount,
        -- 已完成工单个数
        COUNT(CASE WHEN t.task_status = ${@<EMAIL>()} THEN 1 END)
        AS completedTaskCount,
        -- 待完成工单个数
        COUNT(CASE WHEN t.task_status != ${@<EMAIL>()} THEN 1 END)
        AS waitCompleteTaskCount,
        -- 待评价工单个数
        COUNT(CASE WHEN t.evaluation_status = 0 THEN 1 END) AS waitEvaluateTaskCount,
        -- 预计工时
        SUM(t.estimated_hours) as estimated_hours
        FROM mhour_roster u
        LEFT JOIN cost_deliver_task t ON t.manager_id = u.id
        LEFT JOIN eval_task_calibration e ON t.id = e.project_id
        <where>
            
            <include refid="commonWhere"/>
        </where>
        GROUP BY u.id
        ORDER BY u.work_code
    </select>

    <select id="personnelProDimDeliverStatList"
            resultType="com.gok.pboot.pms.cost.entity.vo.PersonnelProDimDeliverStatVO">
        SELECT
        t.manager_id as user_id,
        u.work_code,
        u.alias_name as employee_name,
        p.id AS projectId,
        p.item_no AS projectNo,
        p.item_name AS projectName,
        -- 单项目个人评分
        ifnull(e.adjusted_score,e.current_score) as singleProjectScore,
        -- 分配工单产值
        SUM(t.income) AS income,
        -- 结算工单产值
        SUM(IF(t.task_status = ${@<EMAIL>()}, t.income, 0)) AS
        settleIncome,
        -- 待结算工单产值
        SUM(IF(t.task_status != ${@<EMAIL>()}, t.income, 0)) AS
        waitSettleIncome,
        -- 已下发工单个数
        COUNT(t.id) AS issuedTaskCount,
        -- 已完成工单个数
        COUNT(CASE WHEN t.task_status = ${@<EMAIL>()} THEN 1 END)
        AS completedTaskCount,
        -- 待完成工单个数
        COUNT(CASE WHEN t.task_status != ${@<EMAIL>()} THEN 1 END)
        AS waitCompleteTaskCount,
        -- 待评价工单个数
        COUNT(CASE WHEN t.evaluation_status = 0 THEN 1 END) AS waitEvaluateTaskCount,
        -- 预计工时
        SUM(t.estimated_hours) as estimated_hours
        FROM mhour_roster u
        LEFT JOIN cost_deliver_task t ON u.id = t.manager_id
        LEFT JOIN project_info p on p.id = t.project_id
        LEFT JOIN eval_task_calibration e ON t.project_id = e.project_id
        <where>
            
            <include refid="commonWhere"/>
        </where>
        GROUP BY u.id,t.project_id
        ORDER BY u.work_code, p.ctime DESC
    </select>

    <select id="rosterPage" resultType="com.gok.pboot.pms.entity.domain.Roster">
        select u.id
        from mhour_roster u
        LEFT JOIN cost_deliver_task t ON t.manager_id = u.id
        LEFT JOIN project_info p ON t.project_id = p.id
        <where>
            <include refid="commonWhere"/>
        </where>
        GROUP BY u.id
        ORDER BY u.work_code
    </select>

    <select id="personnelTaskDimDeliverStatList"
            resultType="com.gok.pboot.pms.cost.entity.vo.PersonnelTaskDimDeliverStatVO">
        SELECT
        u.id as user_id,
        u.work_code,
        u.alias_name as employee_name,
        p.id AS projectId,
        p.item_no AS projectNo,
        p.item_name AS projectName,
        t.id as taskId,
        t.task_name,
        t.task_no,
        t.task_status,
        -- 工单综合评分
        e.comprehensive_score,
        -- 结算工单产值
        t.income AS settleIncome,
        -- 结算工单产值占比
        t.income / t2.incomeSum AS settlementValueRatio,
        -- 预计工时
        t.estimated_hours,
        -- 实际工时
        (IF(t.normal_hours is null,0, t.normal_hours)+
        IF(t.work_overtime_hours is null,0, t.work_overtime_hours)+
        IF(t.rest_overtime_hours is null,0, t.rest_overtime_hours)+
        IF(t.holiday_overtime_hours is null,0, t.holiday_overtime_hours)) as actual_hours,
        t.budget_cost as budgetLaborCostStr,
        t.actual_labor_cost as actualLaborCostStr
        FROM mhour_roster u
        LEFT JOIN cost_deliver_task t ON t.manager_id = u.id
        LEFT JOIN (<include refid="getIncomeSum"/>) t2 ON t.project_id = t2.project_id AND t.manager_id = t2.manager_id
        LEFT JOIN eval_task e ON t.id = e.task_id
        LEFT JOIN project_info p ON t.project_id = p.id
        <where>
            <include refid="commonWhere"/>
        </where>
        group by t.id
        order by u.work_code, p.ctime DESC,t.id
    </select>

    <select id="personnelPerDimPreSaleStatPage"
            resultType="com.gok.pboot.pms.cost.entity.vo.PersonnelPerDimPreSaleStatVO">
        SELECT
            u.id as user_id,
            u.work_code,
            u.alias_name as employee_name
        FROM mhour_roster u
        LEFT JOIN cost_deliver_task t ON t.manager_id = u.id
        LEFT JOIN project_info p ON t.project_id = p.id
        <where>
            <include refid="commonWhere"/>
        </where>
        GROUP BY u.id
        ORDER BY u.work_code
    </select>

    <select id="personnelProDimPreSaleStatList"
            resultType="com.gok.pboot.pms.cost.entity.vo.PersonnelProDimPreSaleStatVO">
        SELECT
        u.id as user_id,
        u.work_code,
        u.alias_name as employee_name,
        p.id as projectId,
        p.item_no as projectNo,
        p.item_name as projectName,
        -- 单项目个人评分
        ifnull(e.adjusted_score,e.current_score) as singleProjectScore
        FROM mhour_roster u
        LEFT JOIN cost_deliver_task t ON t.manager_id = u.id
        LEFT JOIN project_info p ON t.project_id = p.id
        LEFT JOIN eval_task_calibration e ON t.project_id = e.project_id
        <where>
            <include refid="commonWhere"/>
        </where>
        GROUP BY u.id, t.project_id
        ORDER BY u.work_code, p.ctime DESC
    </select>

    <select id="personnelTaskDimPreSaleStatList"
            resultType="com.gok.pboot.pms.cost.entity.vo.PersonnelTaskDimPreSaleStatVO">
        SELECT
        u.id as user_id,
        u.work_code,
        u.alias_name as employee_name,
        p.id as projectId,
        p.item_no as projectNo,
        p.item_name as projectName,
        -- 工单综合评分
        e.comprehensive_score,
        t.id as taskId,
        t.task_name,
        t.task_no
        FROM mhour_roster u
        LEFT JOIN cost_deliver_task t ON t.manager_id = u.id
        LEFT JOIN project_info p ON t.project_id = p.id
        LEFT JOIN eval_task e ON t.id = e.task_id
        <where>
            <include refid="commonWhere"/>
        </where>
        GROUP BY t.id
        ORDER BY u.work_code, p.ctime DESC,t.id
    </select>
</mapper>