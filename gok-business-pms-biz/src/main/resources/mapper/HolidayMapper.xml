<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.HolidayMapper">


    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.Holiday">
        <result column="day_date" property="dayDate"/>
        <result column="holiday_type" property="holidayType"/>
    </resultMap>


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.day_date AS 'dayDate'
        a.holiday_type AS 'holidayType'
    </sql>

    <sql id="join"></sql>


    <!--逻辑删除-->
    <delete id="deleteByYear">
        DELETE
        FROM mhour_holiday
        WHERE date_format(day_date, '%Y-%c-%e') like CONCAT(#{year}, '%')
    </delete>

    <select id="findWorkDayClosestByDate" resultType="java.time.LocalDate">
        SELECT day_date
        FROM mhour_holiday
        WHERE day_date &lt;= #{date}
        ORDER BY day_date DESC LIMIT 1
    </select>

    <select id="exists" resultType="java.lang.Boolean">
        SELECT COUNT(day_date)
        FROM mhour_holiday
        WHERE day_date = #{date}
    </select>
    <select id="selectCountByDate" resultType="java.lang.Integer">
        SELECT COUNT(day_date)
        FROM mhour_holiday
        WHERE day_date between #{startDate} and #{endDate}
    </select>

    <select id="selectCountByDatePairs" resultType="java.lang.Integer">
        SELECT COUNT(day_date)
        FROM mhour_holiday
        <trim prefix="WHERE (" suffix=")" prefixOverrides="OR">
            <foreach collection="datePairs" item="pair">
                OR day_date BETWEEN #{pair.first} AND #{pair.second}
            </foreach>
        </trim>
    </select>

    <select id="selectGeneralHolidayCountByDatePairs" resultType="java.lang.Integer">
        select count(day_date)
        from mhour_holiday
        <where>
            holiday_type = 0 and
            <foreach collection="datePairs" item="pair" separator="or" open="(" close=")">
                day_date between #{pair.first} and #{pair.second}
            </foreach>
        </where>
    </select>

    <select id="selectLegalHolidayCountByDatePairs" resultType="java.lang.Integer">
        select count(day_date)
        from mhour_holiday
        <where>
            holiday_type = 1 and
            <foreach collection="datePairs" item="pair" separator="or" open="(" close=")">
                day_date between #{pair.first} and #{pair.second}
            </foreach>
        </where>
    </select>

    <select id="selectHolidayCountByDatePairs" resultType="java.lang.Integer">
        select count(day_date)
        from mhour_holiday
        <where>
            <if test="type != null">
                holiday_type = #{type} and
            </if>
            <foreach collection="datePairs" item="pair" separator="or" open="(" close=")">
                day_date between #{pair.first} and #{pair.second}
            </foreach>
        </where>
    </select>

    <insert id="batchSave">
        INSERT INTO mhour_holiday (
        day_date, holiday_type
        )VALUES
        <foreach collection="poList" item="item" separator=",">
            (
            #{item.dayDate},
            #{item.holidayType}
            )
        </foreach>
    </insert>

    <select id="findByDateRange" resultType="java.time.LocalDate">
        SELECT day_date
        FROM mhour_holiday
        WHERE day_date BETWEEN #{startDate} AND #{endDate}
    </select>

    <select id="findGeneralHolidayByDateRange" resultType="java.time.LocalDate">
        select day_date
        from mhour_holiday
        where day_date between #{startDate} and #{endDate}
          and holiday_type = 0
    </select>

    <select id="findLegalHolidayByDateRange" resultType="java.time.LocalDate">
        select day_date
        from mhour_holiday
        where day_date between #{startDate} and #{endDate}
          and holiday_type = 1
    </select>

    <select id="findHolidayByDateRange" resultType="java.time.LocalDate">
        select day_date
        from mhour_holiday
        where day_date between #{startDate} and #{endDate}
        <if test="type != null">
            and holiday_type = #{type}
        </if>
    </select>

    <select id="findAll" resultType="java.time.LocalDate">
        SELECT day_date
        FROM mhour_holiday
    </select>

    <select id="ifHoliday" resultType="java.lang.Boolean">
        select count(day_date)
        from mhour_holiday
        where day_date = #{date}
    </select>
    <select id="ifGeneralHoliday" resultType="java.lang.Boolean">
        select count(day_date)
        from mhour_holiday
        where day_date = #{date}
          and holiday_type = 0
    </select>
    <select id="ifLegalHoliday" resultType="java.lang.Boolean">
        select count(day_date)
        from mhour_holiday
        where day_date = #{date}
          and holiday_type = 1
    </select>
    <select id="ifHolidayByType" resultType="java.lang.Boolean">
        select count(day_date)
        from mhour_holiday
        where day_date = #{date}
        <if test="type != null">
            and holiday_type = #{type}
        </if>
    </select>
    <select id="selectGeneralHolidayCountByDate" resultType="java.lang.Integer">
        select count(1)
        from mhour_holiday
        where day_date between #{startDate} and #{endDate}
          and holiday_type = 0
    </select>
    <select id="selectLegalHolidayCountByDate" resultType="java.lang.Integer">
        select count(1)
        from mhour_holiday
        where day_date between #{startDate} and #{endDate}
          and holiday_type = 1
    </select>
    <select id="selectHolidayCountByDate" resultType="java.lang.Integer">
        select count(1)
        from mhour_holiday
        where day_date between #{startDate} and #{endDate}
        <if test="type != null">
            and holiday_type = #{type}
        </if>
    </select>
    <select id="findHolidayType" resultType="java.lang.Integer" parameterType="java.time.LocalDate">
        select holiday_type
        from mhour_holiday
        where day_date = #{date}
    </select>

    <select id="selByDate" resultMap="BaseResultMap">
        select day_date,
               holiday_type
        from mhour_holiday
        where day_date = #{dayDate}
    </select>

    <select id="selByDateRange" resultMap="BaseResultMap">
        SELECT day_date,
           holiday_type
        FROM mhour_holiday
        WHERE
        holiday_type is not null
        and day_date BETWEEN #{startDate} AND #{endDate}
    </select>
    <select id="findHolidayList" resultType="com.gok.pboot.pms.entity.Holiday">
        select day_date,
               holiday_type
        from mhour_holiday
    </select>
    <select id="findBatchHolidayType" resultType="com.gok.pboot.pms.entity.Holiday">
        select day_date,
               holiday_type
        from mhour_holiday
        <where>
            <if test="dates != null and dates.size() > 0">
                day_date in
                <foreach collection="dates" item="date" open="(" close=")" separator=",">
                    #{date}
                </foreach>
            </if>
        </where>
    </select>


</mapper>
