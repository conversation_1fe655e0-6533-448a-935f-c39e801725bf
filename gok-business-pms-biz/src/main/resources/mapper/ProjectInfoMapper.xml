<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gok.pboot.pms.mapper.ProjectInfoMapper">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.gok.pboot.pms.entity.domain.ProjectInfo" id="projectInfoMap">
        <result property="id" column="id"/>
        <result property="itemNo" column="item_no"/>
        <result property="itemName" column="item_name"/>
        <result property="projectType" column="project_type"/>
        <result property="projectStatus" column="project_status"/>
        <result property="projectLocation" column="project_location"/>
        <result property="firstLevelDepartmentId" column="first_level_department_id"/>
        <result property="firstLevelDepartment" column="first_level_department"/>
        <result property="proDeliveryDepartmentId" column="pro_delivery_department_id"/>
        <result property="proDeliveryDepartment" column="pro_delivery_department"/>
        <result property="isNotInternalProject" column="is_not_internal_project"/>
        <result property="resourceChannel" column="resource_channel"/>
        <result property="sfzjqht" column="sfzjqht"/>
        <result property="signContract" column="sign_contract"/>
        <result property="projectDate" column="project_date"/>
        <result property="projectFilingTime" column="project_filing_time"/>
        <result property="ygmll" column="ygmll"/>
        <result property="proPackageBudget" column="pro_package_budget"/>
        <result property="expectedOrderAmount" column="expected_order_amount"/>
        <result property="expectedCompleteTime" column="expected_complete_time"/>
        <result property="businessToProjectTime" column="business_to_project_time"/>
        <result property="qdjeppyj" column="qdjeppyj"/>
        <result property="businessDirection" column="business_direction"/>
        <result property="maturity" column="maturity"/>
        <result property="businessType" column="business_type"/>
        <result property="technologyField" column="technology_field"/>
        <result property="proConstructionScope" column="pro_construction_scope"/>
        <result property="wsjsnr" column="wsjsnr"/>
        <result property="customerName" column="customer_name"/>
        <result property="customerContact" column="customer_contact"/>
        <result property="customerPhone" column="customer_phone"/>
        <result property="endCustomerIndustry" column="end_customer_industry"/>
        <result property="salesmanUserId" column="salesman_user_id"/>
        <result property="projectSalesperson" column="project_salesperson"/>
        <result property="preSaleUserId" column="pre_sale_user_id"/>
        <result property="preSaleUserName" column="pre_sale_user_name"/>
        <result property="managerUserId" column="manager_user_id"/>
        <result property="managerUserName" column="manager_user_name"/>
        <result property="regionUserId" column="region_user_id"/>
        <result property="regionUserName" column="region_user_name"/>
        <result property="directorUserId" column="director_user_id"/>
        <result property="directorUserName" column="director_user_name"/>
        <result property="projectStage" column="project_stage"/>
        <result property="projectGraspDegree" column="project_grasp_degree"/>
        <result property="zbfs" column="zbfs"/>
        <result property="gkysfx" column="gkysfx"/>
        <result property="gklsfx" column="gklsfx"/>
        <result property="currentProgress" column="current_progress"/>
        <result property="yddwtjsxdzc" column="yddwtjsxdzc"/>
        <result property="nextStepForwardPlan" column="next_step_forward_plan"/>
        <result property="remark" column="remark"/>
        <result property="ctime" column="ctime"/>
        <result property="contractCustomerId" column="contract_customer_id"/>
        <result property="contractCustomer" column="contract_customer"/>
        <result property="contractCustomerGrade" column="contract_customer_grade"/>
        <result property="contractEntity" column="contract_entity"/>
        <result property="endCustomerId" column="end_customer_id"/>
        <result property="endCustomer" column="end_customer"/>
        <result property="endCustomerGrade" column="end_customer_grade"/>
        <result property="customerCollection" column="customer_collection"/>
        <result property="deliverType" column="deliver_type"/>
        <result property="isExternalProcurement" column="is_external_procurement"/>
        <result property="purchasingCategories" column="purchasing_categories"/>
        <result property="businessMilestone" column="business_milestone"/>
        <result property="businessStage" column="business_stage"/>
        <result property="expectedSignDate" column="expected_sign_date"/>
        <result property="contractSettlementMethod" column="contract_settlement_method"/>
        <result property="technologyType" column="technology_type"/>
        <result property="incomeType" column="income_type"/>
        <result property="secondaryBusinessType" column="secondary_business_type"/>
        <result property="secondLevelDepartmentId" column="second_level_department_id"/>
        <result property="secondLevelDepartment" column="second_level_department"/>
        <result property="isAdvanceInvestment" column="is_advance_investment"/>
        <result property="paymentCondition" column="payment_condition"/>
        <result property="converseOngoingEvidence" column="converse_ongoing_evidence"/>
        <result property="progressQualityRequirement" column="progress_quality_requirement"/>
    </resultMap>

    <resultMap id="projectInDailyPaperEntryMap" type="com.gok.pboot.pms.common.join.ProjectInDailyPaperEntry">
        <id property="id" column="id"/>
        <result property="projectName" column="projectName"/>
        <result property="projectStatus" column="projectStatus"/>
        <result property="ctime" column="ctime"/>
        <result property="managerUserName" column="managerUserName"/>
        <result property="salesmanUserName" column="salesmanUserName"/>
        <result property="preSalesmanUserName" column="preSalesmanUserName"/>
        <result property="isInsideProject" column="isInsideProject"/>
        <result property="collectId" column="collectId"/>
        <result property="collectUserId" column="collectUserId"/>
        <result property="collectTime" column="collectTime"/>
        <collection property="auditorNames" ofType="java.lang.String" javaType="java.util.List">
            <result column="auditorName"/>
        </collection>
    </resultMap>

    <sql id="baseColumnList">
        a.id,
        a.item_no,
        a.item_name,
        a.project_status,
        a.project_location,
        a.first_level_department_id,
        a.first_level_department,
        a.pro_delivery_department_id,
        a.pro_delivery_department,
        a.is_not_internal_project,
        a.resource_channel,
        a.sfzjqht,
        a.sign_contract,
        a.project_date,
        a.project_filing_time,
        a.ygmll,
        a.pro_package_budget,
        a.expected_order_amount,
        a.expected_complete_time,
        a.business_to_project_time,
        a.qdjeppyj,
        a.business_direction,
        a.maturity,
        a.business_type,
        a.technology_field,
        a.pro_construction_scope,
        a.wsjsnr,
        a.customer_name,
        a.customer_contact,
        a.customer_phone,
        a.end_customer_industry,
        a.salesman_user_id,
        a.project_salesperson,
        a.pre_sale_user_id,
        a.pre_sale_user_name,
        a.manager_user_id,
        a.manager_user_name,
        a.region_user_id,
        a.region_user_name,
        a.director_user_id,
        a.director_user_name,
        a.project_stage,
        a.project_grasp_degree,
        a.zbfs,
        a.gkysfx,
        a.gklsfx,
        a.current_progress,
        a.yddwtjsxdzc,
        a.next_step_forward_plan,
        a.remark,
        a.ctime,
        a.project_type,
        a.project_department_id,
        a.project_department,
        a.business_department_id,
        a.business_department,
        a.business_manager_id,
        a.business_manager,
        a.estimated_cost,
        a.business_background_and_situation,
        a.business_requirements,
        a.business_objectives,
        a.progress_requirements,
        a.end_customer,
        a.deliver_type,
        a.contract_customer,
        a.secondary_business_type,
        a.second_level_department_id,
        a.second_level_department,
        a.is_advance_investment,
        a.payment_condition,
        a.converse_ongoing_evidence,
        a.progress_quality_requirement
    </sql>

    <select id="findList" resultType="com.gok.pboot.pms.entity.domain.ProjectInfo">
        SELECT
        a.id,
        a.item_no,
        a.item_name,
        a.project_status,
        a.first_level_department_id,
        a.first_level_department,
        a.pro_delivery_department_id,
        a.pro_delivery_department,
        a.is_not_internal_project,
        a.sign_contract,
        a.project_date,
        a.project_filing_time,
        a.ygmll,
        a.pro_package_budget,
        a.expected_order_amount,
        a.expected_complete_time,
        a.business_to_project_time,
        a.business_type,
        a.salesman_user_id,
        a.project_salesperson,
        a.pre_sale_user_id,
        a.pre_sale_user_name,
        a.manager_user_id,
        a.manager_user_name,
        a.region_user_id,
        a.region_user_name,
        a.director_user_id,
        a.director_user_name,
        a.remark,
        a.ctime,
        a.project_type,
        a.project_department_id,
        a.project_department,
        a.business_department_id,
        a.business_department,
        a.business_manager_id,
        a.business_manager,
        a.estimated_cost,
        a.end_customer,
        a.contract_customer,
        a.deliver_type,
        a.secondary_business_type,
        a.second_level_department_id,
        a.second_level_department,
        a.business_id,
        a.unit_id,
        b.name AS 'businessUnitName',
        c.unit_name AS 'unitName'
        FROM project_info a
        LEFT JOIN customer_business b ON a.business_id =b.id
        LEFT JOIN customer_business_unit c ON a.unit_id =c.id
        <where>
            item_name is not null
            and item_no is not null
            <if test="filter.perm == 'none'">
                and false
            </if>
            <if test="filter.scope == null and filter.projectIdsInDataScope != null and filter.projectIdsInDataScope.size() > 0">
                and a.id in
                <foreach collection="filter.projectIdsInDataScope" item="pId" open="(" separator="," close=")">
                    #{pId}
                </foreach>
            </if>
            <if test="filter.itemName != null">
                <bind name="itemNameLike" value="'%' + filter.itemName + '%'"/>
                AND item_name LIKE #{itemNameLike}
            </if>
            <if test="filter.itemNo != null">
                <bind name="itemNoLike" value="'%' + filter.itemNo + '%'"/>
                AND item_no LIKE #{itemNoLike}
            </if>
            <if test="filter.projectStatusList != null and filter.projectStatusList !=''">
                AND project_status IN
                <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitIntsStr">
                    <property name="intsStr" value="filter.projectStatusList"/>
                </include>
            </if>
            <if test="filter.businessId != null">
                AND a.business_id = #{filter.businessId}
            </if>
            <if test="filter.unitIds != null">
                AND a.unit_id IN
                <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitLongsStr">
                    <property name="longsStr" value="filter.unitIds"/>
                </include>
            </if>
            <if test="filter.managerUserName != null">
                <bind name="managerUserNameLike" value="'%' + filter.managerUserName + '%'"/>
                AND manager_user_name LIKE #{managerUserNameLike}
            </if>
            <if test="filter.managerUserId != null ">
                AND a.manager_user_id = #{filter.managerUserId}
            </if>
            <if test="filter.customerName != null">
                <bind name="customerNameLike" value="'%' + filter.customerName + '%'"/>
                AND (contract_customer LIKE #{customerNameLike} OR end_customer LIKE #{customerNameLike} OR
                customer_name LIKE #{customerNameLike})
            </if>
            <if test="filter.ironTriangleUserIds != null">
                AND (
                salesman_user_id IN
                <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitLongsStr">
                    <property name="longsStr" value="filter.ironTriangleUserIds"/>
                </include>
                OR
                pre_sale_user_id IN
                <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitLongsStr">
                    <property name="longsStr" value="filter.ironTriangleUserIds"/>
                </include>
                OR
                manager_user_id IN
                <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitLongsStr">
                    <property name="longsStr" value="filter.ironTriangleUserIds"/>
                </include>
                )
            </if>

            <if test="filter.itemNameOrItemNo != null and filter.itemNameOrItemNo != ''">
                <bind name="itemNameOrItemNoLike" value="'%' + filter.itemNameOrItemNo + '%'"/>
                AND (item_name LIKE #{itemNameOrItemNoLike} or item_no LIKE #{itemNameOrItemNoLike})
            </if>
            <if test="filter.projectTypeList != null">
                AND a.project_type IN
                <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitIntsStr">
                    <property name="intsStr" value="filter.projectTypeList"/>
                </include>
            </if>
            <if test="filter.businessDeptIds != null">
                AND a.business_department_id IN
                <foreach collection="filter.businessDeptIds" item="l" open="(" separator="," close=")">
                    #{l}
                </foreach>
            </if>
            <if test="filter.projectDeptIds != null">
                AND a.project_department_id IN
                <foreach collection="filter.projectDeptIds" item="l" open="(" separator="," close=")">
                    #{l}
                </foreach>
            </if>
            <if test="filter.businessManager != null and filter.businessManager != ''">
                <bind name="businessManagerLike" value="'%' + filter.businessManager + '%'"/>
                AND a.business_manager LIKE #{businessManagerLike}
            </if>
            <if test="filter.businessManagerId != null ">
                AND a.business_manager_id = #{filter.businessManagerId}
            </if>
            <if test="filter.projectApprovalStart != null and filter.projectApprovalStart != ''">
                AND a.project_date >= #{filter.projectApprovalStart}
            </if>

            <if test="filter.projectApprovalEnd != null and filter.projectApprovalEnd != ''">
                AND #{filter.projectApprovalEnd} >= a.project_date
            </if>
            <if test="filter.isNotInternalProject != null">
                AND a.is_not_internal_project = #{filter.isNotInternalProject}
            </if>

            <if test="filter.projectIds != null and filter.projectIds.size() > 0">
                AND a.id IN
                <foreach collection="filter.projectIds" item="projectId" open="(" close=")" separator=",">
                    #{projectId}
                </foreach>
            </if>
            <if test="filter.deliverType != null and filter.deliverType != '' ">
                AND a.deliver_type IN
                <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitIntsStr">
                    <property name="intsStr" value="filter.deliverType"/>
                </include>
            </if>
            <if test="filter.businessDepartment != null and filter.businessDepartment != '' ">
                AND a.business_department_id IN
                <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitLongsStr">
                    <property name="longsStr" value="filter.businessDepartment"/>
                </include>
            </if>
            <if test="filter.proDeliveryDepartment != null and filter.proDeliveryDepartment != '' ">
                AND a.pro_delivery_department_id IN
                <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitLongsStr">
                    <property name="longsStr" value="filter.proDeliveryDepartment"/>
                </include>
            </if>
            <if test="filter.secondaryBusinessType != null and filter.secondaryBusinessType != '' ">
                AND a.secondary_business_type IN
                <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitIntsStr">
                    <property name="intsStr" value="filter.secondaryBusinessType"/>
                </include>
            </if>
        </where>
        GROUP BY a.id
        ORDER BY a.id DESC
    </select>

    <select id="findListByProjectIds" resultType="com.gok.pboot.pms.entity.domain.ProjectInfo">
        select
        <include refid="baseColumnList"/>
        <where>
            <if test="filter.perm == 'none'">
                AND FALSE
            </if>
            <if test="filter.scope == null and filter.projectIdsInDataScope != null and filter.projectIdsInDataScope.size() > 0">
                AND a.id IN
                <foreach collection="filter.projectIdsInDataScope" item="pId" open="(" separator="," close=")">
                    #{pId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="isInternalProjectById" resultType="java.lang.Boolean">
        SELECT EXISTS(SELECT id
                      FROM project_info
                      WHERE id = #{id}
                        AND is_not_internal_project = 1)
    </select>

    <select id="findByProjectNameLike" resultType="com.gok.pboot.pms.entity.domain.ProjectInfo">
        SELECT
        <include refid="baseColumnList"/>
        FROM
        project_info a
        <where>
            <if test="filter.projectName != null and filter.projectName != '' ">
                AND item_name like concat('%',#{filter.projectName},'%')
            </if>
        </where>
    </select>

    <select id="findByIdListAndStatus" resultType="com.gok.pboot.pms.entity.domain.ProjectInfo">
        SELECT
        <include refid="baseColumnList"/>
        FROM
        project_info a
        <where>
            <if test="idList != null and idList.size() > 0">
                ANd id IN
                <foreach collection="idList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="statusList != null and statusList.size() > 0">
                ANd project_status IN
                <foreach collection="statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
        </where>
    </select>

    <select id="isExist" resultType="java.lang.Boolean">
        SELECT EXISTS(SELECT id
                      FROM project_info
                      WHERE id = #{id})
    </select>

    <!--    <select id="findByIdsForDailyPaperEntry" resultMap="projectInDailyPaperEntryMap">-->
    <!--        SELECT project.id AS id,-->
    <!--        project.project_status AS projectStatus,-->
    <!--        DATE_FORMAT(project.ctime, '%Y-%m-%d') AS ctime,-->
    <!--        project.item_name AS projectName,-->
    <!--        project.project_salesperson AS salesmanUserName,-->
    <!--        project.pre_sale_user_name AS preSalesmanUserName,-->
    <!--        project.manager_user_name AS managerUserName,-->
    <!--        project.is_not_internal_project AS isInsideProject,-->
    <!--        privilege.user_name AS auditorName-->
    <!--        FROM project_info project-->
    <!--        LEFT JOIN mhour_task mTask ON mTask.project_id = project.id-->
    <!--        LEFT JOIN project_task pTask ON pTask.project_id = project.id-->
    <!--        LEFT JOIN-->
    <!--            (-->
    <!--            SELECT project_id, user_name-->
    <!--            FROM mhour_privilege-->
    <!--            WHERE del_flag = 0-->
    <!--            AND privilege_type IN (3, 1)-->
    <!--        )-->
    <!--        privilege ON privilege.project_id = project.id-->
    <!--        WHERE (mTask.del_flag = 0 OR pTask.del_flag = 0)-->
    <!--        <if test="list != null and list.size() > 0">-->
    <!--            AND project.id IN-->
    <!--            <foreach collection="list" open="(" close=")" separator="," item="id">-->
    <!--                #{id}-->
    <!--            </foreach>-->
    <!--        </if>-->

    <!--    </select>-->
    <select id="findByIdsForDailyPaperEntry" resultMap="projectInDailyPaperEntryMap">
        SELECT project.id AS id,
        project.project_status AS projectStatus,
        DATE_FORMAT(project.ctime, '%Y-%m-%d') AS ctime,
        project.item_name AS projectName,
        project.project_salesperson AS salesmanUserName,
        project.pre_sale_user_name AS preSalesmanUserName,
        project.manager_user_name AS managerUserName,
        project.is_not_internal_project AS isInsideProject,
        privilege.user_name AS auditorName
        FROM project_info project
        LEFT JOIN project_taske pTask ON pTask.project_id = project.id
        LEFT JOIN
        (
        SELECT project_id, user_name
        FROM mhour_privilege
        WHERE del_flag = 0
        AND privilege_type IN (3, 1)
        )
        privilege ON privilege.project_id = project.id
        WHERE pTask.del_flag = 0
        <if test="list != null and list.size() > 0">
            AND project.id IN
            <foreach collection="list" open="(" close=")" separator="," item="id">
                #{id}
            </foreach>
        </if>

    </select>

    <select id="selectByProjectName" resultType="com.gok.pboot.pms.entity.domain.ProjectInfo">
        SELECT
        <include refid="baseColumnList"/>
        FROM
        project_info a
        <where>
            <if test="projectNames != null and projectNames.size() > 0">
                ANd item_name IN
                <foreach collection="projectNames" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findId" resultType="java.lang.Long">
        select distinct
        t.id
        from (
        SELECT DISTINCT pi.id
        FROM project_info AS pi
        LEFT JOIN customer_business_person AS cbp ON pi.business_id = cbp.business_id
        LEFT JOIN customer_business_unit AS cbu ON pi.unit_id = cbu.id
        <where>
            pi.item_name IS NOT NULL
            AND pi.item_no IS NOT NULL
            <if test='filter.scope != "all"'>
                AND (
                <!-- 客户（销售）经理 -->
                pi.salesman_user_id IN
                <foreach collection="filter.userIdList" item="uid" open="(" separator="," close=")">
                    #{uid}
                </foreach>
                <!-- 售前经理 -->
                OR pi.pre_sale_user_id IN
                <foreach collection="filter.userIdList" item="uid" open="(" separator="," close=")">
                    #{uid}
                </foreach>
                <!-- 项目经理 -->
                OR pi.manager_user_id IN
                <foreach collection="filter.userIdList" item="uid" open="(" separator="," close=")">
                    #{uid}
                </foreach>
                <!--所属客户经理-->
                OR cbu.unit_manager_id IN
                <foreach collection="filter.userIdList" item="uId" open="(" separator="," close=")">
                    #{uId}
                </foreach>
                <!--经营单元角色-->
                OR cbp.manager_id IN
                <foreach collection="filter.userIdList" item="uId" open="(" separator="," close=")">
                    #{uId}
                </foreach>
                )
            </if>
        </where>

        union all
        <!-- 项目所有干系人 -->
        select
        b.project_id
        from
        project_stakeholder_member b
        <where>
            <if test='filter.scope != "all"'>
                and b.member_id in
                <foreach collection="filter.userIdList" item="uid" open="(" separator="," close=")">
                    #{uid}
                </foreach>
                and b.del_flag = 0
            </if>
        </where>

        union all
        <!-- 项目任务负责人、参与人 -->
        select distinct
        c.project_id
        from
        project_taske c
        left join project_taske_user d on c.id = d.task_id
        <where>
            <if test='filter.scope != "all"'>
                and d.task_role in (0, 1)
                and d.user_id in
                <foreach collection="filter.userIdList" item="uid" open="(" separator="," close=")">
                    #{uid}
                </foreach>
                and d.del_flag = 0
            </if>
        </where>
        union all
        select distinct cpi.project_id from cost_personnel_information as cpi
        <where>
            <if test='filter.scope != "all"'>
                and cpi.personnel_attribute =
                ${@com.gok.pboot.pms.cost.enums.PersonnelAttributeEnum@GOK_STAFF.getValue()}
                and cpi.user_id in
                <foreach collection="filter.userIdList" item="uid" open="(" separator="," close=")">
                    #{uid}
                </foreach>
                and cpi.del_flag = 0
            </if>
        </where>
        ) t
    </select>

    <select id="findAllListByDept" resultType="java.lang.Long">
        SELECT
        a.id
        FROM project_info a
        LEFT JOIN project_stakeholder_member psm ON a.id = psm.project_id
        LEFT JOIN mhour_privilege mp ON a.id = mp.project_id
        where 1=1
        <if test="filter.userId != null and filter.scope != 'all'">
            AND
            (
            a.salesman_user_id=#{filter.userId} or
            a.pre_sale_user_id=#{filter.userId} or
            a.manager_user_id=#{filter.userId} or
            a.region_user_id=#{filter.userId} or
            a.director_user_id=#{filter.userId} or
            (psm.member_id=#{filter.userId} and psm.del_flag = 0) or
            (mp.user_id=#{filter.userId} and mp.del_flag = 0 and mp.privilege_type = 3)
            <if test="filter.deptList != null and filter.deptList.size() > 0">
                OR a.salesman_dept_id IN
                <foreach collection="filter.deptList" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                OR a.pre_sale_dept_id IN
                <foreach collection="filter.deptList" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                OR a.manager_dept_id IN
                <foreach collection="filter.deptList" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                OR a.region_dept_id IN
                <foreach collection="filter.deptList" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                OR a.director_dept_id IN
                <foreach collection="filter.deptList" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            )
        </if>
        group by a.id
    </select>

    <select id="findPageAboutPrivilege" resultType="com.gok.pboot.pms.entity.domain.ProjectInfo">
        SELECT
        <include refid="baseColumnList"/>
        FROM project_info a
        LEFT JOIN (
        SELECT
        mp.project_id AS project_id,
        group_concat( mp.user_name ) AS user_names
        FROM mhour_privilege mp
        WHERE mp.del_flag = 0
        GROUP BY mp.project_id
        ) mp2 ON a.id = mp2.project_id
        <where>
            1 = 1
            <if test="privilegeFindPageDTO.projectCode != null and  privilegeFindPageDTO.projectCode != ''">
                AND a.item_no like concat('%',#{privilegeFindPageDTO.projectCode},'%')
            </if>
            <if test="privilegeFindPageDTO.projectName != null and  privilegeFindPageDTO.projectName != '' ">
                AND a.item_name like concat('%',#{privilegeFindPageDTO.projectName},'%')
            </if>
            <if test="privilegeFindPageDTO.name != null and  privilegeFindPageDTO.name != '' ">
                AND( mp2.user_names like concat('%',#{privilegeFindPageDTO.name},'%')
                OR a.manager_user_name like concat('%',#{privilegeFindPageDTO.name},'%')
                OR a.project_salesperson like concat('%',#{privilegeFindPageDTO.name},'%')
                )
            </if>
        </where>
    </select>

    <select id="findIdAuditableByTriangleUserId" resultType="java.lang.Long">
        SELECT DISTINCT id
        FROM project_info
        WHERE (
        <!-- 商机、商机终止状态，售前、销售可审核 -->
        (
        project_status IN (
        ${@<EMAIL>},
        ${@<EMAIL>}
        )
        AND (
        salesman_user_id = #{userId}
        OR pre_sale_user_id = #{userId}
        )
        )
        <!-- 项目经理可审核 -->
        OR manager_user_id = #{userId}
        )
    </select>

    <select id="findByIdsOrderByItemName" resultType="com.gok.pboot.pms.entity.domain.ProjectInfo">
        SELECT
        <include refid="baseColumnList"/>
        FROM project_info a
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY FIELD (id,
        <foreach collection="ids" item="idOrder" separator=",">
            #{idOrder}
        </foreach>
        )
    </select>

    <select id="findByUserIdForDailyPaperEntry" resultType="com.gok.pboot.pms.entity.domain.ProjectInfo">
        SELECT project.id,
               project.project_status,
               DATE_FORMAT(project.ctime, '%Y-%m-%d') AS ctime,
               project.item_name,
               project.project_salesperson,
               project.pre_sale_user_name,
               project.manager_user_name,
               project.is_not_internal_project
        FROM project_info project
                 INNER JOIN project_taske task ON task.project_id = project.id
                 INNER JOIN project_taske_user tu ON tu.task_id = task.id
        WHERE tu.user_id = #{userId}
          AND tu.task_role = ${@<EMAIL>()}
          AND (task.expect_start_date &lt;= #{date} OR task.permanent_flag = 1)
          AND tu.del_flag = 0
          AND task.del_flag = 0
    </select>


    <select id="findReuseAndDeliveryReview"
            resultType="com.gok.pboot.pms.entity.bo.DailyReviewReuseAndDeliveryProjectBO"
    >
        SELECT
        r.`projectId` AS projectId,
        r.`projectName` AS projectName,
        COUNT(r.`personnelId`) AS approvalNum,
        SUM(r.`aggregatedDays`) AS aggregatedDays
        FROM (
        SELECT
        pi.id AS projectId,
        pi.item_name AS projectName,
        pr.id AS personnelId,
        pr.aggregated_days AS aggregatedDays
        FROM project_info pi
        LEFT JOIN mhour_personnel_reuse pr ON pi.id = pr.project_id
        LEFT JOIN mhour_filing f ON
        f.`year` = DATE_FORMAT(pr.reuse_date, '%Y')
        AND
        f.`month` = DATE_FORMAT(pr.reuse_date, '%m')
        WHERE
        pr.del_flag = 0
        AND
        f.del_flag = 0
        AND
        f.filed != ${@<EMAIL>()}
        <if test="filter.projectIds != null and filter.projectIds.size > 0">
            AND pi.id IN
            <foreach collection="filter.projectIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="filter.approvalStatus != null">
            AND pr.approval_status = #{filter.approvalStatus}
        </if>
        <if test="filter.projectName != null and filter.projectName != ''">
            <bind name="projectNameLike" value="'%' + filter.projectName + '%'"/>
            AND pi.item_name LIKE #{projectNameLike}
        </if>

        UNION ALL

        SELECT
        pi.id AS projectId,
        pi.item_name AS projectName,
        pdh.id AS personnelId,
        pdh.project_consumed AS aggregatedDays
        FROM project_info pi
        LEFT JOIN mhour_personnel_delivery_hour pdh ON pi.id = pdh.project_id
        LEFT JOIN mhour_filing f ON
        f.`year` = DATE_FORMAT(pdh.reuse_date, '%Y')
        AND
        f.`month` = DATE_FORMAT(pdh.reuse_date, '%m')
        WHERE
        pdh.del_flag = 0
        AND
        f.del_flag = 0
        AND
        f.filed != ${@<EMAIL>()}
        <if test="filter.projectIds != null and filter.projectIds.size > 0">
            AND pi.id IN
            <foreach collection="filter.projectIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="filter.approvalStatus != null">
            AND pdh.approval_status = #{filter.approvalStatus}
        </if>
        <if test="filter.projectName != null and filter.projectName != ''">
            <bind name="projectNameLike" value="'%' + filter.projectName + '%'"/>
            AND pi.item_name LIKE #{projectNameLike}
        </if>
        ) r
        GROUP BY r.`projectId`
    </select>

    <update id="updateInitTaskFlag">
        update project_info
        set init_task_flag = #{flag}
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateAutoFinishFag">
        update project_info
        set auto_finish_flag = #{flag}
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="findListByIds" resultType="com.gok.pboot.pms.entity.domain.ProjectInfo">
        SELECT
        <include refid="baseColumnList"/>
        FROM project_info a
        WHERE a.id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="findAll" resultType="com.gok.pboot.pms.entity.domain.ProjectInfo">
        SELECT
        <include refid="baseColumnList"/>
        FROM project_info a
    </select>

    <select id="findToSyncMember" resultType="com.gok.pboot.pms.entity.domain.ProjectStakeholderMember">
        SELECT p.id                as projectId,
               p.manager_user_id   as memberId,
               p.manager_user_name as memberName,
               2                   as roleType
        FROM project_info p
        WHERE p.manager_user_id IS NOT NULL
        GROUP BY p.id;
    </select>

    <select id="findToSyncMember2" resultType="com.gok.pboot.pms.entity.domain.ProjectStakeholderMember">
        SELECT p.id                 as projectId,
               p.pre_sale_user_id   as memberId,
               p.pre_sale_user_name as memberName,
               1                    as roleType
        FROM project_info p
        WHERE p.pre_sale_user_id IS NOT NULL
        GROUP BY p.id;
    </select>

    <select id="findToSyncMember3" resultType="com.gok.pboot.pms.entity.domain.ProjectStakeholderMember">
        SELECT p.id                  as projectId,
               p.salesman_user_id    as memberId,
               p.project_salesperson as memberName,
               0                     as roleType
        FROM project_info p
        WHERE p.salesman_user_id IS NOT NULL
        GROUP BY p.id;
    </select>

    <select id="findIdByInsideProjects" resultType="java.lang.Long">
        SELECT id
        FROM project_info
        WHERE TRUE
        <if test="insideProjects != null">
            AND is_not_internal_project IN
            <foreach collection="insideProjects" item="insideProject" open="(" separator="," close=")">
                #{insideProject}
            </foreach>
        </if>
    </select>

    <select id="countByUnitId" resultType="java.lang.Integer">
        select count(*) from project_info where unit_id = #{unitId}
        <if test="projectStatus != null">
            AND project_status = #{projectStatus}
        </if>
    </select>

    <select id="getCompletedProjectsWithoutSatisfactionSurvey"
            resultType="com.gok.pboot.pms.eval.entity.vo.EvalSatisfactionSurveyProjectVo">
        SELECT
            pi.id AS projectId,
            pi.item_name AS projectName,
            pi.project_salesperson,
            pi.salesman_user_id,
            psc.id AS stakeholderId,
            psc.contact AS customerName,
            psc.contact_phone
        FROM
            project_info pi
                LEFT JOIN project_stakeholder_customer psc ON pi.id = psc.project_id
                AND psc.del_flag = 0
        WHERE
            pi.project_status = 6
          AND psc.satisfaction_survey = 1
          AND NOT EXISTS (
            SELECT
                1
            FROM
                eval_customer_satisfaction_survey s
            WHERE
                s.project_id = pi.id
              AND s.del_flag = 0)
          AND NOT EXISTS (
            SELECT
                1
            FROM
                eval_end_project p
            WHERE
                p.project_id = pi.id)
    </select>

    <select id="getProjectIdScope" resultType="java.lang.Long">
        SELECT pi.id
        FROM project_info pi
        WHERE pi.salesman_user_id = #{userId}

        UNION

        SELECT pi.id
        FROM project_info pi
        WHERE pi.manager_user_id = #{userId}

        UNION

        SELECT pi.id
        FROM project_info pi
                 INNER JOIN customer_business_person cbp ON pi.business_id = cbp.business_id
        WHERE cbp.manager_role = 3 AND cbp.manager_id = #{userId}

    </select>
</mapper>
