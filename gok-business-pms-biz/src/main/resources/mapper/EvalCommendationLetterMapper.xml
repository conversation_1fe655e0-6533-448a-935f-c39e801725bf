<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.eval.mapper.EvalCommendationLetterMapper">

    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.eval.entity.domain.EvalCommendationLetter">
        <id column="id" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="commendation_letter" property="commendationLetter"/>
        <result column="creator" property="creator"/>
        <result column="creator_id" property="creatorId"/>
        <result column="modifier" property="modifier"/>
        <result column="modifier_id" property="modifierId"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <select id="getByProjectId" resultType="com.gok.pboot.pms.eval.entity.domain.EvalCommendationLetter">
        SELECT
            *
        FROM
            eval_commendation_letter AS ecl
        WHERE
            ecl.del_flag = ${@<EMAIL>()}
          AND ecl.project_id = #{projectId}
    </select>

</mapper>