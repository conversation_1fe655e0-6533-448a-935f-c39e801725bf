<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.TaskUserMapper">


        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.domain.TaskUser">
            <id column="id" property="id"/>
            <result column="taskId" property="taskId"/>
            <result column="userId" property="userId"/>
            <result column="userName" property="userName"/>
            <result column="creator" property="creator"/>
            <result column="creatorId" property="creatorId"/>
            <result column="modifier" property="modifier"/>
            <result column="modifierId" property="modifierId"/>
            <result column="ctime" property="ctime"/>
            <result column="mtime" property="mtime"/>
            <result column="delFlag" property="delFlag"/>
        </resultMap>


        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
					a.id AS 'id',
					a.task_id AS 'taskId',
					a.user_id AS 'userId',
                    a.user_name AS 'userName',
					a.creator AS 'creator',
					a.creator_id AS 'creatorId',
					a.modifier AS 'modifier',
					a.modifier_id AS 'modifierId',
					a.ctime AS 'ctime',
					a.mtime AS 'mtime',
                    a.del_flag AS 'delFlag'
        </sql>
    
    <sql id="join"></sql>

    <select id="findList" resultType="com.gok.pboot.pms.entity.domain.TaskUser">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_task_user a
        <include refid="join"/>
        <where>
            a.del_flag = 0
        </where>
        ORDER BY a.id desc
    </select>

    <select id="findAllUserIdByProjectIds" resultType="java.lang.Long">
        SELECT tu.user_id
        FROM mhour_task_user tu
        LEFT JOIN mhour_task t
        ON t.id = tu.task_id
        LEFT JOIN mhour_project p
        ON p.id = t.project_id
        <where>
            t.del_flag = 0
            AND tu.del_flag = 0
            AND p.del_flag = 0
            AND p.id IN
            <foreach collection="projectIds" item="pId" open="(" close=")" separator=",">
                #{pId}
            </foreach>
        </where>
    </select>

    <select id="findByTaskId" resultType="com.gok.pboot.pms.entity.domain.TaskUser">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_task_user a
        WHERE
            a.del_flag = 0
            AND a.task_id = #{taskId}
    </select>

    <!--逻辑删除-->
    <update id="deleteByLogic">
        UPDATE mhour_task_user SET
        del_flag = 1
        WHERE id = #{id}
    </update>


    <insert id="batchSave">
        INSERT INTO mhour_task_user (
                id,
                task_id,
                user_id,
                user_name,
                creator,
                creator_id,
                modifier,
                modifier_id,
                ctime,
                mtime,
                del_flag
        )VALUES
        <foreach collection="poList" item="item" separator=",">
            (
                    #{item.id},
                    #{item.taskId},
                    #{item.userId},
                     #{item.userName},
                    #{item.creator},
                    #{item.creatorId},
                    #{item.modifier},
                    #{item.modifierId},
                    #{item.ctime},
                    #{item.mtime},
                    #{item.delFlag}
            )
        </foreach>
    </insert>

    <update id="batchDel">
        update mhour_task_user set del_flag = 1 where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <insert id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE mhour_task_user  SET
                    id = #{item.id},
                    task_id = #{item.taskId},
                    user_id = #{item.userId},
                    user_name = #{item.userName},
                    creator = #{item.creator},
                    creator_id = #{item.creatorId},
                    modifier = #{item.modifier},
                    modifier_id = #{item.modifierId},
                    ctime = #{item.ctime},
                    mtime = #{item.mtime},
                    del_flag = #{item.delFlag}
            where id = #{item.id}
        </foreach>
    </insert>

    <!--批量更新非空字段-->
    <update id="updateBatch" parameterType="arraylist">
        update mhour_task_user
        <trim prefix="set" suffixOverrides=",">
                    <trim prefix=" task_id =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.taskId!=null">
                                when id=#{item.id} then #{item.taskId}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" user_id =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.userId!=null">
                                when id=#{item.id} then #{item.userId}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" user_id =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.userName!=null">
                                when id=#{item.id} then #{item.userName}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" modifier =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.modifier!=null">
                                when id=#{item.id} then #{item.modifier}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" modifier_id =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.modifierId!=null">
                                when id=#{item.id} then #{item.modifierId}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" mtime =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.mtime!=null">
                                when id=#{item.id} then #{item.mtime}
                            </if>
                        </foreach>
                    </trim>
        </trim>

        where
        id in
        <foreach collection="list" separator="," item="item" open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="deleteBatchUserIds">
        update mhour_task_user set del_flag = 1 where user_id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND task_id = #{taskId}
    </update>
</mapper>
