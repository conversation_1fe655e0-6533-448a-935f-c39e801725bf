<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gok.pboot.pms.mapper.EntityOptionMapper">
    <sql id="baseColumn">
        eo.id AS 'id',
        eo.entity_id AS 'entityId',
        eo.sign AS 'sign',
        eo.creator AS 'creator',
        eo.creator_id AS 'creatorId',
        eo.modifier AS 'modifier',
        eo.modifier_id AS 'modifierId',
        eo.ctime AS 'ctime',
        eo.mtime AS 'mtime',
        eo.del_flag AS 'delFlag'
    </sql>

    <select id="existsByEntityIdAndSign" resultType="java.lang.Boolean">
        SELECT IF(COUNT(id)>0, true, false)
        FROM mhour_entity_option eo
        WHERE
            del_flag = 0
          AND
            sign = #{sign}
          AND
            entity_id = #{entityId}
    </select>

    <select id="findBySign" resultType="com.gok.pboot.pms.entity.EntityOption">
        SELECT
            <include refid="baseColumn"/>
        FROM mhour_entity_option eo
        WHERE
            del_flag = 0
        AND
            sign = #{sign}
    </select>

    <select id="findIdSetBySign" resultType="java.lang.Long">
        SELECT
            entity_id
        FROM mhour_entity_option
        WHERE
            del_flag = 0
        AND
            sign = #{sign}
    </select>

    <select id="findIdsBySign" resultType="java.lang.Long">
        SELECT
            entity_id
        FROM mhour_entity_option
        WHERE
            del_flag = 0
        AND
            sign = #{sign}
    </select>


</mapper>