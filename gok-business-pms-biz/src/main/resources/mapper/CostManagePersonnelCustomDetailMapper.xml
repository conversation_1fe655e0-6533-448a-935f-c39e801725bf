<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostManagePersonnelCustomDetailMapper">


    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.cost.entity.domain.CostManagePersonnelCustomDetail">
        <id column="id" property="id"/>
        <result column="version_id" property="versionId"/>
        <result column="estimation_results_id" property="estimationResultsId"/>
        <result column="subsidy_custom_config_id" property="subsidyCustomConfigId"/>
        <result column="subsidy_custom_name" property="subsidyCustomName"/>
        <result column="subsidy_custom_amount" property="subsidyCustomAmount"/>
        <result column="creator" property="creator"/>
        <result column="creator_id" property="creatorId"/>
        <result column="modifier" property="modifier"/>
        <result column="modifier_id" property="modifierId"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , version_id, estimation_results_id, subsidy_custom_config_id, subsidy_custom_name, subsidy_custom_amount, creator, creator_id, modifier, modifier_id, ctime, mtime, del_flag
    </sql>

    <insert id="batchSave">
        INSERT INTO cost_manage_personnel_custom_detail (
        id,
        version_id,
        estimation_results_id,
        subsidy_custom_config_id,
        subsidy_custom_name,
        subsidy_custom_amount,
        creator,
        creator_id,
        modifier,
        modifier_id,
        ctime,
        mtime,
        del_flag
        ) VALUES
        <foreach collection="saveEntries" item="item" separator=",">
            (
            #{item.id},
            #{item.versionId},
            #{item.estimationResultsId},
            #{item.subsidyCustomConfigId},
            #{item.subsidyCustomName},
            #{item.subsidyCustomAmount},
            #{item.creator},
            #{item.creatorId},
            #{item.modifier},
            #{item.modifierId},
            #{item.ctime},
            #{item.mtime},
            #{item.delFlag}
            )
        </foreach>
    </insert>

    <select id="findByEstimateResultIdList"
            resultType="com.gok.pboot.pms.cost.entity.vo.CostManagePersonnelCustomDetailVO">
        SELECT
            *
        FROM
            cost_manage_personnel_custom_detail
        WHERE
            estimation_results_id IN
            <foreach collection="estimateResultIdList" item="estimateResultId" index="index" open="(" close=")" separator=",">
                #{estimateResultId}
            </foreach>
            AND del_flag = ${@<EMAIL>()}
    </select>

</mapper>
