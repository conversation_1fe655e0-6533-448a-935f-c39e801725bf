<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gok.pboot.pms.mapper.ProjectEstimatedCostMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.gok.pboot.pms.entity.domain.ProjectEstimatedCost" id="projectEstimatedCostMap">
        <result property="id" column="id"/>
        <result property="projectId" column="project_id"/>
        <result property="costExpenseItems" column="cost_expense_items"/>
        <result property="costExpenseItemsValue" column="cost_expense_items_value"/>
        <result property="subjectCode" column="subject_code"/>
        <result property="subjectName" column="subject_name"/>
        <result property="budgetAmount" column="budget_amount"/>
        <result property="budgetAmountNoTax" column="budget_amount_no_tax"/>
        <result property="taxRate" column="tax_rate"/>
        <result property="taxRateValue" column="tax_rate_value"/>
        <result property="budgetManDays" column="budget_man_days"/>
        <result property="costExplanation" column="cost_explanation"/>
        <result property="createDate" column="create_date"/>
    </resultMap>

    <select id="findPage" resultMap="projectEstimatedCostMap">
        SELECT
        *
        FROM project_estimated_cost a
        <where>
            <if test="filter.projectId != null and filter.projectId != '' ">
                AND a.project_id = #{filter.projectId}
            </if>
        </where>
        ORDER BY a.create_date DESC
    </select>

</mapper>