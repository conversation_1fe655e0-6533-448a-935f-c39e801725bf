<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostConfigVersionMapper">



    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.cost.entity.domain.CostConfigVersion">
        <id column="id" property="id" />
        <result column="version_type" property="versionType" />
        <result column="version_name" property="versionName" />
        <result column="version_status" property="versionStatus" />
        <result column="operator_id" property="operatorId" />
        <result column="operator_name" property="operatorName" />
        <result column="operator_dept_id" property="operatorDeptId" />
        <result column="operator_dept_name" property="operatorDeptName" />
        <result column="creator" property="creator" />
        <result column="creator_id" property="creatorId" />
        <result column="modifier" property="modifier" />
        <result column="modifier_id" property="modifierId" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <select id="getMaxVersionNum" resultType="String">
        SELECT
            MAX(DISTINCT version_name) as version_name
        FROM cost_config_version
        WHERE version_type = #{versionTypeEnum.value}
        AND CAST(SUBSTRING_INDEX(version_name, '-', 1) AS UNSIGNED) REGEXP '^[0-9]$'
        FOR UPDATE
    </select>

    <select id="findPage" resultType="com.gok.pboot.pms.cost.entity.vo.VersionHistoryVO">
        SELECT
            id AS versionId,
            version_name,
            ctime AS mtime,
            operator_dept_name,
            operator_name
        FROM
            cost_config_version
        WHERE del_flag = 0
        AND version_type = #{versionTypeEnum.value}
        ORDER BY
            ctime DESC
    </select>
</mapper>
