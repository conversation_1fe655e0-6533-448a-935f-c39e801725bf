<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostIncomeSettlementDetailMapper">
  <resultMap id="BaseResultMap" type="com.gok.pboot.pms.cost.entity.domain.CostIncomeSettlementDetail">
    <!--@mbg.generated-->
    <!--@Table cost_income_settlement_detail-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="end_date" jdbcType="DATE" property="endDate" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="work_code" jdbcType="VARCHAR" property="workCode" />
    <result column="cost_income_calculation_id" jdbcType="BIGINT" property="costIncomeCalculationId" />
    <result column="cost_income_calculation_detail_id" jdbcType="BIGINT" property="costIncomeCalculationDetailId" />
    <result column="operation_settlement_date" jdbcType="TIMESTAMP" property="operationSettlementDate" />
    <result column="estimated_inclusive_amount_tax" jdbcType="DECIMAL" property="estimatedInclusiveAmountTax" />
    <result column="budget_amount_included_tax" jdbcType="DECIMAL" property="budgetAmountIncludedTax" />
    <result column="tax_rate" jdbcType="VARCHAR" property="taxRate" />
    <result column="budget_amount_excluding_tax" jdbcType="DECIMAL" property="budgetAmountExcludingTax" />
    <result column="remarks_desc" jdbcType="VARCHAR" property="remarksDesc" />
    <result column="filing_time" jdbcType="VARCHAR" property="filingTime" />
    <result column="request_id" jdbcType="BIGINT" property="requestId" />
    <result column="request_number" jdbcType="VARCHAR" property="requestNumber" />
    <result column="settlement_details_number" jdbcType="VARCHAR" property="settlementDetailsNumber" />
    <result column="settlement_number" jdbcType="VARCHAR" property="settlementNumber" />
    <result column="data_sources" jdbcType="TINYINT" property="dataSources" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, project_id, start_date, end_date, user_name, work_code,cost_income_calculation_id, cost_income_calculation_detail_id,
    operation_settlement_date, estimated_inclusive_amount_tax, budget_amount_included_tax,
    tax_rate, budget_amount_excluding_tax, remarks_desc, filing_time, request_id, request_number,
    settlement_details_number, settlement_number, data_sources, creator, creator_id,
    modifier, modifier_id, ctime, mtime, del_flag
  </sql>

    <select id="selList" resultType="com.gok.pboot.pms.cost.entity.vo.CostIncomeSettlementDetailVO">
        select id,
               project_id,
               start_date,
               end_date,
               user_name,
               work_code,
               cost_income_calculation_id,
               cost_income_calculation_detail_id,
               operation_settlement_date,
               estimated_inclusive_amount_tax,
               budget_amount_included_tax,
               tax_rate,
               budget_amount_excluding_tax,
               remarks_desc,
               filing_time,
               request_id,
               request_number,
               settlement_details_number,
               settlement_number,
               data_sources
        from cost_income_settlement_detail
                where
                del_flag = '0'
        <if test="dto.projectId != null">
            and project_id = #{dto.projectId}
        </if>
        <if test="dto.startDate != null and dto.startDate != ''">
            and start_date <![CDATA[ >= ]]> #{dto.startDate}
        </if>
        <if test="dto.endDate != null and param1.endDate != ''">
            and end_date <![CDATA[ <= ]]> #{dto.endDate}
        </if>
        <if test="dto.userName != null and dto.userName != ''">
            and (user_name like concat('%', #{dto.userName}, '%')
                    or work_code like concat('%', #{dto.userName}, '%')
                    or settlement_details_number like concat('%', #{dto.userName}, '%')
                    or settlement_number like concat('%', #{dto.userName}, '%'))
        </if>
        <if test="dto.taxRateList != null and dto.taxRateList.size() > 0">
            and tax_rate in
            <foreach collection="dto.taxRateList" item="taxRate" open="(" separator="," close=")">
                #{taxRate}
            </foreach>
        </if>
        <if test="dto.detailsNumberList != null and dto.detailsNumberList.size() > 0">
            and (settlement_details_number in
            <foreach collection="dto.detailsNumberList" item="detailsNumber" open="(" separator="," close=")">
                #{detailsNumber}
            </foreach>
            or settlement_number in
            <foreach collection="dto.detailsNumberList" item="detailsNumber" open="(" separator="," close=")">
                #{detailsNumber}
            </foreach>
            )
        </if>
        <if test="dto.numberList != null and dto.numberList.size() > 0">
            and settlement_number in
            <foreach collection="dto.numberList" item="number" open="(" separator="," close=")">
                #{number}
            </foreach>
        </if>
        <if test="dto.costIncomeCalculationDetailIds != null and dto.costIncomeCalculationDetailIds.size() > 0">
            and cost_income_calculation_detail_id in
            <foreach collection="dto.costIncomeCalculationDetailIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="dto.settlementNumber != null and dto.settlementNumber != ''">
            AND (settlement_number = #{dto.settlementNumber} or settlement_details_number = #{dto.settlementNumber})
        </if>
        <if test="dto.purviewNames != null and dto.purviewNames.size() != 0">
          and (user_name in
          <foreach collection="dto.purviewNames" item="name" open="(" separator="," close=")">
              #{name}
          </foreach>
          or creator in
          <foreach collection="dto.purviewNames" item="name" open="(" separator="," close=")">
              #{name}
          </foreach>
          )
      </if>
        order by start_date desc,settlement_number desc,settlement_details_number desc
    </select>

    <delete id="delByIds">
        update cost_income_settlement_detail
        set del_flag = 1
                where del_flag = '0'
        <if test="idList != null and idList.size() != 0">
            and id in
            <foreach collection="idList" close=")" open="(" item="id" separator=",">
                #{id}
            </foreach>
        </if>
    </delete>

    <update id="batchUpdate">
        <foreach collection="updateEntries" item="item" separator=";">
            UPDATE cost_income_settlement_detail
            SET project_id                        = #{item.projectId},
                start_date                        = #{item.startDate},
                end_date                          = #{item.endDate},
                user_name                         = #{item.userName},
                work_code                         = #{item.workCode},
                cost_income_calculation_id        = #{item.costIncomeCalculationId},
                cost_income_calculation_detail_id = #{item.costIncomeCalculationDetailId},
                operation_settlement_date         = #{item.operationSettlementDate},
                estimated_inclusive_amount_tax    = #{item.estimatedInclusiveAmountTax},
                budget_amount_included_tax        = #{item.budgetAmountIncludedTax},
                tax_rate                          = #{item.taxRate},
                budget_amount_excluding_tax       = #{item.budgetAmountExcludingTax},
                remarks_desc                      = #{item.remarksDesc},
                filing_time                       = #{item.filingTime},
                request_id                        = #{item.requestId},
                request_number                    = #{item.requestNumber},
                settlement_details_number         = #{item.settlementDetailsNumber},
                settlement_number                 = #{item.settlementNumber},
                data_sources                      = #{item.dataSources},
                modifier                          = #{item.modifier},
                modifier_id                       = #{item.modifierId},
                mtime                             = #{item.mtime},
                del_flag                       = #{item.delFlag}
            where id = #{item.id}
        </foreach>
    </update>
    <select id="selListForCalculation"
            resultType="com.gok.pboot.pms.cost.entity.vo.CostIncomeSettlementDetailVO">
        SELECT
            a.cost_income_calculation_id,
            a.settlement_number,
            DATE_FORMAT( a.operation_settlement_date, '%Y-%m-%d' ) AS operation_settlement_date
        FROM
            cost_income_settlement_detail AS a
        WHERE
            del_flag = ${@<EMAIL>()}
            <if test="dto.costIncomeCalculationIds != null and dto.costIncomeCalculationIds.size() > 0">
                AND a.cost_income_calculation_id IN
                <foreach collection="dto.costIncomeCalculationIds" close=")" open="(" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
        GROUP BY
            a.cost_income_calculation_id,
            a.settlement_number,
            DATE_FORMAT(
                    a.operation_settlement_date,
                    '%Y-%m-%d')
        ORDER BY
            a.operation_settlement_date DESC
    </select>
</mapper>