<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostBaselineVersionRecordMapper">

    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.cost.entity.domain.CostBaselineVersionRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="projectId" column="project_id" jdbcType="BIGINT"/>
            <result property="versionName" column="version_name" jdbcType="VARCHAR"/>
            <result property="quotationVersionId" column="quotation_version_id" jdbcType="BIGINT"/>
            <result property="quotationVersionName" column="quotation_version_name" jdbcType="VARCHAR"/>
            <result property="targetVersionId" column="target_version_id" jdbcType="BIGINT"/>
            <result property="targetVersionName" column="target_version_name" jdbcType="VARCHAR"/>
            <result property="costVersionId" column="cost_version_id" jdbcType="BIGINT"/>
            <result property="costVersionName" column="cost_version_name" jdbcType="VARCHAR"/>
            <result property="cashPlanVersionId" column="cash_plan_version_id" jdbcType="BIGINT"/>
            <result property="cashPlanVersionName" column="cash_plan_version_name" jdbcType="VARCHAR"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifierId" column="modifier_id" jdbcType="BIGINT"/>
            <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
            <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,project_id,version_name,
        quotation_version_id,quotation_version_name,target_version_id,
        target_version_name,cost_version_id,cost_version_name,
        plan_version_id,plan_version_name,cash_plan_version_id,cash_plan_version_name,creator,
        creator_id,modifier,modifier_id,
        ctime,mtime,del_flag
    </sql>

    <select id="getVersionRecordPage"
            resultType="com.gok.pboot.pms.cost.entity.vo.CostBaselineVersionRecordVO">
        SELECT
            cbr.project_id,
            cbr.version_name,
            cbr.quotation_version_id,
            cbr.quotation_version_name,
            cbr.target_version_id,
            cbr.target_version_name,
            cbr.cost_version_id,
            cbr.cost_version_name,
            cbr.cash_plan_version_id,
            cbr.cash_plan_version_name,
            cbr.ctime AS updateTime
            FROM cost_baseline_version_record cbr
            WHERE cbr.del_flag = 0
                AND cbr.project_id = #{projectId}
            ORDER BY cbr.ctime DESC
    </select>

    <select id="getCostChangeByProjectIds"
            resultType="com.gok.pboot.pms.cost.entity.domain.CostBaselineVersionRecord">
        SELECT
            bvr.project_id,
            mv.request_id
        FROM
            cost_baseline_version_record AS bvr
                LEFT JOIN cost_manage_version AS mv ON bvr.cost_version_id = mv.id
                AND mv.request_id IS NOT NULL
                AND mv.version_type = 1
                AND mv.request_type = 3
        WHERE
            bvr.del_flag = 0
            AND bvr.project_id IN
            <foreach collection="projectIds" item="projectId" open="(" separator="," close=")">
                #{projectId}
            </foreach>
    </select>

</mapper>
