<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.ConfirmMapper">


    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.Confirm">
        <id column="id" property="id"/>
        <result column="deptId" property="deptId"/>
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="confirmStartDatetime" property="confirmStartDatetime"/>
        <result column="confirmEndDatetime" property="confirmEndDatetime"/>
        <result column="creator" property="creator"/>
        <result column="creatorId" property="creatorId"/>
        <result column="modifier" property="modifier"/>
        <result column="modifierId" property="modifierId"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="delFlag" property="delFlag"/>
    </resultMap>


        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
					a.id AS 'id',
                    a.dept_id AS 'deptId',
					a.year AS 'year',
					a.month AS 'month',
					a.confirm_start_datetime AS 'confirmStartDatetime',
					a.confirm_end_datetime AS 'confirmEndDatetime',
					a.creator AS 'creator',
					a.creator_id AS 'creatorId',
					a.modifier AS 'modifier',
					a.modifier_id AS 'modifierId',
					a.ctime AS 'ctime',
					a.mtime AS 'mtime',
                    a.del_flag AS 'delFlag'
        </sql>
    
    <sql id="join"></sql>

    <select id="findList" resultType="com.gok.pboot.pms.entity.Confirm">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_confirm a
        <include refid="join"/>
        <where>
            a.del_flag = 0
        </where>
        ORDER BY a.id desc
    </select>

    <select id="existsByYearAndMonth" resultType="java.lang.Boolean">
        SELECT COUNT(*)
        FROM mhour_confirm
        WHERE
            del_flag = 0
        AND
            year = #{year}
        AND
            month = #{month}
        AND
            dept_id = #{deptId}
    </select>

    <!--逻辑删除-->
    <update id="deleteByLogic">
        UPDATE mhour_confirm SET
        del_flag = 1
        WHERE id = #{id}
    </update>


    <insert id="batchSave">
        INSERT INTO mhour_confirm (
                id,
                dept_id,
                year,
                month,
                confirm_start_datetime,
                confirm_end_datetime,
                creator,
                creator_id,
                modifier,
                modifier_id,
                ctime,
                mtime,
                del_flag
        )VALUES
        <foreach collection="poList" item="item" separator=",">
            (
                    #{item.id},
                    #{item.deptId},
                    #{item.year},
                    #{item.month},
                    #{item.confirmStartDatetime},
                    #{item.confirmEndDatetime},
                    #{item.creator},
                    #{item.creatorId},
                    #{item.modifier},
                    #{item.modifierId},
                    #{item.ctime},
                    #{item.mtime},
                    #{item.delFlag}
            )
        </foreach>
    </insert>

    <update id="batchDel">
        update mhour_confirm set del_flag = 1 where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <insert id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE mhour_confirm  SET
                    id = #{item.id},
                    dept_id = #{item.deptId},
                    year = #{item.year},
                    month = #{item.month},
                    confirm_start_datetime = #{item.confirmStartDatetime},
                    confirm_end_datetime = #{item.confirmEndDatetime},
                    creator = #{item.creator},
                    creator_id = #{item.creatorId},
                    modifier = #{item.modifier},
                    modifier_id = #{item.modifierId},
                    ctime = #{item.ctime},
                    mtime = #{item.mtime},
                    del_flag = #{item.delFlag}
            where id = #{item.id}
        </foreach>
    </insert>

    <!--批量更新非空字段-->
    <update id="updateBatch" parameterType="arraylist">
        update mhour_confirm
        <trim prefix="set" suffixOverrides=",">
                    <trim prefix=" year =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.year!=null">
                                when id=#{item.id} then #{item.year}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" month =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.month!=null">
                                when id=#{item.id} then #{item.month}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" confirm_start_datetime =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.confirmStartDatetime!=null">
                                when id=#{item.id} then #{item.confirmStartDatetime}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" confirm_end_datetime =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.confirmEndDatetime!=null">
                                when id=#{item.id} then #{item.confirmEndDatetime}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" modifier =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.modifier!=null">
                                when id=#{item.id} then #{item.modifier}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" modifier_id =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.modifierId!=null">
                                when id=#{item.id} then #{item.modifierId}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" mtime =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.mtime!=null">
                                when id=#{item.id} then #{item.mtime}
                            </if>
                        </foreach>
                    </trim>
        </trim>

        where
        id in
        <foreach collection="list" separator="," item="item" open="(" close=")">
            #{item.id}
        </foreach>
    </update>

</mapper>
