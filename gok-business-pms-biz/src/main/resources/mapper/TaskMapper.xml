<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.TaskMapper">


        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.domain.Task">
            <id column="id" property="id"/>
            <result column="taskName" property="taskName"/>
            <result column="taskType" property="taskType"/>
            <result column="taskStatus" property="taskStatus"/>
            <result column="projectId" property="projectId"/>
            <result column="creator" property="creator"/>
            <result column="creatorId" property="creatorId"/>
            <result column="modifier" property="modifier"/>
            <result column="modifierId" property="modifierId"/>
            <result column="ctime" property="ctime"/>
            <result column="mtime" property="mtime"/>
            <result column="delFlag" property="delFlag"/>
        </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMapTaskInfoVo" type="com.gok.pboot.pms.entity.vo.TaskInfoVO">
        <id column="id" property="id"/>
        <result column="taskName" property="taskName"/>
        <result column="taskType" property="taskType"/>
        <result column="taskTypeName" property="taskTypeName"/>
        <result column="taskStatus" property="taskStatus"/>
        <result column="taskStatusName" property="taskStatusName"/>
        <result column="projectId" property="projectId"/>
        <result column="actualHour" property="actualHour"/>
        <result column="normalHour" property="normalHour"/>
        <result column="addedHour" property="addedHour"/>
        <collection property="TaskUserInfoVoList"
                    ofType="com.gok.pboot.pms.entity.vo.TaskUserInfoVO">
            <result column="userId" property="userId"/>
            <result column="name" property="name"/>
        </collection>
    </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
					a.id AS 'id',
					a.task_name AS 'taskName',
					a.task_type AS 'taskType',
					a.task_status AS 'taskStatus',
					a.project_id AS 'projectId',
					a.creator AS 'creator',
					a.creator_id AS 'creatorId',
					a.modifier AS 'modifier',
					a.modifier_id AS 'modifierId',
					a.ctime AS 'ctime',
					a.mtime AS 'mtime',
                    a.del_flag AS 'delFlag'
        </sql>

    <sql id="join"></sql>
    <select id="totalMap" resultType="map">
        SELECT COUNT(totalTasks)  AS 'totalTasks',SUM(totalMhours) AS 'totalMhours' FROM ( SELECT
             COUNT(a.id) AS 'totalTasks',
             IFNULL(SUM(b.normal_hours),0)+IFNULL(SUM(b.added_hours),0) AS 'totalMhours'
        FROM mhour_task a
        LEFT JOIN mhour_daily_paper_entry b ON a.id = b.task_id AND b.del_flag = 0
        AND b.approval_status =  ${@<EMAIL>()}
        <where>
            a.project_id = #{id}
            AND a.del_flag = 0
        </where>
        GROUP BY a.id ) a
    </select>

    <select id="selectListVo" resultMap="BaseResultMapTaskInfoVo">
        SELECT t1.*,t2.* from (
        SELECT
                a.id AS 'id',
                a.task_name AS 'taskName',
                a.task_type AS 'taskType',
                (CASE   WHEN a.task_type =  0 THEN '默认任务'
                        WHEN a.task_type =  1 THEN '手动添加'
                        WHEN a.task_type =  2 THEN '后期维保'
                        END) AS 'taskTypeName',
                a.task_status AS 'taskStatus',
                (CASE   WHEN a.task_status =  0 THEN '正常'
                        WHEN a.task_status =  1 THEN '关闭'
                    END) AS 'taskStatusName',
                a.project_id AS 'projectId',
                IFNULL(SUM(b.normal_hours),0)+IFNULL(SUM(b.added_hours),0) AS 'actualHour',
                IFNULL(SUM(b.normal_hours),0) AS 'normalHour',
                IFNULL(SUM(b.added_hours),0) AS 'addedHour'

        FROM mhour_task a
        LEFT JOIN mhour_daily_paper_entry b ON a.id = b.task_id
                                            AND b.approval_status =  ${@<EMAIL>()}
                                            AND b.del_flag = 0
        <where>
            a.project_id = #{id}
            AND a.del_flag = 0
            <if test="filter.taskName != null and filter.taskName != ''">
                AND a.task_name  like concat('%',#{filter.taskName},'%')
            </if>
        </where>
        GROUP BY a.id
        LIMIT #{adapter.begin} , #{adapter.size}
            ) t1 LEFT JOIN
        (
        SELECT  c.task_id,c.user_id AS 'userId',
        c.user_name AS 'name' from mhour_task_user c
        WHERE  c.del_flag = 0
        ) t2 ON t1.id = t2.task_id
    </select>

    <select id="getTaskInfo" resultMap="BaseResultMapTaskInfoVo">
        SELECT t1.*,t2.* from (
        SELECT
        a.id AS 'id',
        a.task_name AS 'taskName',
        a.task_type AS 'taskType',
        (CASE   WHEN a.task_type =  0 THEN '默认任务'
        WHEN a.task_type =  1 THEN '手动添加'
        WHEN a.task_type =  2 THEN '后期维保'
        END) AS 'taskTypeName',
        a.task_status AS 'taskStatus',
        (CASE   WHEN a.task_status =  0 THEN '正常'
        WHEN a.task_status =  1 THEN '关闭'
        END) AS 'taskStatusName',
        a.project_id AS 'projectId',
        IFNULL(SUM(b.normal_hours),0)+IFNULL(SUM(b.added_hours),0) AS 'actualHour',
        IFNULL(SUM(b.normal_hours),0) AS 'normalHour',
        IFNULL(SUM(b.added_hours),0) AS 'addedHour'

        FROM mhour_task a
        LEFT JOIN mhour_daily_paper_entry b ON a.id = b.task_id
        <if test="approvalStatus != null">
            AND b.approval_status =  ${@<EMAIL>()}
        </if>
        AND b.del_flag = 0
        <where>
            a.id = #{id}
            AND a.del_flag = 0
        </where>
        GROUP BY a.id) t1 LEFT JOIN
        (
        SELECT  c.task_id,c.user_id AS 'userId',
        c.user_name AS 'name' from mhour_task_user c
        WHERE  c.del_flag = 0
        ) t2 ON t1.id = t2.task_id
    </select>
    <select id="selectTaskCount" resultType="Long">
        SELECT
        count(0)
        FROM mhour_task a
        <where>
            a.project_id = #{id} AND a.del_flag = 0
            <if test="filter.taskName != null and filter.taskName != ''">
                AND a.task_name  like concat('%',#{filter.taskName},'%')
            </if>
        </where>
    </select>
    <select id="selectTaskCountMr" resultType="Long">
        SELECT
        count(0)
        FROM mhour_task a
        <where>
            a.project_id = #{id}
        </where>
    </select>

    <select id="findList" resultType="com.gok.pboot.pms.entity.domain.Task">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_task a
        <include refid="join"/>
        <where>
            a.del_flag = 0
        </where>
        ORDER BY a.id desc
    </select>

    <select id="findByUserIdAndProjectIdForEntry" resultType="com.gok.pboot.pms.common.join.TaskInDailyPaperEntry">
        SELECT
            task.id AS id,
            task.task_name AS taskName
        FROM mhour_task task
        LEFT JOIN mhour_task_user tu
        ON
            tu.task_id = task.id
        LEFT JOIN mhour_project project
        ON
            project.id = task.project_id
        WHERE
            tu.user_id = #{userId}
        AND
            project.id = #{projectId}
        AND
            task.task_status = 0
        AND
            tu.del_flag = 0
        AND
            project.del_flag = 0
        AND
            task.del_flag = 0
    </select>

    <!--逻辑删除-->
    <update id="deleteByLogic">
        UPDATE mhour_task SET
        del_flag = 1
        WHERE id = #{id}
    </update>


    <insert id="batchSave">
        INSERT INTO mhour_task (
                id,
                task_name,
                task_type,
                task_status,
                project_id,
                creator,
                creator_id,
                modifier,
                modifier_id,
                ctime,
                mtime,
                del_flag
        )VALUES
        <foreach collection="poList" item="item" separator=",">
            (
                    #{item.id},
                    #{item.taskName},
                    #{item.taskType},
                    #{item.taskStatus},
                    #{item.projectId},
                    #{item.creator},
                    #{item.creatorId},
                    #{item.modifier},
                    #{item.modifierId},
                    #{item.ctime},
                    #{item.mtime},
                    #{item.delFlag}
            )
        </foreach>
    </insert>

    <update id="batchDel">
        update mhour_task set del_flag = 1 where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <insert id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE mhour_task  SET
                    id = #{item.id},
                    task_name = #{item.taskName},
                    task_type = #{item.taskType},
                    task_status = #{item.taskStatus},
                    actual_hour = #{item.actualHour},
                    normal_hour = #{item.normalHour},
                    added_hour = #{item.addedHour},
                    user_type = #{item.userType},
                    project_id = #{item.projectId},
                    task_user_id = #{item.taskUserId},
                    hour_confirmed = #{item.hourConfirmed},
                    hour_confirmer_user_id = #{item.hourConfirmerUserId},
                    hour_confirmer_real_name = #{item.hourConfirmerRealName},
                    creator = #{item.creator},
                    creator_id = #{item.creatorId},
                    modifier = #{item.modifier},
                    modifier_id = #{item.modifierId},
                    ctime = #{item.ctime},
                    mtime = #{item.mtime},
                    del_flag = #{item.delFlag}
            where id = #{item.id}
        </foreach>
    </insert>

    <!--批量更新非空字段-->
    <update id="updateBatch" parameterType="arraylist">
        update mhour_task
        <trim prefix="set" suffixOverrides=",">
                    <trim prefix=" task_name =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.taskName!=null">
                                when id=#{item.id} then #{item.taskName}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" task_type =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.taskType!=null">
                                when id=#{item.id} then #{item.taskType}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" task_status =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.taskStatus!=null">
                                when id=#{item.id} then #{item.taskStatus}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" actual_hour =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.actualHour!=null">
                                when id=#{item.id} then #{item.actualHour}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" normal_hour =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.normalHour!=null">
                                when id=#{item.id} then #{item.normalHour}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" added_hour =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.addedHour!=null">
                                when id=#{item.id} then #{item.addedHour}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" user_type =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.userType!=null">
                                when id=#{item.id} then #{item.userType}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" project_id =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.projectId!=null">
                                when id=#{item.id} then #{item.projectId}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" task_user_id =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.taskUserId!=null">
                                when id=#{item.id} then #{item.taskUserId}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" hour_confirmed =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.hourConfirmed!=null">
                                when id=#{item.id} then #{item.hourConfirmed}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" hour_confirmer_user_id =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.hourConfirmerUserId!=null">
                                when id=#{item.id} then #{item.hourConfirmerUserId}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" hour_confirmer_real_name =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.hourConfirmerRealName!=null">
                                when id=#{item.id} then #{item.hourConfirmerRealName}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" modifier =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.modifier!=null">
                                when id=#{item.id} then #{item.modifier}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" modifier_id =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.modifierId!=null">
                                when id=#{item.id} then #{item.modifierId}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" mtime =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.mtime!=null">
                                when id=#{item.id} then #{item.mtime}
                            </if>
                        </foreach>
                    </trim>
        </trim>

        where
        id in
        <foreach collection="list" separator="," item="item" open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <select id="findByProjectId" resultType="com.gok.pboot.pms.entity.domain.Task">
        SELECT
            <include refid="Base_Column_List"/>
        FROM mhour_task a
        WHERE
            del_flag = 0
        AND
            project_id = #{projectId}
    </select>


    <select id="selectDefaultPersonnel" resultMap="BaseResultMapTaskInfoVo">
        SELECT
        a.id AS 'id',
        a.project_id AS 'projectId',
        c.user_id AS 'userId'
        FROM mhour_task a
        LEFT JOIN mhour_task_user c ON a.id = c.task_id AND c.del_flag = 0
        WHERE
              a.task_type IN (0)
          <if test="projectIds.size() > 0 ">
              AND a.project_id IN
              <foreach collection="projectIds" item="id" open="(" separator="," close=")">
                  #{id}
              </foreach>
          </if>
    </select>

    <select id="findByUserIdAndProjectIdForEntryVos" resultType="com.gok.pboot.pms.entity.vo.TaskInDailyPaperEntryListVo">
        SELECT
            project.id AS projectId,
            task.id AS id,
            task.task_name AS taskName
        FROM mhour_task task
         LEFT JOIN mhour_task_user tu
                   ON
                       tu.task_id = task.id
         LEFT JOIN mhour_project project
                   ON
                       project.id = task.project_id
        WHERE 1=1
          AND tu.user_id = #{userId}
        <if test="list != null and list.size() > 0">
            AND project.id IN
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="list == null or list.size() == 0">
            AND project.id = null
        </if>
        AND
        task.task_status = 0
        AND
        tu.del_flag = 0
        AND
        project.del_flag = 0
        AND
        task.del_flag = 0
    </select>

    <select id="findAll" resultType="com.gok.pboot.pms.entity.domain.Task">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_task a
        WHERE a.del_flag = 0
    </select>

    <select id="findToSyncMember" resultType="com.gok.pboot.pms.entity.domain.ProjectStakeholderMember">
        SELECT
        tu.id as id,
        t.project_id as projectId,
        tu.user_id as memberId,
        tu.user_name as memberName,
        NULL as deptName,
        NULL as position,
        4 as roleType,
        NULL as duty,
        NULL as remark,
        0 as syncOaType,
        tu.creator as creator,
        tu.creator_id as creatorId,
        tu.modifier as modifier,
        tu.modifier_id as modifierId,
        tu.ctime as ctime,
        tu.mtime as mtime,
        0 as del_flag
        FROM
        mhour_task t
        INNER JOIN mhour_task_user tu ON t.id = tu.task_id
        WHERE
        t.del_flag = 0
        AND tu.del_flag = 0
        AND tu.user_id NOT IN ( SELECT m.member_id FROM project_stakeholder_member m WHERE m.project_id = t.project_id AND m.del_flag = 0 )
        GROUP BY
        t.project_id,
        tu.user_id;
    </select>

</mapper>
