<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.ProjectWorkHoursMapper">

    <sql id="Base_Column_List">
        id,
        task_id,
        task_name,
        daily_paper_id,
        user_real_name,
        user_id,
        work_type,
        submission_date,
        normal_hours,
        added_hours,
        work_overtime_hours,
        rest_overtime_hours,
        holiday_overtime_hours,
        description,
        approval_status,
        ctime
    </sql>

    <select id="findApprovalPageByProjectIdAndBetween" resultMap="BaseResultMapProjectTaskFindPageVo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_daily_paper_entry
        <where>
            del_flag = 0
            AND approval_status = ${@<EMAIL>()}
            <if test="filter.projectId != null ">
                AND project_id = #{filter.projectId}
            </if>
            <if test="filter.startDate != null and filter.endDate != null">
                AND submission_date BETWEEN #{filter.startDate} AND #{filter.endDate}
            </if>
            <if test="filter.taskName != null">
                AND task_name like CONCAT('%',#{filter.taskName},'%')
            </if>
            <if test="filter.userRealName != null and filter.userRealName != ''">
                AND user_real_name like CONCAT('%',#{filter.userRealName},'%')
            </if>
            <if test="filter.taskIds != null and filter.taskIds != ''">
                AND task_id IN
                <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitLongsStr">
                    <property name="longsStr" value="filter.taskIds"/>
                </include>
            </if>
        </where>
        GROUP BY
        task_id,
        user_id,
        ctime
        ORDER BY ctime DESC
    </select>

    <select id="getTaskByTaskNameLike" resultType="com.gok.pboot.pms.entity.vo.ProjectTaskFindPageVO">
        SELECT
            id as taskId,
            title as taskName
        FROM project_taske
        where del_flag = 0
          AND project_id = #{projectId}
        ORDER BY ctime DESC
    </select>


    <!--获取昨日计划-->
    <select id="getYesterdayPlan" resultType="com.gok.pboot.pms.entity.vo.WorkHoursDetailVO">
        <foreach collection="voList" item="vo" index="idx">
            <if test="idx != 0">
                UNION ALL
            </if>
            (SELECT
            #{vo.id} AS id,
            description AS yesterdayPlan
            FROM mhour_tomorrow_plan_paper_entry
            <where>
                del_flag = ${@<EMAIL>()}
                AND user_id = #{vo.userId}
                AND project_id = #{filter.projectId}
                AND task_id = #{vo.taskId}
                AND submission_date <![CDATA[ < ]]> #{vo.submissionDate}
            </where>
            ORDER BY
            submission_date DESC
            LIMIT 1)
        </foreach>
    </select>

    <!--根据项目id集合和时间获取昨日数据-->
    <select id="findByTaskIdAndUserIdBetween" resultMap="BaseResultMapProjectTaskFindPageVo">
        SELECT
        id,
        task_id,
        task_name,
        user_real_name,
        user_id,
        submission_date,
        description,
        ctime
        FROM mhour_daily_paper_entry
        <where>
            del_flag = 0
            AND approval_status IN
            (${@<EMAIL>()},
            ${@<EMAIL>()})
            <if test="taskIdList != null and taskIdList.size>0">
                AND task_id IN
                <foreach collection="taskIdList" item="taskId" open="(" separator="," close=")">
                    #{taskId}
                </foreach>
            </if>
            <if test="userIdList != null and userIdList.size>0">
                AND user_id IN
                <foreach collection="userIdList" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="startDate != null and endDate != null">
                AND submission_date BETWEEN #{startDate} AND #{endDate}
            </if>
        </where>
    </select>

    <!--处理工时统计的通用where条件-->
    <sql id="processWorkHoursParams">
        <where>
            del_flag = 0
            and approval_status in(
                ${@<EMAIL>()},
                ${@<EMAIL>()}
            )
            <if test="filter.projectId != null ">
                and project_id = #{filter.projectId}
            </if>
            <if test="filter.startDate != null and filter.endDate != null">
                and submission_date between #{filter.startDate} and #{filter.endDate}
            </if>
            <if test="filter.taskName != null">
                and task_name like CONCAT('%',#{filter.taskName},'%')
            </if>
            <if test="filter.userRealName != null and filter.userRealName != ''">
                and user_real_name like CONCAT('%',#{filter.userRealName},'%')
            </if>
            <if test="filter.taskIds != null and filter.taskIds != ''">
                and task_id in
                <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitLongsStr">
                    <property name="longsStr" value="filter.taskIds"/>
                </include>
            </if>
            <if test="filter.workType != null">
                and work_type = #{filter.workType}
            </if>
            <if test="filter.userIds != null and filter.userIds != ''">
                and user_id in
                <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitLongsStr">
                    <property name="longsStr" value="filter.userIds"/>
                </include>
            </if>
        </where>
    </sql>

    <select id="calculateWorkHours" resultType="com.gok.pboot.pms.entity.vo.ProjectWorkHoursVo">
        select
            sum( t.normal_hours ) avgNormalHours,
            sum( t.added_hours ) avgAddedHours,
            sum( t.work_overtime_hours ) work_overtime_hours,
            sum( t.rest_overtime_hours ) rest_overtime_hours,
            sum( t.holiday_overtime_hours ) holiday_overtime_hours,
            sum( t.dsh_hours ) dsh_hours,
            sum( t.tx_hours ) tx_hours
        from
        (
            select
                sum(
                case when approval_status = ${@<EMAIL>()}
                then normal_hours else 0 end
                ) normal_hours,
                sum(
                case when approval_status = ${@<EMAIL>()}
                then added_hours else 0 end
                ) added_hours,
                sum(
                case when approval_status = ${@<EMAIL>()}
                then work_overtime_hours else 0 end
                ) work_overtime_hours,
                sum(
                case when approval_status = ${@<EMAIL>()}
                then rest_overtime_hours else 0 end
                ) rest_overtime_hours,
                sum(
                case when approval_status = ${@<EMAIL>()}
                then holiday_overtime_hours else 0 end
                ) holiday_overtime_hours,
                sum(
                case when approval_status = ${@<EMAIL>()}
                then normal_hours + added_hours else 0 end
                ) dsh_hours,
                0 as tx_hours,
                ctime
            from
                mhour_daily_paper_entry
            <include refid="processWorkHoursParams"/>

            <if test="filter.dimension == 0 and (filter.taskIds == null or filter.taskIds == '')">
                union all

                select
                    0                   normal_hours,
                    0                   added_hours,
                    0                   work_overtime_hours,
                    0                   rest_overtime_hours,
                    0                   holiday_overtime_hours,
                    0                   dsh_hours,
                    sum(a.hour_data)    tx_hours,
                    null                ctime
                from mhour_compensatory_leave_data a
                <where>
                    not exists (select 1 from mhour_holiday h where h.day_date = a.belong_date)
                    <if test="filter.projectId != null">
                        and a.xmmc = #{filter.projectId}
                    </if>
                    <if test="filter.startDate != null and filter.endDate != null">
                        and a.belong_date between #{filter.startDate} and #{filter.endDate}
                    </if>
                    <if test="filter.userIds != null and filter.userIds != ''">
                        and a.oa_id in
                        <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitLongsStr">
                            <property name="longsStr" value="filter.userIds"/>
                        </include>
                    </if>
                </where>
            </if>
            ) t order by t.ctime desc
    </select>


    <!--获取不同项目累计工时统计-->
    <select id="batchCalculateAvgWorkHours" resultType="com.gok.pboot.pms.entity.vo.ProjectWorkHoursVo">
        <foreach collection="filterList" item="filter" index="idx">
            <if test="idx != 0">
                UNION ALL
            </if>
            (SELECT
            #{filter.projectId} AS projectId,
            SUM( normal_hours ) AS avgNormalHours,
            SUM( added_hours ) AS avgAddedHours,
            #{filter.startDate} AS startDate,
            #{filter.endDate} AS endDate
            FROM mhour_daily_paper_entry
            <where>
                del_flag = 0
                AND approval_status = ${@<EMAIL>()}
                <if test="filter.projectId != null ">
                    AND project_id = #{filter.projectId}
                </if>
                <if test="filter.endDate != null ">
                    <choose>
                        <when test="filter.startDate != null">
                            AND submission_date BETWEEN #{filter.startDate} AND #{filter.endDate} )
                        </when>
                        <when test="filter.startDate == null">
                            AND submission_date <![CDATA[ <= ]]> #{filter.endDate}
                            )
                        </when>
                    </choose>
                </if>
            </where>
        </foreach>
    </select>

    <select id="findGroupByUserId" resultType="com.gok.pboot.pms.entity.vo.ProjectTaskFindPageVO">
        select
            t.id id,
            t.task_id task_id,
            t.task_name task_name,
            t.user_real_name user_real_name,
            t.user_id user_id,
            t.work_type work_type,
            sum( t.normal_hours ) normal_hours,
            sum( t.added_hours ) added_hours,
            sum( t.work_overtime_hours ) work_overtime_hours,
            sum( t.rest_overtime_hours ) rest_overtime_hours,
            sum( t.holiday_overtime_hours ) holiday_overtime_hours,
            sum( t.dsh_hours ) dsh_hours,
            sum( t.tx_hours ) tx_hours,
            t.description description,
            t.approval_status approval_status,
            t.ctime ctime
        from
        (
        select
            id,
            task_id,
            task_name,
            user_real_name,
            user_id,
            work_type,
            sum(
            case when approval_status = ${@<EMAIL>()}
            then normal_hours else 0 end
            ) normal_hours,
            sum(
            case when approval_status = ${@<EMAIL>()}
            then added_hours else 0 end
            ) added_hours,
            sum(
            case when approval_status = ${@<EMAIL>()}
            then work_overtime_hours else 0 end
            ) work_overtime_hours,
            sum(
            case when approval_status = ${@<EMAIL>()}
            then rest_overtime_hours else 0 end
            ) rest_overtime_hours,
            sum(
            case when approval_status = ${@<EMAIL>()}
            then holiday_overtime_hours else 0 end
            ) holiday_overtime_hours,
            sum(
            case when approval_status = ${@<EMAIL>()}
            then normal_hours + added_hours else 0 end
            ) dsh_hours,
            0 as tx_hours,
            description,
            approval_status,
            ctime
        from mhour_daily_paper_entry
            <include refid="processWorkHoursParams"/>
        group by
            user_id

        <if test="filter.taskIds == null or filter.taskIds == ''">
            union all

            select
                null                id,
                null                task_id,
                null                task_name,
                null                user_real_name,
                 a.oa_id            user_id,
                null                work_type,
                0                   normal_hours,
                0                   added_hours,
                0                   work_overtime_hours,
                0                   rest_overtime_hours,
                0                   holiday_overtime_hours,
                0                   dsh_hours,
                sum(a.hour_data )   tx_hours,
                null                description,
                null                approval_status,
                null                ctime
            from mhour_compensatory_leave_data a
            <where>
                not exists (select 1 from mhour_holiday h where h.day_date = a.belong_date)
                <if test="filter.projectId != null">
                    and a.xmmc = #{filter.projectId}
                </if>
                <if test="filter.startDate != null and filter.endDate != null">
                    and a.belong_date between #{filter.startDate} and #{filter.endDate}
                </if>
                <if test="filter.userIds != null and filter.userIds != ''">
                    and a.oa_id in
                    <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitLongsStr">
                        <property name="longsStr" value="filter.userIds"/>
                    </include>
                </if>
            </where>
            group by oa_id
        </if>
        ) t group by t.user_id order by t.ctime desc
    </select>

    <select id="findGroupByTaskId" resultType="com.gok.pboot.pms.entity.vo.ProjectTaskFindPageVO">
        select
            id,
            task_id,
            task_name,
            user_real_name,
            user_id,
            work_type,
            sum(
            case when approval_status = ${@<EMAIL>()}
            then normal_hours else 0 end
            ) normal_hours,
            sum(
            case when approval_status = ${@<EMAIL>()}
            then added_hours else 0 end
            ) added_hours,
            sum(
            case when approval_status = ${@<EMAIL>()}
            then work_overtime_hours else 0 end
            ) work_overtime_hours,
            sum(
            case when approval_status = ${@<EMAIL>()}
            then rest_overtime_hours else 0 end
            ) rest_overtime_hours,
            sum(
            case when approval_status = ${@<EMAIL>()}
            then holiday_overtime_hours else 0 end
            ) holiday_overtime_hours,
            sum(
            case when approval_status = ${@<EMAIL>()}
            then normal_hours + added_hours else 0 end
            ) dsh_hours,
            0 as tx_hours,
            description,
            approval_status,
            ctime
        from mhour_daily_paper_entry
            <include refid="processWorkHoursParams"/>
        group by
            task_id
        order by ctime desc
    </select>

    <select id="getDetailByDimension" resultType="com.gok.pboot.pms.entity.vo.WorkHoursDetailVO">
        select
            a.user_id                user_id,
            a.user_real_name         user_real_name,
            a.task_id                task_id,
            a.task_name              task_name,
            a.work_type              work_type,
            a.description            today_job,
            a.normal_hours           normal_hours,
            a.added_hours            added_hours,
            a.work_overtime_hours    work_overtime_hours,
            a.rest_overtime_hours    rest_overtime_hours,
            a.holiday_overtime_hours holiday_overtime_hours,
            a.submission_date        submission_date,
            a.approval_status        approval_status
        from
            mhour_daily_paper_entry a
        <where>
            a.del_flag = 0
            and a.approval_status = ${@<EMAIL>()}
            <if test="filter.projectId != null">
                and a.project_id = #{filter.projectId}
            </if>
            <if test="filter.startDate != null and filter.endDate != null">
                and a.submission_date between #{filter.startDate} and #{filter.endDate}
            </if>
            <if test="filter.workType != null">
                and a.work_type = #{filter.workType}
            </if>
            <if test="filter.taskIds != null and filter.taskIds != ''">
                and a.task_id in
                <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitLongsStr">
                    <property name="longsStr" value="filter.taskIds"/>
                </include>
            </if>
            <if test="filter.userIds != null and filter.userIds != ''">
                and a.user_id in
                <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitLongsStr">
                    <property name="longsStr" value="filter.userIds"/>
                </include>
            </if>
            <if test="filter.dimensionId != null">
                <choose>
                    <when test="filter.dimension == 1">
                        and a.task_id = #{filter.dimensionId}
                    </when>
                    <otherwise>
                        and a.user_id = #{filter.dimensionId}
                    </otherwise>
                </choose>
            </if>
        </where>
        order by a.submission_date desc
    </select>
    <select id="getYesterdayPlan2" resultType="com.gok.pboot.pms.entity.vo.WorkHoursDetailVO">
        select
            user_id userId,
            task_id taskId,
            description yesterdayPlan,
            submission_date submission_date
        from
            mhour_tomorrow_plan_paper_entry
        <where>
            <if test="filter.projectId != null and records != null and records.size() > 0">
                (project_id, user_id, task_id, submission_date + INTERVAL 1 DAY) in
                <foreach collection="records" item="record" open="(" close=")" separator=",">
                    (#{filter.projectId}, #{record.userId}, #{record.taskId}, #{record.submissionDate})
                </foreach>
            </if>
        </where>
    </select>

    <!-- 分页查询映射结果 -->
    <resultMap id="BaseResultMapProjectTaskFindPageVo" type="com.gok.pboot.pms.entity.vo.ProjectTaskFindPageVO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="task_id" property="taskId"/>
        <result column="task_name" property="taskName"/>
        <result column="user_real_name" property="userRealName"/>
        <result column="user_id" property="userId"/>
        <result column="work_type" property="workType"/>
        <result column="normal_hours" property="normalHours"/>
        <result column="added_hours" property="addedHours"/>
        <result column="approval_status" property="approvalStatus"/>
    </resultMap>
</mapper>
