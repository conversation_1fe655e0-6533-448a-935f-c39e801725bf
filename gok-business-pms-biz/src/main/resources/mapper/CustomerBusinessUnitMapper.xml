<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.CustomerBusinessUnitMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.CustomerBusinessUnit">
        <id column="id" property="id" />
        <result column="business_id" property="businessId" />
        <result column="unit_name" property="unitName" />
        <result column="unit_manager_id" property="unitManagerId" />
        <result column="unit_manager" property="unitManager" />
        <result column="creator" property="creator" />
        <result column="creator_id" property="creatorId" />
        <result column="modifier" property="modifier" />
        <result column="modifier_id" property="modifierId" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>


  <!--  批量逻辑删除  -->
  <update id="batchDel">
    UPDATE customer_business_unit SET del_flag = 1 WHERE id IN
    <foreach collection="list" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </update>

  <delete id="deletedByBusinessId">
    delete from  customer_business_unit  WHERE business_id = #{businessId}
  </delete>


  <select id="selectSimplyList"  resultMap="BasePageResultMap">
    SELECT
    u.id, u.business_id, b.name,u.unit_name,u.unit_manager,u.unit_manager_id
    FROM customer_business_unit u
    left join customer_business b on b.id = u.business_id
    where u.del_flag = 0 and u.business_id= #{businessId}
    ORDER BY u.ctime ASC
  </select>

  <select id="findNameList"  parameterType="com.gok.pboot.pms.entity.dto.CustomerBusinessSearchDTO" resultType="com.gok.pboot.pms.entity.vo.CustomerBusinessListVO">
    select id as id, unit_name as name from customer_business_unit where del_flag = 0
    <if test="filter.key != null ">
      <bind name="keyLike" value="'%' + filter.key + '%'"/>
      AND unit_name LIKE #{keyLike}
    </if>
    <if test="filter.businessId != null ">
      AND business_id = #{filter.businessId}
    </if>
    limit #{filter.limit}
  </select>

  <resultMap id="BasePageResultMap" type="com.gok.pboot.pms.entity.vo.CustomerBusinessUnitPageVO">
    <id column="id" property="id" />
    <result column="name" property="businessName" />
    <result column="unit_name" property="unitName" />
    <result column="unit_manager" property="unitManager" />
    <result column="unit_manager_id" property="unitManagerId" />
    <result column="business_id" property="businessId"/>
  </resultMap>

  <select id="findListPage" parameterType="com.gok.pboot.pms.entity.dto.CustomerBusinessUnitPageDTO" resultMap="BasePageResultMap">
    SELECT
    u.id,b.name,u.business_id,u.unit_name,u.unit_manager
    FROM customer_business_unit u
    left join customer_business b on b.id = u.business_id
    <where>
      u.del_flag = 0
      <if test="filter.unitManager != null">
        <bind name="unitManagerLike" value="'%' + filter.unitManager + '%'"/>
        and u.unit_manager LIKE #{unitManagerLike}
      </if>
      <if test="filter.unitName != null ">
        <bind name="unitNameLike" value="'%' + filter.unitName + '%'"/>
        AND u.unit_name LIKE #{unitNameLike}
      </if>
      <if test="filter.businessName != null ">
        <bind name="businessNameLike" value="'%' + filter.businessName + '%'"/>
        AND b.name LIKE #{businessNameLike}
      </if>
      <if test="filter.scope == null and filter.businessIdsInDataScope != null and filter.businessIdsInDataScope.size() > 0">
        and u.business_id in
        <foreach collection="filter.businessIdsInDataScope" item="pId" open="(" separator="," close=")">
          #{pId}
        </foreach>
      </if>
    </where>
    ORDER BY u.ctime DESC
  </select>

  <select id="findByName" resultType="java.lang.Integer">
    select count(*) from customer_business_unit where unit_name = #{unitName}  and del_flag = 0
    <if test="id != null and id != ''">
      AND id!=#{id}
    </if>
  </select>

  <update id="updateById">
    update customer_business_unit
    <trim prefix="SET" suffixOverrides=",">
      <if test="et.businessId != null">business_id = #{et.businessId},</if>
      <if test="et.unitName != null">unit_name = #{et.unitName},</if>
      <if test="et.unitManagerId != null">unit_manager_id = #{et.unitManagerId},</if>
      <if test="et.unitManagerId == null">unit_manager_id = null,</if>
      <if test="et.unitManager != null">unit_manager = #{et.unitManager},</if>
      <if test="et.unitManager == null">unit_manager = null,</if>
      <if test="et.modifier != null">modifier = #{et.modifier},</if>
      <if test="et.modifierId != null">modifier_id = #{et.modifierId},</if>
      <if test="et.mtime != null">mtime = #{et.mtime},</if>
    </trim>
    where  id = #{et.id}
  </update>
  <select id="findId" resultType="java.lang.Long">
    select distinct
    t.business_id
    from (
    select
    a.business_id
    from
    customer_business_unit a
    <where>
      a.del_flag = 0
      <if test='filter.scope != "all"'>
        and (
        a.unit_manager_id in
        <foreach collection="filter.userIdList" item="uid" open="(" separator="," close=")">
          #{uid}
        </foreach>
        )
      </if>
    </where>
    ) t
  </select>


</mapper>
