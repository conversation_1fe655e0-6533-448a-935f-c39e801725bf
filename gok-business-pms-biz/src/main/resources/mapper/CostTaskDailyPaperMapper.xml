<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostTaskDailyPaperMapper">

    <select id="findList" resultType="com.gok.pboot.pms.cost.entity.domain.CostTaskDailyPaper">
        SELECT *
        FROM cost_task_daily_paper
        <where>
            <if test="filter.userId != null">
                AND user_id = #{filter.userId}
            </if>
            <if test="filter.submissionDate != null">
                AND submission_date = #{filter.submissionDate}
            </if>
        </where>
        ORDER BY submission_date DESC
    </select>

    <select id="findBySubmissionDateAndUserIds" resultType="com.gok.pboot.pms.cost.entity.domain.CostTaskDailyPaper">
        SELECT *
        FROM cost_task_daily_paper
        WHERE submission_date = #{submissionDate}
        AND user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <select id="findBySubmissionDateAndUserId" resultType="com.gok.pboot.pms.cost.entity.domain.CostTaskDailyPaper">
        SELECT *
        FROM cost_task_daily_paper
        WHERE submission_date = #{submissionDate}
        AND user_id = #{userId}
    </select>

    <select id="findBySubmissionDateRange" resultType="com.gok.pboot.pms.cost.entity.vo.CostTaskDailyPaperVO">
        SELECT
            dp.*,
            GROUP_CONCAT(DISTINCT dpe.approval_status) AS approval_status_strings
        FROM cost_task_daily_paper dp
        LEFT JOIN cost_task_daily_paper_entry dpe ON dp.id = dpe.daily_paper_id
        WHERE dp.submission_date BETWEEN #{startDate} AND #{endDate}
        AND dp.user_id = #{userId}
        <if test="projectNameLike != null and projectNameLike != ''">
            AND EXISTS (
                SELECT 1
                FROM cost_task_daily_paper_entry e
                WHERE e.daily_paper_id = dp.id
                AND e.project_name LIKE CONCAT('%', #{projectNameLike}, '%')
            )
        </if>
        <if test="approvalTab != null">
            AND dp.approval_status = #{approvalTab}
        </if>
        <if test="approvalList != null and approvalList.size() > 0">
            AND dp.approval_status IN
            <foreach collection="approvalList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        GROUP BY dp.id
        ORDER BY dp.submission_date DESC
    </select>

    <select id="findSubmissionDateByUserIdAndSubmissionDateRangeAndApprovalStatusNot" resultType="java.time.LocalDate">
        SELECT DISTINCT submission_date
        FROM cost_task_daily_paper
        WHERE user_id = #{userId}
        AND submission_date BETWEEN #{startDate} AND #{endDate}
        AND approval_status != #{approvalStatus}
    </select>

    <insert id="batchSave">
        INSERT INTO cost_task_daily_paper (
            id, user_id, user_status, user_dept_id, submission_date,
            workday, filling_state, approval_status, project_count,
            task_count, daily_hour_count, added_hour_count,
            work_overtime_hours, rest_overtime_hours, holiday_overtime_hours,
            submission_time, creator, ctime, modifier, mtime
        )
        VALUES
        <foreach collection="papers" item="paper" separator=",">
            (
                #{paper.id}, #{paper.userId}, #{paper.userStatus}, #{paper.userDeptId}, #{paper.submissionDate},
                #{paper.workday}, #{paper.fillingState}, #{paper.approvalStatus}, #{paper.projectCount},
                #{paper.taskCount}, #{paper.dailyHourCount}, #{paper.addedHourCount},
                #{paper.workOvertimeHours}, #{paper.restOvertimeHours}, #{paper.holidayOvertimeHours},
                #{paper.submissionTime}, #{paper.creator}, #{paper.ctime}, #{paper.modifier}, #{paper.mtime}
            )
        </foreach>
    </insert>

    <update id="updateApprovalStatus">
        <foreach collection="papers" item="paper" separator=";">
            UPDATE cost_task_daily_paper
            SET approval_status = #{paper.approvalStatus},
                modifier = #{paper.modifier},
                mtime = #{paper.mtime}
            WHERE id = #{paper.id}
        </foreach>
    </update>

    <delete id="cleanUpUseless">
        DELETE FROM cost_task_daily_paper
        WHERE project_count = 0
        AND task_count = 0
        AND daily_hour_count = 0
        AND added_hour_count = 0
        AND approval_status = 0
    </delete>

    <select id="findIdBySubmissionDateAndUserId" resultType="java.lang.Long">
        SELECT id
        FROM cost_task_daily_paper
        WHERE submission_date = #{submissionDate}
        AND user_id = #{userId}
        LIMIT 1
    </select>

    <select id="findOneBeforeBySubmissionDateAndUserId" resultType="com.gok.pboot.pms.cost.entity.domain.CostTaskDailyPaper">
        SELECT *
        FROM cost_task_daily_paper
        WHERE submission_date &lt; #{submissionDate}
        AND user_id = #{userId}
        ORDER BY submission_date DESC
        LIMIT 1
    </select>

    <!-- 公共的CTE和WHERE条件 -->
    <sql id="abnormalCommon">
        SELECT 
            t.id as task_id,
            MIN(d.submission_time) as earliest_date
        FROM cost_task_daily_paper d
        INNER JOIN cost_task_daily_paper_entry e ON d.id = e.daily_paper_id
        INNER JOIN cost_deliver_task t ON e.task_id = t.id
        INNER JOIN project_info p ON e.project_id = p.id
        <where>
            <if test="request.abnormalType != null">
                <if test="request.abnormalType == 2 ">
                    and e.approval_status = 2
                    AND CURRENT_DATE - d.submission_date > 1
                </if>
                <if test="request.abnormalType == 4 ">
                    and t.evaluation_status = 0
                    AND e.approval_status = 4
                    AND t.task_status = 10
                    and t.disassembly_type = 0
                </if>
            </if>
            <if test="request.abnormalType == null">
                and ( (e.approval_status = 2
                AND CURRENT_DATE - d.submission_date > 1)
                or
                (t.evaluation_status = 0
                AND e.approval_status = 4
                AND t.task_status = 10
                and t.disassembly_type = 0))
            </if>
            <if test="request.startDate != null">
                AND d.submission_date >= #{request.startDate}
            </if>
            <if test="request.endDate != null">
                AND d.submission_date &lt;= #{request.endDate}
            </if>
            <if test="request.taskOwnerIds != null and request.taskOwnerIds.size() > 0">
                AND d.user_id IN
                <foreach collection="request.taskOwnerIds" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="request.reviewerIds != null and request.reviewerIds.size() > 0">
                AND (
                    (t.parent_id IS NOT NULL AND t.parent_id IN (
                        SELECT id FROM cost_deliver_task WHERE manager_id IN
                        <foreach collection="request.reviewerIds" item="reviewerId" open="(" separator="," close=")">
                            #{reviewerId}
                        </foreach>
                    ))
                    OR (t.parent_id IS NULL AND p.manager_user_id IN
                        <foreach collection="request.reviewerIds" item="reviewerId" open="(" separator="," close=")">
                            #{reviewerId}
                        </foreach>
                    )
                )
            </if>
            <if test="request.projectName != null and request.projectName != ''">
                AND p.item_name LIKE CONCAT('%', #{request.projectName}, '%')
            </if>
        </where>
        GROUP BY t.id

    </sql>

    <sql id="abnormalWhere">
        <where>
            <if test="request.abnormalType != null">
                <if test="request.abnormalType == 2 ">
                    and e.approval_status = 2
                    AND CURRENT_DATE - d.submission_date > 1
                </if>
                <if test="request.abnormalType == 4 ">
                    and t.evaluation_status = 0
                    AND e.approval_status = 4
                    AND t.task_status = 10
                    and t.disassembly_type = 0
                </if>
            </if>
            <if test="request.abnormalType == null">
                and ( (e.approval_status = 2
                AND CURRENT_DATE - d.submission_date > 1)
                or
                (t.evaluation_status = 0
                AND e.approval_status = 4
                AND t.task_status = 10
                and t.disassembly_type = 0))
            </if>

        <if test="request.startDate != null">
            AND d.submission_date >= #{request.startDate}
        </if>
        <if test="request.endDate != null">
            AND d.submission_date &lt;= #{request.endDate}
        </if>
        <if test="request.taskOwnerIds != null and request.taskOwnerIds.size() > 0">
            AND d.user_id IN
            <foreach collection="request.taskOwnerIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="request.reviewerIds != null and request.reviewerIds.size() > 0">
            AND (
                (t.parent_id IS NOT NULL AND t.parent_id IN (
                    SELECT id FROM cost_deliver_task WHERE manager_id IN
                    <foreach collection="request.reviewerIds" item="reviewerId" open="(" separator="," close=")">
                        #{reviewerId}
                    </foreach>
                ))
                OR (t.parent_id IS NULL AND p.manager_user_id IN
                    <foreach collection="request.reviewerIds" item="reviewerId" open="(" separator="," close=")">
                        #{reviewerId}
                    </foreach>
                )
            )
        </if>
        <if test="request.projectName != null and request.projectName != ''">
            AND p.item_name LIKE CONCAT('%', #{request.projectName}, '%')
        </if>

        </where>
    </sql>

    <select id="findAbnormalList" resultType="com.gok.pboot.pms.cost.entity.vo.CostTaskDailyPaperAbnormalVO">
        SELECT
            t.id as taskId,
            t.task_name as taskName,
            t.parent_id as parentId,
            t.task_level as taskLevel,
            p.id as projectId,
            p.item_name as projectName,
            t.task_category,
            t.task_desc,
            t.manager_id,
            t.manager_name,
            t.task_status as taskStatus,
            CASE
                WHEN t.parent_id IS NOT NULL and t.task_level != 1 THEN (
                    SELECT pt.manager_id
                    FROM cost_deliver_task pt
                    WHERE pt.id = t.parent_id
                )
                WHEN t.manager_id = p.pre_sale_user_id and t.task_level = 1 THEN(
                    SELECT mr.leader_id
                    FROM mhour_roster mr
                    WHERE mr.id = t.manager_id
                )
                ELSE p.pre_sale_user_id
            END as reviewerId,
			CASE
                WHEN t.parent_id IS NOT NULL and t.task_level != 1 THEN (
                    SELECT pt.manager_name
                    FROM cost_deliver_task pt
                    WHERE pt.id = t.parent_id
                )
                WHEN t.manager_id = p.pre_sale_user_id and t.task_level = 1 THEN(
                    SELECT r.alias_name
                    FROM mhour_roster mr
					LEFT JOIN mhour_roster r on mr.leader_id = r.id
                    WHERE mr.id = t.manager_id
                )
                ELSE p.pre_sale_user_name
            END as reviewer,
            SUM(e.normal_hours + e.work_overtime_hours + e.rest_overtime_hours + e.holiday_overtime_hours) as pendingReviewHours,
            p.pre_sale_user_id as preSalesManagerId,
            p.pre_sale_user_name as preSalesManager,
        <include refid="abnormalTypeCase"/> as abnormalType,
        te.earliest_date as earliestSubmissionDate
        FROM cost_task_daily_paper d
        INNER JOIN cost_task_daily_paper_entry e ON d.id = e.daily_paper_id
        INNER JOIN cost_deliver_task t ON e.task_id = t.id
        INNER JOIN project_info p ON e.project_id = p.id
        INNER JOIN (<include refid="abnormalCommon"/>) te ON t.id = te.task_id
        <include refid="abnormalWhere"/>
        GROUP BY t.id,abnormalType
        ORDER BY te.earliest_date DESC
    </select>

    <select id="findAbnormalMsgList" resultType="com.gok.pboot.pms.cost.entity.vo.CostTaskDailyPaperAbnormalMsgVO">
        SELECT
            CASE
                WHEN t.parent_id IS NOT NULL and t.task_level != 1 THEN (
                    SELECT pt.manager_id
                    FROM cost_deliver_task pt
                    WHERE pt.id = t.parent_id
                )
                WHEN t.manager_id = p.pre_sale_user_id and t.task_level = 1 THEN(
                    SELECT mr.leader_id
                    FROM mhour_roster mr
                    WHERE mr.id = t.manager_id
                )
                ELSE p.pre_sale_user_id
            END as reviewerId,
			CASE
                WHEN t.parent_id IS NOT NULL and t.task_level != 1 THEN (
                    SELECT pt.manager_name
                    FROM cost_deliver_task pt
                    WHERE pt.id = t.parent_id
                )
                WHEN t.manager_id = p.pre_sale_user_id and t.task_level = 1 THEN(
                     SELECT r.alias_name
                    FROM mhour_roster mr
					LEFT JOIN mhour_roster r on mr.leader_id = r.id
                    WHERE mr.id = t.manager_id
                )
                ELSE p.pre_sale_user_name
            END as reviewerName,
            COUNT(DISTINCT t.id) as taskCount,
            SUM(e.normal_hours + e.work_overtime_hours + e.rest_overtime_hours + e.holiday_overtime_hours) as totalHours
        FROM cost_task_daily_paper d
        INNER JOIN cost_task_daily_paper_entry e ON d.id = e.daily_paper_id
        INNER JOIN cost_deliver_task t ON e.task_id = t.id
        INNER JOIN project_info p ON e.project_id = p.id
        <include refid="abnormalWhere"/>
        GROUP BY reviewerId
    </select>

    <select id="findSupportTaskApprovalList" resultType="com.gok.pboot.pms.cost.entity.vo.CostSupportTaskApprovalDetailVO">
        select t.id,
               t.project_id as projectId,
               t.project_name as projectName,
               t.normal_hours as normalHours,
               t.work_overtime_hours as workOvertimeHours,
               t.rest_overtime_hours as restOvertimeHours,
               t.holiday_overtime_hours as holidayOvertimeHours,
               t.actual_labor_cost as pendingEstimatedCost
        from cost_task_daily_paper_entry t
        left join project_info p on p.id = t.project_id
        left join cost_deliver_task t2 on t2.id = t.task_id and t2.del_flag = ${@<EMAIL>()}
        WHERE
        t.del_flag = ${@<EMAIL>()}
        and t2.task_type = ${@com.gok.pboot.pms.enumeration.ProjectTaskKindEnum@PRE_SALES_SUPPORT.getValue()}
        <if test="dto.projectName != null and dto.projectName != ''">
            AND t.project_name like CONCAT('%', #{dto.projectName}, '%')
        </if>
        <if test="dto.projectId != null and dto.projectId != ''">
            AND t.project_id = #{dto.projectId}
        </if>
        <if test="dto.approvalStatus == 0">
            and t.approval_status = 2
        </if>
        <if test="dto.approvalStatus == 1">
            and t.approval_status = 4
        </if>
        <if test="dto.taskIds != null and dto.taskIds.size() != 0">
            AND t.task_id IN
            <foreach collection="dto.taskIds" item="taskId" open="(" separator="," close=")">
                #{taskId}
            </foreach>
        </if>
        ORDER BY t.ctime DESC
    </select>

    <select id="findApprovalDetailList" resultType="com.gok.pboot.pms.cost.entity.vo.CostTaskDailyPaperApprovalDetailVO">
        SELECT
            e.id,
            e.user_id,
            e.user_real_name,
            e.submission_date as submissionDate,
            h.holiday_type as holidayType,
            t.id as taskId,
            t.task_name as taskName,
            t.task_category as taskCategory,
            e.normal_hours as normalHours,
            e.added_hours as overtimeHours,
            e.work_overtime_hours as workOvertimeHours,
            e.rest_overtime_hours as restOvertimeHours,
            e.holiday_overtime_hours as holidayOvertimeHours,
            e.description as workContent,
            d.submission_time as submissionTime,
            d.filling_state as fillingState,
            e.approval_status as approvalStatus
        FROM cost_task_daily_paper_entry e
        INNER JOIN cost_task_daily_paper d on e.daily_paper_id = d.id
        INNER JOIN cost_deliver_task t ON e.task_id = t.id
        LEFT JOIN project_info p on p.id = t.project_id
        LEFT JOIN mhour_holiday h ON d.submission_date = h.day_date
        WHERE
            e.del_flag = ${@<EMAIL>()}
        <if test="dto.approvalStatus == 0">
            and e.approval_status = 2
        </if>
        <if test="dto.approvalStatus == 1">
            and e.approval_status = 4
        </if>
        <if test="dto.projectId != null">
            AND e.project_id = #{dto.projectId}
        </if>
        <if test="dto.startDate != null">
            AND d.submission_date &gt;= #{dto.startDate}
        </if>
        <if test="dto.endDate != null">
            AND d.submission_date &lt;= #{dto.endDate}
        </if>
        <if test="dto.taskName != null and dto.taskName != ''">
            AND t.task_name LIKE CONCAT('%', #{dto.taskName}, '%')
        </if>
        <if test="dto.userName != null and dto.userName != ''">
            AND e.user_real_name LIKE CONCAT('%', #{dto.userName}, '%')
        </if>
        <if test="dto.taskIds != null and dto.taskIds.size() != 0">
            AND e.task_id IN
            <foreach collection="dto.taskIds" item="taskId" open="(" separator="," close=")">
                #{taskId}
            </foreach>
        </if>
        ORDER BY d.submission_date desc , d.submission_time desc
    </select>

    <select id="findAbnormalCount" resultType="com.gok.pboot.pms.cost.entity.dto.TaskAbnormalCountDTO">
        SELECT 
           <include refid="abnormalTypeCase"/> as abnormalType,
            COUNT(DISTINCT t.id) as count
        FROM cost_task_daily_paper d
        INNER JOIN cost_task_daily_paper_entry e ON d.id = e.daily_paper_id
        INNER JOIN cost_deliver_task t ON e.task_id = t.id
        INNER JOIN project_info p ON e.project_id = p.id
        <include refid="abnormalWhere"/>
        GROUP BY abnormalType
    </select>

    <sql id="abnormalTypeCase">
        CASE
            WHEN e.approval_status = 2 AND CURRENT_DATE - d.submission_date > 1 THEN 2
            WHEN t.evaluation_status = 0 AND e.approval_status = 4 AND t.task_status = 10 and t.disassembly_type = 0 THEN 4
        END
    </sql>
</mapper>