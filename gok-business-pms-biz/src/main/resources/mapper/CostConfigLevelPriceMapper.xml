<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostConfigLevelPriceMapper">


    <select id="getLevelPriceListByVersionIdPage"
            resultType="com.gok.pboot.pms.cost.entity.vo.CostConfigLevelPriceVO">
        SELECT lp.id,
               lp.region,
               lp.job_activity_id,
               lp.personnel_level,
               lp.level_price,
               lp.base_salary_price,
               lp.salary_per_day,
               lp.social_security_per_day,
               lp.housing_fund_per_day,
               lp.disability_levy_per_day,
               cv.version_name
        FROM cost_config_level_price lp
                 LEFT JOIN cost_config_version cv ON cv.id = lp.version_id AND cv.del_flag = 0
        WHERE lp.version_id = #{versionId}
          AND lp.del_flag = 0
        ORDER BY lp.id
    </select>

</mapper>
