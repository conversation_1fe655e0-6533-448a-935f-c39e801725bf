server:
  port: 4003

spring:
  application:
    name: @artifactId@
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_HOST:127.0.0.1}:${NACOS_PORT:8848}
        namespace: gok-business-${spring.profiles.active}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yml
        group: gok-business
        namespace: gok-business-${spring.profiles.active}
        shared-configs[0]:
          data-id: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
          group: gok-business
          namespace: gok-business-@gok-env@
  profiles:
    active: @gok-env@