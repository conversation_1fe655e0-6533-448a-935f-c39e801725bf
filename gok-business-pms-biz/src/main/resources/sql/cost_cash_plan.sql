CREATE TABLE `cost_cash_plan` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `project_id` bigint(20) NOT NULL COMMENT '项目ID',
  `version_id` bigint(20) NOT NULL COMMENT '版本ID',
  `time_month` varchar(20) NOT NULL COMMENT '时间',
  `plan_month` date NOT NULL COMMENT '计划月份 格式：yyyy-MM',
  `month_income` decimal(16,2) DEFAULT NULL COMMENT '流入预测-当月回款',
  `labor_cost` decimal(16,2) DEFAULT NULL COMMENT '流出预测-人工成本',
  `expense_cost` decimal(16,2) DEFAULT NULL COMMENT '流出预测-费用报销',
  `outsourcing_cost` decimal(16,2) DEFAULT NULL COMMENT '流出预测-外采支出',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `creator_id` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `modifier` varchar(64) DEFAULT NULL COMMENT '修改人',
  `modifier_id` bigint(20) DEFAULT NULL COMMENT '修改人ID',
  `ctime` datetime DEFAULT NULL COMMENT '创建时间',
  `mtime` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `del_flag` int(11) NOT NULL DEFAULT '0' COMMENT '删除标识',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_project_version` (`project_id`,`version_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目现金流计划表';


CREATE TABLE `cost_cash_plan_version` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `project_id` bigint(20) NOT NULL COMMENT '项目ID',
  `version_no` varchar(32) NOT NULL COMMENT '版本号',
  `version_status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '版本状态（0=当前版本，1=历史版本）',
  `cost_version_id` bigint(20) DEFAULT NULL COMMENT '关联成本版本id',
  `cost_version` varchar(32) DEFAULT NULL COMMENT '关联成本版本',
  `creator_role` varchar(64) DEFAULT NULL COMMENT '创建人当前角色',
  `creator` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '创建人',
  `creator_id` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `modifier` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '修改人',
  `modifier_id` bigint(20) DEFAULT NULL COMMENT '修改人ID',
  `ctime` datetime DEFAULT NULL COMMENT '创建时间',
  `mtime` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `del_flag` int(11) NOT NULL DEFAULT '0' COMMENT '删除标识',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='现金流计划版本记录表';