---
description: 
globs: 
alwaysApply: false
---
---
description: 
globs: 
alwaysApply: false
---
---
description: 
globs: 
alwaysApply: true
---
# GOK Business PMS 项目规范

## 1. 项目结构规范

### 1.1 基础包结构
```
com.gok.pboot.pms
├── common          // 公共模块
│   ├── base       // 基础类
│   ├── config     // 配置类
│   └── exception  // 异常处理
├── cost           // 成本模块
│   ├── controller // 控制器
│   ├── service    // 服务层
│   ├── mapper     // 数据访问层
│   └── entity     // 实体类
│       ├── domain // 数据库实体
│       ├── dto    // 数据传输对象
│       └── vo     // 视图对象
└── Util           // 工具类
```

### 1.2 命名规范
- 类名：使用大驼峰命名法，如`CostBaselineQuotationController`
- 方法名：使用小驼峰命名法，如`getGrossProfitMeasurementVersionInfo`
- 变量名：使用小驼峰命名法，如`costBaselineQuotation`
- 常量名：全大写，单词间用下划线分隔，如`AUDIT_MSG_TARGET_ID`
- 包名：全小写，如`com.gok.pboot.pms.cost`

## 2. 代码规范

### 2.1 Controller层规范
- 使用`@RestController`注解
- 使用`@RequiredArgsConstructor`进行构造器注入
- 统一使用`ApiResult`封装响应结果
- 请求路径使用小写，如`/costBaselineQuotation`
- 方法命名规范：
  - 查询：get/query
  - 新增：insert/add
  - 更新：update
  - 删除：delete/remove

### 2.2 Service层规范
- 接口命名以`I`开头，如`ICostBaselineQuotationService`
- 实现类以`Impl`结尾，如`CostBaselineQuotationServiceImpl`
- 使用`@Service`注解
- 使用`@RequiredArgsConstructor`进行构造器注入
- 事务方法使用`@Transactional(rollbackFor = Exception.class)`

### 2.3 Mapper层规范
- 继承`BaseMapper<T>`
- 使用`@Mapper`注解
- XML文件命名与Mapper接口对应
- 使用`@Param`注解标注参数

### 2.4 实体类规范
- Domain类：
  - 使用`@TableName`注解
  - 继承`BeanEntity<T>`
  - 使用Lombok注解简化代码
- DTO类：
  - 用于数据传输
  - 使用Lombok注解
- VO类：
  - 用于视图展示
  - 使用Lombok注解
  - 使用`@JsonFormat`格式化日期

## 3. 数据库规范

### 3.1 表命名规范
- 使用小写字母
- 单词间用下划线分隔
- 如：`cost_baseline_quotation`

### 3.2 字段命名规范
- 使用小写字母
- 单词间用下划线分隔
- 如：`income_amount_included_tax`

### 3.3 字段类型规范
- 金额类型使用`decimal`
- 时间类型使用`datetime`
- 状态字段使用`tinyint`
- ID字段使用`bigint`

## 4. 异常处理规范

### 4.1 业务异常
- 使用`ServiceException`抛出业务异常
- 异常信息要清晰明确
- 在Service层抛出异常

### 4.2 参数校验
- 使用`@Valid`注解进行参数校验
- 使用`ValidationException`处理参数异常

## 5. 日志规范

### 5.1 日志级别使用
- ERROR：系统错误
- WARN：警告信息
- INFO：重要业务信息
- DEBUG：调试信息
- TRACE：详细调试信息

### 5.2 日志格式
- 使用`@Slf4j`注解
- 日志信息要包含关键业务信息
- 异常日志要包含完整的堆栈信息

## 6. 安全规范

### 6.1 权限控制
- 使用`SecurityUtils`获取当前用户信息
- 敏感操作需要权限校验
- 用户信息不要直接暴露给前端

### 6.2 数据安全
- 敏感数据需要加密存储
- 接口需要做防SQL注入处理
- 文件上传需要做类型和大小限制

## 7. 性能规范

### 7.1 数据库操作
- 合理使用索引
- 避免大事务
- 使用批量操作代替循环单条操作
- 避免循环调用数据库操作

### 7.2 缓存使用
- 合理使用缓存
- 注意缓存更新策略
- 避免缓存雪崩

## 8. 测试规范

### 8.1 单元测试
- 使用JUnit5
- 测试用例要覆盖主要业务场景
- 测试数据要独立

### 8.2 接口测试
- 使用Postman等工具
- 测试用例要包含正常和异常场景
- 测试数据要及时清理

## 9. 版本控制规范

### 9.1 Git提交规范
- 类型必须包含[scan,feat,fix,docs,style,refactor,test,chore,revert,Merge,deploy]
- 提交信息要清晰明确
- 使用feature分支开发新功能
- 使用hotfix分支修复紧急bug

### 9.2 版本号规范
- 遵循语义化版本号
- 主版本号：不兼容的API修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正 